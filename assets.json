[{"slug": "load_db_tw", "description": "Loads test windows", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_01_SYS_TEST_WINDOWS"}, "dependencySourcings": [{"param": "tw_ids", "type": "job-config", "config": {"configSlug": "pipeline_config", "param": "test_window_ids"}}]}, {"slug": "load_db_twtar", "description": "Loads test window test design allocation rules based on test window ids", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_02_ASMT_SPECS_TEST_WINDOW_ALLOC_RULES"}, "dependencySourcings": [{"param": "tw_ids", "type": "asset-col", "config": {"asset": "load_db_tw", "col": "test_window_id"}}, {"param": "include_sample_assessments", "type": "job-config", "config": {"configSlug": "pipeline_config", "param": "include_sample_assessments"}}]}, {"slug": "load_db_form_designs", "description": "Loads form designs for the ABED data export", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_03_ASMT_CONTENT_FORM_DESIGNS"}, "dependencySourcings": [{"param": "twtar_ids", "type": "asset-col", "config": {"asset": "load_db_twtar", "col": "twtar_id"}}]}, {"slug": "load_db_item_scale_register_consolidated", "description": "Loads form designs for the ABED data export", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_03_ASMT_CONTENT_ITEM_SCALE_REGISTER"}, "dependencySourcings": [{"param": "item_ids", "type": "asset-col", "config": {"asset": "load_db_tqr", "col": "item_id"}}]}, {"slug": "load_db_tfmi", "description": "Loads test form items based on test window ids", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_03_ASMT_CONTENT_FORM_MODULE_ITEMS"}, "dependencySourcings": [{"param": "twtar_ids", "type": "asset-col", "config": {"asset": "load_db_twtar", "col": "twtar_id"}}]}, {"slug": "load_db_tqr", "description": "Loads test question registers based on test window ids", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_03_ASMT_CONTENT_ITEM_REGISTER"}, "dependencySourcings": [{"param": "td_ids", "type": "asset-col", "config": {"asset": "load_db_twtar", "col": "td_id"}}]}, {"slug": "load_db_td", "description": "Returns test_design information from the db", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_03_ASMT_CONTENT_TEST_DESIGNS"}, "dependencySourcings": [{"param": "td_ids", "type": "asset-col", "config": {"asset": "load_db_twtar", "col": "td_id"}}]}, {"slug": "load_db_tqer", "description": "Returns expected responses to questions from test windows", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_03_ASMT_CONTENT_ITEM_EXPECTED_RESPONSES"}, "dependencySourcings": [{"param": "item_ids", "type": "asset-col", "config": {"asset": "load_db_tqr", "col": "item_id"}}]}, {"slug": "load_db_item_expected_answers", "description": "(duplicate of load_db_tqer) Returns expected responses to questions from test windows", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_03_ASMT_CONTENT_ITEM_EXPECTED_RESPONSES"}, "dependencySourcings": [{"param": "item_ids", "type": "asset-col", "config": {"asset": "load_db_tqr", "col": "item_id"}}]}, {"slug": "load_db_schools", "description": "Loads all available schools", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_04A_GROUPS_SCHOOLS"}, "dependencySourcings": [{"param": "include_sample_schools", "type": "job-config", "config": {"configSlug": "pipeline_config", "param": "include_sample_schools"}}]}, {"slug": "load_db_schools_w_pre_participation", "description": "Loads schools participation status", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_04A_GROUPS_SCHOOL_W_PRE_PARTIC"}, "dependencySourcings": [{"param": "s_ids", "type": "asset-col", "config": {"asset": "load_db_schools", "col": "s_id"}}, {"param": "tw_ids", "type": "asset-col", "config": {"asset": "load_db_tw", "col": "test_window_id"}}]}, {"slug": "load_db_school_groupings", "description": "Loads groupings associated with an administration window (including those that have since been deleted)", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_04B_REGISTRATIONS_SCHOOL_GROUPINGS"}, "dependencySourcings": [{"param": "s_ids", "type": "asset-col", "config": {"asset": "load_db_schools", "col": "s_id"}}, {"param": "tw_ids", "type": "asset-col", "config": {"asset": "load_db_tw", "col": "test_window_id"}}]}, {"slug": "load_db_student_grouping_registrations", "description": "Loads enrolled (or formely enrolled) students linkage ids", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_04B_REGISTRATIONS_STU_G_REGSTRATIONS"}, "dependencySourcings": [{"param": "sc_gids", "type": "asset-col", "config": {"asset": "load_db_school_groupings", "col": "sc_gid"}}]}, {"slug": "load_db_students", "description": "Loads enrolled (or formerly enrolled) students who could have accessed the assessment", "structure": "dataframe", "method": "query-chunked", "methodConfig": {"querySlug": "SQL_04B_REGISTRATIONS_STUDENTS", "chunkSize": 1000}, "dependencySourcings": [{"param": "uids", "type": "asset-col", "config": {"asset": "load_db_student_grouping_registrations", "col": "uid"}}]}, {"slug": "load_db_student_admin_meta", "description": "Loads data for the students sheet in the ABED export, down to a booklet level", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_04B_REGISTRATIONS_STUDENT_ADMIN_META"}, "dependencySourcings": [{"param": "tw_ids", "type": "asset-col", "config": {"asset": "load_db_tw", "col": "test_window_id"}}, {"param": "uids", "type": "asset-col", "config": {"asset": "load_db_student_grouping_registrations", "col": "uid"}}]}, {"slug": "load_db_student_admin_meta_booklets", "description": "booklet index information for current set of students", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_04B_REGISTRATIONS_STUDENT_ADMIN_META_BOOKLETS"}, "dependencySourcings": [{"param": "tw_ids", "type": "asset-col", "config": {"asset": "load_db_tw", "col": "test_window_id"}}, {"param": "uids", "type": "asset-col", "config": {"asset": "load_db_student_grouping_registrations", "col": "uid"}}]}, {"slug": "load_db_class_form_alloc", "description": "identify classroom form locks", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_04B_REGISTRATIONS_CLASS_FORM_ALLOC"}, "dependencySourcings": [{"param": "tw_ids", "type": "asset-col", "config": {"asset": "load_db_tw", "col": "test_window_id"}}]}, {"slug": "load_db_item_register_consolidated", "description": "Loads fields for the item_register_consolidated export", "structure": "dataframe", "method": "query", "methodConfig": {"querySlug": "SQL_03_ASMT_CONTENT_ITEM_REGISTER_CONSOLIDATED"}, "dependencySourcings": [{"param": "twtar_ids", "type": "asset-col", "config": {"asset": "load_db_twtar", "col": "twtar_id"}}]}, {"slug": "trsfm_key_details", "is_rename_req": true, "description": "Creates the dataframe to create Key Details from", "structure": "dataframe", "method": "transforms", "methodConfig": {"sequence": [{"type": "filter-col", "df_output": "tqer", "config": {"df_input": "load_db_tqer", "col": "score", "comparison": "not-equal", "value": 0}}, {"type": "merge", "df_output": "tqr_td", "config": {"df_inputs_left": "tqr", "df_inputs_right": "td", "on_left": "test_design_id", "on_right": "id"}}, {"type": "merge", "df_output": "tqer_tqr_td", "config": {"df_inputs_left": "tqr_td", "df_inputs_right": "tqer", "on_left": "item_id", "on_right": "item_id"}}], "output": "tqer_tqr_td"}, "dependencySourcings": [{"param": "load_db_td", "type": "dataframe"}, {"param": "load_db_item_scale_register_consolidated", "type": "dataframe"}, {"param": "load_db_tqer", "type": "dataframe"}]}, {"slug": "load_db_students_excluding_sample", "is_rename_req": true, "is_implemented": false, "description": "Creates the dataframe to create Key Details from", "structure": "dataframe", "method": "transforms", "methodConfig": {"sequence": [{"type": "filter-col", "df_output": "df", "config": {"df_input": "load_db_tqer", "col": "is_sample", "comparison": "not-equal", "value": 1}}], "output": "df"}, "dependencySourcings": [{"param": "load_db_students", "type": "dataframe"}]}, {"slug": "trfm_form_allocation_summary", "is_implemented": false, "description": "Summary of the number of times each form is allocated per twtar type slug", "structure": "dataframe", "method": "transforms", "methodConfig": {"sequence": [{"type": "group-by", "df_output": "form_summary", "config": {"df_input": "load_db_class_form_alloc", "group_by": ["type_slug", "test_form_id"], "agg": [{"col_new": "allocation_count", "agg_type": "count"}]}}], "output": "form_summary"}, "dependencySourcings": [{"param": "load_db_class_form_alloc", "type": "dataframe"}]}, {"slug": "trfm_class_multiform_allocation_summary", "is_implemented": false, "description": "Summary of class form allocations with multiple forms per twtar and student assignment status", "structure": "dataframe", "method": "transforms", "methodConfig": {"sequence": [{"type": "group-by", "df_output": "summary", "config": {"df_input": "load_db_class_form_alloc", "group_by": ["school_class_id", "type_slug"], "agg": [{"col_new": "accommodation_slugs", "agg_type": "nunique", "col_target": "accommodation_slug"}, {"col_new": "student_count", "agg_type": "mean", "col_target": "n_students"}, {"col_new": "max_started_on", "agg_type": "max", "col_target": "max_ta_started_on"}]}}, {"type": "filter-col", "df_output": "multi_form_classes", "config": {"df_input": "summary", "col": "form_count", "comparison": "greater-than", "value": 1}}, {"type": "map-col", "df_output": "multi_form_classes", "config": {"is_in_place": true, "col_output": "is_students_assigned", "col_source": "student_count", "type": "binary", "comparison_type": "greater-than", "comparison_value": 0, "value_true": "Yes", "value_false": "No"}}, {"type": "map-col", "df_output": "multi_form_classes", "config": {"is_in_place": true, "col_output": "is_students_started", "col_source": "max_started_on", "type": "binary", "comparison_type": "is-not-null", "value_true": "Yes", "value_false": "No"}}], "output": "multi_form_classes"}, "dependencySourcings": [{"param": "load_db_class_form_alloc", "type": "dataframe"}]}, {"slug": "trfm_class_multiform_allocation_summary", "is_implemented": false, "description": "Get sccf.id for classes with multiple forms and student assignment status", "structure": "dataframe", "method": "transforms", "methodConfig": {"sequence": [{"type": "restrict-cols", "df_output": "df_cmas", "config": {"df_input": "trfm_class_multiform_allocation_summary", "cols": ["school_class_id", "type_slug", "form_count", "is_students_assigned", "is_students_started"]}}, {"type": "merge", "df_output": "df_cfa_cmas", "config": {"df_inputs_left": "load_db_class_form_alloc", "df_inputs_right": "df_cmas", "true": ["school_class_id", "type_slug"], "how": "inner"}}, {"type": "restrict-cols", "df_output": "result", "config": {"df_input": "df_cfa_cmas", "cols": ["id", "school_class_id", "type_slug", "form_count", "is_students_assigned", "is_students_started"]}}], "output": "result"}, "dependencySourcings": [{"param": "load_db_class_form_alloc", "type": "dataframe"}, {"param": "trfm_class_multiform_allocation_summary", "type": "dataframe"}]}, {"slug": "trsfm_student_accommodations_consolidated", "is_implemented": false, "description": "combines meta data definitions and strips null records", "structure": "dataframe", "method": "transforms", "methodConfig": {"sequence": [{"type": "filter-col", "df_output": "df_accomm", "config": {"df_input": "load_db_student_accommodations", "col": "accommodation_value", "comparison": "not-null"}}, {"type": "merge", "df_output": "df_merge", "config": {"df_inputs_left": "df_accomm", "df_inputs_right": "load_db_aw_accommodation_options", "true": ["acc_id"], "how": "inner"}}, {"type": "restrict-cols", "df_output": "df", "config": {"df_input": "df_merge", "cols": ["ua_id", "uid", "acc_id", "accommodation_value", "accommodation_number", "accommodation_name", "value_data_type", "acc_type_slug"]}}], "output": "df"}, "dependencySourcings": [{"param": "load_db_student_accommodations", "type": "dataframe"}, {"param": "load_db_aw_accommodation_options", "type": "dataframe"}]}, {"slug": "trsfm_student_accommodations_aggregated", "tags": [{"type": "group", "tag": "04c_alt_requests"}], "is_implemented": false, "description": null, "structure": "dataframe", "method": "transforms", "methodConfig": {"sequence": [{"type": "filter-col", "df_output": "given_accomms", "config": {"df_input": "load_db_student_accommodations", "col": "accommodation_value", "comparison": "equals", "value": 1}}, {"type": "group-by", "df_output": "slugs_grouped", "config": {"df_input": "given_accomms", "group_by": ["asn", "uid"], "agg": [{"col_new": "accommodation_slug", "agg_type": "list_unique", "col_target": "accommodation_slug"}]}}, {"type": "group-by", "df_output": "ids_grouped", "agg": [{"col_new": "foreign_id", "agg_type": "list_unique", "col_target": "foreign_id"}]}, {"type": "merge", "df_output": "output", "config": {"df_inputs_left": "slugs_grouped", "df_inputs_right": "ids_grouped", "true": ["asn", "uid"], "how": "outer"}}], "output": "output"}, "dependencySourcings": [{"param": "load_db_student_accommodations", "type": "dataframe"}, {"param": "load_db_aw_accommodation_options", "type": "dataframe"}]}]