provider "aws" {
  profile = "default"
  region = "ca-central-1"
}

# Setup IAM Instance Profile for API Instances To Communicate With CloudWatch For Capturing Logs

module "cloudwatch_iam_profile" {
  source = "./modules/mpt-instance/iam-cloudwatch"
}

# Create IAM role for Lambda re: CloudWatch alerts for APIs

module "iam_api_alert_lambda_role" {
  source = "./modules/mpt-instance/lambda/iam/cloudwatch-api-logs-role"
}

# Create Lambda function for outputting notification to Mattermost when new error is logged by an API

module "lambda_mattermost_api_error_notification" {
  source = "./modules/mpt-instance/lambda/api-error-mattermost-notification"
  iam_role_arn = module.iam_api_alert_lambda_role.cloudwatch_api_logs_role_arn
}

# Create Lambda function for streaming API error messages to Mattermost

module "lambda_mattermost_api_error_stream" {

  source = "./modules/mpt-instance/lambda/api-error-mattermost-stream"
  iam_role_arn = module.iam_api_alert_lambda_role.cloudwatch_api_logs_role_arn

}

# Create Lambda function for streaming VEA infrastructure alerts to Mattermost

module "lambda_vea_infrastructure_alerts" {

  source = "./modules/mpt-instance/lambda/vea-infrastructure-alerts"
  iam_role_arn = module.iam_api_alert_lambda_role.cloudwatch_api_logs_role_arn

}

# Load instance profile created within vretta-cloud-infrastructure/002-ec2-access-control to use with API instances

data "terraform_remote_state" "instance_profile" {

  backend = "s3"

  config = {

    bucket = "devops-vrt-terraform-state"
    key = "002-ec2-access-control"
    region = "ca-central-1"
    dynamodb_table = "devops-vrt-terraform-state-locks"

  }

}

# Create IAM Group for VEA Web Client Deployers

module "vea-live-deployers-group" {

  source = "./modules/mpt-front-end/vea-iam-group"

}

# Auto Front End / API Deployment Infrastructure Resources (GitLab deploy IAM user, DynamoDB tables, etc.)

module "auto_deploy_config" {

  source = "./modules/auto-deploy-config"

}

# Create RDS parameter groups for configuring database settings; e.g., max_execution_time for SELECT statements

module "rds_parameter_groups" {

  source = "./modules/mpt-instance/rds-parameter-groups"

}

# Begin environment deployments here

variable "xqc1_db_password" {
  type = string
}
variable "product_tag" {
  description = "Value for the Product tag"
  type = string
  default = null # set a default value if you want
}
# module "xqc1" {
#   source = "./modules/mpt-instance"
#   deployment_name = "xqc1"
#   mpt_api_ami_id = "ami-08dfaffcc86c75f34"
#   api_asg_min_size = 0
#   api_asg_desired_capacity = 1
#   api_asg_max_size = 2
#   db_password = var.xqc1_db_password
#   db_instance_class = "db.t3.medium"
#   db_num_replicas = 0
#   db_snapshot_identifier = "mpt-xqc1-master-final-snapshot-2"
# }

variable "www_db_password" {
  type = string
}
# production deployment
module "www" {
  source = "./modules/mpt-instance"
  deployment_name = "www"
  web_ui_alt_domains = ["testcompetencesmaths.ca", "www.testcompetencesmaths.ca"]
  serve_ui_from_base_domain = true
  mpt_api_ami_id = "ami-0426dacce5a457d15"
  api_instance_class = "c5n.large"
  api_domain_alt = "eqao-api.vretta.com"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  rds_parameter_group = module.rds_parameter_groups.mysql-slow-log-parameter-group
  db_password = var.www_db_password
  backup_master_db = true
  db_instance_class = "db.m5.4xlarge"
  db_max_allocated_storage = "2000"
  db_replica_instance_class = "db.t3.xlarge"
  db_num_replicas = 1
  nat_gateway_eip = "*************"
  web_ui_cors_origins = ["https://qc3.mathproficiencytest.ca", "https://www.mathproficiencytest.ca", "https://mathproficiencytest.ca"]
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Enable privileged API deployer
  privileged_api_deployments_enabled = true

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm


  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  # Enable front end S3 deployer
  # vea_deployer_group = module.vea-live-deployers-group.group_name

  vea_rds_snapshot_tool_enabled = false
  vea_rds_snapshot_tool_schedule = "0 * * * ? *"
  vea_rds_snapshot_tool_interval = 1

  aurora_cluster_identifier = "doesnotexist"
}


module "eqao2-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao2"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-0b31d4c7624bc534b"
  api_instance_class = "c5n.xlarge"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  api_branch_override = "vea-eqao2-asg"
  nat_gateway_eip = "*************"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  product_tag = "VEA-ON"
  type_tag = "PROD"
  api_config_file = "eqao2.json"
  api_config_data = <<EOT
{
  "isStressTest": false,
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "clustercfg.vea-eqao2-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host" : "mpt-www-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "__DB_PASSWORD__",
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "mysql_read": {
    "connection": {
      "host" : "mpt-www-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "__DB_PASSWORD__",
      "database": "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "mpt-www-master-aurora-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "__DB_PASSWORD__",
      "timezone": "Z",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "***********************************************************************************************************",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_LeAvLFpyqiEy7NKBKHawSs6RrSoqtVvW"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-07fd56894eda879eb",
  "eqaoScanningServiceConfig": {
    "url": "http://eqao-pdf-scanning-1133926475.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}
module "eqao-prod-mirror-072523" {
  source = "./modules/vea-api"
  deployment_name = "eqao-prod-mirror-072523"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-0db042b661d841bce"
  api_instance_class = "t3a.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  api_branch_override = "vea-eqao-prod-mirror-072523-asg"
  nat_gateway_eip = "*************"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role     = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  api_config_file = "eqao2.json"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "mysql_write": {
    "connection":  {
      "host" : "eqao-prod-mirror-072523-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database" : "mpt_dev"
    }
  },
  "mysql_read": {
    "connection": {
      "host" : "eqao-prod-mirror-072523-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database": "mpt_dev"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "eqao-prod-mirror-072523-reader.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
          "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  }
}
EOT
}

module "eqao-main-mirror" {
  source = "./modules/mpt-instance"
  deployment_name = "eqao-main-mirror"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-02c8c49f96130613f"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  api_asg_max_size = 1
  db_password = var.www_db_password
  db_max_allocated_storage = 999
  db_performance_insights = false
  db_instance_class = "db.t3.small"
  db_num_replicas = 0
  db_multi_az = false
  db_snapshot_identifier = "rds:mpt-www-master-2022-08-25-03-33"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"
    product_tag = "VEA-ON"
  api_config_file = "eqao-main-mirror.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "mysql_write": {
    "client": "mysql2",
    "connection":  {
      "host" : "mpt-eqao-main-mirror-master-aurora.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
          "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "mysql_read": {
    "client": "mysql2",
    "connection": {
      "host" : "mpt-eqao-main-mirror-master-aurora.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
          "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
 "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "sk_test_51LhJczE6jMjPjNPk0GvNt6TvRpVLD2nFpkc5UT8qe3bchK6XkGzB1j0DGGYWJTE8eNvd38i6UjQvOkKfvnJypvx700Ljqgl6Ur",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_4gk6hv2U0fdaWSfikm8Np5TIkWtw3Otd"
  }
}
EOT
}

module "nbed" {
  source = "./modules/mpt-front-end"
  deployment_name = "nbed"
  base_domain = "vretta.com"
  product_tag = "VEA-NB"
}

module "nbed-api" {
  source = "./modules/vea-api"
  deployment_name = "nbed"
  vpc_id = "vpc-0665fa28ec8c67f27"
  api_b_subnet_id = "subnet-0c600f9917e761879"
  api_a_subnet_id = "subnet-0cbf14e8c388abd0d"
  pub_subnet_ids = ["subnet-051c92f41b0d8f430", "subnet-09acc98a403d6001c", "subnet-00db6fc555f6fdc4b"]
  allow_http_security_group_id = "sg-0d540d632c5b93dab"
  allow_tls_security_group_id = "sg-084c47e8efac8839e"
  mpt_api_ami_id = "ami-0ac1c63cd840d9d2e"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-NB"
  type_tag = "PROD"
  api_config_file = "production.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "redis": {
    "host": "master.vea-nbed-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "mpt-nbed-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-nbed-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host": "mpt-nbed-master-aurora-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "logQueryTiming": false,
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "nbed-qc" {
  source = "./modules/mpt-front-end"
  deployment_name = "nbed-qc"
  base_domain = "vretta.com"
  product_tag = "VEA-NB"
}

module "nbed-qc-api" {
  source = "./modules/vea-api"
  deployment_name = "nbed-qc"
  vpc_id = "vpc-0665fa28ec8c67f27"
  api_b_subnet_id = "subnet-0c600f9917e761879"
  api_a_subnet_id = "subnet-0cbf14e8c388abd0d"
  pub_subnet_ids = ["subnet-051c92f41b0d8f430", "subnet-09acc98a403d6001c", "subnet-00db6fc555f6fdc4b"]
  allow_http_security_group_id = "sg-0d540d632c5b93dab"
  allow_tls_security_group_id = "sg-084c47e8efac8839e"
  mpt_api_ami_id = "ami-0947b2a5c38bcd43a"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-NB"
  type_tag = "QC"
  api_config_file = "nbed-qc.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "redis": {
    "host": "master.vea-nbed-qc-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-nbed-qc-db-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-nbed-qc-db-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

## 2025-07-25: removed environment
# module "nbed-sso" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "nbed-sso"
#   base_domain = "vretta.com"
#   product_tag = "VEA-NB"
# }

## 2025-07-25: removed environment
# module "nbed-sso-api" {
#   source = "./modules/vea-api"
#   deployment_name = "nbed-sso"
#   vpc_id = "vpc-0665fa28ec8c67f27"
#   api_b_subnet_id = "subnet-0c600f9917e761879"
#   api_a_subnet_id = "subnet-0cbf14e8c388abd0d"
#   pub_subnet_ids = ["subnet-051c92f41b0d8f430", "subnet-09acc98a403d6001c", "subnet-00db6fc555f6fdc4b"]
#   allow_http_security_group_id = "sg-0d540d632c5b93dab"
#   allow_tls_security_group_id = "sg-084c47e8efac8839e"
#   mpt_api_ami_id = "ami-0947b2a5c38bcd43a"
#   api_instance_class = "t3.medium"
#   api_asg_min_size = 1
#   api_asg_desired_capacity = 1
#   lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
#   api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
#   # Enable privileged API deployer
#   privileged_api_deployments_enabled = false
#   # Enable API auto deploys
#   enable_api_s3_deployments = true
#   asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
#   # Enable CloudWatch alerts for VEA APIs and RDS databases
#   vea_cloudwatch_alarms_enabled = true
#   vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
#   product_tag = "VEA-NB"
#   api_config_file = "nbed-sso.json"
#   api_config_data = <<EOT
# {
#   "host": "0.0.0.0",
#   "port": 3030,
#   "mysql_write": {
#     "connection": {
#       "host": "mpt-nbed-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
#       "port": 3306,
#       "password" : "__DB_PASSWORD__"
#     }
#   },
#   "mysql_read": {
#     "connection": {
#       "host": "mpt-nbed-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
#       "port": 3306,
#       "password" : "__DB_PASSWORD__"
#     }
#   },
#   "oct_api": {
#     "password": ""
#   }
# }
# EOT
# }

module "nbed-elpa" {
  source = "./modules/mpt-front-end"
  deployment_name = "nbed-elpa"
  base_domain = "vretta.com"
  product_tag = "VEA-NB"
}

module "nbed-elpa-api" {
  source = "./modules/vea-api"
  deployment_name = "nbed-elpa"
  vpc_id = "vpc-0665fa28ec8c67f27"
  api_b_subnet_id = "subnet-0c600f9917e761879"
  api_a_subnet_id = "subnet-0cbf14e8c388abd0d"
  pub_subnet_ids = ["subnet-051c92f41b0d8f430", "subnet-09acc98a403d6001c", "subnet-00db6fc555f6fdc4b"]
  allow_http_security_group_id = "sg-0d540d632c5b93dab"
  allow_tls_security_group_id = "sg-084c47e8efac8839e"
  mpt_api_ami_id = "ami-098caa0c455de2211"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-NB"
  api_config_file = "nbed-elpa.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "redis": {
    "host": "clustercfg.vea-nbed-elpa-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "mpt-nbed-elpa-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-nbed-elpa-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "nbed-stress-test-20240115-api" {
  source = "./modules/vea-api"
  deployment_name = "nbed-stress-test-20240115"
  vpc_id = "vpc-0bbcc84e7e0e2f68f"
  api_b_subnet_id = "subnet-00bea275e1b6a600b"
  api_a_subnet_id = "subnet-0e4521267de55448d"
  pub_subnet_ids = ["subnet-0e01b42d07c16ad87"]
  allow_http_security_group_id = "sg-020129927a49255a8"
  allow_tls_security_group_id = "sg-0bf505da8e13ae5d9"
  mpt_api_ami_id = "ami-028eff5f41a5e172d"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-LoadTest"
  lb_idle_timeout = 120
  api_config_file = "nbed-stress-test-20240115.json"
  api_config_data = <<EOT
{
  "isStressTest": true,
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-nbed-stress-test-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "nbed-stress-test-db.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "nbed-stress-test-db.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host": "nbed-stress-test-db-ro.internal.vretta.com",
      "port": 3306,
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "logQueryTiming": false,
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": null,
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "nbed-stress-test-20240115" {
  source = "./modules/mpt-front-end"
  deployment_name = "nbed-stress-test-20240115"
  base_domain = "vretta.com"
  product_tag = "VEA-LoadTest"
}

module "abed-to-nbed" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-to-nbed"
  base_domain = "vretta.com"
  web_ui_alt_domains = ["nbed-2025.vretta.com"]
  product_tag = "VEA-NB"
}

module "abed-to-nbed-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-to-nbed"
  vpc_id = "vpc-0665fa28ec8c67f27"
  api_b_subnet_id = "subnet-0c600f9917e761879"
  api_a_subnet_id = "subnet-0cbf14e8c388abd0d"
  pub_subnet_ids = ["subnet-051c92f41b0d8f430", "subnet-09acc98a403d6001c", "subnet-00db6fc555f6fdc4b"]
  allow_http_security_group_id = "sg-0d540d632c5b93dab"
  allow_tls_security_group_id = "sg-084c47e8efac8839e"
  mpt_api_ami_id = "ami-0ed5ccc8b78d42b98"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-NB"
  api_config_file = "abed-to-nbed.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "redis": {
    "host": "master.vea-abed-to-nbed-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "nbed-prod-mirror-20241105-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "nbed-prod-mirror-20241105-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "nbed-uat" {
  source = "./modules/mpt-front-end"
  deployment_name = "nbed-uat"
  base_domain = "vretta.com"
  product_tag = "VEA-NB"
}

module "nbed-uat-api" {
  source = "./modules/vea-api"
  deployment_name = "nbed-uat"
  vpc_id = "vpc-0665fa28ec8c67f27"
  api_b_subnet_id = "subnet-0c600f9917e761879"
  api_a_subnet_id = "subnet-0cbf14e8c388abd0d"
  pub_subnet_ids = ["subnet-051c92f41b0d8f430", "subnet-09acc98a403d6001c", "subnet-00db6fc555f6fdc4b"]
  allow_http_security_group_id = "sg-0d540d632c5b93dab"
  allow_tls_security_group_id = "sg-084c47e8efac8839e"
  mpt_api_ami_id = "ami-0ac1c63cd840d9d2e"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-NB"
  type_tag = "QC"
  api_config_file = "nbed-uat.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "redis": {
    "host": "master.vea-nbed-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "mpt-nbed-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-nbed-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host": "mpt-nbed-master-aurora-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "logQueryTiming": false,
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

# As of 2024-08-28, this configuration does not seem safe to apply to existing assets. 
module "eassessment-qc" {
  source = "./modules/mpt-instance"
  deployment_name = "eassessment-qc"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-0947b2a5c38bcd43a"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  api_asg_max_size = 1
  db_password = var.www_db_password
  aurora_snapshot_identifier = "arn:aws:rds:ca-central-1:066432346479:cluster-snapshot:rds:mpt-vea-master-aurora-cluster-2022-11-17-08-30"
  aurora_cluster_identifier ="mpt-eassessment-qc"
  aurora_db_parameter_group_name = "vea-aurora-mysql8-custom"
  aurora_instance_class = "db.t3.medium"

  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-NB"
  api_config_file = "vea-qc.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "mysql_write": {
    "connection": {
      "host": "mpt-eassessment-qc-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-eassessment-qc-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read_with_replica": {
    "__comment": "rename the parent key to 'mysql_read' and delete/rename the existing 'mysql_read' to activate this",
    "connection": {
      "host": "mpt-eassessment-qc-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": {
    "password": ""
  },
  "stripe_api": {
    "secretKey": "sk_test_51PqKLEFxSPwOifyEplo0wO95ffWJYNdey1k1lP6E38W6Fk9APKFDzn91or4gPtARExPKHye9xIw9oizAoNX21TkQ00Z7yA4Wiz",
    "apiVersion": "2024-06-20",
    "endpointSecret": "whsec_0VObqpBqiGbNbpi42pp5OYmTHmO24xbD"
  }
}
EOT
}

module "eassessment-uat" {
  source = "./modules/mpt-front-end"
  deployment_name = "eassessment-uat"
  base_domain = "vretta.com"
  product_tag = "VEA-NB"
}

module "eassessment-uat-api" {
  source = "./modules/vea-api"
  deployment_name = "eassessment-uat"
  vpc_id = "vpc-0665fa28ec8c67f27"
  api_b_subnet_id = "subnet-0c600f9917e761879"
  api_a_subnet_id = "subnet-0cbf14e8c388abd0d"
  pub_subnet_ids = ["subnet-051c92f41b0d8f430", "subnet-09acc98a403d6001c", "subnet-00db6fc555f6fdc4b"]
  allow_http_security_group_id = "sg-0d540d632c5b93dab"
  allow_tls_security_group_id = "sg-084c47e8efac8839e"
  mpt_api_ami_id = "ami-0ede19d36b2af65c7"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-NB"
  api_config_file = "eassessment-uat.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "mpt-vea-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-vea-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": null,
  "stripe_api": {
    "secretKey": "sk_test_51PqKLEFxSPwOifyEplo0wO95ffWJYNdey1k1lP6E38W6Fk9APKFDzn91or4gPtARExPKHye9xIw9oizAoNX21TkQ00Z7yA4Wiz",
    "apiVersion": "2024-06-20",
    "endpointSecret": "whsec_0VObqpBqiGbNbpi42pp5OYmTHmO24xbD"
  },
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "eassessment-f25" {
  source = "./modules/mpt-front-end"
  deployment_name = "eassessment-f25"
  base_domain = "vretta.com"
  product_tag = "VEA-Private"
}

module "eassessment-f25-api" {
  source = "./modules/vea-api"
  deployment_name = "eassessment-f25"
  vpc_id = "vpc-0665fa28ec8c67f27"
  api_b_subnet_id = "subnet-0c600f9917e761879"
  api_a_subnet_id = "subnet-0cbf14e8c388abd0d"
  pub_subnet_ids = ["subnet-051c92f41b0d8f430", "subnet-09acc98a403d6001c", "subnet-00db6fc555f6fdc4b"]
  allow_http_security_group_id = "sg-0d540d632c5b93dab"
  allow_tls_security_group_id = "sg-084c47e8efac8839e"
  mpt_api_ami_id = "ami-0ede19d36b2af65c7"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-Private"
  type_tag = "QC"
  api_config_file = "eassessment-f25.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "vea-eassessment-f25-db-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-eassessment-f25-db-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": null,
  "stripe_api": {
    "secretKey": "sk_test_51PqKLEFxSPwOifyEplo0wO95ffWJYNdey1k1lP6E38W6Fk9APKFDzn91or4gPtARExPKHye9xIw9oizAoNX21TkQ00Z7yA4Wiz",
    "apiVersion": "2024-06-20",
    "endpointSecret": "whsec_mKN8WQlSFG054x9jdonAwIhvUMl4YAPc"
  },
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "qc10" {
  source = "./modules/mpt-instance"
  deployment_name = "qc10"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-08ac9400312060d4a"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  api_asg_max_size = 1

  aurora_snapshot_identifier = "arn:aws:rds:ca-central-1:066432346479:cluster-snapshot:awsbackup:job-0d1b686e-2590-e271-3d85-dc8a05ae6b49"
  aurora_cluster_identifier ="qc10-master"
  aurora_db_parameter_group_name = "vea-aurora-mysql8-custom"
  aurora_instance_class = "db.t3.medium"

  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  api_config_file = "qc10.json"
  product_tag = "VEA-ON"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "mysql_write": {
    "connection": {
      "host" : "qc10-master.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database" : "mpt_dev"
    }
  },
  "mysql_read": {
    "connection": {
      "host" : "qc10-master.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************"
    }
  },
  "old_oct_api": {
    "domain": "https://apps.oct.ca",
    "password": "20S;MDsk?zqJCT+34X20"
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "eqaoSdcApiUrl": "http://*********/api.rsc/",
  "eqaoSdcApiAuthToken": "5p6Y7t2u4Z1l8c5N0j6t"
}
EOT
}


module "demo" {
  source = "./modules/mpt-instance"
  deployment_name = "demo"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-0770f2644f81a5ebb"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  api_asg_max_size = 1
  db_password = var.www_db_password
  db_max_allocated_storage = 999
  db_performance_insights = false
  db_instance_class = "db.t3.small"
  db_num_replicas = 0
  db_multi_az = false
  db_snapshot_identifier = "mpt-demo-master-snapshot"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "mpt-demo-master-aurora"
}

module "bced-eao-temp" {
  source = "./modules/mpt-instance"
  deployment_name = "bced-eao-temp"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-0c1759bb5671e108f"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  api_asg_max_size = 1
  db_password = var.xqc1_db_password
  db_max_allocated_storage = 999
  db_performance_insights = false
  db_instance_class = "db.t3.small"
  db_num_replicas = 0
  db_multi_az = false
  db_snapshot_identifier = "rds:mpt-eqao-main-mirror-master-2022-09-16-08-33"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"
}

# module "eqao-load-api" {
#   source = "./modules/vea-api"
#   deployment_name = "eqao-load"
#   vpc_id = "vpc-0f7a81e271f942cf1"
#   api_b_subnet_id = "subnet-0feb5003a065b8a67"
#   api_a_subnet_id = "subnet-0d4799aaa4920185d"
#   pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
#   allow_http_security_group_id = "sg-07a1ad50289426116"
#   allow_tls_security_group_id = "sg-063839eb1cc41db7c"
#   mpt_api_ami_id = "ami-0db042b661d841bce"
#   api_instance_class = "c5n.large"
#   api_asg_min_size = 1
#   api_asg_desired_capacity = 1
#   api_branch_override = "release/eqao2-main"
#   nat_gateway_eip = "*************"
#   lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
#   api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
#   # Enable privileged API deployer
#   privileged_api_deployments_enabled = false
#   # Enable API auto deploys
#   enable_api_s3_deployments = true
#   asg_instance_iam_role     = module.auto_deploy_config.api_iam_profile_nm
#   # Enable CloudWatch alerts for VEA APIs and RDS databases
#   vea_cloudwatch_alarms_enabled = true
#   vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
#   lb_idle_timeout = 120
# }

module "qc7-api" {
  source = "./modules/vea-api"
  deployment_name = "qc7"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-04a39d512dd57747b"
  api_instance_class = "t3a.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "*************"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  api_branch_override = "vea-qc7-asg"
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  api_config_file = "eqao2.json"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "host": "0.0.0.0",
  "port": 3030,
  "redis": {
    "host": "master.qc7-redis-replication-group.it4fg7.cac1.cache.amazonaws.com",
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "mpt-www-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": "3306",
      "password": "********************************",
      "database": "mpt_dev"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-www-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": "3306",
      "password": "********************************"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "mpt-www-master-aurora-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
          "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "old_oct_api": {
    "domain": "https://apps.oct.ca",
    "password": "20S;MDsk?zqJCT+34X20"
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "eqaoSdcApiUrl": "http://*********/api.rsc/",
  "eqaoSdcApiAuthToken": "5p6Y7t2u4Z1l8c5N0j6t",
  "stripe_api": {
    "secretKey": "sk_test_51Ln0tBCEr2P06DQPETwD39irqAB0iM4iuLqqgFq4xbH5wp0q64X0x5OvM1awmfdcYuxcKFqnFaZUhQcw5Ll0cxDw001JzIewPy",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_prV2Txkig44Sk6pt3gyCtErgc7HYhk5t"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-07fd56894eda879eb",
  "authentication": {
    "jwtOptions": {
      "expiresIn": "5m"
    }
  }
}
EOT
}

module "qc8-api" {
  source = "./modules/vea-api"
  deployment_name = "qc8"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-08361cc5edba95acb"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "*************"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  api_config_file = "qc8.json"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "master.qc8-redis-replication-group.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection":  {
      "host" : "mpt-www-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "e25NkBmx9wKuDRcXR8TW",
      "database" : "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read": {
    "connection": {
      "host" : "mpt-www-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "e25NkBmx9wKuDRcXR8TW",
      "database": "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "mpt-www-master-aurora-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "e25NkBmx9wKuDRcXR8TW",
      "timezone": "Z",
      "ssl" : {
          "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "old_db_hosts": [
    "mpt-www-replica-0.cednqnegvay6.ca-central-1.rds.amazonaws.com",
    "mpt-www-replica0.cednqnegvay6.ca-central-1.rds.amazonaws.com"
  ],
  "old_oct_api": {
    "domain": "https://apps.oct.ca",
    "password": "20S;MDsk?zqJCT+34X20"
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "***********************************************************************************************************",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_LeAvLFpyqiEy7NKBKHawSs6RrSoqtVvW"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-07fd56894eda879eb",
  "eqaoScanningServiceConfig": {
    "url": "http://vea-eqao-qc-scanner-alb-141814604.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}

module "qc-dev-api" {
  source = "./modules/vea-api"
  deployment_name = "qc-dev"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-08361cc5edba95acb"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "*************"
  # eip disabled due to CORS error; load balancer subnet must be manually modified after terraform apply
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  api_branch_override = "vea-qc-dev-asg"
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  api_config_file = "qc-dev.json"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "qc-dev-redis-replication-group.it4fg7.ng.0001.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": false
  },
  "mysql_write": {
    "connection":  {
      "host" : "mpt-www-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "e25NkBmx9wKuDRcXR8TW",
      "database" : "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read": {
    "connection": {
      "host" : "mpt-www-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "e25NkBmx9wKuDRcXR8TW",
      "database": "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "mpt-www-master-aurora-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "e25NkBmx9wKuDRcXR8TW",
      "timezone": "Z",
      "ssl" : {
          "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "old_db_hosts": [
    "mpt-www-replica-0.cednqnegvay6.ca-central-1.rds.amazonaws.com",
    "mpt-www-replica0.cednqnegvay6.ca-central-1.rds.amazonaws.com"
  ],
  "old_oct_api": {
    "domain": "https://apps.oct.ca",
    "password": "20S;MDsk?zqJCT+34X20"
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "***********************************************************************************************************",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_LeAvLFpyqiEy7NKBKHawSs6RrSoqtVvW"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-07fd56894eda879eb",
  "eqaoScanningServiceConfig": {
    "url": "http://vea-eqao-qc-scanner-alb-141814604.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}

module "eqao-dev-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-dev"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-08361cc5edba95acb"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "*************"
  # eip disabled due to CORS error; load balancer subnet must be manually modified after terraform apply
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  api_branch_override = "vea-eqao-dev-asg" # This must match aws_autoscaling_group_api.name or else API deploys will fail
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  api_config_file = "eqao-dev.json"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "clustercfg.eqao-dev-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection":  {
      "host" : "vea-eqao-prod-mirror-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database" : "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read": {
    "connection": {
      "host" : "vea-eqao-prod-mirror-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database": "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "vea-eqao-prod-mirror-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
          "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "old_db_hosts": [
    "mpt-www-replica-0.cednqnegvay6.ca-central-1.rds.amazonaws.com",
    "mpt-www-replica0.cednqnegvay6.ca-central-1.rds.amazonaws.com"
  ],
  "old_oct_api": {
    "domain": "https://apps.oct.ca",
    "password": "20S;MDsk?zqJCT+34X20"
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "***********************************************************************************************************",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_LeAvLFpyqiEy7NKBKHawSs6RrSoqtVvW"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-07fd56894eda879eb",
  "eqaoScanningServiceConfig": {
    "url": "http://vea-eqao-qc-scanner-alb-141814604.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}

module "eqao-auth-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-auth"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-08361cc5edba95acb"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "*************" # required for correct subnets
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  api_branch_override = "vea-eqao-auth-asg" # This must match aws_autoscaling_group_api.name or else API deploys will fail
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  type_tag = "QC"
  api_config_file = "eqao-auth.json"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "master.vea-eqao-auth-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection":  {
      "host" : "vea-eqao-auth-20250121-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "database" : "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read": {
    "connection": {
      "host" : "vea-eqao-auth-20250121-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "database": "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "vea-eqao-auth-20250121-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
          "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "***********************************************************************************************************",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_LeAvLFpyqiEy7NKBKHawSs6RrSoqtVvW"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-07fd56894eda879eb",
  "eqaoScanningServiceConfig": {
    "url": "http://vea-eqao-qc-scanner-alb-141814604.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}

module "eqao-auth-qc-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-auth-qc"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-08361cc5edba95acb"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "*************" # required for correct subnets
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  api_branch_override = "vea-eqao-auth-qc-asg" # This must match aws_autoscaling_group_api.name or else API deploys will fail
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  type_tag = "QC"
  api_config_file = "eqao-auth-qc.json"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "mysql_write": {
    "connection":  {
      "host" : "vea-eqao-auth-20250121-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "database" : "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read": {
    "connection": {
      "host" : "vea-eqao-auth-20250121-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "database": "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "vea-eqao-auth-20250121-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
          "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "***********************************************************************************************************",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_LeAvLFpyqiEy7NKBKHawSs6RrSoqtVvW"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-07fd56894eda879eb",
  "eqaoScanningServiceConfig": {
    "url": "http://vea-eqao-qc-scanner-alb-141814604.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}
module "eqao-scoring-20240409-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-scoring-20240409"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-08361cc5edba95acb"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  nat_gateway_eip = "*************"
  # eip disabled due to CORS error; load balancer subnet must be manually modified after terraform apply
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  api_branch_override = "vea-eqao-scoring-20240409-asg" # This must match aws_autoscaling_group_api.name or else API deploys will fail
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  type_tag = "QC"
  api_config_file = "eqao-scoring-20240409.json"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "clustercfg.eqao-dev-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection":  {
      "host" : "eqao-prod-mirror-aurora.internal.vretta.com",
      "port" : "3306",
      "password" : "********************************",
      "database" : "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read": {
    "connection": {
      "host" : "eqao-prod-mirror-aurora.internal.vretta.com",
      "port" : "3306",
      "password" : "********************************",
      "database": "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "eqao-prod-mirror-aurora.internal.vretta.com",
      "port" : "3306",
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
          "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "sk_test_51LPUPKAmrD0QWgJbgL75hx4q3QoMBLprbTuErtKmHTe27PSPSuxvmVKGqY156M96sy6xiR6nWNsseWjuQy6Smzb900QKoQN21w",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_FdO3NHirgq6B2Aj9TpMwvjUGJcoLrLeT"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-07fd56894eda879eb",
  "eqaoScanningServiceConfig": {
    "url": "http://vea-eqao-qc-scanner-alb-141814604.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}

module "eqao-respondus-1-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-respondus-1"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-014a315fb2b931b84"
  api_a_subnet_id = "subnet-041d664817e8a0350"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-08361cc5edba95acb"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  # eip disabled due to CORS error; load balancer subnet must be manually modified after terraform apply
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  api_branch_override = "vea-eqao-respondus-1-asg" # This must match aws_autoscaling_group_api.name or else API deploys will fail
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  type_tag = "QC"
  api_config_file = "eqao-respondus-1.json"
  api_domain_alt = "respondus-1-api.vretta.com"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "master.eqao-respondus-1-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "mpt-qc6-master.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-qc6-master.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "sk_test_51LPUPKAmrD0QWgJbgL75hx4q3QoMBLprbTuErtKmHTe27PSPSuxvmVKGqY156M96sy6xiR6nWNsseWjuQy6Smzb900QKoQN21w",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_FdO3NHirgq6B2Aj9TpMwvjUGJcoLrLeT"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-07fd56894eda879eb"
}
EOT
}


module "eqao-dump-restore-drrun-20240501-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-dump-restore-drrun-20240501"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-08361cc5edba95acb"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "*************"
  # eip disabled due to CORS error; load balancer subnet must be manually modified after terraform apply
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  api_branch_override = "vea-eqao-dump-restore-drrun-20240501-asg" # This must match aws_autoscaling_group_api.name or else API deploys will fail
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  api_config_file = "eqao-dump-restore.json"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "master.eqao-dev-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection":  {
      "host" : "mpt-www-master-aurora-cluster-known-good-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database" : "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read": {
    "connection": {
      "host" : "mpt-www-master-aurora-cluster-known-good-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database": "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "mpt-www-master-aurora-cluster-known-good-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
          "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "old_db_hosts": [
    "mpt-www-replica-0.cednqnegvay6.ca-central-1.rds.amazonaws.com",
    "mpt-www-replica0.cednqnegvay6.ca-central-1.rds.amazonaws.com"
  ],
  "old_oct_api": {
    "domain": "https://apps.oct.ca",
    "password": "20S;MDsk?zqJCT+34X20"
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "***********************************************************************************************************",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_LeAvLFpyqiEy7NKBKHawSs6RrSoqtVvW"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-07fd56894eda879eb",
  "eqaoScanningServiceConfig": {
    "url": "http://vea-eqao-qc-scanner-alb-141814604.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}

module "eqao-dump-restore-drrun-20240501" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-dump-restore-drrun-20240501"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

module "eqao-qc-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao2-qc"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-08361cc5edba95acb"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "*************"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  api_config_file = "eqao2-qc.json"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "master.vea-eqao-qc-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection":  {
      "host" : "vea-eqao-qc-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database" : "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read": {
    "connection": {
      "host" : "vea-eqao-qc-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database": "mpt_dev"
    },
    "logQueryTiming": true
  },
  "old_db_hosts": [
    "mpt-www-replica-0.cednqnegvay6.ca-central-1.rds.amazonaws.com",
    "mpt-www-replica0.cednqnegvay6.ca-central-1.rds.amazonaws.com"
  ],
  "old_oct_api": {
    "domain": "https://apps.oct.ca",
    "password": "20S;MDsk?zqJCT+34X20"
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-07fd56894eda879eb",
  "stripe_api": {
    "secretKey": "sk_test_51R6bMSIjxCIae3q4QfQVLjCLr5jAAMo9Ezf3DjqD2OS6PZSysaI8Qcq6moSMDxwVR2iJCIcSQRpXUFHPj4atvcKW00GZWo3v02",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_TAAHh8dS4KzODeBnuUIXMAYKOuaS9M0n"
  }
}
EOT
}

/* Marked as not in use and removed 2022-12-16 */
# module "eqaovulnerabilitytest-api" {
#   source = "./modules/vea-api"
#   deployment_name = "eqaovulnerabilitytest"
#   vpc_id = "vpc-0f7a81e271f942cf1"
#   api_b_subnet_id = "subnet-0feb5003a065b8a67"
#   api_a_subnet_id = "subnet-0d4799aaa4920185d"
#   pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
#   allow_http_security_group_id = "sg-07a1ad50289426116"
#   allow_tls_security_group_id = "sg-063839eb1cc41db7c"
#   mpt_api_ami_id = "ami-006af9e839a1e79ad"
#   api_instance_class = "c5n.large"
#   api_asg_min_size = 1
#   api_asg_desired_capacity = 1
#   nat_gateway_eip = "*************"
#   lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
#   api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
#   # Enable privileged API deployer
#   privileged_api_deployments_enabled = false
#   # Enable API auto deploys
#   enable_api_s3_deployments = true
#   asg_instance_iam_role     = module.auto_deploy_config.api_iam_profile_nm
#   # Enable CloudWatch alerts for VEA APIs and RDS databases
#   vea_cloudwatch_alarms_enabled = true
#   vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
# }

## 2025-07-25: removed environment
# module "bced-qc-mark" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "bced-qc-mark"
#   base_domain = "vretta.com"
#   product_tag = "VEA-BC"
# }

## 2025-07-25: removed environment
# module "bced-qc-mark-api" {
#   source = "./modules/vea-api"
#   deployment_name = "bced-qc-mark"
#   vpc_id = "vpc-093ffb10075111fd2"
#   api_b_subnet_id = "subnet-0f87707061851ba0a"
#   api_a_subnet_id = "subnet-0e578be210f239dcd"
#   pub_subnet_ids = ["subnet-0c726d0d2cbd6334b", "subnet-0a0c09c31c9e94b8e", "subnet-01c2def9b98c00c27"]
#   allow_http_security_group_id = "sg-07b4af33c2d16a9f6"
#   allow_tls_security_group_id = "sg-00ec3bf7903a0fd00"
#   mpt_api_ami_id = "ami-0ed244e8989375a27"
#   api_instance_class = "t3a.medium"
#   api_asg_min_size = 0
#   api_asg_desired_capacity = 1
#   lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
#   api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
#   # Enable privileged API deployer
#   privileged_api_deployments_enabled = false
#   # Enable API auto deploys
#   enable_api_s3_deployments = true
#   asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
#   # Enable CloudWatch alerts for VEA APIs and RDS databases
#   vea_cloudwatch_alarms_enabled = true
#   vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
#   product_tag = "VEA-BC"
#   type_tag = "QC"
#   api_config_file = "bced-qc-mark.json"
#   api_config_data = <<EOT
# {
#   "host": "0.0.0.0",
#   "port": 3030,
#   "mysql_write": {
#     "connection": {
#       "host": "mpt-bced-mirror-qc-master.internal.vretta.com",
#       "port": 3306,
#       "password" : "d372299ded855179a12e589274c5cf69"
#     }
#   },
#   "mysql_read": {
#     "connection": {
#       "host": "mpt-bced-mirror-qc-master.internal.vretta.com",
#       "port": 3306,
#       "password" : "d372299ded855179a12e589274c5cf69"
#     }
#   },
#   "oct_api": {
#     "password": ""
#   }
# }
# EOT
# }

# 2023/11/01: bced-qc-grad removed https://bubo.vretta.com/vea/platform/vea-infrastructure/-/issues/1181
# module "bced-qc-grad" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "bced-qc-grad"
#   base_domain = "vretta.com"
#   product_tag = "VEA-BC"
# }
# module "bced-qc-grad-api" {
#   source = "./modules/vea-api"
#   deployment_name = "bced-qc-grad"
#   vpc_id = "vpc-093ffb10075111fd2"
#   api_b_subnet_id = "subnet-0f87707061851ba0a"
#   api_a_subnet_id = "subnet-0e578be210f239dcd"
#   pub_subnet_ids = ["subnet-0c726d0d2cbd6334b", "subnet-0a0c09c31c9e94b8e", "subnet-01c2def9b98c00c27"]
#   allow_http_security_group_id = "sg-07b4af33c2d16a9f6"
#   allow_tls_security_group_id = "sg-00ec3bf7903a0fd00"
#   mpt_api_ami_id = "ami-00b0c104dd6d9ed89"
#   api_instance_class = "t3a.medium"
#   api_asg_min_size = 1
#   api_asg_desired_capacity = 1
#   lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
#   api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
#   # Enable privileged API deployer
#   privileged_api_deployments_enabled = false
#   # Enable API auto deploys
#   enable_api_s3_deployments = true
#   asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
#   # Enable CloudWatch alerts for VEA APIs and RDS databases
#   vea_cloudwatch_alarms_enabled = true
#   vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
#   product_tag = "VEA-BC"
#   api_config_file = "mirror-bced-qc.json"
#   api_config_data = <<EOT
# {
#   "host": "0.0.0.0",
#   "port": 3030,
#   "httpTimeoutMs": 900000,
#   "mysql_write": {
#     "connection": {
#       "host": "mpt-bced-mirror-qc-master.internal.vretta.com",
#       "port": 3306,
#       "password" : "d372299ded855179a12e589274c5cf69"
#     }
#   },
#   "mysql_read": {
#     "connection": {
#       "host": "mpt-bced-mirror-qc-master.internal.vretta.com",
#       "port": 3306,
#       "password" : "d372299ded855179a12e589274c5cf69"
#     }
#   },
#   "oct_api": {
#     "password": ""
#   }
# }
# EOT

# }

module "bced-cred" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-cred"
  base_domain = "vretta.com"
  product_tag = "VEA-BC"
}

module "bced-cred-api" {
  source = "./modules/vea-api"
  deployment_name = "bced-cred"
  vpc_id = "vpc-0aed1b2542b3dd25c"
  api_b_subnet_id = "subnet-0ff81653e43478147"
  api_a_subnet_id = "subnet-07cc1d7cea54e14c5"
  pub_subnet_ids = ["subnet-01cdede0b8ae7fcc2", "subnet-02197ab97de7c2047", "subnet-0e73369328a7851ef"]
  allow_http_security_group_id = "sg-004da543c87d6cf38"
  allow_tls_security_group_id = "sg-0a2f9d27bb840065a"
  mpt_api_ami_id = "ami-02ace433c95246d80"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "***********"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-BC"
  api_config_file = "bced.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "mpt-bced-master.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "7939ec6e55f98ab794c065f70f83e185"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-bced-master.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "7939ec6e55f98ab794c065f70f83e185"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

/* Marked as not in use and removed 2022-12-16 */
# module "bced-ltp-api" {
#   source = "./modules/vea-api"
#   deployment_name = "bced-ltp"
#   vpc_id = "vpc-0aed1b2542b3dd25c"
#   api_b_subnet_id = "subnet-0ff81653e43478147"
#   api_a_subnet_id = "subnet-07cc1d7cea54e14c5"
#   pub_subnet_ids = ["subnet-0154ffc5ddba360df", "subnet-06778ae6b95b78140", "subnet-0e73369328a7851ef"]
#   allow_http_security_group_id = "sg-004da543c87d6cf38"
#   allow_tls_security_group_id = "sg-0a2f9d27bb840065a"
#   mpt_api_ami_id = "ami-078ccced4b2e5970c"
#   api_instance_class = "c5n.large"
#   api_asg_min_size = 1
#   api_asg_desired_capacity = 1
#   nat_gateway_eip = "***********"
#   lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
#   api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
#   # Enable privileged API deployer
#   privileged_api_deployments_enabled = false
#   # Enable API auto deploys
#   enable_api_s3_deployments = true
#   asg_instance_iam_role     = module.auto_deploy_config.api_iam_profile_nm
#   # Enable CloudWatch alerts for VEA APIs and RDS databases
#   vea_cloudwatch_alarms_enabled = true
#   vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
# }
module "bced-fsa-qc-api" {
  source = "./modules/vea-api"
  deployment_name = "bced-fsa-qc"
  vpc_id = "vpc-0aed1b2542b3dd25c"
  api_b_subnet_id = "subnet-0ff81653e43478147"
  api_a_subnet_id = "subnet-07cc1d7cea54e14c5"
  pub_subnet_ids = ["subnet-0154ffc5ddba360df", "subnet-06778ae6b95b78140", "subnet-0e73369328a7851ef"]
  allow_http_security_group_id = "sg-004da543c87d6cf38"
  allow_tls_security_group_id = "sg-0a2f9d27bb840065a"
  mpt_api_ami_id = "ami-078ccced4b2e5970c"
  api_instance_class = "t3.large"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "***********"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = false
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  product_tag = "VEA-BC"
}

module "eqao-to-bced-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-to-bced"
  vpc_id = "vpc-0aed1b2542b3dd25c"
  api_b_subnet_id = "subnet-0ff81653e43478147"
  api_a_subnet_id = "subnet-07cc1d7cea54e14c5"
  pub_subnet_ids = ["subnet-0154ffc5ddba360df", "subnet-06778ae6b95b78140", "subnet-0e73369328a7851ef"]
  allow_http_security_group_id = "sg-004da543c87d6cf38"
  allow_tls_security_group_id = "sg-0a2f9d27bb840065a"
  mpt_api_ami_id = "ami-09461b44e0046683b"
  api_instance_class = "t3a.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "***********"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-BC"
  api_config_file = "bced.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "mpt-bced-master.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "7939ec6e55f98ab794c065f70f83e185"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-bced-master.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "7939ec6e55f98ab794c065f70f83e185"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "bcedvulnerabilitycheck" {
  source = "./modules/mpt-instance"
  deployment_name = "bcedvulnerabilitycheck"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-0d3f86e8731ba1700"
  api_instance_class = "t3.large"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  api_asg_max_size = 2
  db_password = var.xqc1_db_password
  db_max_allocated_storage = 999
  db_performance_insights = false
  db_instance_class = "db.t3.micro"
  db_num_replicas = 0
  db_multi_az = false
  db_snapshot_identifier = "rds:mpt-bced-master-2022-04-11-08-30"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Enable API auto deploys
  enable_api_s3_deployments = false
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"
}

/* Marked as not in use and removed 2022-12-16 */
# module "bced-ltp" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "bced-ltp"
#   base_domain = "vretta.com"
#   product_tag = "VEA-BC"
# }

module "eqao-to-bced" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-to-bced"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

/* Marked as not in use and removed 2022-12-16 */
# module "eqaovulnerabilitytest" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "eqaovulnerabilitytest"
#   base_domain = "vretta.com"
# }

/* Marked as not in use and removed 2022-12-16 */
# module "bcedvulnerabilitytest" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "bcedvulnerabilitytest"
#   base_domain = "vretta.com"
# }


/* Marked as not in use and removed 2022-12-16 */
# module "bced-qc-mkg" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "bced-qc-mkg"
#   base_domain = "vretta.com"
#   product_tag = "VEA-BC"
# }

/* Marked as not in use and removed 2022-12-16 */
# module "bced-qc-mkg-api" {
#   source = "./modules/vea-api"
#   deployment_name = "bced-qc-mkg"
#   vpc_id = "vpc-093ffb10075111fd2"
#   api_b_subnet_id = "subnet-0f87707061851ba0a"
#   api_a_subnet_id = "subnet-0e578be210f239dcd"
#   pub_subnet_ids = ["subnet-0c726d0d2cbd6334b", "subnet-0a0c09c31c9e94b8e", "subnet-01c2def9b98c00c27"]
#   allow_http_security_group_id = "sg-07b4af33c2d16a9f6"
#   allow_tls_security_group_id = "sg-00ec3bf7903a0fd00"
#   mpt_api_ami_id = "ami-00361ef451ddb7fb2"
#   api_instance_class = "c5n.large"
#   api_asg_min_size = 1
#   api_asg_desired_capacity = 1
#   lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
#   api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
#   # Enable privileged API deployer
#   privileged_api_deployments_enabled = false
#   # Enable API auto deploys
#   enable_api_s3_deployments = true
#   asg_instance_iam_role     = module.auto_deploy_config.api_iam_profile_nm
#   # Enable CloudWatch alerts for VEA APIs and RDS databases
#   vea_cloudwatch_alarms_enabled = true
#   vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
# }

module "bced-qc-fsa" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-qc-fsa"
  base_domain = "vretta.com"
  product_tag = "VEA-BC"
}
module "bced-testing" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-testing"
  base_domain = "vretta.com"
  product_tag = "VEA-BC"
}
module "bced-qc-fsa-api" {
  source = "./modules/vea-api"
  deployment_name = "bced-qc-fsa"
  vpc_id = "vpc-093ffb10075111fd2"
  api_b_subnet_id = "subnet-0f87707061851ba0a"
  api_a_subnet_id = "subnet-0e578be210f239dcd"
  pub_subnet_ids = ["subnet-0c726d0d2cbd6334b", "subnet-0a0c09c31c9e94b8e", "subnet-01c2def9b98c00c27"]
  allow_http_security_group_id = "sg-07b4af33c2d16a9f6"
  allow_tls_security_group_id = "sg-00ec3bf7903a0fd00"
  mpt_api_ami_id = "ami-00361ef451ddb7fb2"
  api_instance_class = "c5n.large"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
}

module "bced-testing-api" {
  source = "./modules/vea-api"
  deployment_name = "bced-testing"
  vpc_id = "vpc-093ffb10075111fd2"
  api_b_subnet_id = "subnet-0f87707061851ba0a"
  api_a_subnet_id = "subnet-0e578be210f239dcd"
  pub_subnet_ids = ["subnet-0c726d0d2cbd6334b", "subnet-0a0c09c31c9e94b8e", "subnet-01c2def9b98c00c27"]
  allow_http_security_group_id = "sg-07b4af33c2d16a9f6"
  allow_tls_security_group_id = "sg-00ec3bf7903a0fd00"
  mpt_api_ami_id = "ami-04560ae2db666c303"
  api_instance_class = "t3a.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-BC"
  api_config_file = "mirror-bced-qc.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "mpt-bced-mirror-qc-master.internal.vretta.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-bced-mirror-qc-master.internal.vretta.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

variable "qc3_db_password" {
  type = string
}
module "qc3" {
  source = "./modules/mpt-instance"
  deployment_name = "qc3"
  # mpt_api_ami_id = "ami-031256fb047918af5"
  mpt_api_ami_id = "ami-0f2763294fb8eaa2b" # updated 2024-02-14 with new config.json
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  api_asg_max_size = 2
  rds_parameter_group = module.rds_parameter_groups.mysql-default-parameter-group
  db_password = var.qc3_db_password
  db_instance_class = "db.t3.medium"
  db_num_replicas = 0
  # db_snapshot_identifier = "rds:mpt-www-master-2020-03-27-08-32"
  db_snapshot_identifier = "rds:mpt-www-master-aurora-cluster-2024-02-13-03-29" # this does not work
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Configure APIs to use user data script and download API builds from S3 when they first start up, this allows
  # for API auto deployments
  enable_api_s3_deployments = true

  # Configure APIs to use Instance Profile which has necessary permissions for API auto deployments
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Configure this deployment to not require credentials when deploying APIs automatically from GitLab
  privileged_api_deployments_enabled = false

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "vea-qc3-cluster"
  aurora_instance_tag = "vea-qc3"
  aurora_db_parameter_group_name = "vea-aurora-mysql8-custom"
  aurora_instance_class = "db.t3.medium"

  product_tag = "VEA-ON"
  type_tag = "QC"
}

## Removed 2025-07-25 as part of environment lifecycle
# module "qc3-uat" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "qc3-uat"
#   base_domain = "mathproficiencytest.ca"
#   product_tag = "VEA-ON"
# }

# QC4 API environment (ASG and RDS database) were retired 5/17/2021, front end deployment still exists however
# which currently points to EQAO (module "www") APIs. If running a terraform apply or targeting the qc4 module
# with Terraform, it will prompt to recreate the RDS database and ASG.

variable "qc4_db_password" {
  type = string
}
module "qc4" {
  source = "./modules/mpt-instance"
  deployment_name = "qc4"
  mpt_api_ami_id = "ami-05fc2b1f2e0cb8340"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  api_asg_max_size = 2
  rds_parameter_group = module.rds_parameter_groups.mysql-default-parameter-group
  db_password = var.qc4_db_password
  db_instance_class = "db.t3.small"
  db_num_replicas = 0
  db_snapshot_identifier = "rds:mpt-xqc2-master-2020-09-30-08-28"
  db_performance_insights = false
  asg_instance_iam_role = data.terraform_remote_state.instance_profile.outputs.ssm_iam_profile_nm
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  aurora_cluster_identifier = "doesnotexist"
}

/* Marked as not in use and removed 2022-12-16 */
# module "qc6" {
#   source = "./modules/mpt-instance"
#   deployment_name = "qc6"
#   mpt_api_ami_id = "ami-05fc2b1f2e0cb8340"
#   api_instance_class = "t3.small"
#   api_asg_min_size = 0
#   api_asg_desired_capacity = 1
#   api_asg_max_size = 2
#   rds_parameter_group = module.rds_parameter_groups.mysql-slow-log-parameter-group
#   db_password = var.qc4_db_password
#   db_instance_class = "db.t3.small"
#   db_num_replicas = 1
#   db_snapshot_identifier = "rds:mpt-www-master-2021-07-19-08-32"
#   db_performance_insights = false
#   db_max_allocated_storage = 999
#   asg_instance_iam_role = data.terraform_remote_state.instance_profile.outputs.ssm_iam_profile_nm
#   lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
#   api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

#   enable_api_s3_deployments = true

#   # DB Snapshots
#   # vea_rds_snapshot_tool_enabled = true
#   # vea_rds_snapshot_tool_schedule = "0 4 * * ? *"
#   # vea_rds_snapshot_tool_interval = 24

#   aurora_cluster_identifier = "doesnotexist"
# }

variable "vea_db_password" {
  type = string
}
# eassessment.vretta.com
module "vea" {
  source = "./modules/mpt-instance"
  deployment_name = "vea"
  # vea_deployer_group = module.vea-live-deployers-group.group_name
  web_ui_alt_domains = ["eassessment.vretta.com", "nbed.vretta.com", "mbed.vretta.com"]
  mpt_api_ami_id = "ami-022972dacca00bf0c"
  api_domain = "api-eassessment.vretta.com"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 2
  api_asg_max_size = 2
  rds_parameter_group = module.rds_parameter_groups.mysql-default-parameter-group
  db_password = var.vea_db_password
  backup_master_db = true
  db_instance_class = "db.t3.medium"
  db_num_replicas = 0
  db_performance_insights = true
  db_snapshot_identifier = "qc3-to-vea-migration-20200421"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Configure APIs to use user data script and download API builds from S3 when they first start up, this allows
  # for API auto deployments
  enable_api_s3_deployments = true

  # Configure APIs to use Instance Profile which has necessary permissions for API auto deployments
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Configure this deployment to not require credentials when deploying APIs automatically from GitLab
  privileged_api_deployments_enabled = false

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"
}

# QC4 API environment (ASG and RDS database) were retired 5/17/2021, front end deployment still exists however
# which currently points to EQAO (module "www") APIs. If running a terraform apply or targeting the qc4 module
# with Terraform, it will prompt to recreate the RDS database and ASG.

/* Marked as not in use and removed 2022-12-16 */
# variable "xqc3_db_password" {
#   type = string
# }
# module "xqc3" {
#   source = "./modules/mpt-instance"
#   deployment_name = "xqc3"
#   web_ui_alt_domains = ["eqao-xqc.vretta.com"]
#   mpt_api_ami_id = "ami-0832f2159b1158803"
#   api_domain = "api-eqao-xqc.vretta.com"
#   api_instance_class = "t3.medium"
#   api_asg_min_size = 0
#   api_asg_desired_capacity = 1
#   api_asg_max_size = 2
#   rds_parameter_group = module.rds_parameter_groups.mysql-default-parameter-group
#   db_password = var.xqc3_db_password
#   db_instance_class = "db.t3.medium"
#   db_num_replicas = 0
#   db_performance_insights = true
#   db_snapshot_identifier = "rds:mpt-www-master-2021-01-14-08-32" # latest www snapshot as of xqc3 initialization
#   asg_instance_iam_role = data.terraform_remote_state.instance_profile.outputs.ssm_iam_profile_nm
#   lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
#   api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

#   aurora_cluster_identifier = "doesnotexist"
# }

module "eqao" {
  source = "./modules/mpt-front-end"
  web_ui_alt_domains = ["eqao.vretta.com", "qc7.vretta.com"]
  deployment_name = "eqao"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"

  # This is a privileged deployment so we enable the vea_deployer_group variable to password protect deployments to
  # the front end
  # vea_deployer_group = module.vea-live-deployers-group.group_name
}

#zqc2 was retired on 16 June 2021
#testing evviorment
# module "zqc2" {

#   source              = "./modules/mpt-front-end"
#   deployment_name     = "zqc2"
#   # web_ui_alt_domains  = [<list-of-cnames-to-add-to-route-53-for-the-deployment>] # it is not necessary , it is just for redirecting
#   base_domain         = "vretta.com"

#   # Create privileged deployment user by setting vea_deploy_group to module.vea-live-deployers-group.group_name
#   vea_deployer_group  = module.vea-live-deployers-group.group_name

# }

/* Marked as not in use and removed 2022-12-16 */
# module "eqao-main-qc" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "eqao-main-qc"
#   base_domain = "vretta.com"
# }

module "eqaosample" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqaosample"
  base_domain = "vretta.com"
  web_ui_alt_domains = ["eqao-pj-public-sample.vretta.com"]
  product_tag = "VEA-ON"
}

module "eqao-pj-sample-staging" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-pj-sample-staging"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

# eqao2.mathproficiencytest.ca
# CloudFront: E3TDQJDLUT5FDP
module "eqao2" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao2"
  web_ui_alt_domains = [
    "eqao2.vretta.com",
    "eqao-g9-public-sample.vretta.com"
  ]
  product_tag = "VEA-ON"
}

module "eqao3" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao3"
  web_ui_alt_domains = [
    "eqao-osslt-public-sample.vretta.com",
    "eqao-lit.vretta.com"
  ]
  product_tag = "VEA-ON"

  # This is a privileged deployment so we enable the vea_deployer_group variable to password protect deployments to
  # the front end
  # vea_deployer_group = module.vea-live-deployers-group.group_name
}

/* Marked as not in use and removed 2022-12-16 */
# module "eqao-lit-qc1" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "eqao-lit-qc1"
#   base_domain = "vretta.com"
# }

module "proctoring_dev" {
  source = "./modules/vea-proctoring"
}

/* Marked as not in use and removed 2022-12-16 */
# module "qc5" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "qc5"
#   web_ui_alt_domains = ["vea-qc5.vretta.com"]
# }

variable "bced_db_password" {
  type = string
}

module "bced" {
  source = "./modules/mpt-instance"
  deployment_name = "bced"
  base_domain = "vretta.com"
  api_asg_min_size = 1
  api_asg_desired_capacity = 16
  api_asg_max_size = 100
  mpt_api_ami_id = "ami-09af8b740af123604"
  api_instance_class = "c6gn.large"
  lb_idle_timeout = 180
  rds_parameter_group = module.rds_parameter_groups.mysql-slow-log-parameter-group
  db_password = var.bced_db_password
  backup_master_db = true
  db_instance_class = "db.m5.4xlarge"
  db_num_replicas = 1
  db_performance_insights = true
  db_snapshot_identifier = "rds:mpt-vea-master-2020-11-17-08-23"
  subnet_pub_a_cidr_block = "********/28"
  subnet_pub_b_cidr_block = "*********/28"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  type_tag = "PROD"

  nat_gateway_eip = "***********"

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  # Create privileged deployment user by setting vea_deploy_group to module.vea-live-deployers-group.group_name
  vea_deployer_group = module.vea-live-deployers-group.group_name

  # Deploy a single instance, standalone QC API environment, outside the ASG for the deployment
  # All resources for this are deployed with modules/mpt-instance/qc-api.tf
  qc_api = {
    enabled = true
    # EC2 Instance Settings
    instance_ami = "ami-02a59c85743776aaf"
    instance_type = "t3.medium"
    associate_public_ip_address = true
    volume_size = 20
    key_name = "vrt-ca"
    iam_instance_profile = "vrt-session-manager-cloudwatch-profile"
    # Route53 Configurationyes
    qc_api_domain = "bced-qc-api.vretta.com"
  }

  api_config_file = "production.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "mpt-bced-master.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password": "__DB_PASSWORD__"
    },
    "pool": {
      "min": 5,
      "max": 20
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-bced-master.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password": "__DB_PASSWORD__"
    },
    "pool": {
      "min": 5,
      "max": 20
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT

  # vea_rds_snapshot_tool_enabled = true
  # vea_rds_snapshot_tool_schedule = "0 * * * ? *"
  # vea_rds_snapshot_tool_interval = 1

  aurora_cluster_identifier = "doesnotexist"
  product_tag = "VEA-BC"
}

module "bced-auth" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-auth"
  base_domain = "vretta.com"
  product_tag = "VEA-BC"
}

module "bced-auth-api" {
  source = "./modules/vea-api"
  deployment_name = "bced-auth"
  vpc_id = "vpc-0aed1b2542b3dd25c"
  api_b_subnet_id = "subnet-0ff81653e43478147"
  api_a_subnet_id = "subnet-07cc1d7cea54e14c5"
  pub_subnet_ids = ["subnet-01cdede0b8ae7fcc2", "subnet-02197ab97de7c2047", "subnet-0e73369328a7851ef"]
  allow_http_security_group_id = "sg-004da543c87d6cf38"
  allow_tls_security_group_id = "sg-0a2f9d27bb840065a"
  mpt_api_ami_id = "ami-078ccced4b2e5970c"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "***********"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-BC"
  type_tag = "QC"
  api_config_file = "bced-auth.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "bced-auth-db-20250203.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "w42i5PTbpr37EoM5B6Z8m43e9jtwD5we"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "bced-auth-db-20250203.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "w42i5PTbpr37EoM5B6Z8m43e9jtwD5we"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "bced-ads-template" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-ads-template"
  base_domain = "vretta.com"
  product_tag = "VEA-BC"
}

module "bced-ads-template-api" {
  source = "./modules/vea-api"
  deployment_name = "bced-ads-template"
  vpc_id = "vpc-0aed1b2542b3dd25c"
  api_b_subnet_id = "subnet-0ff81653e43478147"
  api_a_subnet_id = "subnet-07cc1d7cea54e14c5"
  pub_subnet_ids = ["subnet-01cdede0b8ae7fcc2", "subnet-02197ab97de7c2047", "subnet-0e73369328a7851ef"]
  allow_http_security_group_id = "sg-004da543c87d6cf38"
  allow_tls_security_group_id = "sg-0a2f9d27bb840065a"
  mpt_api_ami_id = "ami-0516f0d3eef3f3783"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "***********"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-BC"
  type_tag = "UAT"
  api_config_file = "bced-ads-template.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "mpt-bced-master.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "7939ec6e55f98ab794c065f70f83e185"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-bced-master.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "7939ec6e55f98ab794c065f70f83e185"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "bced-uat-3" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-uat-3"
  base_domain = "vretta.com"
  product_tag = "VEA-BC"
}

module "bced-uat-3-api" {
  source = "./modules/vea-api"
  deployment_name = "bced-uat-3"
  vpc_id = "vpc-0aed1b2542b3dd25c"
  api_b_subnet_id = "subnet-0ff81653e43478147"
  api_a_subnet_id = "subnet-07cc1d7cea54e14c5"
  pub_subnet_ids = ["subnet-01cdede0b8ae7fcc2", "subnet-02197ab97de7c2047", "subnet-0e73369328a7851ef"]
  allow_http_security_group_id = "sg-004da543c87d6cf38"
  allow_tls_security_group_id = "sg-0a2f9d27bb840065a"
  mpt_api_ami_id = "ami-044b7be672676bf2f"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "***********"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-BC"
  type_tag = "UAT"
  api_config_file = "bced-uat-3.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "bceduat3-20241219.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "7939ec6e55f98ab794c065f70f83e185"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "bceduat3-20241219.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "7939ec6e55f98ab794c065f70f83e185"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "bced-uat-4" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-uat-4"
  base_domain = "vretta.com"
  product_tag = "VEA-BC"
}

module "bced-uat-4-api" {
  source = "./modules/vea-api"
  deployment_name = "bced-uat-4"
  vpc_id = "vpc-0aed1b2542b3dd25c"
  api_b_subnet_id = "subnet-0ff81653e43478147"
  api_a_subnet_id = "subnet-07cc1d7cea54e14c5"
  pub_subnet_ids = ["subnet-01cdede0b8ae7fcc2", "subnet-02197ab97de7c2047", "subnet-0e73369328a7851ef"]
  allow_http_security_group_id = "sg-004da543c87d6cf38"
  allow_tls_security_group_id = "sg-0a2f9d27bb840065a"
  mpt_api_ami_id = "ami-044b7be672676bf2f"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  nat_gateway_eip = "***********"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-BC"
  type_tag = "UAT"
  api_config_file = "bced-uat-4.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "bced-uat-4-20250728.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "bced-uat-4-20250728.cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "bced-xqc1" {
  source = "./modules/mpt-instance"
  deployment_name = "bced-xqc1"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-065043b1db5214807"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lb_idle_timeout = 180
  rds_parameter_group = module.rds_parameter_groups.mysql-slow-log-parameter-group
  db_multi_az = false
  db_password = var.xqc1_db_password
  backup_master_db = true
  db_instance_class = "db.t3.2xlarge"
  db_num_replicas = 1
  db_performance_insights = true
  db_snapshot_identifier = "mpt-bced-xqc1-master-final-20210818t194051z"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"
  product_tag = "VEA-BC"
}

module "bced-mirror-qc" {
  source = "./modules/mpt-instance"
  deployment_name = "bced-mirror-qc"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-0484d1370db8e26d5"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lb_idle_timeout = 180
  rds_parameter_group = module.rds_parameter_groups.mysql-slow-log-parameter-group
  db_multi_az = false
  db_password = var.xqc1_db_password
  backup_master_db = true
  db_instance_class = "db.t3.large"
  db_num_replicas = 0
  db_performance_insights = true
  db_snapshot_identifier = "rds:mpt-bced-master-2022-01-24-08-30"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"
  product_tag = "VEA-BC"
  api_config_file = "production.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "mysql_write": {
    "connection": {
      "host": "mpt-bced-mirror-qc-master.internal.vretta.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-bced-mirror-qc-master.internal.vretta.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "bced-uat" {
  source = "./modules/mpt-instance"
  deployment_name = "bced-uat"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3a.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lb_idle_timeout = 180
  rds_parameter_group = module.rds_parameter_groups.mysql-slow-log-parameter-group
  db_multi_az = false
  db_password = var.xqc1_db_password
  backup_master_db = true
  db_instance_class = "db.t3.large"
  db_num_replicas = 0
  db_performance_insights = true
  db_snapshot_identifier = "rds:mpt-bced-master-2022-02-07-08-30"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"
  product_tag = "VEA-BC"
  api_config_file = "production.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "mpt-bced-uat-master.internal.vretta.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-bced-uat-master.internal.vretta.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "bced-uat-2" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-uat-2"
  base_domain = "vretta.com"
  product_tag = "VEA-BC"
  web_ui_alt_domains = ["nwt-uat.vretta.com"]
}

/* Marked as not in use and removed 2022-12-16 */
# module "bced-preview" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "bced-preview"
#   base_domain = "vretta.com"
# }

/* Marked as not in use and removed 2022-12-16 */
# module "bced-preview2" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "bced-preview2"
#   base_domain = "vretta.com"
# }

module "bced-qc" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-qc"
  base_domain = "vretta.com"
  product_tag = "VEA-BC"
}

/* Marked as not in use and removed 2022-12-16 */
# module "bced-feat11" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "bced-feat11"
#   base_domain = "vretta.com"
# }

/* Marked as not in use and removed 2022-12-16 */
# module "bced-feat12" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "bced-feat12"
#   base_domain = "vretta.com"
# }

## 2025-07-25: removed environment
# module "bced-qc-asmt-preview" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "bced-qc-asmt-preview"
#   base_domain = "vretta.com"
#   product_tag = "VEA-BC"
# }

module "bced-asmt-preview" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-asmt-preview"
  base_domain = "vretta.com"
  product_tag = "VEA-BC"

  # Create privileged deployment user by setting vea_deploy_group to module.vea-live-deployers-group.group_name
  # vea_deployer_group  = module.vea-live-deployers-group.group_name
}

module "qc7" {
  source = "./modules/mpt-front-end"
  deployment_name = "qc7"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}
module "qc8" {
  source = "./modules/mpt-front-end"
  deployment_name = "qc8"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}
module "qc-dev" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-qc-dev"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}
module "eqao-dev" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-dev"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

module "eqao-auth" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-auth"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}
module "eqao-auth-qc" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-auth-qc"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

module "eqao-scoring-20240409" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-scoring-20240409"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

module "eqao-respondus-1" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-respondus-1"
  base_domain = "vretta.com"
  web_ui_alt_domains = ["respondus-1.vretta.com"]
  product_tag = "VEA-ON"
}

module "eqao-qc" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-qc"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

#  ===== depp-number-line environment retired 5/17/2021 =====

variable "depp_number_line_db_password" { # This is used for ops-testing as well
  type = string
}

# module "depp-number-line" {
#   source = "./modules/mpt-instance"
#   deployment_name = "depp-number-line"
#   base_domain = "vretta.com"
#   mpt_api_ami_id = "ami-0cea11e5938f5732e"    # mpt-api-image-src-20210127-live, created Jan. 28, 2020
#   api_asg_min_size = 0
#   api_asg_desired_capacity = 1
#   db_password = var.depp_number_line_db_password
#   db_instance_class = "db.t3.small"
#   db_num_replicas = 0
#   db_snapshot_identifier = "rds:mpt-qc3-master-2021-02-02-08-26"
#   db_performance_insights = false
#   asg_instance_iam_role = data.terraform_remote_state.instance_profile.outputs.ssm_iam_profile_nm
#   lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
#   api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
# }

# module "bced-feat1" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "bced-feat1"
#   base_domain = "vretta.com"
# }

/* Marked as not in use and removed 2022-12-16 */
# module "ops-testing" {
#   source              = "./modules/mpt-instance"
#   deployment_name     = "ops-testing"
#   base_domain         = "vretta.com"

#   # This is a privileged deployment so we enable the vea_deployer_group variable to password protect deployments to
#   # the front end
#   vea_deployer_group  = module.vea-live-deployers-group.group_name

#   # API configuration
#   api_instance_class        = "t2.medium"
#   mpt_api_ami_id            = "ami-04cd86c61c87ac3b8"    # vea-devops-testing-api-image-src20210405B-live
#   api_asg_min_size          = 0
#   api_asg_desired_capacity  = 0

#   # Enable API S3 deployments
#   enable_api_s3_deployments = true

#   # Enable privileged API deployer
#   privileged_api_deployments_enabled = true

#   # RDS DB configuration
#   rds_parameter_group             = module.rds_parameter_groups.mysql-default-parameter-group
#   db_password                     = var.depp_number_line_db_password
#   db_instance_class               = "db.t2.small"
#   db_num_replicas                 = 0
#   db_snapshot_identifier          = "rds:mpt-bced-master-2021-05-18-08-29"
#   db_performance_insights         = false
#   asg_instance_iam_role           = module.auto_deploy_config.api_iam_profile_nm
#   lambda_sns_arn                  = module.lambda_mattermost_api_error_notification.sns_topic_arn
#   api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

#   # Deploy a single instance, standalone QC API environment, outside the ASG for the deployment
#   # All resources for this are deployed with modules/mpt-instance/qc-api.tf
#   qc_api = {
#     enabled                     = true
#     # EC2 Instance Settings
#     instance_ami                = "ami-04cd86c61c87ac3b8"
#     instance_type               = "t3.medium"
#     associate_public_ip_address = true
#     volume_size                 = 20
#     key_name                    = "vrt-ca"
#     iam_instance_profile        = "vrt-session-manager-cloudwatch-profile"
#     # Route53 Configurationyes
#     qc_api_domain = "ops-testing-qc-api.vretta.com"
#   }

#   aurora_cluster_identifier = "doesnotexist"
# }


#  ===== zqc1 environment retired 5/17/2021 & 6/9/2021 =====

# module "zqc1" {
#   source = "./modules/mpt-instance"
#   deployment_name = "vea-zqc1"
#   base_domain = "vretta.com"
#   mpt_api_ami_id = "ami-035efe8c1b6ab71a0"
#   api_instance_class = "t3.medium"
#   api_asg_min_size = 0
#   api_asg_desired_capacity = 2
#   api_asg_max_size = 50
#   db_password = var.xqc1_db_password
#   db_instance_class = "db.t3.medium"
#   db_num_replicas = 0
#   db_snapshot_identifier = "rds:mpt-vea-master-2021-07-08-08-23"
#   lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
#   api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

#   # Enable API auto deploys
#   enable_api_s3_deployments = true
#   api_branch_override = "mpt-vea-zqc1-bc-fieldtest-num-3-asg"
#   asg_instance_iam_role     = module.auto_deploy_config.api_iam_profile_nm

#   # Enable CloudWatch alerts for VEA APIs and RDS databases
#   vea_cloudwatch_alarms_enabled = false
#   vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

# }

module "bced-stress-test-202109-frontend" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-stress-test-202109"
  base_domain = "vretta.com"
  product_tag = "VEA-LoadTest"
}

module "bced-stress-test-202109" {
  source = "./modules/mpt-instance"
  deployment_name = "bced-stress-test-202109"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-0419af11848b24310"
  api_instance_class = "c5n.large"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  api_asg_max_size = 50
  api_asg_heartbeat_timeout = 60
  db_password = var.xqc1_db_password
  db_instance_class = "db.m5.large"
  db_num_replicas = 0
  rds_parameter_group = module.rds_parameter_groups.mysql-slow-log-parameter-group
  db_snapshot_identifier = "rds:mpt-www-master-2022-04-12-08-33"
  lambda_sns_arn = false
  api_error_log_lambda_stream_arn = false

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"
}

module "bced-stress-test-202109-api" {
  source = "./modules/vea-api"
  deployment_name = "bced-stress-test-202109"
  vpc_id = "vpc-0145dbeb52abd771f"
  api_b_subnet_id = "subnet-0722beb37003022e2"
  api_a_subnet_id = "subnet-0bc45cb32f00c4f0e"
  # not publicly accessible
  pub_subnet_ids = ["subnet-0ab22d05d0a0fdbbe"]
  allow_http_security_group_id = "sg-017cb381101b80028"
  allow_tls_security_group_id = "sg-03f13090e101dcdb1"
  # mpt_api_ami_id = "ami-0419af11848b24310"
  # mpt_api_ami_id = "ami-09f0a964715ea6f0f"
  mpt_api_ami_id = "ami-00193a77bf9f91578"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 0
  api_asg_max_size = 6
  api_branch_override = "mpt-bced-stress-test-202109-asg"
  nat_gateway_eip = "" # not publicly accessible
  lambda_sns_arn = null
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = null
  lb_idle_timeout = 120
  product_tag = "VEA-LoadTest"
  api_config_file = "bced-stress-test-202109.json"
  api_config_data = <<EOT
{
  "isStressTest": true,
  "host": "0.0.0.0",
  "port": 3030,
  "isStressTestMode": true,
  "mysql_write": {
    "connection":  {
      "host": "bced-stress-test-db.internal.vretta.com",
      "port": 3306,
      "password" : "7939ec6e55f98ab794c065f70f83e185"
    },
    "pool": {
      "min": 2,
      "max": 20
    }
  },
  "mysql_read": {
    "connection":  {
      "host": "bced-stress-test-db.internal.vretta.com",
      "port": 3306,
      "password" : "7939ec6e55f98ab794c065f70f83e185"
    },
    "pool": {
      "min": 2,
      "max": 20
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "vea-sys-demo" {
  source = "./modules/mpt-instance"
  deployment_name = "vea-sys-demo"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-0bd06b4fed76f26e8"
  api_instance_class = "t3.small"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  api_asg_max_size = 2
  rds_parameter_group = module.rds_parameter_groups.mysql-default-parameter-group
  db_password = var.qc3_db_password
  db_instance_class = "db.t3.small"
  db_performance_insights = false
  db_num_replicas = 0
  db_snapshot_identifier = "mpt-vea-sys-demo-master-final-snapshot"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Configure APIs to use user data script and download API builds from S3 when they first start up, this allows
  # for API auto deployments
  enable_api_s3_deployments = true

  # Configure APIs to use Instance Profile which has necessary permissions for API auto deployments
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Configure this deployment to not require credentials when deploying APIs automatically from GitLab
  privileged_api_deployments_enabled = false

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"
}


# Backup Test, uncomment to validate backups
# Run the following commands:
# 1) "terraform apply --target=module.backup-validation.aws_acm_certificate.web_ui"
# 2) "terraform apply --target=module.backup-validation.aws_acm_certificate.api"
# Takes 20 mins to complete deployment. aws_db_instance takes the longest
# Once launched, connect to an API launched in the new environment, validate the NODE_ENV setting within
# /opt/ecosystem.config.js > And edit the corresponding file within /opt/mpt-api/config/<something>.json,
# so that the DB instance name and password to the DB are updated to match the backup environment. Without doing
# this, you will receive 500 errors when running the check_api.sh script

# Add a 32 character, unqie strong password to the secrets file matching this variable name
# variable "backup_db_password" {
#   type = string
# }

# module "backup-validation" {
#   source = "./modules/mpt-instance"
#   deployment_name = "backup-validation"
#   base_domain = "vretta.com"
#   mpt_api_ami_id = "ami-0832f2159b1158803" # Update this to be a recent AMI when testing
#   api_asg_min_size = 1
#   api_asg_desired_capacity = 1
#   db_password = var.backup_db_password
#   db_instance_class = "db.t2.small"
#   db_num_replicas = 0
#   db_snapshot_identifier = "rds:mpt-vea-master-2021-01-07-08-23" # Update this to be a recent snapshot when testing
#   asg_instance_iam_role = module.cloudwatch_iam_profile.cloudwatch_iam_profile_name
#   lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
#   api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
# }

module "eqao-stress-test-202110" {
  source = "./modules/mpt-instance"
  deployment_name = "eqao-stress-test-202110"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-05c10eab67ca5215d"
  api_instance_class = "c5n.large"
  api_asg_min_size = 0
  api_asg_desired_capacity = 2
  api_asg_max_size = 2
  db_password = var.xqc1_db_password
  db_max_allocated_storage = 999
  db_performance_insights = false
  db_instance_class = "db.t3.small"
  db_num_replicas = 0
  db_multi_az = false
  db_snapshot_identifier = "rds:mpt-www-master-2021-10-05-08-33"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"
}
module "xqc4" {
  source = "./modules/mpt-instance"
  deployment_name = "xqc4"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-0acd3e0b98e887b32"
  api_instance_class = "t3.small"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  api_asg_max_size = 2
  rds_parameter_group = module.rds_parameter_groups.mysql-default-parameter-group
  db_password = var.xqc1_db_password
  db_instance_class = "db.t3.small"
  db_performance_insights = false
  db_num_replicas = 0
  db_snapshot_identifier = "rds:mpt-bced-xqc1-master-2021-10-26-08-25"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Configure APIs to use user data script and download API builds from S3 when they first start up, this allows
  # for API auto deployments
  enable_api_s3_deployments = true

  # Configure APIs to use Instance Profile which has necessary permissions for API auto deployments
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Configure this deployment to not require credentials when deploying APIs automatically from GitLab
  privileged_api_deployments_enabled = false

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"
}

module "eqao-dump-res-drun-240501-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-dump-res-drun-240501"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-08361cc5edba95acb"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  nat_gateway_eip = "*************"
  # eip disabled due to CORS error; load balancer subnet must be manually modified after terraform apply
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  api_branch_override = "vea-eqao-dump-res-drun-240501-asg" # This must match aws_autoscaling_group_api.name or else API deploys will fail
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  api_config_file = "eqao-dump-restore.json"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "master.eqao-dump-res-drun-240501-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection":  {
      "host" : "mpt-www-master-aurora-cluster-known-good-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database" : "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read": {
    "connection": {
      "host" : "mpt-www-master-aurora-cluster-known-good-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database": "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "mpt-www-master-aurora-cluster-known-good-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
          "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "old_db_hosts": [
    "mpt-www-replica-0.cednqnegvay6.ca-central-1.rds.amazonaws.com",
    "mpt-www-replica0.cednqnegvay6.ca-central-1.rds.amazonaws.com"
  ],
  "old_oct_api": {
    "domain": "https://apps.oct.ca",
    "password": "20S;MDsk?zqJCT+34X20"
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "***********************************************************************************************************",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_LeAvLFpyqiEy7NKBKHawSs6RrSoqtVvW"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-07fd56894eda879eb",
  "eqaoScanningServiceConfig": {
    "url": "http://vea-eqao-qc-scanner-alb-141814604.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}

module "eqao-dump-res-drun-240501" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-dump-res-drun-240501"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

module "xqc5" {
  source = "./modules/mpt-instance"
  deployment_name = "xqc5"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-07981ec814e30ab4d"
  api_instance_class = "c5n.large"
  api_asg_min_size = 0
  api_asg_desired_capacity = 16
  api_asg_max_size = 2
  rds_parameter_group = module.rds_parameter_groups.mysql-default-parameter-group
  db_password = var.qc4_db_password
  db_instance_class = "db.t3.small"
  db_performance_insights = false
  db_num_replicas = 0
  db_snapshot_identifier = "rds:mpt-qc6-master-uat-2021-11-02-08-25"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Configure APIs to use user data script and download API builds from S3 when they first start up, this allows
  # for API auto deployments
  enable_api_s3_deployments = true

  # Configure APIs to use Instance Profile which has necessary permissions for API auto deployments
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Configure this deployment to not require credentials when deploying APIs automatically from GitLab
  privileged_api_deployments_enabled = false

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"
}

module "eqao2-stress-06032022-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao2-stress-06032022"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  # mpt_api_ami_id = "ami-0c039489c5ea62f66" # Updated image with max_memory_restart=2GB
  # mpt_api_ami_id = "ami-0cf175bed2ccc34f8" # Updated image with instances = -1
  # mpt_api_ami_id = "ami-04ce5351372c7d4b4" # Updated image with instances = 3 - For stress testing
  mpt_api_ami_id = "ami-0b31d4c7624bc534b"
  api_instance_class = "c5n.xlarge"
  api_asg_min_size = 1
  api_asg_desired_capacity = 0
  api_asg_max_size = 120
  api_branch_override = "vea-eqao2-stress-06032022-asg"
  nat_gateway_eip = "*************"
  lambda_sns_arn = null
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = null
  lb_idle_timeout = 120
  product_tag = "VEA-LoadTest-ON"
  api_config_file = "eqao2.json"
  api_config_data = <<EOT
{
  "isStressTest": true,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "master.mpt-www-stress-29aug-cluster-nocm.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host" : "eqao2-stress-test-db.internal.vretta.com",
      "port" : "3306",
      "user" : "admin",
      "password" : "********************************",
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 12
    },
    "logQueryTiming": true
  },
  "mysql_read": {
    "connection": {
      "host" : "eqao2-stress-test-db.internal.vretta.com",
      "port" : "3306",
      "user" : "admin",
      "password" : "********************************",
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 12
    },
    "logQueryTiming": true
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "eqao2-stress-test-db-ro.internal.vretta.com",
      "port" : "3306",
      "user" : "admin",
      "password" : "********************************",
      "timezone": "Z",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 12
    },
    "logQueryTiming": true
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "***********************************************************************************************************",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_LeAvLFpyqiEy7NKBKHawSs6RrSoqtVvW"
  },
  "disableIpRegistration": true,
    "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-0d4c85495f4766379", 
  "websockets": {
    "invigilation": {
      "url": "g7m57vnn70.execute-api.us-east-1.amazonaws.com/production",
      "region": "ca-central-1",
      "secretPass": "2nNLdmA38S3Q48DC",
      "gracePeriod": 30000
    }
  },
  "eqaoScanningServiceConfig": {
    "url": "http://vea-eqao-qc-scanner-alb-141814604.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}

module "eqao2-stress-06032022" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao2-stress-06032022"
  base_domain = "vretta.com"
  product_tag = "VEA-LoadTest"
}


module "eqao-g9-tw-25-report-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-g9-tw-25-report-api"
  product_tag = "VEA-ON"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-0db042b661d841bce"
  api_instance_class = "t3a.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  api_branch_override = "vea-eqao-g9-tw-25-report-api-asg"
  nat_gateway_eip = "*************"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  api_config_file = "eqao2.json"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "mysql_write": {
    "connection": {
      "host" : "mpt-www-master-aurora-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "__DB_PASSWORD__",
      "database" : "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read": {
    "connection": {
      "host" : "mpt-www-master-aurora-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "__DB_PASSWORD__",
      "database": "mpt_dev"
    },
    "logQueryTiming": true
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "mpt-www-master-aurora-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "__DB_PASSWORD__",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "***********************************************************************************************************",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_LeAvLFpyqiEy7NKBKHawSs6RrSoqtVvW"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  }
}
EOT
}

module "eqao-g9-tw-25-report" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-g9-tw-25-report"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

module "bced-uat-qc-api" {
  source = "./modules/vea-api"
  deployment_name = "bced-uat-qc-api"
  vpc_id = "vpc-0721034b760dbe03f"
  api_a_subnet_id = "subnet-06f813182a803dd54"
  api_b_subnet_id = "subnet-0703bd10b44c56321"
  pub_subnet_ids = ["subnet-0a98f3605b5db7de9"]
  allow_http_security_group_id = "sg-075a8caa1bd77d3dd"
  allow_tls_security_group_id = "sg-08a1d31b34ce089d0"
  mpt_api_ami_id = "ami-0ed244e8989375a27"
  api_instance_class = "t3a.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  api_branch_override = "vea-bced-uat-qc-api-asg"
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  product_tag = "VEA-BC"
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  api_config_file = "mirror-bced-qc.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "mpt-bced-uat-master.internal.vretta.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-bced-uat-master.internal.vretta.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "bced-uat-qc" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-uat-qc"
  base_domain = "vretta.com"
  product_tag = "VEA-BC"
}

module "eqao-to-bced-qc-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-to-bced-qc"
  vpc_id = "vpc-0721034b760dbe03f"
  api_a_subnet_id = "subnet-06f813182a803dd54"
  api_b_subnet_id = "subnet-0703bd10b44c56321"
  pub_subnet_ids = ["subnet-0a98f3605b5db7de9"]
  allow_http_security_group_id = "sg-075a8caa1bd77d3dd"
  allow_tls_security_group_id = "sg-08a1d31b34ce089d0"
  mpt_api_ami_id = "ami-0b9098d87eed2c32d"
  api_instance_class = "t3a.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  api_branch_override = "vea-eqao-to-bced-qc-asg"
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
    product_tag = "VEA-BC"
  api_config_file = "mirror-bced-qc.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "mpt-bced-uat-master.internal.vretta.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-bced-uat-master.internal.vretta.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}


module "eqao-to-bced-qc" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-to-bced-qc"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

module "bced-prod-mirror-api" {
  source = "./modules/vea-api"
  deployment_name = "bced-prod-mirror"
  vpc_id = "vpc-0721034b760dbe03f"
  api_a_subnet_id = "subnet-06f813182a803dd54"
  api_b_subnet_id = "subnet-0703bd10b44c56321"
  pub_subnet_ids = ["subnet-0a98f3605b5db7de9"]
  allow_http_security_group_id = "sg-075a8caa1bd77d3dd"
  allow_tls_security_group_id = "sg-08a1d31b34ce089d0"
  mpt_api_ami_id = "ami-09af8b740af123604"
  api_instance_class = "c6gn.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-BC"
  api_config_file = "production.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "mpt-bced-uat-master.internal.vretta.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "mpt-bced-uat-master.internal.vretta.com",
      "port": 3306,
      "password" : "d372299ded855179a12e589274c5cf69"
    }
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "bced-prod-mirror" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-prod-mirror"
  base_domain = "vretta.com"
  product_tag = "VEA-BC"
}

module "bced-auth-staging" {
  source = "./modules/mpt-front-end"
  deployment_name = "bced-auth-staging"
  base_domain = "vretta.com"
  product_tag = "VEA-BC"
}

module "eqao-editing-tools-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-editing-tools"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  # Note 2023/10/17: the public subnets are set to the above values by terraform and must be
  #   manually changed to the below values. https://mattermost.vretta.com/vretta/pl/cj33cints7npddnc1wbz1f7g9r
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-09af8b740af123604"
  api_instance_class = "t4g.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  # ignore_subnet_changes = true
  nat_gateway_eip = "*************" // nat-0916119935de8c008
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  api_config_file = "production.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-eqao-editing-tools-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-eqao-editing-tools-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-eqao-editing-tools-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "vea-eqao-editing-tools-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "logQueryTiming": false,
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "stripe_api": {
    "secretKey": "",
    "apiVersion": "",
    "endpointSecret": ""
  },
  "oct_api": {
    "password": ""
  }
}
EOT
}

module "eqao-editing-tools" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-editing-tools"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

module "abed-api" {
  source = "./modules/vea-api"
  deployment_name = "abed"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  # mpt_api_ami_id = "ami-09af8b740af123604"
  # mpt_api_ami_id = "ami-0f50491bddfaa5b36" # Updated image with max_memory restart = 1536M v2
  mpt_api_ami_id = "ami-0fdca2c979c060074" # Updated image with max_memory restart = 1536M v3
  api_instance_class = "t4g.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  product_tag = "VEA-AB"
  type_tag = "PROD"
  api_config_file = "production.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-abed-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "authentication": {
    "jwtOptions": {
      "expiresIn": "12h"
    }
  },
  "mysql_write": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "logQueryTiming": false,
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": null,
  "eqaoDataInstanceId": "i-03ac1680b709014e2",
  "scanningServiceConfig": {
    "url": "http://vea-abed-scanner-alb-1850209071.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}

module "abed" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
  web_ui_alt_domains = ["caec.vretta.com"]
    create_cname_records  = {
    "caec.vretta.com" = "abed.vretta.com"
  }
}


module "abed-staging-cycle17" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-staging-cycle17"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-virtualtools-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-virtualtools"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  # mpt_api_ami_id = "ami-09af8b740af123604"
  # mpt_api_ami_id = "ami-0f50491bddfaa5b36" # Updated image with max_memory restart = 1536M v2
  mpt_api_ami_id = "ami-0fdca2c979c060074" # Updated image with max_memory restart = 1536M v3
  api_instance_class = "t4g.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  product_tag = "VEA-AB"
  type_tag = "QC"
  api_config_file = "abed-virtualtools.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-abed-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "authentication": {
    "jwtOptions": {
      "expiresIn": "12h"
    }
  },
  "mysql_write": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": null,
  "eqaoDataInstanceId": "i-03ac1680b709014e2",
  "scanningServiceConfig": {
    "url": "http://vea-abed-scanner-alb-1850209071.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}

module "abed-virtualtools" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-virtualtools"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-respondus-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-respondus"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  mpt_api_ami_id = "ami-09af8b740af123604"
  api_instance_class = "t4g.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  product_tag = "VEA-AB"
  type_tag = "QA"
  api_config_file = "production.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.abed-respondus.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "authentication": {
    "jwtOptions": {
      "expiresIn": "12h"
    }
  },
  "mysql_write": {
    "connection": {
      "host": "abed-prod-mirror-aurora.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "abed-prod-mirror-aurora.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host": "abed-prod-mirror-aurora.internal.vretta.com",
      "port": 3306,
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "logQueryTiming": false,
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": null,
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "abed-respondus" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-respondus"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

# Removed 2025-03-03: https://www.notion.so/vretta/bb555fd53efd43d5b2b340deee46af8b
module "abed-stress-test-202312" {
  source = "./modules/mpt-instance"
  deployment_name = "abed-stress-test-202312"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-0947b2a5c38bcd43a"
  api_instance_class = "c5n.large"
  api_asg_min_size = 0
  api_asg_desired_capacity = 2
  api_asg_max_size = 2
  db_password = var.xqc1_db_password
  db_max_allocated_storage = 999
  db_performance_insights = false
  db_instance_class = "db.t3.small"
  db_num_replicas = 0
  db_multi_az = false
  db_snapshot_identifier = "rds:vea-abed-cluster-cluster-2023-12-11-08-32"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"
}

module "abed-stress-test-20231211-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-stress-test-20231211"
  vpc_id = "vpc-0445a1e028656c29c"
  api_b_subnet_id = "subnet-032e7fd33ba40eb1c"
  api_a_subnet_id = "subnet-0cd04f0ad83e530b0"
  pub_subnet_ids = ["subnet-04dfaf33dde07c44d"]
  allow_http_security_group_id = "sg-064f7c0dee22c4445"
  allow_tls_security_group_id = "sg-0226243a707963695"
  mpt_api_ami_id = "ami-09af8b740af123604"
  api_instance_class = "t4g.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-LoadTest"
  api_config_file = "abed-stress-test-20231211.json"
  api_config_data = <<EOT
{
  "isStressTest": true,
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "abed-stress-test-db.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "abed-stress-test-db.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host": "abed-stress-test-db-ro.internal.vretta.com",
      "port": 3306,
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "logQueryTiming": false,
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": null,
  "eqaoDataInstanceId": "i-03ac1680b709014e2",
  "redis": {
    "host": "master.vea-abed-stress-test-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  }

}
EOT
}

module "abed-stress-test-20231211" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-stress-test-20231211"
  base_domain = "vretta.com"
  product_tag = "VEA-LoadTest"
}

module "abed-print" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-print"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-staging" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-staging"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-qc-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-qc"
  vpc_id = "vpc-056eb501207ce93ef"
  nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-0aa9ae7991c3401ee"
  api_b_subnet_id = "subnet-07d27c3faa2c9b4e3"
  # api_d_subnet_id = "subnet-0c99fc69b58e55e94" # manually added to work around running out of IPs
  pub_subnet_ids = ["subnet-0c462568e86ad238e", "subnet-0dab58e9bcab6b59b"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  # mpt_api_ami_id = "ami-09fee929929e834a8" 
  mpt_api_ami_id = "ami-0fdca2c979c060074" # abed prod ami for stress testing
  api_instance_class = "t4g.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-AB"
  type_tag = "QC"
  api_config_file = "abed-qc.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-abed-qc-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-abed-qc-20250617-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-abed-qc-20250617-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": null,
  "scanningServiceConfig": {
    "url": "http://vea-abed-qc-scanner-alb-1179263512.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}

module "abed-qc" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-qc"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "newmeridian" {
  source = "./modules/mpt-front-end"
  deployment_name = "newmeridian"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "newmeridian-api" {
  source = "./modules/vea-api"
  deployment_name = "newmeridian"
  vpc_id = "vpc-056eb501207ce93ef"
  nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-0aa9ae7991c3401ee"
  api_b_subnet_id = "subnet-07d27c3faa2c9b4e3"
  pub_subnet_ids = ["subnet-0c462568e86ad238e", "subnet-0dab58e9bcab6b59b"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-AB"
  api_config_file = "newmeridian.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "vea-newmeridian-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-newmeridian-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": null
}
EOT
}

module "abed-qc-pasi-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-qc-pasi"
  vpc_id = "vpc-056eb501207ce93ef"
  nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-0aa9ae7991c3401ee"
  api_b_subnet_id = "subnet-07d27c3faa2c9b4e3"
  pub_subnet_ids = ["subnet-0c462568e86ad238e", "subnet-0dab58e9bcab6b59b"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-AB"
  api_config_file = "production.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-abed-qc-pasi-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-abed-pasi-qc-db.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-abed-pasi-qc-db.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host": "vea-abed-pasi-qc-db-ro.internal.vretta.com",
      "port": 3306,
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "logQueryTiming": false,
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": null
}
EOT
}

module "abed-qc-pasi" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-qc-pasi"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-uat-auth-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-uat-auth"
  vpc_id = "vpc-056eb501207ce93ef"
  nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-0aa9ae7991c3401ee"
  api_b_subnet_id = "subnet-07d27c3faa2c9b4e3"
  pub_subnet_ids = ["subnet-0c462568e86ad238e", "subnet-0dab58e9bcab6b59b"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-AB"
  type_tag = "QC"
  api_config_file = "abed-uat-auth.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-abed-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": null
}
EOT
}

module "abed-uat-auth" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-uat-auth"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-qc2-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-qc2"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  # mpt_api_ami_id = "ami-09af8b740af123604"
  # mpt_api_ami_id = "ami-0f50491bddfaa5b36" # Updated image with max_memory restart = 1536M v2
  mpt_api_ami_id = "ami-0fdca2c979c060074" # Updated image with max_memory restart = 1536M v3
  api_instance_class = "t4g.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  product_tag = "VEA-AB"
  type_tag = "QC"
  api_config_file = "abed-qc2.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-abed-qc2-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "authentication": {
    "jwtOptions": {
      "expiresIn": "12h"
    }
  },
  "mysql_write": {
    "connection": {
      "host": "vea-abed-qc2-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-abed-qc2-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host": "vea-abed-qc2-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "logQueryTiming": false,
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": null,
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-03ac1680b709014e2",
  "scanningServiceConfig": {
    "url": "http://vea-abed-qc-scanner-alb-1179263512.ca-central-1.elb.amazonaws.com:5000"
  }
}
EOT
}

module "abed-qc2" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-qc2"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-staging-depload-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-staging-depload"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  # mpt_api_ami_id = "ami-09af8b740af123604"
  # mpt_api_ami_id = "ami-0f50491bddfaa5b36" # Updated image with max_memory restart = 1536M v2
  mpt_api_ami_id = "ami-0fdca2c979c060074" # Updated image with max_memory restart = 1536M v3
  api_instance_class = "t4g.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  product_tag = "VEA-AB"
  type_tag = "QC"
  api_config_file = "abed-staging-depload.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-abed-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "authentication": {
    "jwtOptions": {
      "expiresIn": "12h"
    }
  },
  "mysql_write": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": null,
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "abed-staging-depload" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-staging-depload"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-print-uat" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-print-uat"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-staging-cycle18-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-staging-cycle18"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-AB"
  type_tag = "QC"
  api_config_file = "abed-staging-cycle18.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-abed-prod-mirror-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "abed-prod-mirror-aurora.internal.vretta.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "abed-prod-mirror-aurora.internal.vretta.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": null,
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "abed-staging-cycle18" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-staging-cycle18"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-post-it" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-post-it"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-devkb" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-devkb"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "dsbn-api" {
  source = "./modules/vea-api"
  deployment_name = "dsbn"
  vpc_id = "vpc-056eb501207ce93ef"
  nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-0aa9ae7991c3401ee"
  api_b_subnet_id = "subnet-07d27c3faa2c9b4e3"
  pub_subnet_ids = ["subnet-0c462568e86ad238e", "subnet-0dab58e9bcab6b59b"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-AB"
  type_tag = "PROD"
  api_config_file = "dsbn.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-dsbn-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-dsbn-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-dsbn-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": null
}
EOT
}

module "dsbn" {
  source = "./modules/mpt-front-end"
  deployment_name = "dsbn"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
  web_ui_alt_domains = ["assess.vretta.com"]
}

module "multiplex-uat-api" {
  source = "./modules/vea-api"
  deployment_name = "multiplex-uat"
  vpc_id = "vpc-02162041c8b21bc29"
  # nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-03d4eb05f80addfef"
  api_b_subnet_id = "subnet-0695dde78791c05e4"
  pub_subnet_ids = ["subnet-0add941015c41c2fc"]
  allow_http_security_group_id = "sg-0515c1873f447ac02"
  allow_tls_security_group_id = "sg-02c57a7901bbd34a9"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-Board"
  type_tag = "QC"
  api_config_file = "multiplex-uat.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-multiplex-uat-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-multiplex-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-multiplex-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": null
}
EOT
}

module "multiplex-tts-api" {
  source = "./modules/vea-api"
  deployment_name = "multiplex-tts"
  vpc_id = "vpc-02162041c8b21bc29"
  # nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-03d4eb05f80addfef"
  api_b_subnet_id = "subnet-0695dde78791c05e4"
  pub_subnet_ids = ["subnet-0add941015c41c2fc"]
  allow_http_security_group_id = "sg-0515c1873f447ac02"
  allow_tls_security_group_id = "sg-02c57a7901bbd34a9"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-Board"
  type_tag = "QC"
  api_config_file = "multiplex-tts.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-multiplex-tts-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-multiplex-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-multiplex-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": null
}
EOT
}


module "multiplex-privschls-api" {
  source = "./modules/vea-api"
  deployment_name = "multiplex-privschls"
  vpc_id = "vpc-0665fa28ec8c67f27"
  api_b_subnet_id = "subnet-0c600f9917e761879"
  api_a_subnet_id = "subnet-0cbf14e8c388abd0d"
  pub_subnet_ids = ["subnet-051c92f41b0d8f430", "subnet-09acc98a403d6001c", "subnet-00db6fc555f6fdc4b"]
  allow_http_security_group_id = "sg-0d540d632c5b93dab"
  allow_tls_security_group_id = "sg-084c47e8efac8839e"
  mpt_api_ami_id = "ami-0ede19d36b2af65c7"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-Private"
  type_tag = "QC"
  api_config_file = "multiplex-privschls.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-multiplex-privschls-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-eassessment-f25-db-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-eassessment-f25-db-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": null,
  "stripe_api": {
    "secretKey": "sk_test_51PqKLEFxSPwOifyEplo0wO95ffWJYNdey1k1lP6E38W6Fk9APKFDzn91or4gPtARExPKHye9xIw9oizAoNX21TkQ00Z7yA4Wiz",
    "apiVersion": "2024-06-20",
    "endpointSecret": "whsec_0VObqpBqiGbNbpi42pp5OYmTHmO24xbD"
  },
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "multiplex-mtic-api" {
  source = "./modules/vea-api"
  deployment_name = "multiplex-mtic"
  vpc_id = "vpc-02162041c8b21bc29"
  # nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-03d4eb05f80addfef"
  api_b_subnet_id = "subnet-0695dde78791c05e4"
  pub_subnet_ids = ["subnet-0add941015c41c2fc"]
  allow_http_security_group_id = "sg-0515c1873f447ac02"
  allow_tls_security_group_id = "sg-02c57a7901bbd34a9"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-Board"
  type_tag = "QC"
  api_config_file = "multiplex-mtic.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-multiplex-mtic-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-multiplex-mtic-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-multiplex-mtic-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": null
}
EOT
}

module "multiplex-stress-api" {
  source = "./modules/vea-api"
  deployment_name = "multiplex-stress"
  vpc_id = "vpc-02162041c8b21bc29"
  # nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-03d4eb05f80addfef"
  api_b_subnet_id = "subnet-0695dde78791c05e4"
  pub_subnet_ids = ["subnet-0add941015c41c2fc"]
  allow_http_security_group_id = "sg-0515c1873f447ac02"
  allow_tls_security_group_id = "sg-02c57a7901bbd34a9"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-LoadTest"
  type_tag = "QC"
  api_config_file = "multiplex-stress.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-multiplex-stress-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-multiplex-stress-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-multiplex-stress-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": null
}
EOT
}

module "multiplex-uat" {
  source = "./modules/mpt-front-end"
  deployment_name = "multiplex-uat"
  base_domain = "vretta.com"
  product_tag = "VEA-Board"
  web_ui_alt_domains = [
    "ema-uat.vretta.com"
  ]
}

module "multiplex-tts" {
  source = "./modules/mpt-front-end"
  deployment_name = "multiplex-tts"
  base_domain = "vretta.com"
  product_tag = "VEA-Board"
}

module "multiplex-privschls" {
  source = "./modules/mpt-front-end"
  deployment_name = "multiplex-privschls"
  base_domain = "vretta.com"
  product_tag = "VEA-Private"
}

module "multiplex-mtic" {
  source = "./modules/mpt-front-end"
  deployment_name = "multiplex-mtic"
  base_domain = "vretta.com"
  product_tag = "VEA-Board"
}

module "multiplex-calc" {
  source = "./modules/mpt-front-end"
  deployment_name = "multiplex-calc"
  base_domain = "vretta.com"
  product_tag = "VEA-Board"
}

module "multiplex-stress" {
  source = "./modules/mpt-front-end"
  deployment_name = "multiplex-stress"
  base_domain = "vretta.com"
  product_tag = "VEA-Board"
}

# Used 2024-07-12 to create VPC for 'multiplex' environment
# https://bubo.vretta.com/vea/platform/vea-infrastructure/-/issues/2866
module "multiplex" {
  source = "./modules/mpt-instance"
  deployment_name = "multiplex"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-09af8b740af123604" # vea-api-image-20230202-v2
  api_instance_class = "t4g.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 2
  api_asg_max_size = 2
  db_password = var.xqc1_db_password
  db_max_allocated_storage = 999
  db_performance_insights = false
  db_instance_class = "db.t4g.medium"
  aurora_instance_class = "db.t4g.medium"
  db_num_replicas = 0
  db_multi_az = false
  db_snapshot_identifier = "vea-dsbn-cluster-20240712-1619"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn

  aurora_cluster_identifier = "doesnotexist"

  product_tag = "VEA-Board"
  type_tag = "PROD"
}

module "multiplex-api" {
  source = "./modules/vea-api"
  deployment_name = "multiplex"
  vpc_id = "vpc-02162041c8b21bc29"
  # nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-03d4eb05f80addfef"
  api_b_subnet_id = "subnet-0695dde78791c05e4"
  pub_subnet_ids = ["subnet-0add941015c41c2fc"]
  allow_http_security_group_id = "sg-0515c1873f447ac02"
  allow_tls_security_group_id = "sg-02c57a7901bbd34a9"
  mpt_api_ami_id = "ami-06ea4367c32b9cdd1"
  api_instance_class = "r6a.large"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-Board"
  type_tag = "PROD"
  api_config_file = "multiplex.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-multiplex-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-multiplex-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-multiplex-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": null
}
EOT
}

module "multiplex-wc" {
  source = "./modules/mpt-front-end"
  deployment_name = "multiplex"
  base_domain = "vretta.com"
  product_tag = "VEA-Board"
  type_tag = "PROD"
  web_ui_alt_domains = [
    "ema-numeracy-screener.vretta.com",
    "earlyyears.vretta.com", 
    "assessment.vretta.com",
    "dsbn.vretta.com",
    "earlyears.vretta.com", 
    "early-years.vretta.com", 
    "ema.vretta.com",
    "cscn.vretta.com",
    "eqao-interim.vretta.com"
  ]
  # assess.vretta.com manually set up to redirect to assessment.vretta.com - https://www.notion.so/vretta/new-URL-set-up-for-Multiplex-1fc6ed9659b2803aacfbdd1ae422ca7d
}

module "multiplex-qc2-api" {
  source = "./modules/vea-api"
  deployment_name = "multiplex-qc2"
  vpc_id = "vpc-02162041c8b21bc29"
  # nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-03d4eb05f80addfef"
  api_b_subnet_id = "subnet-0695dde78791c05e4"
  pub_subnet_ids = ["subnet-0add941015c41c2fc"]
  allow_http_security_group_id = "sg-0515c1873f447ac02"
  allow_tls_security_group_id = "sg-02c57a7901bbd34a9"
  mpt_api_ami_id = "ami-0cb4dacf648ebf6b2"
  api_instance_class = "r6a.large"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-Board"
  type_tag = "QC"
  api_config_file = "multiplex-qc2.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-multiplex-qc2-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-multiplex-qc2-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-multiplex-qc2-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": null
}
EOT
}

module "multiplex-qc2" {
  source = "./modules/mpt-front-end"
  deployment_name = "multiplex-qc2"
  base_domain = "vretta.com"
  product_tag = "VEA-Board"
}

# Removed 2025-03-03: https://www.notion.so/vretta/bb555fd53efd43d5b2b340deee46af8b
module "abed-fall24" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-fall24"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-ncr" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-ncr"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-ncr2" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-ncr2"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-pat6" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-pat6"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-tts" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-tts"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-mk" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-mk"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

# Removed 2025-03-03: https://www.notion.so/vretta/bb555fd53efd43d5b2b340deee46af8b
module "abed-ang10" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-auth-preview"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-ang10-qc" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-qc-auth-preview"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-ang10-qc-temp" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-qc-auth-preview-temp"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-print-preview" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-print-preview"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-tts-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-tts"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-AB"
  type_tag = "QC"
  api_config_file = "abed-tts.json"
  api_config_data = <<EOT
{
  "redis": {
    "isDisabled": true
  },
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": null,
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "caec-qc-api" {
  source = "./modules/vea-api"
  deployment_name = "caec-qc"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  mpt_api_ami_id = "ami-09af8b740af123604"
  api_instance_class = "t4g.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  product_tag = "VEA-CAEC"
  type_tag = "UAT"
  api_config_file = "caec-qc.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-caec-qc-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "caec-qc-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "caec-qc-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host": "caec-qc-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "logQueryTiming": false,
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": null,
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "caec-qc" {
  source = "./modules/mpt-front-end"
  deployment_name = "caec-qc"
  base_domain = "vretta.com"
  product_tag = "VEA-CAEC"
}

module "caec-qc-dev-api" {
  source = "./modules/vea-api"
  deployment_name = "caec-qc-dev"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  mpt_api_ami_id = "ami-09af8b740af123604"
  api_instance_class = "t4g.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  product_tag = "VEA-CAEC"
  type_tag = "QC"
  api_config_file = "caec-qc-dev.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-caec-qc-dev-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "abed-prod-dev-aurora.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "abed-prod-dev-aurora.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": null,
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "abed-prod-dev" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-prod-dev"
  base_domain = "vretta.com"
  product_tag = "VEA-CAEC"
}

module "abed-prod-dev-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-prod-dev"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  mpt_api_ami_id = "ami-09af8b740af123604"
  api_instance_class = "t4g.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  product_tag = "VEA-CAEC"
  type_tag = "QC"
  api_config_file = "abed-prod-dev.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-caec-qc-dev-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "abed-prod-dev-aurora.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "abed-prod-dev-aurora.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": null,
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "caec-qc-dev" {
  source = "./modules/mpt-front-end"
  deployment_name = "caec-qc-dev"
  base_domain = "vretta.com"
  product_tag = "VEA-CAEC"
}

module "caec-uat-api" {
  source = "./modules/vea-api"
  deployment_name = "caec-uat"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  mpt_api_ami_id = "ami-09af8b740af123604"
  api_instance_class = "t4g.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  product_tag = "VEA-CAEC"
  type_tag = "PROD"
  api_config_file = "caec-uat.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "logQueryTiming": false,
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": null,
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}



module "caec-uat" {
  source = "./modules/mpt-front-end"
  deployment_name = "caec-uat"
  base_domain = "vretta.com"
  product_tag = "VEA-CAEC"
}

module "caec-api" {
  source = "./modules/vea-api"
  deployment_name = "caec"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  mpt_api_ami_id = "ami-09af8b740af123604"
  api_instance_class = "t4g.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  product_tag = "VEA-CAEC"
  type_tag = "PROD"
  api_config_file = "caec.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "logQueryTiming": false,
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": null,
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "eqao-sdc-load-20230321-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-sdc-load-20230321"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-0db042b661d841bce"
  api_instance_class = "t3a.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  api_branch_override = "vea-eqao-sdc-load-20230321-asg"
  nat_gateway_eip = "*************" // nat-0916119935de8c008
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  type_tag = "QC"
  lb_idle_timeout = 120
  api_config_file = "sdc-load.json"
  api_config_data = <<EOT
{
  "isDevMode": true,
  "logLevel": "debug",
  "redis": {
    "host": "master.vea-eqao-sdc-load-20230321-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "client": "mysql2",
    "connection": {
      "host" : "sdc-load-test-20240510-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database" : "mpt_dev"
    },
    "logQueryTiming": false
  },
  "mysql_read": {
    "client": "mysql2",
    "connection": {
      "host" : "sdc-load-test-20240510-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database": "mpt_dev"
    },
    "logQueryTiming": false
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "sdc-load-test-20240510-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "logQueryTiming": false,
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": null,
  "stripe_api": {
    "secretKey": "",
    "apiVersion": "",
    "endpointSecret": ""
  }
}
EOT
}

module "eqao-sdc-load-20230321" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-sdc-load-20230321"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

module "abed-uat-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-uat"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  # mpt_api_ami_id = "ami-034888fba59882aa9" # Updated image with max_memory restart = 1.5GB
  mpt_api_ami_id = "ami-09bd70192e1fb31a8" # Updated image with max_memory restart = 1536M
  # mpt_api_ami_id = "ami-09fee929929e834a8"  
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-AB"
  type_tag = "UAT"
  api_config_file = "abed-uat.json"
  api_config_data = <<EOT
{
  "redis": {
    "host": "master.vea-abed-uat-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-abed-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "vea-abed-cluster-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "oct_api": null,
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "abed-uat" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-uat"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-prod-test" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-prod-test"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-prod-mirror" {
  source = "./modules/mpt-front-end"
  deployment_name = "abed-prod-mirror"
  base_domain = "vretta.com"
  product_tag = "VEA-AB"
}

module "abed-prod-mirror-api" {
  source = "./modules/vea-api"
  deployment_name = "abed-prod-mirror"
  vpc_id = "vpc-056eb501207ce93ef"
  api_b_subnet_id = "subnet-0dab58e9bcab6b59b"
  api_a_subnet_id = "subnet-0c462568e86ad238e"
  pub_subnet_ids = ["subnet-0dab58e9bcab6b59b", "subnet-0c462568e86ad238e"]
  allow_http_security_group_id = "sg-0defe997e8f5130b1"
  allow_tls_security_group_id = "sg-04bba50dbc5c7aa34"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-AB"
  type_tag = "QC"
  api_config_file = "abed-prod-mirror.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-abed-prod-mirror-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "abed-prod-mirror-aurora.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "abed-prod-mirror-aurora.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": null,
  "eqaoDataInstanceId": "i-03ac1680b709014e2"
}
EOT
}

module "eqao-pentest" {
  source = "./modules/mpt-instance"
  deployment_name = "eqao-pentest"
  base_domain = "vretta.com"
  mpt_api_ami_id = "ami-0db042b661d841bce"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  api_asg_max_size = 2
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  aurora_cluster_identifier = "doesnotexist"

  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm

  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  api_config_file = "eqao-pentest.json"
  api_config_data = <<EOT
{
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "master.eqao-pentest-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host" : "eqao-pentest-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database" : "mpt_dev"
    }
  },
  "mysql_read": {
    "connection": {
      "host" : "eqao-pentest-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database": "mpt_dev"
    }
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "eqao-pentest-cluster.cluster-ro-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "timezone": "Z",
      "user" : "admin",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 25
    }
  },
  "old_db_hosts": [
    "mpt-www-replica-0.cednqnegvay6.ca-central-1.rds.amazonaws.com",
    "mpt-www-replica0.cednqnegvay6.ca-central-1.rds.amazonaws.com"
  ],
  "old_oct_api": {
    "domain": "https://apps.oct.ca",
    "password": "20S;MDsk?zqJCT+34X20"
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "stripe_api": {
    "secretKey": "sk_test_51QNftOPQrydDgkRH5eMmDcbBTgJhEWWNxkr2fDJr0x2GOd59XNOfD81WwPoPqUZFBIGsb7FJIp3ToyTNm3vsGqMH00bgLvl8jY",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_SojkRABaWAbfM2eq6NtWDxnnvbDC3vU9"
  },
  "awsCredentials": {
    "secretAccessKey": "qQnac19/DVVTs+Rz+DITEw3yV1tmTtfWx1iKnjVk",
    "accessKeyId": "********************",
    "region": "ca-central-1"
  },
  "eqaoDataInstanceId": "i-07fd56894eda879eb"
}
EOT
}

module "caec" {
  source = "./modules/mpt-front-end"
  deployment_name = "caec"
  base_domain = "vretta.com"
  product_tag = "VEA-CAEC"
  web_ui_alt_domains = ["caec-transition.vretta.com"]
}

# module "eqao-pentest" {
#   source = "./modules/mpt-front-end"
#   deployment_name = "eqao-pentest"
#   base_domain = "vretta.com"
#   product_tag = "VEA-ON"
# }

module "eqao-mpt-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-mpt"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-0d4799aaa4920185d", "subnet-0feb5003a065b8a67"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-0b31d4c7624bc534b"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-MPT"
  type_tag = "PROD"
  api_config_file = "eqao-mpt.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host" : "vea-eqao-mpt-primary-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "mysql_read": {
    "connection": {
      "host" : "vea-eqao-mpt-primary-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "database": "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "****************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  }
}
EOT
}

module "eqao-mpt" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-mpt"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
  web_ui_alt_domains = [
    "www.mathproficiencytest.ca",
    "testcompetencesmaths.ca",
    "mathproficiencytest.ca",
    "www.testcompetencesmaths.ca"
  ]
}

module "eqao-mpt-qc-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-mpt-qc"
  vpc_id = "vpc-06a39f13c3b1a00f9"
  api_b_subnet_id = "subnet-0848c4c15fc5be478"
  api_a_subnet_id = "subnet-04a2377fc46371aea"
  pub_subnet_ids = ["subnet-0a1eb3d1cab1bc271"]
  allow_http_security_group_id = "sg-07fde4871133a5cdd"
  allow_tls_security_group_id = "sg-04fc43c79609174c1"
  mpt_api_ami_id = "ami-0426dacce5a457d15"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-MPT"
  type_tag = "QC"
  api_config_file = "eqao-mpt-qc.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host" : "vea-eqao-mpt-qc-db-clone-test-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database": "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "mysql_read": {
    "connection": {
      "host" : "vea-eqao-mpt-qc-db-clone-test-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database": "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "****************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "websockets": {
    "chat": {
      "url": "0v9zha9gy2.execute-api.ca-central-1.amazonaws.com/mpt_qc",
      "region": "ca-central-1"
    }
  },
  "stripe_api": {
    "secretKey": "sk_test_51RT5cz4RvQn7zPthiCQgxghirXY2qfl87s2APXwq9Yt5ZUNvkxLGZcvC0BsTlfbA1KUYxqWYBUuwbRBapG8dSBK200mRJKLsvD",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_rVXXykAUnybbpUoHyacDIcLwjhayicfl"
  }
}
EOT
}

module "eqao-mpt-qc" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-mpt-qc"
  base_domain = "vretta.com"
  product_tag = "VEA-MPT"
}

module "eqao-mpt-prelive-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-mpt-prelive"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  nat_gateway_eip = "*************" # vpc-0f7a81e271f942cf1; required to assign public subnet to load balancer correctly
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-0b31d4c7624bc534b"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-MPT"
  type_tag = "QC"
  api_config_file = "eqao-mpt-prelive.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host" : "vea-eqao-mpt-primary-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "mysql_read": {
    "connection": {
      "host" : "vea-eqao-mpt-primary-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  }
}
EOT
}

module "eqao-mpt-uat-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-mpt-uat"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-0b31d4c7624bc534b"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  nat_gateway_eip = "*************" # required to assign public subnet to load balancer correctly
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-MPT"
  type_tag = "UAT"
  api_config_file = "eqao-mpt-uat.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host" : "eqao-mpt-uat-db-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "mysql_read": {
    "connection": {
      "host" : "eqao-mpt-uat-db-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database": "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "stripe_api": {
    "secretKey": "sk_test_51RT5cz4RvQn7zPthiCQgxghirXY2qfl87s2APXwq9Yt5ZUNvkxLGZcvC0BsTlfbA1KUYxqWYBUuwbRBapG8dSBK200mRJKLsvD",
    "apiVersion": "2020-08-27",
    "endpointSecret": "whsec_rVXXykAUnybbpUoHyacDIcLwjhayicfl"
  }
}
EOT
}

module "eqao-mpt-prelive" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-mpt-prelive"
  base_domain = "vretta.com"
  product_tag = "VEA-MPT"
}

module "eqao-mpt-pentest-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-mpt-pentest"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-0b31d4c7624bc534b"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-MPT"
  type_tag = "QC"
  api_config_file = "eqao-mpt-pentest.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "mysql_write": {
    "connection": {
      "host" : "eqao-mpt-pentest-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "mysql_read": {
    "connection": {
      "host" : "eqao-mpt-pentest-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "********************************",
      "database": "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  }
}
EOT
}

module "eqao-mpt-pentest" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-mpt-pentest"
  base_domain = "vretta.com"
  product_tag = "VEA-MPT"
}

module "mpt-admin-api" {
  source = "./modules/vea-api"
  deployment_name = "mpt-admin"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  nat_gateway_eip = "*************" # mpt-www-nat-gw / vpc-0f7a81e271f942cf1
  mpt_api_ami_id = "ami-0b31d4c7624bc534b"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-ON"
  type_tag = "QC"
  api_config_file = "mpt-admin.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.mpt-admin-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host" : "vea-eqao-mpt-primary-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "mysql_read": {
    "connection": {
      "host" : "vea-eqao-mpt-primary-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "database": "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "****************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  }
}
EOT
}

module "mpt-admin" {
  source = "./modules/mpt-front-end"
  deployment_name = "mpt-admin"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

module "mpt-admin-qc-api" {
  source = "./modules/vea-api"
  deployment_name = "mpt-admin-qc"
  vpc_id = "vpc-06a39f13c3b1a00f9"
  api_b_subnet_id = "subnet-0848c4c15fc5be478"
  api_a_subnet_id = "subnet-04a2377fc46371aea"
  pub_subnet_ids = ["subnet-0a1eb3d1cab1bc271"]
  allow_http_security_group_id = "sg-07fde4871133a5cdd"
  allow_tls_security_group_id = "sg-04fc43c79609174c1"
  mpt_api_ami_id = "ami-0426dacce5a457d15"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-MPT"
  type_tag = "QC"
  api_config_file = "mpt-admin-qc.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.mpt-admin-qc-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host" : "mpt-admin-qc-20250604-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "mysql_read": {
    "connection": {
      "host" : "mpt-admin-qc-20250604-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "password" : "__DB_PASSWORD__",
      "database": "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "****************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  }
}
EOT
}

module "mpt-admin-qc" {
  source = "./modules/mpt-front-end"
  deployment_name = "mpt-admin-qc"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

module "nled-api" {
  source = "./modules/vea-api"
  deployment_name = "nled"
  vpc_id = "vpc-02162041c8b21bc29"
  # nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-03d4eb05f80addfef"
  api_b_subnet_id = "subnet-0695dde78791c05e4"
  pub_subnet_ids = ["subnet-0add941015c41c2fc"]
  allow_http_security_group_id = "sg-0515c1873f447ac02"
  allow_tls_security_group_id = "sg-02c57a7901bbd34a9"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.large"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-NL"
  type_tag = "PROD"
  api_config_file = "nled-prod.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-nled-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-nled-aurora-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-nled-aurora-cluster-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": null
}
EOT
}

module "nled" {
  source = "./modules/mpt-front-end"
  deployment_name = "nled"
  base_domain = "vretta.com"
  product_tag = "VEA-NL"
  web_ui_alt_domains = ["evalnl.vretta.com"]
}

module "nled-qc-api" {
  source = "./modules/vea-api"
  deployment_name = "nled-qc"
  vpc_id = "vpc-02162041c8b21bc29"
  # nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-03d4eb05f80addfef"
  api_b_subnet_id = "subnet-0695dde78791c05e4"
  pub_subnet_ids = ["subnet-0add941015c41c2fc"]
  allow_http_security_group_id = "sg-0515c1873f447ac02"
  allow_tls_security_group_id = "sg-02c57a7901bbd34a9"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3a.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-NL"
  type_tag = "QC"
  api_config_file = "nled-qc.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-nled-qc-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "nled-qc.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "nled-qc-ro.internal.vretta.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": null
}
EOT
}

module "nled-qc" {
  source = "./modules/mpt-front-end"
  deployment_name = "nled-qc"
  base_domain = "vretta.com"
  product_tag = "VEA-NL"
}

module "nled-stress-test-api" {
  source = "./modules/vea-api"
  deployment_name = "nled-stress-test"
  vpc_id = "vpc-02162041c8b21bc29"
  # nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-03d4eb05f80addfef"
  api_b_subnet_id = "subnet-0695dde78791c05e4"
  pub_subnet_ids = ["subnet-0add941015c41c2fc"]
  allow_http_security_group_id = "sg-0515c1873f447ac02"
  allow_tls_security_group_id = "sg-02c57a7901bbd34a9"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.large"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-LoadTest-NL"
  type_tag = "LoadTest"
  api_config_file = "nled-stress-test.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-nled-stress-test-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-nled-stress-test-20250529-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-nled-stress-test-20250529-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "__DB_PASSWORD__"
    }
  },
  "oct_api": null
}
EOT
}

module "nled-stress-test" {
  source = "./modules/mpt-front-end"
  deployment_name = "nled-stress-test"
  base_domain = "vretta.com"
  product_tag = "VEA-LoadTest-NL"
}

module "eqao-g9-sample-staging" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-g9-sample-staging"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

module "eqao-mpt-uat" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-mpt-uat"
  base_domain = "vretta.com"
  product_tag = "VEA-MPT"
}

module "eqao-mpt-stress-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-mpt-stress"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  # mpt_api_ami_id = "ami-0c039489c5ea62f66" # Updated image with max_memory_restart=2GB
  # mpt_api_ami_id = "ami-0cf175bed2ccc34f8" # Updated image with instances = -1
  # mpt_api_ami_id = "ami-04ce5351372c7d4b4" # Updated image with instances = 3 - For stress testing
  mpt_api_ami_id = "ami-0b31d4c7624bc534b"
  nat_gateway_eip = "*************" # mpt-www-nat-gw / vpc-0f7a81e271f942cf1
  api_instance_class = "c5n.xlarge"
  api_asg_min_size = 1
  api_asg_desired_capacity = 0
  api_asg_max_size = 120
  api_branch_override = "vea-eqao-mpt-stress-asg"
  lambda_sns_arn = null
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = false
  vea_cloudwatch_alarms_sns_arn = null
  lb_idle_timeout = 120
  arctic_wolf_agent_enabled = true
  product_tag = "VEA-LoadTest-MPT"
  api_config_file = "eqao-mpt-stress.json"
  api_config_data = <<EOT
{
  "isStressTest": true,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "clustercfg.vea-eqao-mpt-stress-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host" : "eqao-mpt-stress-test-db.internal.vretta.com",
      "port" : "3306",
      "user" : "admin",
      "password" : "********************************",
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 12
    },
    "logQueryTiming": true
  },
  "mysql_read": {
    "connection": {
      "host" : "eqao-mpt-stress-test-db.internal.vretta.com",
      "port" : "3306",
      "user" : "admin",
      "password" : "********************************",
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 12
    },
    "logQueryTiming": true
  },
  "mysql_read_reporting": {
    "client": "mysql2",
    "connection": {
      "host" : "eqao-mpt-stress-test-db-ro.internal.vretta.com",
      "port" : "3306",
      "user" : "admin",
      "password" : "********************************",
      "timezone": "Z",
      "ssl" : {
        "rejectUnauthorized": false
      },
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 12
    },
    "logQueryTiming": true
  },
  "oct_api": {
    "domain": "https://apps.oct.ca",
    "username": "eqao",
    "password": "",
    "applicationId": "e3e0869e-b1ed-41ed-9d48-5fd66d1aae8f ",
    "clientSecret": "*************************************",
    "B2cTenantName": "octapi",
    "B2cAppName": "eqao",
    "endpointOAuth": "onmicrosoft.com"
  },
  "eqaoDataInstanceId": "i-0d4c85495f4766379"
}
EOT
}

module "eqao-mpt-stress" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-mpt-stress"
  base_domain = "vretta.com"
  product_tag = "VEA-LoadTest-MPT"
}

module "multiplex-qc-api" {
  source = "./modules/vea-api"
  deployment_name = "multiplex-qc"
  vpc_id = "vpc-02162041c8b21bc29"
  # nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-03d4eb05f80addfef"
  api_b_subnet_id = "subnet-0695dde78791c05e4"
  pub_subnet_ids = ["subnet-0add941015c41c2fc"]
  allow_http_security_group_id = "sg-0515c1873f447ac02"
  allow_tls_security_group_id = "sg-02c57a7901bbd34a9"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.medium"
  arctic_wolf_agent_enabled = true
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-Board"
  type_tag = "QC"
  api_config_file = "multiplex-qc.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.vea-multiplex-qc-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "vea-multiplex-qc-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "vea-multiplex-qc-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": null
}
EOT
}
module "multiplex-qc" {
  source = "./modules/mpt-front-end"
  deployment_name = "multiplex-qc"
  base_domain = "vretta.com"
  product_tag = "VEA-Board"
}

module "eqao-main-template" {
  source = "./modules/mpt-front-end"
  deployment_name = "eqao-main-template"
  base_domain = "vretta.com"
  product_tag = "VEA-ON"
}

module "eqao-main-template-api" {
  source = "./modules/vea-api"
  deployment_name = "eqao-main-template"
  vpc_id = "vpc-0f7a81e271f942cf1"
  api_b_subnet_id = "subnet-0feb5003a065b8a67"
  api_a_subnet_id = "subnet-0d4799aaa4920185d"
  pub_subnet_ids = ["subnet-014a315fb2b931b84", "subnet-041d664817e8a0350", "subnet-0be668dc0fd432849"]
  allow_http_security_group_id = "sg-07a1ad50289426116"
  allow_tls_security_group_id = "sg-063839eb1cc41db7c"
  mpt_api_ami_id = "ami-0b31d4c7624bc534b"
  api_instance_class = "t3.medium"
  api_asg_min_size = 1
  api_asg_desired_capacity = 1
  api_branch_override = "vea-eqao-main-template-asg"
  nat_gateway_eip = "*************"
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  lb_idle_timeout = 120
  product_tag = "VEA-ON"
  type_tag = "QC"
  api_config_file = "eqao-main-template.json"
  api_config_data = <<EOT
{
  "isStressTest": false,
  "isDevMode": false,
  "logLevel": "debug",
  "host": "0.0.0.0",
  "port": 3030,
  "authentication": {
    "jwtOptions": {
      "expiresIn": "6h"
    }
  },
  "redis": {
    "host": "clustercfg.vea-eqao2-redis-cluster.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host" : "eqao-main-template-mirror-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "__DB_PASSWORD__",
      "database" : "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  },
  "mysql_read": {
    "connection": {
      "host" : "eqao-main-template-mirror-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port" : "3306",
      "user": "vea_api",
      "password" : "__DB_PASSWORD__",
      "database": "mpt_dev"
    },
    "pool": {
      "min": 0,
      "max": 11
    }
  }
}
EOT
}

module "nbed-mplex" {
  source = "./modules/mpt-front-end"
  deployment_name = "nbed-mplex"
  base_domain = "vretta.com"
  product_tag = "VEA-Board"
}

module "nbed-mplex-api" {
  source = "./modules/vea-api"
  deployment_name = "nbed-mplex"
  vpc_id = "vpc-02162041c8b21bc29"
  # nat_gateway_eip = "*************"
  api_a_subnet_id = "subnet-03d4eb05f80addfef"
  api_b_subnet_id = "subnet-0695dde78791c05e4"
  pub_subnet_ids = ["subnet-0add941015c41c2fc"]
  allow_http_security_group_id = "sg-0515c1873f447ac02"
  allow_tls_security_group_id = "sg-02c57a7901bbd34a9"
  mpt_api_ami_id = "ami-09fee929929e834a8"
  api_instance_class = "t3.medium"
  api_asg_min_size = 0
  api_asg_desired_capacity = 1
  lambda_sns_arn = module.lambda_mattermost_api_error_notification.sns_topic_arn
  api_error_log_lambda_stream_arn = module.lambda_mattermost_api_error_stream.api_error_log_lambda_stream_arn
  # Enable privileged API deployer
  privileged_api_deployments_enabled = false
  # Enable API auto deploys
  enable_api_s3_deployments = true
  asg_instance_iam_role = module.auto_deploy_config.api_iam_profile_nm
  # Enable CloudWatch alerts for VEA APIs and RDS databases
  vea_cloudwatch_alarms_enabled = true
  vea_cloudwatch_alarms_sns_arn = module.lambda_vea_infrastructure_alerts.sns_topic_arn
  product_tag = "VEA-Board"
  type_tag = "QC"
  api_config_file = "nbed-mplex.json"
  api_config_data = <<EOT
{
  "host": "0.0.0.0",
  "port": 3030,
  "httpTimeoutMs": 900000,
  "redis": {
    "host": "master.nbed-mplex-redis.it4fg7.cac1.cache.amazonaws.com",
    "port": 6379,
    "tls": {}
  },
  "mysql_write": {
    "connection": {
      "host": "nbed-mplex-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "mysql_read": {
    "connection": {
      "host": "nbed-mplex-cluster.cluster-cednqnegvay6.ca-central-1.rds.amazonaws.com",
      "port": 3306,
      "password" : "********************************"
    }
  },
  "oct_api": null
}
EOT
}
