# Create DynamoDB table for holding front end git branch to S3 bucket mappings

resource "aws_dynamodb_table" "front_end_targets" {

  name            = "devops-vea-web-client-deployment-targets"
  billing_mode    = "PROVISIONED"
  read_capacity   = 5
  write_capacity  = 5
  hash_key        = "branchName"

  attribute {
    name = "branchName"
    type = "S"
  }

  tags = {
    terraform-managed = "true"
    Name = "devops-vea-web-client-deployment-targets"
    Product = "Internal"
  }
}

# Create Front End Target Mappings In the DynamoDB table

resource "aws_dynamodb_table_item" "front_end_target_mappings" {

  table_name  = aws_dynamodb_table.front_end_targets.name
  hash_key    = aws_dynamodb_table.front_end_targets.hash_key
  for_each    = {for mapping in local.front_end_mappings: mapping.branch_name => mapping}

  item = <<ITEM
{
  "attributes": {
    "M": {
      "bucketName": {
        "S": "${each.value.bucket_name}"
      },
      "environment": {
        "S": "${each.value.environment}"
      }
    }
  },
  "branchName": {
    "S": "${each.value.branch_name}"
  }
}
ITEM

}

# Define Front End Target Mappings Here
# Set privileged_deploy to false to allow GitLab to auto deploy to the target without prompting for credentials.
# This parameter is commented out for now as this feature is non functional yet and all targets need to be reviewed
# and confirmed which ones are privileged deploys

locals {

  front_end_mappings = [
    {
      branch_name       = "release/abed"
      bucket_name       = "abed.vretta.com"
      environment       = "release/abed"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/abed-qc"
      bucket_name       = "abed-qc.vretta.com"
      environment       = "release/abed-qc"
    },
    {
      branch_name       = "release/abed-qc-pasi"
      bucket_name       = "abed-qc-pasi.vretta.com"
      environment       = "release/abed-qc-pasi"
    },
    {
      branch_name       = "release/abed-ncr"
      bucket_name       = "abed-ncr.vretta.com"
      environment       = "release/abed-ncr"
    },
    {
      branch_name       = "release/abed-ncr2"
      bucket_name       = "abed-ncr2.vretta.com"
      environment       = "release/abed-ncr2"
    },
    {
      branch_name       = "release/abed-tts"
      bucket_name       = "abed-tts.vretta.com"
      environment       = "release/abed-tts"
    },
    {
      branch_name       = "release/abed-pat6"
      bucket_name       = "abed-pat6.vretta.com"
      environment       = "release/abed-pat6"
    },
    {
      branch_name       = "release/abed-mk"
      bucket_name       = "abed-mk.vretta.com"
      environment       = "release/abed-mk"
    },
    {
      branch_name       = "release/abed-uat"
      bucket_name       = "abed-uat.vretta.com"
      environment       = "release/abed-uat"
    },
    {
      branch_name       = "release/abed-prod-mirror"
      bucket_name       = "abed-prod-mirror.vretta.com"
      environment       = "release/abed-prod-mirror"
    },
    {
      branch_name       = "release/abed-prod-test"
      bucket_name       = "abed-prod-test.vretta.com"
      environment       = "release/abed-prod-test"
    },
    {
      branch_name       = "release/abed-ang10"
      bucket_name       = "abed-auth-preview.vretta.com"
      environment       = "release/abed-ang10"
    },
    {
      branch_name       = "release/abed-ang10-qc"
      bucket_name       = "abed-qc-auth-preview.vretta.com"
      environment       = "release/abed-ang10-qc"
    },
    {
      branch_name       = "release/abed-ang10-qc-temp"
      bucket_name       = "abed-qc-auth-preview-temp.vretta.com"
      environment       = "release/abed-ang10-qc-temp"
    },
    {
      branch_name       = "release/abed-print-preview"
      bucket_name       = "abed-print-preview.vretta.com"
      environment       = "release/abed-print-preview"
    },
    {
      branch_name       = "release/abed-uat-auth"
      bucket_name       = "abed-uat-auth.vretta.com"
      environment       = "release/abed-uat-auth"
    },
    {
      branch_name       = "release/abed-stress-test-20231211"
      bucket_name       = "abed-stress-test-20231211.vretta.com"
      environment       = "release/abed-stress-test-20231211"
    },
    {
      branch_name       = "release/abed-print"
      bucket_name       = "abed-print.vretta.com"
      environment       = "release/abed-print"
    },
    {
      branch_name       = "release/abed-staging"
      bucket_name       = "abed-staging.vretta.com"
      environment       = "release/abed-staging"
    },
    {
      branch_name       = "release/abed-fall24"
      bucket_name       = "abed-fall24.vretta.com"
      environment       = "release/abed-fall24"
    },
    {
     branch_name       = "release/abed-respondus"
     bucket_name       = "abed-respondus.vretta.com"
     environment       = "release/abed-respondus"
    },
      {
      branch_name       = "release/abed-virtualtools"
      bucket_name       = "abed-virtualtools.vretta.com"
      environment       = "release/abed-virtualtools"
    },
    {
      branch_name       = "release/abed-prod-dev"
      bucket_name       = "abed-prod-dev.vretta.com"
      environment       = "release/abed-prod-dev"
    },
    {
      branch_name       = "release/abed-staging-cycle17"
      bucket_name       = "abed-staging-cycle17.vretta.com"
      environment       = "release/abed-staging-cycle17"
    },
    {
      branch_name       = "release/abed-qc2"
      bucket_name       = "abed-qc2.vretta.com"
      environment       = "release/abed-qc2"
    },
    {
      branch_name       = "release/abed-staging-depload"
      bucket_name       = "abed-staging-depload.vretta.com"
      environment       = "release/abed-staging-depload"
    },
    {
      branch_name       = "release/abed-print-uat"
      bucket_name       = "abed-print-uat.vretta.com"
      environment       = "release/abed-print-uat"
    },
    {
      branch_name       = "release/abed-staging-cycle18"
      bucket_name       = "abed-staging-cycle18.vretta.com"
      environment       = "release/abed-staging-cycle18"
    },
    {
      branch_name       = "release/abed-post-it"
      bucket_name       = "abed-post-it.vretta.com"
      environment       = "release/abed-post-it"
    },
    {
      branch_name       = "release/abed-devkb"
      bucket_name       = "abed-devkb.vretta.com"
      environment       = "release/abed-devkb"
    },
    {
      branch_name       = "release/dsbn"
      bucket_name       = "dsbn.vretta.com"
      environment       = "release/dsbn"
    },
    {
      branch_name       = "release/multiplex-uat"
      bucket_name       = "multiplex-uat.vretta.com"
      environment       = "release/multiplex-uat"
    },
    {
      branch_name       = "release/multiplex-tts"
      bucket_name       = "multiplex-tts.vretta.com"
      environment       = "release/multiplex-tts"
    },
    {
      branch_name       = "release/multiplex-privschls"
      bucket_name       = "multiplex-privschls.vretta.com"
      environment       = "release/multiplex-privschls"
    },
    {
      branch_name       = "release/multiplex-calc"
      bucket_name       = "multiplex-calc.vretta.com"
      environment       = "release/multiplex-calc"
    },
    {
      branch_name       = "release/multiplex-mtic"
      bucket_name       = "multiplex-mtic.vretta.com"
      environment       = "release/multiplex-mtic"
    },
    {
      branch_name       = "release/multiplex-stress"
      bucket_name       = "multiplex-stress.vretta.com"
      environment       = "release/multiplex-stress"
    },
    {
      branch_name       = "release/multiplex"
      bucket_name       = "multiplex.vretta.com"
      environment       = "release/multiplex"
    },
    {
      branch_name       = "release/newmeridian"
      bucket_name       = "newmeridian.vretta.com"
      environment       = "release/newmeridian"
    },
    {
      branch_name       = "milestone/bc-markingleader-5"
      bucket_name       = "bced-feat3.vretta.com"
      environment       = "milestone/bc-markingleader-5"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/bced"
      bucket_name       = "bced.vretta.com"
      environment       = "release/bced"
      # privileged_deploy = false
    },
    {
      branch_name       = "release/bced-auth"
      bucket_name       = "bced-auth.vretta.com"
      environment       = "release/bced-auth"
      # privileged_deploy = false
    },
    {
      branch_name       = "release/bced-ads-template"
      bucket_name       = "bced-ads-template.vretta.com"
      environment       = "release/bced-ads-template"
    },
    {
      branch_name       = "release/bced-uat-3"
      bucket_name       = "bced-uat-3.vretta.com"
      environment       = "release/bced-uat-3"
    },
    {
      branch_name       = "release/bced-uat-4"
      bucket_name       = "bced-uat-4.vretta.com"
      environment       = "release/bced-uat-4"
    },
    {
      branch_name       = "release/bced-asmt-preview"
      bucket_name       = "bced-asmt-preview.vretta.com"
      environment       = "release/bced-asmt-preview"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/bced-qc-asmt-preview"
      bucket_name       = "bced-qc-asmt-preview.vretta.com"
      environment       = "release/bced-qc-asmt-preview"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/bced-feat1"
      bucket_name       = "bced-feat1.vretta.com"
      environment       = "release/bced-feat1"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/bced-feat2"
      bucket_name       = "bced-feat2.vretta.com"
      environment       = "release/bced-feat2"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/bced-feat3"
      bucket_name       = "bced-feat3.vretta.com"
      environment       = "release/bced-feat3"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/bced-feat4"
      bucket_name       = "bced-feat4.vretta.com"
      environment       = "release/bced-feat4"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/bced-feat5"
      bucket_name       = "bced-feat5.vretta.com"
      environment       = "release/bced-feat5"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/bced-feat6"
      bucket_name       = "bced-feat6.vretta.com"
      environment       = "release/bced-feat6"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/bced-feat10"
      bucket_name       = "bced-feat10.vretta.com"
      environment       = "release/bced-feat10"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/bced-feat11"
      bucket_name       = "bced-feat11.vretta.com"
      environment       = "release/bced-feat11"
    },
    {
      branch_name       = "release/bced-feat12"
      bucket_name       = "bced-feat12.vretta.com"
      environment       = "release/bced-feat12"
    },
    {
      branch_name       = "release/bced-qc"
      bucket_name       = "bced-qc.vretta.com"
      environment       = "release/bced-qc"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/caec-qc"
      bucket_name       = "caec-qc.vretta.com"
      environment       = "release/caec-qc"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/caec-qc-dev"
      bucket_name       = "caec-qc-dev.vretta.com"
      environment       = "release/caec-qc-dev"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/ci-dev"
      bucket_name       = "ops-testing.vretta.com"
      environment       = "release/ci-dev"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/depp-number-line"
      bucket_name       = "depp-number-line.vretta.com"
      environment       = "release/depp-number-line"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/eqao-g9-20210119"
      bucket_name       = "xqc2.mathproficiencytest.ca"
      environment       = "release/eqao-g9-20210119"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/eqao-g9-qc"
      bucket_name       = "qc1.mathproficiencytest.ca"
      environment       = "release/eqao-g9-qc"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/eqao-lit"
      bucket_name       = "eqao3.mathproficiencytest.ca"
      environment       = "release/eqao-lit"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/eqao-lit-qc"
      bucket_name       = "eqao-lit-qc1.vretta.com"
      environment       = "release/eqao-lit-qc"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/eqao-main"
      bucket_name       = "eqao.vretta.com"
      environment       = "release/eqao-main"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/eqao-main-qc"
      bucket_name       = "eqao-main-qc.vretta.com"
      environment       = "release/eqao-main-qc"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/editing-tools"
      bucket_name       = "eqao-editing-tools.vretta.com"
      environment       = "release/editing-tools"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/eqao-mpt"
      bucket_name       = "eqao-mpt.vretta.com" # www.mathproficiencytest.ca
      environment       = "release/eqao-mpt"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/eqao-mpt-qc"
      bucket_name       = "eqao-mpt-qc.vretta.com" # qc3.mathproficiencytest.ca
      environment       = "release/eqao-mpt-qc"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/eqao-mpt-prelive"
      bucket_name       = "eqao-mpt-prelive.vretta.com"
      environment       = "release/eqao-mpt-prelive"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/eqao-mpt-uat"
      bucket_name       = "eqao-mpt-uat.vretta.com"
      environment       = "release/eqao-mpt-uat"
    },
    {
      branch_name       = "release/multiplex-qc"
      bucket_name       = "multiplex-qc.vretta.com"
      environment       = "release/multiplex-qc"
    },
    {
      branch_name       = "release/multiplex-qc2"
      bucket_name       = "multiplex-qc2.vretta.com"
      environment       = "release/multiplex-qc2"
    },
    {
      branch_name       = "release/multiplex-qc3"
      bucket_name       = "multiplex-qc3.vretta.com"
      environment       = "release/multiplex-qc3"
    },
    {
      branch_name       = "release/eqao-mpt-stress"
      bucket_name       = "eqao-mpt-stress.vretta.com"
      environment       = "release/eqao-mpt-stress"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/eqao-mpt-pentest"
      bucket_name       = "eqao-mpt-pentest.vretta.com"
      environment       = "release/eqao-mpt-pentest"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/mpt-admin"
      bucket_name       = "mpt-admin.vretta.com"
      environment       = "release/mpt-admin"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/mpt-admin-qc"
      bucket_name       = "mpt-admin-qc.vretta.com"
      environment       = "release/mpt-admin-qc"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/qc3-uat"
      bucket_name       = "qc3-uat.mathproficiencytest.ca"
      environment       = "release/qc3-uat"
    },
    {
      branch_name       = "release/eqao-pen-test-20210115"
      bucket_name       = "xqc3.mathproficiencytest.ca"
      environment       = "release/eqao-pen-test-20210115"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/ongr9m-58"
      bucket_name       = "xqc3.mathproficiencytest.ca"
      environment       = "release/ongr9m-58"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/osslt-qa"
      bucket_name       = "qc5.mathproficiencytest.ca"
      environment       = "release/osslt-qa"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/osslt-uat"
      bucket_name       = "qc4.mathproficiencytest.ca"
      environment       = "release/osslt-uat"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/eassessment"
      bucket_name       = "vea.mathproficiencytest.ca"
      environment       = "release/eassessment"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/nbed"
      bucket_name       = "nbed.vretta.com"
      environment       = "release/nbed"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/nbed-qc"
      bucket_name       = "nbed-qc.vretta.com"
      environment       = "release/nbed-qc"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/nbed-sso"
      bucket_name       = "nbed-sso.vretta.com"
      environment       = "release/nbed-sso"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/nbed-elpa"
      bucket_name       = "nbed-elpa.vretta.com"
      environment       = "release/nbed-elpa"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/abed-to-nbed"
      bucket_name       = "abed-to-nbed.vretta.com"
      environment       = "release/abed-to-nbed"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/nbed-stress-test-20240115"
      bucket_name       = "nbed-stress-test-20240115.vretta.com"
      environment       = "release/nbed-stress-test-20240115"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/nbed-uat"
      bucket_name       = "nbed-uat.vretta.com"
      environment       = "release/nbed-uat"
      # privileged_deploy = true
    },
    {
      branch_name       = "release/vea-zqc1"
      bucket_name       = "vea-zqc1.vretta.com"
      environment       = "release/vea-zqc1"
      # privileged_deploy = false
    },
    {
      branch_name       = "release/vea-zqc2"
      bucket_name       = "zqc2.vretta.com"
      environment       = "release/vea-zqc2"
      # privileged_deploy = false
    },
    {
      branch_name       = "release/vea-sys-demo"
      bucket_name       = "vea-sys-demo.vretta.com"
      environment       = "release/vea-sys-demo"
      # privileged_deploy = false
    },
    {
      branch_name       = "release/deploy-training"
      bucket_name       = "deploy-training.vretta.com"
      environment       = "release/deploy-training"
      # privileged_deploy = false
    },
    {
      branch_name       = "release/xqc4"
      bucket_name       = "xqc4.vretta.com"
      environment       = "release/xqc4"
      # privileged_deploy = false
    },
    {
      branch_name       = "release/xqc5"
      bucket_name       = "xqc5.vretta.com"
      environment       = "release/xqc5"
      # privileged_deploy = false
    },
    {
      branch_name       = "release/qc7"
      bucket_name       = "qc7.vretta.com"
      environment       = "release/qc7"
      # privileged_deploy = false
    },
    {
     branch_name       = "release/bced-stress-test-202109"
     bucket_name       = "bced-stress-test-202109.vretta.com"
     environment       = "release/bced-stress-test-202109"
    },
    {
     branch_name       = "release/bced-mirror-qc"
     bucket_name       = "bced-mirror-qc.vretta.com"
     environment       = "release/bced-mirror-qc"
    },
    {
     branch_name       = "release/eqaosample"
     bucket_name       = "eqaosample.vretta.com"
     environment       = "release/eqaosample"
    },
    {
     branch_name       = "release/eqao-pj-sample-staging"
     bucket_name       = "eqao-pj-sample-staging.vretta.com"
     environment       = "release/eqao-pj-sample-staging"
    },
    {
     branch_name       = "release/bced-uat"
     bucket_name       = "bced-uat-2.vretta.com"
     environment       = "release/bced-uat"
    },
    {
     branch_name       = "release/bced-qc-grad"
     bucket_name       = "bced-qc-grad.vretta.com"
     environment       = "release/bced-qc-grad"
    },
        {
     branch_name       = "release/bced-qc-fsa"
     bucket_name       = "bced-qc-fsa.vretta.com"
     environment       = "release/bced-qc-fsa"
    },
    {
     branch_name       = "release/bced-qc-mark"
     bucket_name       = "bced-qc-mark.vretta.com"
     environment       = "release/bced-qc-mark"
    },
    {
     branch_name       = "release/bced-qc-mkg"
     bucket_name       = "bced-qc-mkg.vretta.com"
     environment       = "release/bced-qc-mkg"
    },
    {
     branch_name       = "release/bced-ltp"
     bucket_name       = "bced-ltp.vretta.com"
     environment       = "release/bced-ltp"
    },
    {
     branch_name       = "release/bced-auth-staging"
     bucket_name       = "bced-auth-staging.vretta.com"
     environment       = "release/bced-auth-staging"
    },
    {
     branch_name       = "release/eqao-to-bced"
     bucket_name       = "eqao-to-bced.vretta.com"
     environment       = "release/eqao-to-bced"
    },
    {
     branch_name       = "release/eqaovulnerabilitytest"
     bucket_name       = "eqaovulnerabilitytest.vretta.com"
     environment       = "release/eqaovulnerabilitytest"
    },
    {
     branch_name       = "release/bcedvulnerabilitycheck"
     bucket_name       = "bcedvulnerabilitycheck.vretta.com"
     environment       = "release/bcedvulnerabilitycheck"
    },
    {
     branch_name       = "release/bced-testing"
     bucket_name       = "bced-testing.vretta.com"
     environment       = "release/bced-testing"
    },
    {
     branch_name       = "release/eqao2-stress-06032022"
     bucket_name       = "eqao2-stress-06032022.vretta.com"
     environment       = "release/eqao2-stress-06032022"
    },
    {
     branch_name       = "release/eqao-main-mirror"
     bucket_name       = "eqao-main-mirror.vretta.com"
     environment       = "release/eqao-main-mirror"
    },
    {
     branch_name       = "release/eqao-pen"
     bucket_name       = "eqao-pentest.vretta.com"
     environment       = "release/eqao-pen"
    },
    {
     branch_name       = "release/eqao-sdc-load-20230321"
     bucket_name       = "eqao-sdc-load-20230321.vretta.com"
     environment       = "release/eqao-sdc-load-20230321"
    },
    {
     branch_name       = "release/bced-cred"
     bucket_name       = "bced-cred.vretta.com"
     environment       = "release/bced-cred"
    },
    {
     branch_name       = "release/qc8"
     bucket_name       = "qc8.vretta.com"
     environment       = "release/qc8"
    },
    {
     branch_name       = "release/qc-dev"
     bucket_name       = "eqao-qc-dev.vretta.com"
     environment       = "release/qc-dev"
    },
    {
     branch_name       = "release/eqao-dev"
     bucket_name       = "eqao-dev.vretta.com"
     environment       = "release/eqao-dev"
    },
    {
     branch_name       = "release/eqao-auth"
     bucket_name       = "eqao-auth.vretta.com"
     environment       = "release/eqao-auth"
    },
    {
     branch_name       = "release/eqao-scoring-20240409"
     bucket_name       = "eqao-scoring-20240409.vretta.com"
     environment       = "release/eqao-scoring-20240409"
    },
    {
     branch_name       = "release/eqao-dump-res-drun-240501"
     bucket_name       = "eqao-dump-res-drun-240501.vretta.com"
     environment       = "release/eqao-dump-res-drun-240501"
    },
    {
     branch_name       = "release/eqao-respondus-1"
     bucket_name       = "eqao-respondus-1.vretta.com"
     environment       = "release/eqao-respondus-1"
    },
    {
      branch_name       = "release/eqao-qc"
      bucket_name       = "eqao-qc.vretta.com"
      environment       = "release/eqao-qc"
    },
    {
     branch_name       = "release/caec-uat"
     bucket_name       = "caec-uat.vretta.com"
     environment       = "release/caec-uat"
    },
    {
     branch_name       = "release/bced-eao-temp"
     bucket_name       = "bced-eao-temp.vretta.com"
     environment       = "release/bced-eao-temp"
    },
    {
     branch_name       = "release/demo"
     bucket_name       = "demo.vretta.com"
     environment       = "release/demo"
    },
        {
     branch_name       = "release/qc10"
     bucket_name       = "qc10.vretta.com"
     environment       = "release/qc10"
    } ,
    {
     branch_name       = "release/eassessment-qc"
     bucket_name       = "eassessment-qc.vretta.com"
     environment       = "release/eassessment-qc"
    },
    {
     branch_name       = "release/eassessment-uat"
     bucket_name       = "eassessment-uat.vretta.com"
     environment       = "release/eassessment-uat"
    },
    {
     branch_name       = "release/eassessment-f25"
     bucket_name       = "eassessment-f25.vretta.com"
     environment       = "release/eassessment-f25"
    },
    {
     branch_name       = "release/bced-mirror-prod"
     bucket_name       = "bced-prod-mirror.vretta.com"
     environment       = "release/bced-mirror-prod"
    },
    {
     branch_name       = "release/eqao-pentest"
     bucket_name       = "eqao-pentest.vretta.com"
     environment       = "release/eqao-pentest"
    },
    {
     branch_name       = "release/nled"
     bucket_name       = "nled.vretta.com"
     environment       = "release/nled"
    },
    {
     branch_name       = "release/nled-qc"
     bucket_name       = "nled-qc.vretta.com"
     environment       = "release/nled-qc"
    },
    {
     branch_name       = "release/nled-stress-test"
     bucket_name       = "nled-stress-test.vretta.com"
     environment       = "release/nled-stress-test"
    },
    {
     branch_name       = "release/eqao-auth-qc2"
     bucket_name       = "eqao-auth-qc.vretta.com"
     environment       = "release/eqao-auth-qc2"
    },
    {
     branch_name       = "release/eqao-g9-samples-2021-09"
     bucket_name       = "eqao2.mathproficiencytest.ca"
     environment       = "release/eqao-g9-samples-2021-09"
    },

    {
     branch_name       = "release/eqao-g9-sample-staging"
     bucket_name       = "eqao-g9-sample-staging.vretta.com"
     environment       = "release/eqao-g9-sample-staging"
    },
     {
     branch_name       = "release/eqao-main-template"
     bucket_name       = "eqao-main-template.vretta.com"
     environment       = "release/eqao-main-template"
    },
    {
      branch_name       = "release/multiplex-nbed-to-mplex"
      bucket_name       = "nbed-mplex.vretta.com"
      environment       = "release/multiplex-nbed-to-mplex"
    },
    {
      branch_name       = "release/deployments-staging"
      bucket_name       = "deployments-staging.vretta.com"
      environment       = "release/deployments-staging"
    }

  ]

}
