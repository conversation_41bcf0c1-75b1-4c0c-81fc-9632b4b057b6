const USE_DB_CONNECTION = 'abed' // change this

////////
const fs = require('fs');
const USE_DB = 'mpt_dev';

const main = async (db_ref,  options) => {

  console.log('sync from', db_ref);

  return;

  options = options || {}

  const getConfig = (slug) => JSON.parse(fs.readFileSync(`./config/${slug}.json`));
  const knexFrom = require('knex')(getConfig(USE_DB_CONNECTION).mysql_write);

  const newTables = [];
  const newTableColumns = [];
  const newViews = [];
  let updatedViews = [];
  const getTablesAndViews = async (knex) => {
    const tablesAndViews = await knex.raw('SELECT * FROM information_schema.tables');
    const tables = [];
    const views = [];
    const tableRef = new Map();
    const viewRef = new Map();
    const tableTypes = new Map();
    tablesAndViews[0].forEach(tableInfo => {
      tableTypes.set(tableInfo.TABLE_TYPE, true)
      if (tableInfo.TABLE_SCHEMA === USE_DB){
        const tableName = tableInfo.TABLE_NAME
        switch (tableInfo.TABLE_TYPE){
          case 'BASE TABLE': tables.push(tableName); tableRef.set(tableName, true); break;
          case 'VIEW': views.push(tableName); viewRef.set(tableName, true); break;
        }
      }
    });
    return {tables, views, tableRef, viewRef}
  }
  const sourceTablesAndViews = await getTablesAndViews(knexFrom);

  console.log(':::::: TABLES (diffs)::::::');
  const str_contains = (str, s) => str && s && (str.indexOf(s) !== -1);
  const appendColumnDetails = async (knex, tableName, columns) => {
    // await knexFrom(tableName).columnInfo();
    const columnRes = await knex.raw('SHOW COLUMNS FROM ' + tableName);
    columnRes[0].forEach(column => {
      const columnName = column['Field'];
      const columnSettings = columns[columnName];
      if (str_contains(column['Extra'], 'auto_increment')){
        columnSettings.isAutoIncrement = true;
      }
      if (str_contains(column['Key'], 'PRI')){
        columnSettings.isPrimaryKey = true;
      }
      if (str_contains(column['Key'], 'UNI')){
        columnSettings.isUnique = true;
      }
      columnSettings.isNullable = ! str_contains(column['Null'], 'NO') ;
      columnSettings.default = column['Default'];
      columnSettings.type = column['Type'];
      columnSettings.name = columnName;
    });
  }

//   fs.writeFileSync(`./scripts/sync-db-logs/run-${+(new Date())}.json`, JSON.stringify({
//     newTables,
//     newTableColumns,
//   }, null, 2))

  process.exit(0);

}

main();
