#!/bin/bash

# Variables
api_build_file="project/api_build/${build_file}"
api_build_src="/${api_build_file}"
api_build_dest="/opt/mpt-api"
check_api=true
check_script_src="/project/scripts/gitlab-ci-scripts/api-deploy-automation/bash/check_api.sh"
check_script_dest="/opt/check_api.sh"

# Functions
check_enable_bracketed_paste() {
    echo "Checking enable-bracketed-paste setting before bind"
    bind_output=$(bind -V | grep enable-bracketed-paste)
    echo "${bind_output}"
    echo "Disabling bracketed paste mode"
    bind 'set enable-bracketed-paste off'
    echo "Checking enable-bracketed-paste setting after bind"
    bind_output=$(bind -V | grep enable-bracketed-paste)
    echo "${bind_output}"
}

find_and_delete_old_files() {
    echo "Finding old downloaded API build files to delete in API application directory"
    files_to_delete=$(find /opt/mpt-api -name '_00_RELEASE-COMMIT-*' -o -name 'api_build*.tgz')
    echo "${files_to_delete}"
    echo "Removing old downloaded API files"
    rm -f ${files_to_delete}
}

copy_api_build() {
    echo "Copying API build to the test API"
    cp "${api_build_src}" "${api_build_dest}"
    chown -R ubuntu "${api_build_dest}"
    chgrp -R ubuntu "${api_build_dest}"
    chmod -R 775 "${api_build_dest}"
}

decompress_api_build() {
    echo "Decompressing API build archive"
    tar -xzf "${api_build_dest}/${build_file}" -C "${api_build_dest}"
}

copy_api_check_script() {
    echo "Copying API check script to the test API system"
    cp "${check_script_src}" "${check_script_dest}"
    chown ubuntu:ubuntu "${check_script_dest}"
    chmod 774 "${check_script_dest}"
}

restart_api() {
    echo "Restarting API"
    su - ubuntu -c '. ~/.profile ; pm2 stop all ; pm2 flush ; pm2 restart /opt/ecosystem.config.js'
}

validate_api() {
    echo "Running API validation script"
    sleep 30
    chmod +x "${check_script_dest}"
    bash "${check_script_dest}"
}

# Main execution
check_enable_bracketed_paste
find_and_delete_old_files
copy_api_build
decompress_api_build

if [ "$check_api" = true ]; then
    copy_api_check_script
    restart_api
    validate_api
fi