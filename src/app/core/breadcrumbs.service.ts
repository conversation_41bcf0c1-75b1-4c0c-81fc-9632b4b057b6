import { Injectable } from '@angular/core';
import { LangService } from './lang.service';
import { AccountType } from '../constants/account-types';
import { WhitelabelService } from '../domain/whitelabel.service';

export interface IBreadcrumbRoute {
  route: string,
  caption: string,
}

@Injectable({
  providedIn: 'root'
})
export class BreadcrumbsService {

  constructor(
    private lang:LangService,
    private whiteLabel: WhitelabelService
  ) { }

  private cLang(){
    return this.lang.getCurrentLanguage()
  }

  public learnMoreSection = {
    APPLICANT: 'applicant',
    TESTADMIN: 'testadmin',
  }

  _HEAD(accountType:AccountType){ 
    switch(accountType){
      case AccountType.TEST_CTRL: 
      case AccountType.CERT_BODY: 
      case AccountType.TEST_ADMIN: return this.TESTADMIN_LANDING();
      default:
      case AccountType.TEST_TAKER: return this.APPLICANT_LANDING();
    }
  }
  _CURRENT(caption:string, route:string, params?) { return { caption, route, params } }
  HOME() {                return { caption: 'Home',                 route: `/${this.cLang()}/login-portal`} }
  
  APPLICANT_LANDING() {   return { caption: this.lang.tra('title_teach_app'),   route: this.whiteLabel.getSiteFlag('IS_SMCS') ? null : `/${this.cLang()}/applicant/landing`} }
  TESTADMIN_LANDING() {   return { caption: this.lang.tra('title_test_admins'), route: this.whiteLabel.getSiteFlag('IS_SMCS') ? null : `/${this.cLang()}/testadmin/landing`} }

  APPLICANT_LEARNMORE() { return { caption: this.lang.tra('title_learn_more'), route: `/${this.cLang()}/applicant/learn`} }
  TESTADMIN_LEARNMORE() { return { caption: this.lang.tra('title_learn_more'), route: `/${this.cLang()}/testadmin/learn`} }
  APPLICANT_DASHBOARD() {   return { caption: this.lang.tra('title_dashboard'),   route: `/${this.cLang()}/test-taker/dashboard`} }
  TESTADMIN_DASHBOARD()   { return { caption: this.lang.tra('title_dashboard'), route: `/${this.cLang()}/${AccountType.TEST_ADMIN}/dashboard`} }
  SUPPORT_DASHBOARD()   { return { caption: this.lang.tra('title_dashboard'), route: `/${this.cLang()}/${AccountType.SUPPORT}/dashboard`} }
  
  TESTAUTH_DASHBOARD()   { return { caption: this.lang.tra('auth_authoring_dashboard'), route: `/${this.cLang()}/${AccountType.TEST_AUTH}/dashboard`} }
  
  MRKG_CTRL_DASHBOARD()   { return { caption: 'Scoring Controller Dashboard', route: `/${this.cLang()}/${AccountType.MRKG_CTRL}/dashboard`} }
  MRKG_SUPR_DASHBOARD()   { return { caption: 'Scoring Supervisor Dashboard', route: `/${this.cLang()}/${AccountType.MRKG_SUPR}/dashboard`} }
  MRKG_MRKR_DASHBOARD()   { return { caption: 'Marker Dashboard', route: `/${this.cLang()}/${AccountType.MRKG_MRKR}/dashboard`} }
  MRKG_CAND_DASHBOARD()   { return { caption: 'Scoring Candidate Dashboard', route: `/${this.cLang()}/${AccountType.MRKG_CAND}/dashboard`} }
  MRKG_UPLD_DASHBOARD()   { return { caption: 'Response Uploader Dashboard', route: `/${this.cLang()}/${AccountType.MRKG_UPLD}/dashboard`} }
  
  SCOR_SCOR_DASHBOARD()   { return { caption: this.lang.tra('lbl_dashbord_scor'), route: `/${this.cLang()}/${AccountType.SCOR_SCOR}/dashboard`} }

  MRKG_COORD_DASHBOARD()   { return { caption: 'Scoring Coordinator Dashboard', route: `/${this.cLang()}/${AccountType.MRKG_COORD}/dashboard`} }
  MRKG_LEAD_DASHBOARD()   { return { caption: 'Scoring Leader Dashboard', route: `/${this.cLang()}/${AccountType.MRKG_LEAD}/dashboard`} }

  TESTADMIN_CREATE_TEST_SESSION(setupId:string, routeSuffix:string='test-window')   { return { caption: this.lang.tra('title_create_sessions'),          route: `/${this.cLang()}/${AccountType.TEST_ADMIN}/create-test-sessions/${setupId}/${routeSuffix}`} }
  TESTCTRL_DASHBOARD()   { return { caption: this.lang.tra('title_test_ctrl'), route: `/${this.cLang()}/${AccountType.TEST_CTRL}/dashboard`} }
  TESTCTRLD_DASHBOARD()   { return { caption: this.lang.tra('Data Specialists'), route: `/${this.cLang()}/${AccountType.TEST_CTRLD}/dashboard`} }
  SYS_ACCESS_DASHBOARD()   { return { caption: 'System Access Dashboard', route: `/${this.cLang()}/${AccountType.SYS_ACCESS}/dashboard`} }
  LEARNMORE_ARTICLE(caption:string, basePath:string, articlePath:string) {   return { caption, route: basePath+'/'+articlePath } }

}
