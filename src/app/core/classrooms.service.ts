import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { G9DemoDataService } from '../ui-schooladmin/g9-demo-data.service';
import { IUserInfo, AuthService } from '../api/auth.service';
import { RoutesService } from '../api/routes.service';
import { Router } from '@angular/router';
import { LangService } from './lang.service';
import { IStudentAccount } from '../ui-schooladmin/data/types';
import { ActivatedRoute } from '@angular/router';
import { LoginGuardService } from '../api/login-guard.service';

interface ICreateAssessmentSessionPayload  {
  capacity: number, 
  duration_m:number, 
  slug:string, 
  school_class_id:number, 
  isScheduled:boolean,
  scheduled_time:string|string[], 
  date_time_end: string, 
  is_fi:boolean, 
  sessionName?:string, 
  isCustomAssessment?:boolean, 
  custom_item_set_id?:number,
  custom_test_design_id?:number, // deprecated
}

@Injectable({
    providedIn: 'root'
  })
export class ClassroomsService {
  // updateSubSessionTime(subSessionId: any, refinedPayload: any, params: { query: { school_class_group_id: any; }; }) {
  //   throw new Error('Method not implemented.');
  // }

  constructor(
    private g9DemoData: G9DemoDataService,
    private auth: AuthService,
    private route: ActivatedRoute,
    private routes: RoutesService,
    private router: Router,
    private lang: LangService,
    public loginGuard: LoginGuardService,
  ) {
    this.classrooms = []; //this.demoData.teacherClassrooms.list;
    this.schl_group_id = this.route.snapshot.queryParams['school']
    this.auth.user().subscribe(v => {
      this._userInfo = v;
      if (this._userInfo) {
        this.loadSchoolData();
      }
    });
  }

  public currentAcademicYear:string;

  // classroom = this.dataSource.asObservable();
  classrooms = []
  private _userInfo:IUserInfo;
  private info: BehaviorSubject<any> = new BehaviorSubject(null);
  private dataSource = new BehaviorSubject('');
  private schl_group_id;
  private schoolData
  private activeSchoolInfo: { // this is likely a duplicate intention of the above, but the above is always null and I am not sure what will happen if I suddenly feed it with a value
    lang: string,
    foreign_id: string,
    name: string,
    school_type: string,
    sd_foreign_id: string,
    sd_name: string,
    type_slug: string,
    is_active: number,
    is_free_start: number,
    is_kiosk_approved: number,
    is_private: number,
    is_sandbox: number,
    is_sasn_login: number,
    // is_insecure
    // is_insecure_g9
    // is_insecure_g10
    // is_insecure_pj
    // is_softlock_enabled
    // is_softlock_enabled_g9
    // is_softlock_enabled_g10
    // is_softlock_enabled_pj

  }
  isIntervalInitialized:boolean;
  currentTestSessionReport?:{test_session_id:number, payload:any};
  isNamesHidden:boolean = true;
  // private classGroupIds = [];

  public sub(){
    return this.info;
  }

  isLoadingInited:boolean;
  private loadSchoolData(){
    if (this.isLoadingInited){
      return Promise.resolve();
    }
    else{
      this.isLoadingInited = true;
      return this.auth
        .apiFind(this.routes.EDUCATOR_SCHOOL, {query: {schl_group_id: this.schl_group_id}})
        .then (async schoolsData => {
          const activeSchool = schoolsData[0];
          this.activeSchoolInfo = activeSchool.school[0]
          // console.log('activeSchoolInfo', this.activeSchoolInfo)
          await this.g9DemoData.init(activeSchool, this.schl_group_id); // always just one school from this request
          this.classrooms = this.g9DemoData.teacherClassrooms.list;
          this.schoolData = this.g9DemoData.schoolData[0];
          this.info.next(schoolsData);
          this.initInterval();
        });
    }
  }

  setAcademicYear(academicYear:string, isDefaultOnly:boolean = false){
    if (!(isDefaultOnly && this.currentAcademicYear)){
      this.currentAcademicYear = academicYear;
    }
    return this.currentAcademicYear;
  }

  getSchoolByClassroomId(classroomId:number){
    const schools = this.info.value;
    if (schools && schools.length ){
      const schoolRecords = schools[0].school;
      if (schoolRecords && schoolRecords.length){
        return schoolRecords[0];
      }
    }
  }

  isFreeStart(classroomId:number){
    const school = this.getSchoolByClassroomId(classroomId);
    if (school){
      // to do: make it match the classroom
      return (school.is_free_start == 1);
    }
  }

  isSASNLogin(classroomId:number){
    const school = this.getSchoolByClassroomId(classroomId);
    if (school){
      return (school.is_sasn_login == 1);
    }
  }

  public navigateToClassroom(classroomId:string, queryParams = {}){
    this.router.navigate([this.lang.c(),
      'educator',
      'classrooms',
      classroomId
    ], {
      queryParams,
      queryParamsHandling: 'preserve'
    });
  }
  public navigateToAssessmentScheduler(classroomId:string){
    this.router.navigate([this.lang.c(),
      'educator',
      'assessment-scheduler',
      classroomId
    ]);
  }
  public navigateToAssessmentSession(classroomId:string, sessionId:string, queryParams = {}){
    this.router.navigate([this.lang.c(),
      'educator',
      'assessment',
      classroomId,
      sessionId
    ], {
      queryParams,
      queryParamsHandling: 'preserve'
    });
  }

  public closeAssessmentSession(school_class_id:string, sessionId:string,params:{query:{school_class_group_id:number,is_session_completed:boolean, is_score_entry?: boolean, sc_id?:number}}){
    return this.auth
      .apiRemove(this.routes.EDUCATOR_SESSION, sessionId,params)
      .then(() => {
        if(params.query.is_session_completed){
          const classroom = this.getClassroomById(school_class_id);
          this.removeAssessment(classroom.openAssessments,sessionId);
        }
        else{
          const classroom = this.getClassroomById(school_class_id);
          this.removeAssessment(classroom.scheduledAssessments,sessionId);
        }
      })
  }
  removeAssessment(assessments,sessionId){
    for (let i=0; i<assessments.length; i++){
      const session = assessments[i];
      if (session.test_session_id == sessionId){
        assessments.splice(i ,1);
      }
    }
  }

  public updateStudentInfo(student, schoolClassGroupId){
    return this.auth.apiUpdate(this.routes.EDUCATOR_STUDENTS, student.id, {
      student,
      schoolClassGroupId
    },
    {
      query: {school_class_group_id: schoolClassGroupId}
    })
  }

  public createStudent(schoolClassId, student, schoolClassGroupId, isSasnLogin = false){
    return this.auth.apiCreate(this.routes.EDUCATOR_STUDENTS, {
      schoolClassId,
      student,
      schoolClassGroupId,
      isSasnLogin : isSasnLogin? 1:0 
    },
    {
      query: {
        school_class_group_id:schoolClassGroupId
      }
    })
    // this.auth.apiCreate(this.routes.LOG, {
    //   slug: 'TEACHER_CREATE_STUDENT_PENDING',
    //   data: {
    //     classroom_id,
    //     student,
    //   }
    // });
  }

  public moveStudent(student: IStudentAccount, school_class_group_id: number, previous_school_class_group_id?: number, crossSchool: boolean = false){
    return this.auth.apiPatch(this.routes.EDUCATOR_STUDENTS, student.id, {},
    {
      query: { 
        school_class_group_id, 
        keepOldTWRecord: true,
        ASN: student?.StudentIdentificationNumber,
        crossSchool: crossSchool ? 1: 0,
      }
    }).then(res => {
      // Find the student's new class room (Moved to)
      const targetClass = this.classrooms.find(classroom => classroom.group_id === school_class_group_id);
      let currentStudent: IStudentAccount = {...student};
      if (targetClass !== undefined) {
        // Find the student's previous class room (Moved From)
        const studentPreviousClass = this.classrooms.find(clr => {
          if(+clr.group_id === +previous_school_class_group_id){
            const existingStudent = clr.currentStudents.list.find(st => {
              if (st.id === student.id) {
                return student;
              }
            });
            if (existingStudent) {
              currentStudent = existingStudent;
              return clr;
            }
          }
        }
        );

        // ========= Handle internal moves =========

        // Remove the student from the previous classroom
        if (studentPreviousClass) {
          // Find the index
          const index = studentPreviousClass.currentStudents.list.indexOf(currentStudent);
          if (index > -1) {
            // Remove from the previous classroom
            studentPreviousClass.currentStudents.list.splice(index, 1);
          }

          if (studentPreviousClass.group_type === targetClass.group_type) {
            targetClass.currentStudents.list.push(currentStudent);
          }
        } else { // We moved the student from other teacher's class to target class
          // This student is new for our scope we will creat tmp object and add it to the target class
          student.displayName = student.first_name + " " + student.last_name;
          student.eqao_g9_class_code = targetClass.id;
          student.eqao_g9_class_code_label = targetClass.name;
          // const movedStudent = {
          //   id: student.id,
          //   first_name: student.first_name,
          //   last_name: student.last_name,
          //   eqao_gender: student.meta.Gender,
          //   displayName: student.first_name + " " + student.last_name,
          //   eqao_g9_class_code: targetClass.id,
          //   eqao_g9_class_code_label: targetClass.name,
          //   eqao_student_gov_id: student.eqao_student_gov_id,
          //   uid: student.id,
          // };
          targetClass.currentStudents.list.push(student);
          targetClass.currentStudents.map[student.id] = student;
        }
        // Update the UI
        this.sub().next(this.sub().value);
      }      
    }).catch(err => {
      // for secondary ASN error for ABED
      if (err.code === 403) {
        // todo: ensure translations
        this.loginGuard.quickPopup(err.message);
      }
    })
  }

  public async teacherReportIssue(school_class_id, test_session_id, msg, categorySelection, student?, extra?:any){
    const {phone_number, contact_email} = (extra || {})
    await this.auth.apiCreate('public/educator/session-reported-issue', {
      school_class_id,
      test_session_id,
      msg,
      student,
      categorySelection,
      phone_number, 
      contact_email,
    },
    { 
      query: { 
        schl_class_group_id: this.getClassroomById(school_class_id).group_id
      }
    })
    .catch(e => {
      alert('Failed to save reported issue')
    })

    this.loginGuard.quickPopup(this.lang.tra('lbl_issues_reported') + ':\n\n' + msg)
  }

  public createAssessmentSession(config:ICreateAssessmentSessionPayload, params:{query:{school_class_group_id:number}}){
    return this.auth
      .apiCreate(this.routes.EDUCATOR_SESSION, config, params )
      .then( (session:any) => {
        const classroom = this.getClassroomById(config.school_class_id);
        if (classroom && !config.isScheduled){
          // classroom.openAssessments.unshift(this.g9DemoData.processSessionEntry(session))
          classroom.openAssessments.push(this.g9DemoData.processSessionEntry(session))
        }
        return session;
      })
      .catch(error =>{
        if (error.message === 'ONGOING_SAMPLE_ASSESSMENT') {
          if (config.slug === 'PRIMARY_SAMPLE') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_sample_primary'))  
          } else if (config.slug === 'JUNIOR_SAMPLE') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_sample_junior'))  
          } else if (config.slug === 'G9_SAMPLE') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_practice_g9'))  
          } else if (config.slug === 'OSSLT_SAMPLE') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_practice_osslt'));
          }else{
            this.loginGuard.disabledPopup('abed_one_asmt_msg');
          }
        }
        else if (error.message === 'ONGOING_LIVE_ASSESSMENT') {
          if (config.slug === 'PRIMARY_OPERATIONAL') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_primary'))  
          } else if (config.slug === 'JUNIOR_OPERATIONAL') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_junior'))  
          } else if (config.slug === 'G9_OPERATIONAL') {
            this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_operational_assessment'))  
          } else if (config.slug === 'OSSLT_OPERATIONAL') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_osslt_assessment'));   
          }else{
            this.loginGuard.disabledPopup('abed_one_asmt_msg');
          }
        }
        else if (error.message === "SCHEDULED_TIME_OUT_OF_TW_RANGE"){
          this.loginGuard.quickPopup('Scheduled date not allowed.')
        }
        else{
          this.loginGuard.quickPopup('Error Creating Session: ' + error.message)
          throw error
        }
      })
  }
  getActiveAssessments(params:{query:{school_class_group_id,school_class_id,slug}}){
    return   this.auth
    .apiFind(this.routes.EDUCATOR_SESSION, params)
  }
  getClassroomById(classroomId){
    let match;
    this.classrooms.forEach(classroom => {
      if (classroom.id == classroomId){
        match = classroom;
      }
    })
    return match;
  }
  constructClassGroupIdQuery(classroomId){
    const classroom = this.getClassroomById(classroomId);
    if (classroom){
      return {
        query: {
          school_class_group_id: classroom.group_id
        }
      }
    }
    return null;
  }
  private initInterval(){
    if (!this.isIntervalInitialized){
      this.isIntervalInitialized = true;
      setInterval(this.refreshStudentData, 5*1000);
      // this.refreshStudentData();
    }
  }
  refreshStudentData = () => {
    if (this.classrooms){
      // this.classGroupIds = this.classrooms.map(classroom => classroom.group_id);
        const group_ids = this.classrooms.map(classroom => classroom.group_id)
        // return this.auth
        //   .apiCreate(this.routes.EDUCATOR_STUDENTS, {type: 'TEACHER_INTERVAL', group_ids})
        //   .then (res => {

        //     // console.log(studentSummaries, this.classrooms)
        //     this.classrooms.forEach(classroom => {
        //       classroom.currentStudents.list.forEach(student => {
        //         if (res.studentSummaries[student.id]){
        //           student.status = res.studentSummaries[student.id];
        //         }
        //       })
        //     });

        //     // hack
        //     const classroom = this.classrooms[0];
        //     if (res.activeAssessment && classroom.openAssessments.length === 0){
        //       classroom.openAssessments = [
        //         {
        //           name: 'Sample Assessment ',
        //           timeDirectStart: 'Nov 2',
        //           session_id: res.activeAssessment
        //         },
        //       ]
        //     }
        //   })
      }
  }


  invigOpenCloseSubSessionForStudents(testSessionId:number, subSessionId:number, twtdarOrder:number, studentUids:number[],classId:number, isClosing:boolean, params:{query:{school_class_group_id:number}}, isPJ=false, isSubmitting?: boolean){
    return this.auth.apiPatch(this.routes.EDUCATOR_SESSION_SUB, testSessionId, {
      subSessionId,
      twtdarOrder,
      isClosing,
      studentUids,
      classId,
      isPJ,
      isSubmitting
    },params)
  }

  invigOpenCloseTestAttemptForStudents(testAttemptId:number, isPausing:boolean, params:{query:{school_class_group_id:number}}){
    return this.auth.apiPatch(this.routes.EDUCATOR_SOFT_LOCK, testAttemptId, {
      isPausing,
    },params)
  }
  
  updateSubSessionTime(subSessionId:number,payload,params:{query:{school_class_group_id:number}}){
    return this.auth.apiUpdate(this.routes.EDUCATOR_SESSION_SUB, subSessionId, payload,params)
  }

  setActiveClassroom(classroom){
    this.dataSource.next(classroom);
  }

  saveClassroom(classroom){
    this.classrooms.push(classroom)
  }

  getSchlGpId(){
    return this.schl_group_id;
  }

  reloadSchool(isLimittedToNecessary:boolean=false){
    const schl_group_id = this.route.snapshot.queryParams['school']
    let isReloadReq = true;
    if (isLimittedToNecessary){
      if (schl_group_id){
        const isLocalMatch = (schl_group_id == this.schl_group_id);
        const isGlobalMatch = (schl_group_id == this.g9DemoData.schl_group_id);
        if (isLocalMatch && isGlobalMatch){
          isReloadReq = false
        }
      }
      else {
        isReloadReq = false;
      }
    }
    if (isReloadReq){
      this.info.next(null);
      this.schl_group_id = schl_group_id
      this.isLoadingInited = false;
      this.loadSchoolData();
    }
  }

  getSchoolName(){
    if (this.activeSchoolInfo){
      return `${this.activeSchoolInfo.name} (${this.activeSchoolInfo.foreign_id})`
    }
    return '...'
  }
}
