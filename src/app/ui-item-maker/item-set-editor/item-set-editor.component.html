
  <!-- <test-runner
    *ngIf="previewCtrl.sampleTestForm"
    [testFormType]="frameworkCtrl.testFormConstructionMethod.value"
    [testFormId]="previewCtrl.currentTestFormId"
    [currentTestDesign]="previewCtrl.sampleTestForm.currentTestDesign"
    [frameWorkTags]="frameworkCtrl.asmtFmrk.tags"
    [questionSrcDb]="previewCtrl.sampleTestForm.questionSrcDb"
    [questionStates]="previewCtrl.sampleTestForm.questionStates"
    [testLang]="previewCtrl.sampleTestForm.testLang"
    [testTakerName]="previewCtrl.getTestTakerName()"
    [sectionIndexInit]="0"
    [questionIndexInit]="0"
    [regularTimeRemaining]="999"
    [testSessionId]="-1"
    [isPrintMode]="previewCtrl.isTestFormPrintView"
    [isTimeEnabled]="!frameworkCtrl.asmtFmrk.isTimerDisabled"
    [documentItems]="frameworkCtrl.asmtFmrk.referenceDocumentPages"
    [helpPageItem]="frameworkCtrl.asmtFmrk.helpPageId"
    [rubricDownloadLink]="frameworkCtrl.asmtFmrk.rubricDownloadLink"
    [asmtFmrk]="frameworkCtrl.asmtFmrk"
    [isShowQuestionLabel]="true"
    [saveQuestion]="previewCtrl.saveQuestionResponse"
    [submitTest]="previewCtrl.showResults"
    [isExitEnabled]="true"
    [isPreview]="true"
    [isShowingResults]="previewCtrl.isShowingResults"
    [exitResults]="previewCtrl.exitSampleTestForm"
    (exit)="previewCtrl.exitSampleTestForm()"
  ></test-runner> -->

<header 
  *ngIf="itemBankCtrl.isPsychometricViewEnabled && !previewCtrl.sampleTestForm" 
  [breadcrumbPath]="breadcrumb" 
  [noLangSwitcher]="true" data-why-noLangSwitcher="because there is an embedded language switcher that may have specific hooks"
  [isForceNewTab]="true"
></header>

<div (click)="dropdown.hideAllDropdowns()" class="item-set-editor">

  <!--  *ngif="isInited" -->

  <div [ngStyle]="{display: itemBankCtrl.editModeItemId ? 'none' : 'block'}">
    <test-runner
            *ngIf="this.previewCtrl.isShowingTestRunner()"
            [testFormType]="frameworkCtrl.testFormConstructionMethod.value"
            [testFormId]="previewCtrl.currentTestFormId"
            [currentTestDesign]="previewCtrl.sampleTestForm.currentTestDesign"
            [frameWorkTags]="frameworkCtrl.asmtFmrk.tags"
            [questionSrcDb]="previewCtrl.sampleTestForm.questionSrcDb"
            [questionStates]="previewCtrl.sampleTestForm.questionStates"
            [testLang]="previewCtrl.sampleTestForm.testLang"
            [testTakerName]="previewCtrl.getTestTakerName()"
            [sectionIndexInit]="0"
            [questionIndexInit]="0"
            [regularTimeRemaining]="999"
            [testSessionId]="-1"
            [isPreview]="true"
            [isPrintMode]="previewCtrl.isTestFormPrintView"
            [isTimeEnabled]="!frameworkCtrl.asmtFmrk.isTimerDisabled"
            [documentItems]="frameworkCtrl.asmtFmrk.referenceDocumentPages"
            [helpPageItem]="frameworkCtrl.asmtFmrk.helpPageId"
            [rubricDownloadLink]="frameworkCtrl.asmtFmrk.rubricDownloadLink"
            [asmtFmrk]="frameworkCtrl.asmtFmrk"
            [isShowQuestionLabel]="true"
            [saveQuestion]="previewCtrl.saveQuestionResponse"
            [submitTest]="previewCtrl.submitTest"
            [postSubmit]="previewCtrl.showResults"
            [isExitEnabled]="true"
            [isShowingResults]="previewCtrl.isShowingResults"
            [exitResults]="previewCtrl.exitSampleTestForm"
            [ignoreDevTools]="true"
            (questionTitles)="previewCtrl.questionTitleMap = $event"
            (exit)="previewCtrl.exitSampleTestForm()"
            (onEditItem)="editItem($event)"
            [isFromItemSetEditor]="true"
    ></test-runner>
    <external-results *ngIf="previewCtrl.sampleTestForm && previewCtrl.isShowingExternalResults()" 
      [asmtFmrk]="frameworkCtrl.asmtFmrk" 
      [testForm]="previewCtrl.sampleTestForm" 
      [questionTitleMap]="previewCtrl.questionTitleMap" 
      [questionScores]="previewCtrl.questionScores"
      [exitButtonCaption]="'Exit'"
      (exitClick)="previewCtrl.exitSampleTestForm()"
      >
    </external-results>
  </div>

  <div *ngIf="(getUser() | async) && (!previewCtrl.sampleTestForm || (previewCtrl.sampleTestForm && itemBankCtrl.editModeItemId))">
    <ng-container [ngSwitch]="!!printViewCtrl.isPrintModeActive()">
      <widget-simple-print-view *ngSwitchCase="true"
      [itemBankCtrl]="itemBankCtrl"
      [itemEditCtrl]="itemEditCtrl"
      [printViewCtrl]="printViewCtrl"
      [frameworkCtrl]="frameworkCtrl"
      ></widget-simple-print-view>
      <ng-container *ngSwitchCase="false">
        <widget-authoring-main *ngIf="!itemBankCtrl.isPsychometricViewEnabled" 
          [assetLibraryCtrl]="assetLibraryCtrl"
          [frameworkCtrl]="frameworkCtrl"
          [auditCtrl]="auditCtrl"
          [itemBankCtrl]="itemBankCtrl"
          [itemEditCtrl]="itemEditCtrl"
          [itemFilterCtrl]="itemFilterCtrl"
          [memberAssignmentCtrl]="memberAssignmentCtrl"
          [panelCtrl]="panelCtrl"
          [previewCtrl]="previewCtrl"
          [printViewCtrl]="printViewCtrl"
          [publishingCtrl]="publishingCtrl"
          [quadrantCtrl]="quadrantCtrl"
          [saveLoadCtrl]="saveLoadCtrl"
          [testFormGen]="testFormGen"
          [testletCtrl]="testletCtrl"
          [authRestrictions]="authRestrictions"
          [authoringView]="authoringView"
        ></widget-authoring-main>
        <ng-container *ngIf="itemBankCtrl.isPsychometricViewEnabled" >
          <widget-framework-main 
            *ngIf="!itemBankCtrl.isFormStatisticsViewEnabled"
            [previewCtrl]="previewCtrl"
            [frameworkCtrl]="frameworkCtrl"
            [assetLibraryCtrl]="assetLibraryCtrl"
            [auditCtrl]="auditCtrl"
            [itemBankCtrl]="itemBankCtrl"
            [itemEditCtrl]="itemEditCtrl"
            [itemFilterCtrl]="itemFilterCtrl"
            [memberAssignmentCtrl]="memberAssignmentCtrl"
            [panelCtrl]="panelCtrl"
            [printViewCtrl]="printViewCtrl"
            [publishingCtrl]="publishingCtrl"
            [quadrantCtrl]="quadrantCtrl"
            [saveLoadCtrl]="saveLoadCtrl"
            [testFormGen]="testFormGen"
            [testletCtrl]="testletCtrl"
            [authRestrictions]="authRestrictions"
          ></widget-framework-main>
          <div *ngIf="itemBankCtrl.isFormStatisticsViewEnabled">
            <widget-form-stats
              [itemBankCtrl]="itemBankCtrl"
              [saveLoadCtrl]="saveLoadCtrl"
              [frameworkCtrl]="frameworkCtrl"
            ></widget-form-stats>
          </div>
        </ng-container>
      </ng-container>
    </ng-container>
  </div>
  <widget-item-params-import 
    *ngIf="frameworkCtrl.isImportOpen" 
    [frameworkCtrl]="frameworkCtrl"
  ></widget-item-params-import>
  <widget-element-import 
    *ngIf="itemEditCtrl.isElementImportExportOpen" 
    [itemBankCtrl]="itemBankCtrl"
    [itemEditCtrl]="itemEditCtrl"
    [saveLoadCtrl]="saveLoadCtrl"
    [authRestrictions]="authRestrictions"
  ></widget-element-import>
  <widget-element-restore 
    *ngIf="itemEditCtrl.isElementRestoreOpen" 
    [itemBankCtrl]="itemBankCtrl"
    [itemEditCtrl]="itemEditCtrl"
    [saveLoadCtrl]="saveLoadCtrl"
  ></widget-element-restore>
  <widget-assign-user 
    *ngIf="memberAssignmentCtrl.openedAssignWindowNote"
    [memberAssignmentCtrl]="memberAssignmentCtrl"
  ></widget-assign-user>
  <template-library [hideArchived]="true" [authRestrictions]="authRestrictions"></template-library>
  <div class="custom-modal" *ngIf="assetLibraryCtrl.isAssetLibraryOpen" style="z-index: 100;">
    <div class="modal-contents is-max-size">
      <!-- <div class="simple-content-bounds">
        <asset-library [initEditing]="assetLibraryCtrl.initAssetEditing" [focusedField]="assetLibraryCtrl.focusedAssetEditField" [activeElement]="assetLibraryCtrl.currentAsset" (close)="assetLibraryCtrl.closeAssetLibrary()"></asset-library>
        <button (click)="assetLibraryCtrl.closeAssetLibrary()" style="position: absolute; bottom: 1em; left: 1em;" class="button is-danger">Close</button> -->
      <div>
        <asset-library 
            [initEditing]="assetLibraryCtrl.initAssetEditing" 
            [focusedField]="assetLibraryCtrl.focusedAssetEditField" 
            [activeElement]="assetLibraryCtrl.currentAsset" 
            [itemElement]="assetLibraryCtrl.itemElement"
            [assetId]="assetLibraryCtrl.assetId"
            [itemBankCtrl]="itemBankCtrl"
            (close)="assetLibraryCtrl.closeAssetLibrary()">
        </asset-library>
        <button (click)="assetLibraryCtrl.closeAssetLibrary()" style="position: absolute; bottom: 1em; left: 1em;" class="button is-danger"><tra slug="ie_close_modal"></tra></button>
      </div>
    </div>
  </div>

</div>
