import { ITestDef } from '../../../ui-testrunner/sample-questions/data/sections';

export enum QuestionView {
    QUESTION_BANK = 'QUESTION_BANK',
    MSCAT_MODULES = 'MSCAT_MODULES',
    MSCAT_PANELS = 'MSCAT_PANELS',
    QUADRANTS = 'QUADRANTS',
    ASSEMBLED_FORMS = 'ASSEMBLED_FORMS',
    TESTLETS = 'TESTLETS',
    FORM_CONSTRUCTION = 'FORM_CONSTRUCTION',
    TEMPLATE_REVIEW = 'TEMPLATE_REVIEW',
    AUDITS = 'AUDITS',
    SAMPLE_FORMS = 'SAMPLE_FORMS',
    STYLE_PROFILE = 'STYLE_PROFILE',
    SCORE_ENTRY = 'SCORE_ENTRY'
  }
  
  export interface ISampleTestForm {
    currentTestDesign: ITestDef,
    questionSrcDb: Map<number, any>,
    questionStates: {[key: string]: any},
    testLang: string,
  };

  export enum EditViewMode {
    BEFORE = 'BEFORE',
    AFTER = 'AFTER'
  }
  
export enum AuthoringView {
  ITEM_SET = 'ITEM_SET',
  CLASSROOM_ASSESSMENT = 'CLASSROOM_ASSESSMENT',
}