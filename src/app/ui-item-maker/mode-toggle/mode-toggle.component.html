<div class="mode-toggle"  *ngIf="itemBankCtrl.personalEditorSettings['isPsychometricsEnabled'] && !saveLoadCtrl.isInspecting">

  <!-- btn-menu-auth-item-focus-view -->
  <a id="btn-menu-auth-item-focus-view" 
    *ngIf="showFocusView()" 
    class="mode-toggle-option" 
    [class.is-disabled-black]="isSingularReadOnly()" 
    (click)="itemBankCtrl.toggleFocusView(true)"
  >
    <tra slug="auth_focus_view"></tra>
    <i style="margin-left:0.2em" class="fas fa-eye"></i>
  </a>

  <!-- btn-menu-auth-item-review-view -->
  <a id="btn-menu-auth-item-edit-view" 
    *ngIf="isEditingReviewTabVisible()" 
    class="mode-toggle-option" 
    [class.is-active]="isMode(ModeToggleOption.EditView)" 
    [class.is-disabled-black]="isMode(ModeToggleOption.EditView) || isSingularReadOnly()" 
    (click)="itemBankCtrl.switchToEditingView()"
  >
    <tra [slug]="getEditingSlug()"></tra>
    <i style="margin-left:0.2em" class="fas fa-pen-alt"></i>
  </a>

  <!-- btn-menu-auth-item-edit-view -->
  <a id="btn-menu-auth-item-review-view" 
    class="mode-toggle-option" 
    [class.is-active]="isMode(ModeToggleOption.ReviewView)" 
    [class.is-disabled-black]="isMode(ModeToggleOption.ReviewView) || isSingularReadOnly()" 
    (click)="itemBankCtrl.switchToEditorView()"
  >
    <tra slug="auth_authoring"></tra>
    <i style="margin-left:0.2em" class="fas fa-pencil-alt"></i>
  </a>

  <!-- btn-menu-auth-item-form-view -->
  <a id="btn-menu-auth-form-view" 
    *ngIf="showFormView()" 
    class="mode-toggle-option" 
    [class.is-active]="isMode(ModeToggleOption.FormView)" 
    [class.is-disabled-black]="isMode(ModeToggleOption.FormView)" 
    (click)="itemBankCtrl.openFramework()"
  >
    <tra slug="auth_framework"></tra> <i style="margin-left:0.2em" class="fas fa-bars"></i>
  </a>

  <!-- btn-menu-auth-item-form-stats-view -->
  <a  id="btn-menu-auth-form-stats-view" 
    *ngIf="showFormStatsView()" 
    class="mode-toggle-option" 
    [class.is-active]="isMode(ModeToggleOption.FormStatsView)" 
    [class.is-disabled-black]="isMode(ModeToggleOption.FormStatsView)" 
    (click)="itemBankCtrl.switchToFormStatisticsView()"
  >
    <tra slug="Form Statistics"></tra> 
    <i style="margin-left:0.2em" class="fas fa-chart-bar"></i>
  </a>

</div>
