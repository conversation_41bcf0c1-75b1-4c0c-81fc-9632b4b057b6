import { Component, Input, OnInit } from '@angular/core';
import { AuthScopeSetting, AuthScopeSettingsService } from '../auth-scope-settings.service';
import { ItemComponentEditService } from '../item-component-edit.service';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import {UserRoles} from "./../../api/models/roles"
import { EditingDisabledService } from '../editing-disabled.service';
import { AuthoringView } from '../item-set-editor/models/types';

enum ModeToggleOption {
  FocusView = 'focus-view',
  ReviewView = 'review-view', 
  EditView = 'edit-view',
  FormView = 'form-view',
  FormStatsView = 'form-stats-view'
}

@Component({
  selector: 'mode-toggle',
  templateUrl: './mode-toggle.component.html',
  styleUrls: ['./mode-toggle.component.scss']
})
export class ModeToggleComponent implements OnInit {

  constructor(public itemComponentEdit: ItemComponentEditService,
    private authScopeSettings: AuthScopeSettingsService,
    private editingDisabled: EditingDisabledService) { }
  @Input() itemBankCtrl: ItemBankCtrl
  @Input() saveLoadCtrl: ItemBankSaveLoadCtrl
  @Input() frameworkCtrl: ItemSetFrameworkCtrl
  @Input() authoringView: AuthoringView = AuthoringView.ITEM_SET

  ModeToggleOption = ModeToggleOption
  AuthoringView = AuthoringView

  ngOnInit(): void {
  }

  getEditingSlug() {
    return this.itemComponentEdit.usingEditingMode() ? 'auth_editing' : 'auth_review';
  }

  isEditingReviewTabVisible() {
    const isCurrUserEditor = this.authScopeSettings.getSetting(AuthScopeSetting.IS_EDITOR)
    const isCurrAuthOrSuper = [UserRoles.TEST_ITEM_AUTHOR, UserRoles.TEST_ITEM_AUTHOR_SUPER].includes(this.itemBankCtrl.getCurrActualRole());
    const isEditStage = this.itemBankCtrl.isEditStage(this.itemBankCtrl.currentQuestion?.id)
    const isReviewStage = this.itemBankCtrl.isReviewStage(this.itemBankCtrl.currentQuestion?.id)
    const isCurrStageWorkCompleted = this.itemBankCtrl.isCurrStageWorkCompleted()

    const isTabVisible = (
      this.itemBankCtrl.getCurrQTrackChanges() && 
      (this.itemComponentEdit.usingEditingMode() || this.itemComponentEdit.hasSuggestions()) && 
      !this.authScopeSettings.getSetting(AuthScopeSetting.DISABLE_REVIEW_EDIT_TAB) &&
      (
        (isCurrUserEditor && isEditStage) ||
        (isCurrAuthOrSuper && isReviewStage)
      ) &&
      !isCurrStageWorkCompleted
    )
    
    return isTabVisible;
  }

  determineActiveModeToggleOption() {
    if (this.itemBankCtrl.isFocusView) {
      return ModeToggleOption.FocusView;
    }
    else if (this.itemBankCtrl.isPsychometricViewEnabled) {
      if (this.itemBankCtrl.isFormStatisticsViewEnabled) {
        return ModeToggleOption.FormStatsView;
      }
      else {
        return ModeToggleOption.FormView;
      }
    }
    else {
      if (this.itemBankCtrl.isEditing) {
        return ModeToggleOption.EditView;
      }
      else {
        return ModeToggleOption.ReviewView;
      }
    }
  }

  isMode(mode: ModeToggleOption) {
    return this.determineActiveModeToggleOption() === mode;
  }
  
  showFormStatsView() {
    return (this.itemBankCtrl.testWindowAllocRuleId && this.itemBankCtrl.isPsychometricViewEnabled) || this.isMode(ModeToggleOption.FormStatsView);
  }

  showFormView() {
    // Hide form view button when in classroom assessment mode
    if (this.authoringView === AuthoringView.CLASSROOM_ASSESSMENT) {
      return false;
    }
    return this.itemBankCtrl.getItems() && this.itemBankCtrl.getItems().length;
  }

  showFocusView() {
    return true && 
           this.itemBankCtrl.getItems() && 
           this.itemBankCtrl.getItems().length && 
           this.itemBankCtrl.currentQuestion && 
           !this.itemBankCtrl.currentQuestionIsSequence() && 
           !this.itemBankCtrl.isBulkLocking && 
           true;
  }
  
  isSingularReadOnly() {
    return this.editingDisabled.isSingularReadOnly();
  }
}
