import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { PasswordResetComponent } from './password-reset/password-reset.component';
import { ConfirmRegistrationComponent } from './confirm-registration/confirm-registration.component';
import { LogoutComponent } from './logout/logout.component';
import { NewsComponent } from './news/news.component';
import { ContactusComponent } from './contactus/contactus.component';
import { LegalComponent } from './legal/legal.component';
import { PrivacyComponent } from './privacy/privacy.component';
import { ApplicantLandingComponent } from './applicant-landing/applicant-landing.component';
import { ApplicantLearnmoreComponent } from './applicant-learnmore/applicant-learnmore.component';
import { ProctorLandingComponent } from './proctor-landing/proctor-landing.component';
import { ProctorLearnmoreComponent} from './proctor-learnmore/proctor-learnmore.component';
import { SampleQuestionsPreComponent } from './sample-questions-pre/sample-questions-pre.component';
import { SampleQuestionsComponent } from '../ui-testrunner/sample-questions/sample-questions.component';
import { AttestationComponent } from './attestation/attestation.component';
import { G9SampleTestComponent } from './g9-sample-test/g9-sample-test.component';
import { G9LinearSampleTestComponent } from './g9-linear-sample-test/g9-linear-sample-test.component';
import { ViewLoginRouterStComponent } from './view-login-router-st/view-login-router-st.component';
import { ViewLoginEducatorComponent } from './view-login-educator/view-login-educator.component';
import { ViewLoginStudentComponent } from './view-login-student/view-login-student.component';
import { ViewFieldTestComponent } from './view-field-test/view-field-test.component';
import { MultiFactorAuthenticationComponent } from '../ui-login/multi-factor-authentication/multi-factor-authentication.component';
import { LdbRedirectComponent } from './ldb-redirect/ldb-redirect.component'
import { LdbLaunchPageComponent } from './ldb-launch-page/ldb-launch-page.component';

const routes: Routes = [

  { path: ':accountType/login', component: LoginComponent },
  { path: ':accountType/login/:dontLogout', component: LoginComponent },
  { path: ':accountType/login/:emailEncoded', component: LoginComponent, pathMatch: 'full' },
  { path: ':accountType/login/:emailEncoded/:dontLogout', component: LoginComponent },
  { path: ':accountType/forgot-password', component: ForgotPasswordComponent },
  { path: 'password-reset/:emailEncoded/:resetKey', component: PasswordResetComponent},
  { path: ':accountType/authenticate', component: MultiFactorAuthenticationComponent },
  { path: 'activate-account/:emailEncoded/:activationKey', component: ConfirmRegistrationComponent },
  { path: 'logout', component: LogoutComponent },
  { path: 'news', component: NewsComponent },
  { path: 'contact', component: ContactusComponent },
  { path: 'legal', component: LegalComponent },
  { path: 'privacy', component: PrivacyComponent },
  { path: 'attestation', component: AttestationComponent },
  { path: 'applicant/landing', component: ApplicantLandingComponent },
  { path: 'applicant/learn/:topicPath', component: ApplicantLearnmoreComponent, pathMatch: 'full' },
  { path: 'g9-sample', component: G9SampleTestComponent },
  { path: 'g9-linear-sample', component: G9LinearSampleTestComponent },
  { path: 'field/:assessmentSlug', component: ViewFieldTestComponent},
  { path: 'login-router-st', component: ViewLoginRouterStComponent},
  { path: 'login-educator', component: ViewLoginEducatorComponent},
  { path: 'login-student', component: ViewLoginStudentComponent},
  { path: 'login-student-sasn', component: ViewLoginStudentComponent},
  { path: 'bc-fsa/login-student', component: ViewLoginStudentComponent},
  { path: 'bc-grad/login-student', component: ViewLoginStudentComponent},
  { path: 'login-student/:kioskPassword', component: ViewLoginStudentComponent},
  { path: 'login-student-sasn/:kioskPassword', component: ViewLoginStudentComponent},
  { path: 'ldb-launch-page/:accessCode/:studentNumber/:isSasnLogin/:dob', component: LdbLaunchPageComponent},
  { path: 'ldb-redirect/:accessCode/:studentNumber/:isSasnLogin/:dob', component: LdbRedirectComponent},
  { path: 'ldb-redirect/:authToken', component: LdbRedirectComponent},
  {
    path: 'testadmin',
    children: [
      {
        path: 'landing', component: ProctorLandingComponent
      },
      {
        path: 'learn/:topicPath', component: ProctorLearnmoreComponent
      }
    ]
  },
  { path: 'sample-questions-pre', component: SampleQuestionsPreComponent },
  { path: 'sample-questions', component: SampleQuestionsComponent }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UiLandingRoutingModule { }
