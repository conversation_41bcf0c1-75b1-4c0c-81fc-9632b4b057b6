import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { ActivatedRoute, Router } from '@angular/router';
import { LangService } from '../../core/lang.service';
//import { FormControl, FormGroup } from '@angular/forms';
import { SidepanelService } from '../../core/sidepanel.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { RoutesService } from '../../api/routes.service';
import { Subscription } from 'rxjs';
import { AuthService, IUserInfo, F_ERR_MSG__INVALID_LOGIN  } from '../../api/auth.service';
import { AccountType } from '../../constants/account-types';
import { LoginValidationService } from '../../core/login-validation.service';
import { WhitelabelService } from '../../domain/whitelabel.service'
import { FormFail, LoginCtrl } from '../../core/login-ctrl';

interface ILoginFormErrors 
{
  isBlank?:boolean,
  isInvalid?: boolean,
}
@Component({
  selector: 'view-login-educator',
  templateUrl: './view-login-educator.component.html',
  styleUrls: ['./view-login-educator.component.scss']
})
export class ViewLoginEducatorComponent implements OnInit, OnDestroy 
{
  
  constructor(
    private loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private router:Router,
    private auth: AuthService,
    private whiteLabel: WhitelabelService,
    public lang:LangService,
    private sidePanel: SidepanelService,
    private route:ActivatedRoute,
    private loginValidatorService: LoginValidationService,
    private routes : RoutesService,
  ) 
  {

  }

  public isFormValidated:boolean;
  public isFormSent:boolean;
  public isFormFailed:boolean;
  public routeSub:Subscription;
  public accountType:AccountType;
  private isLoginAttempted:boolean;
  private dontLogout:string;
  public loginCtrl:LoginCtrl;
  public FormFail = FormFail;
  private formSubs:Subscription[] = [];
  public isLoaded:boolean;
  private userSub:Subscription;
  public prefilledEmail:string
  isFormValidatedSubscription: Subscription;
  isFormSentSubscription: Subscription;
  isFormFailedSubscription: Subscription;
  formFailReasonSubscription: Subscription;
  public breadcrumb = [];
  signInText = this.lang.tra('g9_sign_in')
  forgotPassRouterLink:string = `/${this.lang.c()}/general/forgot-password`;
  isLoginTroubleshooting:boolean;
  loginErrorTroubleshootMsg: any;
  public userNeedsMFASetUp: boolean = false;

  ngOnInit(): void 
  {
    this.sidePanel.deactivate();
    this.loginCtrl = this.loginValidatorService.initCtrl()
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT( this.lang.tra('lbl_login'), `/${this.lang.c()}/login-router-st`),
      this.breadcrumbsService._CURRENT( this.lang.tra(this.getAdminLoginSlug()), this.router.url),
    ];
    this.initRouteView();

    this.route.queryParams.subscribe((queryParams) => {
      if (queryParams['bypass'] === 'uat'){
        this.loginGuard.isHealthCheckBypassed = true;
      }
    });

    this.loginCtrl.loginFailSub.subscribe(flag => {
      if (flag){
        this.isLoggingIn = false
      }
    })

    if (this.auth.totpAuthenticationFailed)
    {
      this.loginCtrl.isFormFailed = true;
      this.loginCtrl.formFailReason = (FormFail.NOT_FOUND);
      this.auth.totpAuthenticationFailed = false;
    }

  }
  ngOnDestroy() 
  {
    if(this.userSub)
    {
      this.userSub.unsubscribe()
    }
  }

  supportReqActivate()
  {
    let techSupportInfo = this.whiteLabel.getSiteData(null);
    this.loginGuard.supportReqActivate(techSupportInfo);
  }

  getAdminLoginSlug = () => this.whiteLabel.getSiteText( 'login_admins', 'lbl_administrators');

  public initRouteView() {
    this.isLoaded = true;
    this.loginCtrl.mfaData$.subscribe(mfaData => {
      if(this.loginCtrl.isLoginAttempted && !(mfaData.isMFAUser == null)) {
        if (mfaData.isTotpUser) {
          if (mfaData.isFirstLoginWithTotp) {
            this.userNeedsMFASetUp = true;
            return;
          }

          const authenticateRouterLink:string = `/${this.lang.c()}/general/authenticate`;
          this.auth.logout();
          return this.router.navigateByUrl(authenticateRouterLink);
        } 

        if(mfaData.isMFAUser) {
          this.userNeedsMFASetUp = true;
          return;
        }

        this.auth.userSub.subscribe((userInfo:IUserInfo) => {
          if (!userInfo) {
            return;
          }
          return this.loginGuard.gotoUserDashboard(userInfo);
        });
      }

      // Reset MFA setup UI when MFA data is cleared (auth failure)
      if(mfaData.isMFAUser === null) {
        this.userNeedsMFASetUp = false;
      }
    });
  }

  isLoggingIn:boolean;
  public async attemptLogin(): Promise<void>
  {
    this.isLoggingIn = true;
    await this.loginCtrl.submitForm();
  }
}
