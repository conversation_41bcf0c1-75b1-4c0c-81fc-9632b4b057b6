import { StripPasiStatusComponent } from './strip-pasi-status/strip-pasi-status.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ViewNewSessionModalComponent } from './view-new-session-modal/view-new-session-modal.component';
import { UiWidgetEntryModule } from '../ui-schooladmin/ui-widget-entry.module';
import { UiPartialModule } from '../ui-partial/ui-partial.module';
import { MatRadioModule } from '@angular/material/radio';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ViewClassroomAssessmentListComponent } from './view-classroom-assessment-list/view-classroom-assessment-list.component';

@NgModule({
  declarations: [
    ViewNewSessionModalComponent,
    StripPasiStatusComponent,
    ViewClassroomAssessmentListComponent,
  ],
  imports: [
    CommonModule,
    UiWidgetEntryModule,
    MatRadioModule,
    ReactiveFormsModule,
    FormsModule,
    UiPartialModule,
    RouterModule,
  ],
  exports: [
    ViewNewSessionModalComponent,
    StripPasiStatusComponent
  ]
})
export class UISessionModalModule { }
