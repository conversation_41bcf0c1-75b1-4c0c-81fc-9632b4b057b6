<div *ngIf="!isLoading">
  <!-- Show personal group creation if user doesn't have any personal groups -->
  <div *ngIf="!hasPersonalGroups" class="notification is-warning">
    <p><strong>No Personal Group Found</strong></p>
    <p>You need a personal group to create classroom assessments. Personal groups allow you to store assessments that only you can access.</p>
    <button 
      class="button is-warning" 
      (click)="createPersonalGroup()" 
      [disabled]="isCreatingPersonalGroup">
      <span *ngIf="!isCreatingPersonalGroup">Create Personal Group</span>
      <span *ngIf="isCreatingPersonalGroup">Creating...</span>
    </button>
  </div>

  <!-- Show existing content only if user has personal groups -->
  <div *ngIf="hasPersonalGroups">

    <div *ngIf="!isCreatingNewAssessment">
      <button 
        class="button" 
        [class.is-large]="itemSets.length == 0" 
        (click)="initCreateNewAssessment()"
      >
        <i class="fa fa-plus" style="margin-right: 0.5em;"></i>
        Create Custom Assessment
      </button>
    </div>
    <div *ngIf="isCreatingNewAssessment">
      <strong>New Question Set:</strong>
      <div style="display: flex; flex-direction: row; align-items: center;">
        <input [(ngModel)]="newAssessmentName" placeholder="Name" type="text" maxlength="45" class="input" style="margin-right:1em;" >
        <button class="button is-success" (click)="createNewAssessment()">Create</button>
        <button class="button is-error" (click)="isCreatingNewAssessment = false">Cancel</button> 
      </div>
    </div>

    <div style="padding:1em;overflow:auto; max-height:60vh;" *ngIf="itemSets.length > 0">
      <div>
        <strong>My Assessments</strong>
      </div>
        <mat-radio-group [(ngModel)]="selectedItemSetId" (change)="onSelectItemSet($event)">
          <!-- *ngIf="!itemSet.is_test_design" -->
           <ng-container *ngFor="let itemSet of itemSets" >
            <div class="assessment-option" [class.selected]="selectedItemSetId === itemSet.id">
              <mat-radio-button 
                [value]="itemSet.id"
                class="assessment-radio"
              >
                {{itemSet.name}}
              </mat-radio-button>
              <a 
                class="button is-small edit-button" 
                target="_blank" 
                [routerLink]="getItemSetRoute(itemSet.id)" 
              >
                Edit
              </a>
            </div>
            <!-- (click)="selectItemSet(itemSet)" -->
           </ng-container>
        </mat-radio-group>
    </div>



  </div>
</div>

<div *ngIf="isLoading" class="notification is-info">
  <p>Loading classroom assessments...</p>
</div>