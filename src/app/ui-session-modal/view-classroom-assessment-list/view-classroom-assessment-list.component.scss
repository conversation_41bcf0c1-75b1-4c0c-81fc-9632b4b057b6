table {
  tr:not(:first-child) {
    cursor: pointer;
    &.is-selected {
      background-color: #3298dc;
    }
  }
}

.notification {
  margin-bottom: 1rem;
  
  &.is-warning {
    background-color: #fffbeb;
    color: #92400e;
    border: 1px solid #f59e0b;
  }
  
  &.is-info {
    background-color: #eff6ff;
    color: #1e40af;
    border: 1px solid #3b82f6;
  }
}

.button {
  &.is-warning {
    background-color: #f59e0b;
    color: white;
    
    &:hover {
      background-color: #d97706;
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

// Assessment option styling
.assessment-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin: 8px 0;
  background-color: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  
  &:hover {
    background-color: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  &.selected {
    background-color: #dbeafe;
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6;
    
    .assessment-radio {
      ::ng-deep .mat-radio-outer-circle {
        border-color: #3b82f6;
      }
      
      ::ng-deep .mat-radio-inner-circle {
        background-color: #3b82f6;
      }
    }
  }
  
  .assessment-radio {
    flex: 1;
    margin-right: 12px;
    
    ::ng-deep .mat-radio-label {
      font-weight: 500;
      color: #374151;
    }
    
    ::ng-deep .mat-radio-outer-circle {
      border-color: #9ca3af;
      border-width: 2px;
    }
    
    ::ng-deep .mat-radio-inner-circle {
      background-color: #3b82f6;
    }
    
    ::ng-deep .mat-radio-button.mat-radio-checked .mat-radio-outer-circle {
      border-color: #3b82f6;
    }
    
    ::ng-deep .mat-radio-ripple .mat-ripple-element {
      background-color: rgba(59, 130, 246, 0.2);
    }
  }
  
  .edit-button {
    background-color: #6b7280;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: #4b5563;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

// Override default mat-radio colors
::ng-deep .mat-radio-button.mat-radio-checked .mat-radio-outer-circle {
  border-color: #3b82f6 !important;
}

::ng-deep .mat-radio-button.mat-radio-checked .mat-radio-inner-circle {
  background-color: #3b82f6 !important;
}

::ng-deep .mat-radio-button .mat-radio-ripple .mat-ripple-element {
  background-color: rgba(59, 130, 246, 0.2) !important;
}