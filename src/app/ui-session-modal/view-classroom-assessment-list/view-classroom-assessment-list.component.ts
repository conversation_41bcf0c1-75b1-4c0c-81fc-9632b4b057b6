import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';
import { IItemSetDef, ItemMakerService } from 'src/app/ui-item-maker/item-maker.service';

@Component({
  selector: 'view-classroom-assessment-list',
  templateUrl: './view-classroom-assessment-list.component.html',
  styleUrls: ['./view-classroom-assessment-list.component.scss']
})
export class ViewClassroomAssessmentListComponent implements OnInit {
  
  @Output() onItemSetIdSelected = new EventEmitter<IItemSetDef>();

  itemSets: IItemSetDef[] = [];
  selectedItemSetId: number | undefined;
  isLoading = false; 
  
  isCreatingNewAssessment = false;
  newAssessmentSlug = 'custom';
  newAssessmentName = '';
  newAssessmentDescription = '---';
  
  hasPersonalGroups = false;
  isCreatingPersonalGroup = false;

  constructor(
    private auth: AuthService,
    private routes: RoutesService,
    private itemMakerService: ItemMakerService,
    private lang: LangService,
  ) { }

  ngOnInit(): void {
    this.loadItemSets();
  }
  
  async loadItemSets() {
    this.isLoading = true;
    try {
      const [itemSets, ..._] = await Promise.all([
        this.itemMakerService.loadMyItemSets(),
        this.itemMakerService.loadMyAuthoringGroups()
      ]);
      const personalAuthGroupIds = this.itemMakerService.getAuthoringGroups()
        .filter(g => g.isPersonal).map(g => g.group_id);
      
      this.hasPersonalGroups = personalAuthGroupIds.length > 0;
      
      const personalItemSets = (itemSets as IItemSetDef[])
        .filter(set => personalAuthGroupIds.includes(set.group_id))
      this.itemSets = personalItemSets;
      
    } catch(e) {
      window.alert('Error loading item sets' + e);
    }
    this.isLoading = false;
  }
  
  async createPersonalGroup() {
    this.isCreatingPersonalGroup = true;
    try {
      await this.itemMakerService.initPersonalAuthGroup();
      await this.loadItemSets();
      
      this.isCreatingPersonalGroup = false;
    } catch(e) {
      window.alert('Error creating personal group: ' + e);
      this.isCreatingPersonalGroup = false;
    }
  }
  
  async getLatestTestDesignId(itemSetId: number) {
    const testDesigns = await this.auth.apiFind(this.routes.TEST_AUTH_TEST_DESIGNS, { 
      query: { source_item_set_id: itemSetId } 
    });
    if (testDesigns.length > 0){
      return testDesigns[0].id;
    }
    return null;
  }
  
  onSelectItemSet($event:any){
    const itemSet = this.itemSets.find(set => set.id === this.selectedItemSetId);
    this.onItemSetIdSelected.emit(itemSet);
  }
  
  getItemSetRoute(itemSetId:number){
    return `/${this.lang.c()}/test-auth/classroom-assessment-editor/${itemSetId}`;
  }
  
  initCreateNewAssessment(){
    this.selectedItemSetId = undefined;
    this.isCreatingNewAssessment = true;
    this.newAssessmentName = '';
  }
  createNewAssessment(){
    const personalAuthGroupId = this.itemMakerService.getAuthoringGroups()
      .filter(g => g.isPersonal)[0]?.group_id;
    
    const errors = [];
    if (!this.newAssessmentSlug){ errors.push(this.lang.tra('test_auth_short_name')); }
    if (!this.newAssessmentName){ errors.push(this.lang.tra('test_auth_title')); }
    if (!this.newAssessmentDescription){ errors.push(this.lang.tra('test_auth_description')); }
    if (!personalAuthGroupId){ errors.push('Personal auth group not found'); }
    
    if (errors.length > 0){
      window.alert(`${this.lang.tra('required_fields_error')} (${errors.join(', ')})`);
    } else {
      const payload = {
        group_id: personalAuthGroupId,
        slug: this.newAssessmentSlug,
        name: this.newAssessmentName,
        description: this.newAssessmentDescription,
      }
      this.itemMakerService.createNewItemBank(payload)
        .then(() => {
          this.isCreatingNewAssessment = false;
          this.selectedItemSetId = undefined;
          this.loadItemSets();
      });
    }
  }

}
