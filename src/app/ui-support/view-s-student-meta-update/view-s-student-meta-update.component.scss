:host {
  --flat-red: #dc3545;

  // Tags with danger class only
  .tag.is-danger {
    background-color: var(--flat-red) !important;
    color: white !important;
  }

  // Buttons with danger class only
  .button.is-danger {
    background-color: var(--flat-red) !important;
    border-color: var(--flat-red) !important;
    color: white !important;
  }

  // Notifications with danger class
  .notification.is-danger {
    background-color: #f8d7da !important;
    color: var(--flat-red) !important;
  }

  // Text with danger class only
  .has-text-danger {
    color: var(--flat-red) !important;
  }

  // Icons with danger class only
  .icon.has-text-danger {
    color: var(--flat-red) !important;
  }

  // Custom validation error borders
  .validation-error-border {
    border-left: 3px solid var(--flat-red) !important;
  }
}