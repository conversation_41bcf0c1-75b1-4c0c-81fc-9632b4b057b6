import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PanelSysAccountTypeRoutesComponent } from './panel-sys-account-type-routes.component';

describe('PanelSysAccountTypeRoutesComponent', () => {
  let component: PanelSysAccountTypeRoutesComponent;
  let fixture: ComponentFixture<PanelSysAccountTypeRoutesComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PanelSysAccountTypeRoutesComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PanelSysAccountTypeRoutesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
