import { Component, OnInit } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { BreadcrumbsService } from 'src/app/core/breadcrumbs.service';
import { Router } from '@angular/router';
import { AccountType } from 'src/app/constants/account-types';
import { GridApi, GridOptions, ColDef } from 'ag-grid-community';

interface IRoleRouting {
  id: number;
  role_type: string;
  group_type: string;
  account_type: string | null;
  route_template: string;
  route_join: number;
  caption: string;
  color: string;
  display_order: number;
  created_on: string;
}

@Component({
  selector: 'app-panel-sys-account-type-routes',
  templateUrl: './panel-sys-account-type-routes.component.html',
  styleUrls: ['./panel-sys-account-type-routes.component.scss']
})
export class PanelSysAccountTypeRoutesComponent implements OnInit {
  
  public isLoaded: boolean = false;
  public breadcrumb: any[] = [];
  public accountType = AccountType.SYS_ACCESS;
  
  // Grid properties
  public gridApi: GridApi | null = null;
  public gridOptions: GridOptions = {};
  public rowData: IRoleRouting[] = [];
  
  // Editing panel
  public showEditPanel: boolean = false;
  public selectedRoute: IRoleRouting | null = null;
  public editForm = {
    role_type: '',
    group_type: '',
    account_type: '',
    route_template: '',
    route_join: 0,
    caption: '',
    color: '',
    display_order: 0
  };

  constructor(
    public lang: LangService,
    private auth: AuthService,
    private loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private router: Router,
    private routes: RoutesService
  ) { }

  ngOnInit() {
    // Activate login guard
    this.loginGuard.activate([this.accountType]);
    
    // Set up breadcrumbs
    this.breadcrumb = [
      this.breadcrumbsService.SYS_ACCESS_DASHBOARD(),
      this.breadcrumbsService._CURRENT('Account Type Routing', this.router.url),
    ];
    
    // Initialize grid
    this.initGrid();
    
    // Load data
    this.loadRoleRouting();
  }

  initGrid() {
    const columnDefs: ColDef[] = [
      {
        headerName: 'ID',
        field: 'id',
        sortable: true,
        filter: true,
        width: 80,
        type: 'numericColumn'
      },
      {
        headerName: 'Role Type',
        field: 'role_type',
        sortable: true,
        filter: true,
        width: 150
      },
      {
        headerName: 'Group Type',
        field: 'group_type',
        sortable: true,
        filter: true,
        width: 150
      },
      {
        headerName: 'Account Type',
        field: 'account_type',
        sortable: true,
        filter: true,
        width: 120,
        valueFormatter: (params: any) => {
          return params.value || 'N/A';
        }
      },
      {
        headerName: 'Route Template',
        field: 'route_template',
        sortable: true,
        filter: true,
        width: 200
      },
      {
        headerName: 'Caption',
        field: 'caption',
        sortable: true,
        filter: true,
        width: 150
      },
      {
        headerName: 'Color',
        field: 'color',
        sortable: true,
        filter: true,
        width: 100,
        cellRenderer: (params: any) => {
          return `<span class="color-swatch" style="background-color: ${params.value}; display: inline-block; width: 20px; height: 20px; border-radius: 3px; margin-right: 5px;"></span>${params.value}`;
        }
      },
      {
        headerName: 'Display Order',
        field: 'display_order',
        sortable: true,
        filter: true,
        width: 120,
        type: 'numericColumn'
      },
      {
        headerName: 'Actions',
        width: 120,
        cellRenderer: (params: any) => {
          return `
            <button class="btn btn-sm btn-primary" onclick="window.panelSysAccountTypeRoutes.editRoute(${params.data.id})">
              Edit
            </button>
          `;
        }
      }
    ];

    this.gridOptions = {
      columnDefs,
      rowData: [],
      pagination: true,
      paginationPageSize: 200,
      // domLayout: 'autoHeight',
      defaultColDef: {
        resizable: true,
        sortable: true,
        filter: true
      }
    };

    // Make component methods available globally for grid actions
    (window as any).panelSysAccountTypeRoutes = this;
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
  }

  async loadRoleRouting() {
    try {
      this.isLoaded = false;
      this.rowData = await this.auth.apiFind('public/sys-access/role-routing', {});
    } catch (err) {
      console.error('Failed to load role routing data:', err);
      this.rowData = [];
    } finally {
      this.isLoaded = true;
    }
  }

  editRoute(id: number) {
    const route = this.rowData.find(r => r.id === id);
    if (route) {
      this.selectedRoute = route;
      this.editForm = {
        role_type: route.role_type,
        group_type: route.group_type,
        account_type: route.account_type || '',
        route_template: route.route_template,
        route_join: route.route_join,
        caption: route.caption,
        color: route.color,
        display_order: route.display_order
      };
      this.showEditPanel = true;
    }
  }

  async saveRoute() {
    if (!this.selectedRoute) return;

    try {
      await this.auth.apiPatch('public/sys-access/role-routing', this.selectedRoute.id, this.editForm);
      await this.loadRoleRouting(); // Reload data
      this.closeEditPanel();
      alert('Route updated successfully');
    } catch (error) {
      console.error('Error updating route:', error);
      alert('Error updating route');
    }
  }

  closeEditPanel() {
    this.showEditPanel = false;
    this.selectedRoute = null;
    this.editForm = {
      role_type: '',
      group_type: '',
      account_type: '',
      route_template: '',
      route_join: 0,
      caption: '',
      color: '',
      display_order: 0
    };
  }
}
