<div *ngIf="isLoaded" class="feature-flags-container">
  <!-- Page header -->
  <div class="page-header">
    <h1>Feature Access Management</h1>
    <div class="header-actions">
      <button class="btn btn-primary" (click)="showCreateForm()">
        <i class="fas fa-plus"></i> Add Feature Access
      </button>
      <button class="btn btn-secondary" (click)="exportToCSV()">
        <i class="fas fa-download"></i> Export CSV
      </button>
    </div>
  </div>

  <!-- Main content area with grid and edit panel -->
  <div class="content-area">
    <!-- Grid section -->
    <div class="grid-section">
      <h3>Feature Access Groups</h3>
      <div class="grid-container">
        <ag-grid-angular
          [gridOptions]="gridOptions"
          [rowData]="rowData"
          (gridReady)="onGridReady($event)"
          class="ag-theme-alpine">
        </ag-grid-angular>
      </div>
    </div>

    <!-- Edit panel section -->
    <div class="edit-panel" *ngIf="showEditPanel">
      <div class="edit-panel-header">
        <h3>{{ isCreating ? 'Create Feature Access' : 'Feature Access Details' }}</h3>
        <button class="close-btn" (click)="closeEditPanel()">&times;</button>
      </div>
      
      <div class="edit-panel-body">
        <!-- Create/Edit Form -->
        <div *ngIf="isCreating">
          <form (ngSubmit)="saveFeatureAccess()">
            <div class="form-group">
              <label for="featureSlug">Feature:</label>
              <select id="featureSlug" [(ngModel)]="editForm.feature_slug" name="featureSlug">
                <option value="CUSTOM_CLASSROOM_ASSESSMENTS">Custom Classroom Assessments</option>
                <option value="ADVANCED_REPORTING">Advanced Reporting</option>
                <option value="BULK_OPERATIONS">Bulk Operations</option>
              </select>
            </div>
            
            <div class="form-group">
              <label for="groupId">Group ID:</label>
              <input type="number" id="groupId" [(ngModel)]="editForm.group_id" name="groupId" required>
            </div>
            
            <div class="form-group">
              <label for="expiresOn">Expires On (optional):</label>
              <input type="datetime-local" id="expiresOn" [(ngModel)]="editForm.expires_on" name="expiresOn">
            </div>
            
            <div class="form-group">
              <label for="notes">Notes:</label>
              <textarea id="notes" [(ngModel)]="editForm.notes" name="notes" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" (click)="closeEditPanel()">Cancel</button>
              <button type="submit" class="btn btn-primary">Create Access</button>
            </div>
          </form>
        </div>

        <!-- Details View -->
        <div *ngIf="!isCreating && selectedGroupId">
          <div class="group-info">
            <h4>Group Information</h4>
            <p><strong>Group ID:</strong> {{ selectedGroupId }}</p>
          </div>
          
          <div class="member-list">
            <h4>Group Members ({{ selectedGroupMembers.length }})</h4>
            <div *ngFor="let member of selectedGroupMembers" class="member-item">
              <div class="member-info">
                <strong>{{member.first_name}} {{member.last_name}}</strong>
                <span class="email">{{member.contact_email}}</span>
                <span class="role">{{member.role_type}}</span>
              </div>
              <div class="member-dates">
                <small>Role created: {{member.role_created_on | date}}</small>
                <small *ngIf="member.role_expires_on">Expires: {{member.role_expires_on | date}}</small>
              </div>
            </div>
            <div *ngIf="selectedGroupMembers.length === 0" class="no-members">
              No members found for this group.
            </div>
          </div>
          
          <div class="form-actions">
            <button type="button" class="btn btn-danger" (click)="removeFeatureAccess()">
              Remove Access
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
