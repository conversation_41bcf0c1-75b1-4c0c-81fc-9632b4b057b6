.feature-flags-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-shrink: 0;
  padding: 1rem 0;
  
  h1 {
    margin: 0;
    color: #333;
  }
  
  .header-actions {
    display: flex;
    gap: 1rem;
  }
}

.content-area {
  display: flex;
  flex: 1;
  gap: 2rem;
  overflow: hidden;
  padding: 0 1rem 1rem 1rem;
}

.grid-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  min-height: 0;
  
  h3 {
    margin-bottom: 1rem;
    color: #333;
    flex-shrink: 0;
  }
}

.grid-container {
  flex: 1;
  min-height: 0;
  border: 1px solid #ddd;
  border-radius: 4px;
  height: 0; /* Force ag-grid to respect flex constraints */
}

ag-grid-angular {
  height: 100%;
}

.edit-panel {
  flex: 1;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  max-width: 400px;
  min-height: 0;
}

.edit-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
  flex-shrink: 0;
  
  h3 {
    margin: 0;
    color: #333;
  }
  
  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    
    &:hover {
      color: #333;
    }
  }
}

.edit-panel-body {
  padding: 1.5rem;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.form-group {
  margin-bottom: 1rem;
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
  }
  
  input, select, textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    
    &:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }
  }
  
  textarea {
    resize: vertical;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
  flex-shrink: 0;
}

.group-info {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
  
  h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
  }
  
  p {
    margin: 0;
    color: #666;
  }
}

.member-list {
  margin-bottom: 1.5rem;
  
  h4 {
    margin: 0 0 1rem 0;
    color: #333;
  }
}

.member-item {
  padding: 1rem;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  
  .member-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    
    .email {
      color: #666;
      font-size: 0.9rem;
    }
    
    .role {
      color: #007bff;
      font-size: 0.8rem;
      font-weight: 600;
    }
  }
  
  .member-dates {
    margin-top: 0.5rem;
    
    small {
      display: block;
      color: #888;
      font-size: 0.8rem;
    }
  }
}

.no-members {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
}
