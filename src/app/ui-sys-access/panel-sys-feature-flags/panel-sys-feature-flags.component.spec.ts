import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PanelSysFeatureFlagsComponent } from './panel-sys-feature-flags.component';

describe('PanelSysFeatureFlagsComponent', () => {
  let component: PanelSysFeatureFlagsComponent;
  let fixture: ComponentFixture<PanelSysFeatureFlagsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PanelSysFeatureFlagsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PanelSysFeatureFlagsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
