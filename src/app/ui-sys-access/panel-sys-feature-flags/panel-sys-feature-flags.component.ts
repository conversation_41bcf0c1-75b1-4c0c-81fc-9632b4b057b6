import { Component, OnInit } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { BreadcrumbsService } from 'src/app/core/breadcrumbs.service';
import { Router } from '@angular/router';
import { AccountType } from 'src/app/constants/account-types';
import { GridApi, GridOptions, ColDef } from 'ag-grid-community';

interface IFeatureAccessGroup {
  id: number;
  group_id: number;
  feature_slug: string;
  group_name: string;
  group_type: string;
  created_on: string;
  n_users: number;
}

interface IGroupMember {
  uid: number;
  first_name: string;
  last_name: string;
  contact_email: string;
  role_type: string;
  role_created_on: string;
  role_expires_on: string | null;
}

interface IFeatureAccessForm {
  feature_slug: string;
  group_id: number | null;
  expires_on: string | null;
  notes: string;
}

@Component({
  selector: 'app-panel-sys-feature-flags',
  templateUrl: './panel-sys-feature-flags.component.html',
  styleUrls: ['./panel-sys-feature-flags.component.scss']
})
export class PanelSysFeatureFlagsComponent implements OnInit {
  
  public isLoaded: boolean = false;
  public breadcrumb: any[] = [];
  public accountType = AccountType.SYS_ACCESS;
  
  // Grid properties
  public gridApi: GridApi | null = null;
  public gridOptions: GridOptions = {};
  public rowData: IFeatureAccessGroup[] = [];
  
  // Side panel
  public showEditPanel: boolean = false;
  public selectedGroupId: number | null = null;
  public selectedGroupMembers: IGroupMember[] = [];
  
  // Form data
  public editForm: IFeatureAccessForm = {
    feature_slug: 'CUSTOM_CLASSROOM_ASSESSMENTS',
    group_id: null,
    expires_on: null,
    notes: ''
  };
  
  public isCreating: boolean = false;

  constructor(
    public lang: LangService,
    private auth: AuthService,
    private loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private router: Router,
    private routes: RoutesService
  ) { }

  ngOnInit() {
    // Activate login guard
    this.loginGuard.activate([this.accountType]);
    
    // Set up breadcrumbs
    this.breadcrumb = [
      this.breadcrumbsService.SYS_ACCESS_DASHBOARD(),
      this.breadcrumbsService._CURRENT('Feature Access Management', this.router.url),
    ];
    
    // Initialize grid
    this.initGrid();
    
    // Load data
    this.loadFeatureAccessData();
  }

  initGrid() {
    const columnDefs: ColDef[] = [
      {
        headerName: 'ID',
        field: 'id',
        sortable: true,
        filter: true,
        width: 100
      },
      {
        headerName: 'Feature',
        field: 'feature_slug',
        sortable: true,
        filter: true,
        width: 200
      },
      {
        headerName: 'Group',
        field: 'group_name',
        sortable: true,
        filter: true,
        width: 200
      },
      {
        headerName: 'Group Type',
        field: 'group_type',
        sortable: true,
        filter: true,
        width: 200
      },
      {
        headerName: 'Users',
        field: 'n_users',
        sortable: true,
        filter: true,
        width: 100,
        type: 'numericColumn'
      },
      {
        headerName: 'Created On',
        field: 'created_on',
        sortable: true,
        filter: true,
        width: 150,
      },
      {
        headerName: 'Details',
        width: 120,
        cellRenderer: (params: any) => {
          return `
            <button class="btn btn-sm btn-info" onclick="window.panelSysFeatureFlags.showDetails(${params.data.group_id})">
              Details
            </button>
          `;
        }
      }
    ];

    this.gridOptions = {
      columnDefs,
      rowData: [],
      pagination: true,
      paginationPageSize: 20,
      domLayout: 'autoHeight',
      defaultColDef: {
        resizable: true,
        sortable: true,
        filter: true
      }
    };

    // Make component methods available globally for grid actions
    (window as any).panelSysFeatureFlags = this;
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
  }

  async loadFeatureAccessData() {
    try {
      this.isLoaded = false;
      this.rowData = await this.auth.apiFind(this.routes.SYS_ACCESS_FEATURE_FLAGS, {});
    } catch (error) {
      console.error('Error loading feature access data:', error);
    } finally {
      this.isLoaded = true;
    }
  }

  async showDetails(groupId: number) {
    try {
      this.selectedGroupId = groupId;
      const response = await this.auth.apiGet(this.routes.SYS_ACCESS_FEATURE_FLAGS, groupId, {
        query: { group_id: groupId }
      });
      this.selectedGroupMembers = response.users || [];
      this.showEditPanel = true;
    } catch (error) {
      console.error('Error loading group members:', error);
    }
  }

  closeEditPanel() {
    this.showEditPanel = false;
    this.selectedGroupMembers = [];
    this.selectedGroupId = null;
    this.isCreating = false;
    this.resetForm();
  }

  showCreateForm() {
    this.isCreating = true;
    this.showEditPanel = true;
    this.resetForm();
  }

  resetForm() {
    this.editForm = {
      feature_slug: 'CUSTOM_CLASSROOM_ASSESSMENTS',
      group_id: null,
      expires_on: null,
      notes: ''
    };
  }

  async saveFeatureAccess() {
    if (!this.editForm.group_id) {
      alert('Please select a group');
      return;
    }

    try {
      if (this.isCreating) {
        await this.auth.apiCreate(this.routes.SYS_ACCESS_FEATURE_FLAGS, this.editForm);
        alert('Feature access created successfully');
      } else {
        // For editing, we would need an update endpoint
        // For now, we'll just close the panel
        alert('Edit functionality not yet implemented');
      }
      
      await this.loadFeatureAccessData(); // Reload data
      this.closeEditPanel();
    } catch (error) {
      console.error('Error saving feature access:', error);
      alert('Error saving feature access');
    }
  }

  async removeFeatureAccess() {
    if (!this.selectedGroupId) return;
    
    if (confirm('Are you sure you want to remove this feature access?')) {
      try {
        await this.auth.apiRemove(this.routes.SYS_ACCESS_FEATURE_FLAGS, this.selectedGroupId);
        await this.loadFeatureAccessData(); // Reload data
        this.closeEditPanel();
        alert('Feature access removed successfully');
      } catch (error) {
        console.error('Error removing feature access:', error);
        alert('Error removing feature access');
      }
    }
  }

  exportToCSV() {
    if (!this.gridApi) return;
    
    const csvContent = this.gridApi.getDataAsCsv();
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `feature-access-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  }
}
