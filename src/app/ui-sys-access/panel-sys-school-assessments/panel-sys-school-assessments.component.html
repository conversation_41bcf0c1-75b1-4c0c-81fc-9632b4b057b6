<div *ngIf="isLoaded">
  <!-- Page header -->
  <div class="page-header">
    <h1>School Assessment Types</h1>
    <p>Look up assessment mappings by school and configure feature flags.</p>
  </div>

  <!-- Assessment lookup form -->
  <div class="form-group">
    <label for="schoolForeignId">School Foreign ID:</label>
    <input 
      type="text" 
      id="schoolForeignId" 
      [(ngModel)]="schoolForeignId" 
      placeholder="Enter school foreign ID"
      class="form-control">
    <button 
      (click)="lookupAssessments()" 
      class="btn btn-primary"
      [disabled]="!schoolForeignId">
      Lookup Assessments
    </button>
  </div>
  
  <!-- Assessment results -->
  <div *ngIf="assessments.length > 0" class="assessment-results">
    <h5>Assessment Results:</h5>
    <table class="table">
      <thead>
        <tr>
          <th>Assessment Name</th>
          <th>Course Code</th>
          <th>School Name</th>
          <th>Created On</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let assessment of assessments">
          <td>{{ assessment.assessment_name }}</td>
          <td>{{ assessment.course_code }}</td>
          <td>{{ assessment.school_name }}</td>
          <td>{{ assessment.mapping_created_on | date }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
