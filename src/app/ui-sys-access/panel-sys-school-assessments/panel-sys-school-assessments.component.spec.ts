import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PanelSysSchoolAssessmentsComponent } from './panel-sys-school-assessments.component';

describe('PanelSysSchoolAssessmentsComponent', () => {
  let component: PanelSysSchoolAssessmentsComponent;
  let fixture: ComponentFixture<PanelSysSchoolAssessmentsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PanelSysSchoolAssessmentsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PanelSysSchoolAssessmentsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
