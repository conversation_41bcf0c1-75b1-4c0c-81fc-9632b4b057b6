import { Component, OnInit } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { BreadcrumbsService } from 'src/app/core/breadcrumbs.service';
import { Router } from '@angular/router';
import { AccountType } from 'src/app/constants/account-types';

@Component({
  selector: 'app-panel-sys-school-assessments',
  templateUrl: './panel-sys-school-assessments.component.html',
  styleUrls: ['./panel-sys-school-assessments.component.scss']
})
export class PanelSysSchoolAssessmentsComponent implements OnInit {
  
  public isLoaded: boolean = false;
  public breadcrumb: any[] = [];
  public accountType = AccountType.SYS_ACCESS;
  
  // Assessment lookup properties
  public schoolForeignId: string = '';
  public assessments: any[] = [];

  constructor(
    public lang: LangService,
    private auth: AuthService,
    private loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private router: Router,
    private routes: RoutesService
  ) { }

  ngOnInit() {
    // Activate login guard
    this.loginGuard.activate([this.accountType]);
    
    // Set up breadcrumbs
    this.breadcrumb = [
      this.breadcrumbsService.SYS_ACCESS_DASHBOARD(),
      this.breadcrumbsService._CURRENT('School Assessment Types', this.router.url),
    ];
    
    this.isLoaded = true;
  }

  async lookupAssessments() {
    if (!this.schoolForeignId) {
      return;
    }

    try {
      const result = await this.auth.apiFind('public/sys-access/assessments', {
        school_foreign_id: this.schoolForeignId
      });
      this.assessments = result.data || [];
    } catch (err) {
      console.error('Failed to lookup assessments:', err);
      this.assessments = [];
    }
  }
}
