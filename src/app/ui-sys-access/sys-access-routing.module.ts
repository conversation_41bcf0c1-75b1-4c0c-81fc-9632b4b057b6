import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ViewSysDashboardComponent } from './view-sys-dashboard/view-sys-dashboard.component';

const routes: Routes = [
  { path: '', component: ViewSysDashboardComponent },
  { path: 'dashboard', component: ViewSysDashboardComponent },
  { path: 'feature-flags', component: ViewSysDashboardComponent },
  { path: 'assessment-types', component: ViewSysDashboardComponent },
  { path: 'account-routing', component: ViewSysDashboardComponent }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SysAccessRoutingModule { } 