import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiPartialModule } from '../ui-partial/ui-partial.module';
import { AgGridModule } from 'ag-grid-angular';
import { SysAccessRoutingModule } from './sys-access-routing.module';
import { ViewSysDashboardComponent } from './view-sys-dashboard/view-sys-dashboard.component';
import { PanelSysFeatureFlagsComponent } from './panel-sys-feature-flags/panel-sys-feature-flags.component';
import { PanelSysSchoolAssessmentsComponent } from './panel-sys-school-assessments/panel-sys-school-assessments.component';
import { PanelSysAccountTypeRoutesComponent } from './panel-sys-account-type-routes/panel-sys-account-type-routes.component';

@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    FormsModule,
    UiPartialModule,
    AgGridModule,
    SysAccessRoutingModule
  ],
  declarations: [
    ViewSysDashboardComponent,
    PanelSysFeatureFlagsComponent,
    PanelSysSchoolAssessmentsComponent,
    PanelSysAccountTypeRoutesComponent
  ]
})
export class UiSysAccessModule { } 