<div class="page-body">
    <div>
      <!-- Header with breadcrumbs -->
      <header
        [breadcrumbPath]="breadcrumb"
        [hasPrint]="false"
        [accountType]="accountType">
      </header>
      
      <!-- Main content area -->
      <div class="page-content is-fullpage">
        <div *ngIf="isLoaded">
          <!-- Page-specific content -->
          <h3>System Access Controller</h3>

          <p>Relevant tables:</p>
          <pre>
assessment_class_asmt_type_options
          </pre>
          
          <!-- Menu tabs for the three main sections -->
          <menu-bar 
            [menuTabs]="menuTabs"
            [tabIdInit]="selectedTab"
            (change)="selectTab($event)">
          </menu-bar>
          
          <!-- Card-style links when no tab is selected -->
          <div *ngIf="!selectedTab" class="menu-cards">
            <a class="menu-card" routerLink="/en/sys-access/feature-flags" >
              <div class="card-icon">
                <i class="fas fa-flag"></i>
              </div>
              <div class="card-content">
                <h4>User Feature Flag Management</h4>
                <p>Manage feature flags and group-based entitlements for features like Custom Classroom Assessments.</p>
              </div>
            </a>
            
            <a class="menu-card" routerLink="/en/sys-access/assessment-types" >
              <div class="card-icon">
                <i class="fas fa-graduation-cap"></i>
              </div>
              <div class="card-content">
                <h4>School Assessment Types</h4>
                <p>Look up assessment mappings by school and configure feature flags.</p>
              </div>
            </a>
            
            <a class="menu-card" routerLink="/en/sys-access/account-routing" >
              <div class="card-icon">
                <i class="fas fa-route"></i>
              </div>
              <div class="card-content">
                <h4>Account Type Routing</h4>
                <p>Manage and review role-route mappings for user access control.</p>
              </div>
            </a>
          </div>
  
          <!-- Panel for the selected tab -->
          <div *ngIf="selectedTab === 'feature-flags'">
            <app-panel-sys-feature-flags></app-panel-sys-feature-flags>
          </div>
          <div *ngIf="selectedTab === 'assessment-types'">
            <app-panel-sys-school-assessments></app-panel-sys-school-assessments>
          </div>
          <div *ngIf="selectedTab === 'account-routing'">
            <app-panel-sys-account-type-routes></app-panel-sys-account-type-routes>
          </div>
          
        </div>
      </div>
    </div>
    
    <!-- Footer -->
    <footer [hasLinks]="true"></footer>
  </div> 