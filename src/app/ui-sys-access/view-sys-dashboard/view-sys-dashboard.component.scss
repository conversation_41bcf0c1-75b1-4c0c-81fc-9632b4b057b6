@import '../../../styles/page-types/standard.scss';

.page-body {
  @extend %page-body;
}

.tab-content {
  margin-top: 20px;
}

.menu-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.menu-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  text-decoration: none;
  color: inherit;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #3273dc;
    text-decoration: none;
    color: inherit;
  }

  .card-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    background: #3273dc;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
  }

  .card-content {
    flex: 1;

    h4 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.1rem;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
      line-height: 1.4;
    }
  }
}

.feature-flags-tab,
.assessment-types-tab,
.account-routing-tab {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 15px;
}

.form-group {
  margin-bottom: 15px;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
  
  &.btn-primary {
    background-color: #007bff;
    color: white;
    
    &:hover {
      background-color: #0056b3;
    }
    
    &:disabled {
      background-color: #6c757d;
      cursor: not-allowed;
    }
  }
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
  
  th, td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
  }
  
  th {
    background-color: #f8f9fa;
    font-weight: bold;
  }
  
  tr:hover {
    background-color: #f5f5f5;
  }
}

.assessment-results,
.role-routing-results {
  margin-top: 20px;
}

.color-swatch {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 3px;
  border: 1px solid #ddd;
  margin-right: 5px;
  vertical-align: middle;
}

h4 {
  margin-bottom: 15px;
  color: #333;
}

h5 {
  margin-bottom: 10px;
  color: #555;
}

p {
  margin-bottom: 15px;
  color: #666;
} 