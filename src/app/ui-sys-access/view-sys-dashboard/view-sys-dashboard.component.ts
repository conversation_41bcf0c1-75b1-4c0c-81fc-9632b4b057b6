import { Component, OnInit } from '@angular/core';
import { AccountType } from '../../constants/account-types';
import { LoginGuardService } from '../../api/login-guard.service';
import { AuthService } from '../../api/auth.service';
import { LangService } from '../../core/lang.service';
import { Router, ActivatedRoute } from '@angular/router';
import { BreadcrumbsService, IBreadcrumbRoute } from '../../core/breadcrumbs.service';
import { IMenuTabConfig } from '../../ui-partial/menu-bar/menu-bar.component';

@Component({
  selector: 'view-sys-dashboard',
  templateUrl: './view-sys-dashboard.component.html',
  styleUrls: ['./view-sys-dashboard.component.scss']
})
export class ViewSysDashboardComponent implements OnInit {
  
  public isLoaded: boolean = false;
  public breadcrumb: IBreadcrumbRoute[] = [];
  public accountType = AccountType.SYS_ACCESS;
  public currentGroupId: number | null = null;
  public groups: any[] = [];
  
  // Menu tabs configuration
  public menuTabs: IMenuTabConfig<string>[] = [
    { id: 'feature-flags', caption: 'User Feature Flag Management' },
    { id: 'assessment-types', caption: 'School Assessment Types' },
    { id: 'account-routing', caption: 'Account Type Routing' }
  ];
  
  public selectedTab: string = '';

  constructor(
    public lang: LangService,
    private auth: AuthService,
    private loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private router: Router,
    private route: ActivatedRoute
  ) { }

  ngOnInit() {
    // 1. Activate login guard
    this.loginGuard.activate([this.accountType]);
    
    // 2. Set selected tab based on current route
    this.setSelectedTabFromRoute();
    
    // 3. Initialize route view
    this.initRouteView();
  }

  initRouteView() {
    // 4. Load data and set loaded state
    this.loadData().then(() => {
      this.isLoaded = true;
    });
  }

  private async loadData() {
    try {
      // Load groups for sys-access
      this.groups = await this.auth.apiFind('public/sys-access/summary', {
      });
    } catch (err) {
      console.error('Failed to load sys-access data:', err);
    }
  }

  private setSelectedTabFromRoute() {
    const url = this.router.url;
    if (url.includes('feature-flags')) {
      this.selectedTab = 'feature-flags';
    } else if (url.includes('assessment-types')) {
      this.selectedTab = 'assessment-types';
    } else if (url.includes('account-routing')) {
      this.selectedTab = 'account-routing';
    } else {
      this.selectedTab = '';
    }
    
    // Update breadcrumb based on selected tab
    this.updateBreadcrumb();
  }
  
  private updateBreadcrumb() {
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT('System Access Controller', this.router.url)
    ];
    
    // Add sub-menu breadcrumb if on a specific tab
    if (this.selectedTab) {
      const tabConfig = this.menuTabs.find(tab => tab.id === this.selectedTab);
      if (tabConfig) {
        this.breadcrumb = [
          this.breadcrumbsService._CURRENT('System Access Controller', '/en/sys-access/'),
          this.breadcrumbsService._CURRENT(tabConfig.caption, this.router.url)
        ];
      }
    }
  }

  selectTab(tabId: string) {
    // Prevent duplicate navigation
    if (this.selectedTab === tabId) {
      return;
    }
    
    this.selectedTab = tabId;
    
    // Navigate to the appropriate route based on the selected tab
    const baseUrl = `/en/sys-access`;
    switch (tabId) {
      case 'feature-flags':
        this.router.navigate([`${baseUrl}/feature-flags`]);
        break;
      case 'assessment-types':
        this.router.navigate([`${baseUrl}/assessment-types`]);
        break;
      case 'account-routing':
        this.router.navigate([`${baseUrl}/account-routing`]);
        break;
    }
    
    // Update breadcrumb after navigation
    setTimeout(() => this.updateBreadcrumb(), 100);
  }

  selectGroup(id: number) {
    this.currentGroupId = id;
    // Future: load features/assessments for group
  }
} 