import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { G9DemoDataService } from '../../ui-schooladmin/g9-demo-data.service';
import { ColumnApi, GridApi, GridOptions, GridReadyEvent } from 'ag-grid-community';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { LangService } from 'src/app/core/lang.service';
// import { ITwUserMetas } from '../../ui-schooladmin/data/types';

export interface ITSchlStuModalData {
  first_name: string,
  last_name: string,
  studentIdentificationNumber: number,
  dob: string,
  studentGrade?: number
}

@Component({
  selector: 't-modal-school-students',
  templateUrl: './t-modal-school-students.component.html',
  styleUrls: ['./t-modal-school-students.component.scss']
})
export class TModalSchoolStudentsComponent implements OnInit {

  @Input() schoolStudentsList: any[]
  @Input() defaultSelectAll: boolean = false;
  @Input() invalidStudentIds?: number[];
  @Input() isSasnLogin: boolean = false;
  @Output() selectionConfirmed = new EventEmitter<ITSchlStuModalData[]>();
  constructor(
    private whiteLabelService: WhitelabelService,
    public lang: LangService
  ) { }

  getStudentAccountLookupSlug = () => {
    if(this.whiteLabelService.isNBED() || this.whiteLabelService.isMBED()){
      return 'student_account_num_nbed'
    } else if(this.whiteLabelService.isABED()) {
      return this.whiteLabelService.getSiteText('student_ident');
    }
    //EQAO
    if(this.isSasnLogin) return 'student_account_num_sasn';
    //ABED
    if(this.whiteLabelService.isABED()) return 'lbl_ASN_ABED'
    return 'student_account_num' 
  }


  gridApi: GridApi
  gridColumnApi: ColumnApi
  gridOptions: GridOptions = {
    columnDefs: [
      { headerName: this.lang.tra(this.getStudentAccountLookupSlug()), field: 'studentIdentificationNumber', checkboxSelection: true},
      { headerName: this.lang.tra('abed_grade'), field: 'studentGrade'}, 
      { headerName: this.lang.tra('lbl_first_name'), field: 'first_name'}, 
      { headerName: this.lang.tra('lbl_last_name'), field: 'last_name'}, 
      { headerName: this.lang.tra('abed_dob_lbl'), field: 'dob'}, 
    ],
    defaultColDef: {
      sortable: true, 
      resizable: true,
      filter: true,
    },
    rowSelection: "multiple",
    enableCellTextSelection: false,
    rowMultiSelectWithClick: true,
  }


  data: ITSchlStuModalData[]
  selectedRows: ITSchlStuModalData[] = [];

  ngOnInit(): void {
    this.initData();
  }

  initData = () => {
    const data = [];
    this.schoolStudentsList.forEach(student => {
      const {first_name, last_name, studentIdentificationNumber, tw_user_metas } = student; 
      const dob = tw_user_metas.find(user_meta => user_meta.key == "DateofBirth")
      const studentGrade = tw_user_metas.find(user_meta => user_meta.key == "StudentGrade")?.value
      const stuInfo: ITSchlStuModalData = {
        first_name,
        last_name,
        studentGrade,
        dob: dob ? this.displayDate(dob.value) : '',
        studentIdentificationNumber,
      }
      data.push(stuInfo)
    })
    this.data = data;  
    // console.log('students', this.schoolStudentsList )

  }

  onGridReady = (params: GridReadyEvent) => {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    params.columnApi.autoSizeAllColumns();
    if (this.defaultSelectAll){
      this.gridApi.forEachNode((node: any) => {
        node.setSelected(true);
      });
    }
  }

  onBatchGroupSelect = ($event) => {
    this.selectedRows = this.gridApi.getSelectedRows();
    this.selectionConfirmed.emit(this.selectedRows);
  }

  displayDate(raw){
    if(!raw) return '';
    return `${raw.slice(0,4)}-${raw.slice(4,6)}-${raw.slice(6,8)}`;
  }

}
