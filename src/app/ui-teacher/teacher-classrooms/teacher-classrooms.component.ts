import { sortBy } from './../../time/sort.pipe';
import * as _ from 'lodash';
import { Component, OnInit, OnDestroy, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { SidepanelService } from '../../core/sidepanel.service';
import { UserSiteContextService } from '../../core/usersitecontext.service';
import { Router, ActivatedRoute } from '@angular/router';
import { PageModalController, PageModalService } from "../../ui-partial/page-modal.service";
import { Subscription } from 'rxjs';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { ClassroomsService } from '../../core/classrooms.service';
import { AssessmentService } from '../../core/assessment.service';
import { randInt } from '../../ui-testadmin/demo-data.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { IClassroomArchivedTeachers, IUserInfo,IClassroomSlot,IClassroom, ISessionBegin, ASSESSMENT } from '../data/types';
import { SCHEDULER } from 'src/app/ui-schooladmin/sa-assessment-sessions/sa-assessment-sessions.component';
import { SessionModal, ConfidentialityAgreement, ClassroomModal } from 'src/app/ui-schooladmin/data/types';
import { ProfResponsibilityTeacherAgreement, ScanSessionInfo, TwSlot } from './data/types';
import { IAgreementConfirmConfig, IAgreementConfirmData } from 'src/app/ui-schooladmin/view-schooladmin-dashboard/data/types';
import { generateAccessCode, G9DemoDataService } from '../../ui-schooladmin/g9-demo-data.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { RoutesService } from '../../api/routes.service';
import { AccountType } from '../../constants/account-types';
import * as moment from 'moment-timezone';
import { AuthService } from '../../api/auth.service';
import { ITypeSlug, SchoolTeacherAdminQuestionnaireService } from '../../ui-questionnaire/school-teacher-admin-questionnaire.service';
import { AssessmentTypeOptions } from 'src/app/ui-session-modal/view-new-session-modal/view-new-session-modal.component';
import { formatDate } from '@angular/common';
import { AssessmentSlugs } from '../view-invigilate/types';
import { GridApi, GridOptions } from 'ag-grid-community';
import { RowNodeEvent } from 'ag-grid-community/dist/lib/entities/rowNode';


const IS_REPORTS_ENABLED = true;

enum Overlays {
  NEW_CLASSROOM = 'NEW_CLASSROOM',
  NEW_ASSESSMENT = 'NEW_ASSESSMENT',
  ACTIVE_ASSESSMENT = 'ACTIVE_ASSESSMENT',
  ASSESSMENT_SCHEDULER = 'ASSESSMENT_SCHEDULER',
  ADD_REMOVE_INVIGILATORS = 'ADD_REMOVE_INVIGILATORS'
}

interface ITwSlot {
  tw_id: number, 
  name?: string, 
  academic_year?: string, 
  sortOrder:string, 
  classes: IClassroomSlot[]
}

@Component({
  selector: 'teacher-classrooms',
  templateUrl: './teacher-classrooms.component.html',
  styleUrls: ['./teacher-classrooms.component.scss']
})
export class TeacherClassroomsComponent implements OnInit {

  @ViewChild('selectedClassroomPanel') selectedClassroomPanel:ElementRef;
  isAssessmentScheduled: boolean = false;
  config: any;
  isActiveLoaded: boolean = false;
  isSessionsCompleted: string[];
  sessionDetails: any;
  AssessmentSlugs = AssessmentSlugs

  constructor(
    public whitelabelService: WhitelabelService,
    private route: ActivatedRoute,
    private router: Router,
    private sidePanel: SidepanelService,
    public lang: LangService,
    private classroomService: ClassroomsService,
    private userSiteContext: UserSiteContextService,
    public  loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private assessmentService: AssessmentService,
    private g9demoService: G9DemoDataService,
    private auth: AuthService,
    private routes: RoutesService,
    private taQuestionnaire: SchoolTeacherAdminQuestionnaireService,
    private changeDetector: ChangeDetectorRef,
    private pageModalService: PageModalService,
  ) { }

  activeClassroom:IClassroom;
  classroomId:string;
  classroomSlots:IClassroomSlot[]=[];
  testWindowSlots:ITwSlot[]=[];
  classrooms:IClassroom[] = [];
  newClassName = new FormControl(null);
  classroomNameForm = new FormGroup({name: new FormControl('')});
  pageModal: PageModalController;
  SessionModal = SessionModal;
  ConfidentialityAgreement = ConfidentialityAgreement;
  ClassroomModal = ClassroomModal;
  isEditingClassroomName: boolean;
  isSavingClassroomName: boolean;
  isAddingNewClassroom:boolean;
  isOnlineStatusShow:boolean = false;
  isClickBufferActive:boolean;
  activeTeachers:IUserInfo[] = [];
  OVERLAYS = Overlays;
  MAX_STUDENT_DISPLAY = 7;
  MAX_TEACHER_DISPLAY = 5;
  inputValue = this.lang.tra('g9_name_to_show')
  dummyTeacherlist = [ ]
  pendingClassroomSelection;
  activeSubSessions;
  completedSubSessions;
  subSessions;
  breadcrumb = [];
  

  twNames = {}; // init in loadClassrooms
  nextTwIds = {}; // init in loadClassrooms

  isPrivateSchool:boolean = false;
  routeSub:Subscription;
  currentQueryParams;
  // Questionnaire
  questionnaireSessionId: number
  questionnaireDueOn: string
  haveQuestionnaireSession = false

  private classroomClassCodeCache:Map<string, string> = new Map();
  private signinSub:Subscription;
  schoolName:string;

  MAX_COMPLETED_SHOWN = 5
  numCompletedAssessmentsCap = this.MAX_COMPLETED_SHOWN;

  gridApi: GridApi;
  selectedReport: any;
  scoringReportRecords: any[];
  scoringReportGridOptions: GridOptions = 
  {
    columnDefs: [
      { headerName:'', field:'', width:100, checkboxSelection:true },
      { headerName:'Report Type', field:'type', width:200 },
      { headerName:'Test Window', field:'_test_window_title', width:200 },
      { headerName:'Marking Window', field:'marking_window_title', width:300,  },
      { headerName:'Assessment', field:'assessment_name', width:300 },
      { headerName:'School Year', field:'academic_year', width:200 },
    ],  
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
    rowSelection: 'single',
  };
  
  showNextBatchCompletedAssessments(dir=1){
    this.numCompletedAssessmentsCap += this.MAX_COMPLETED_SHOWN*dir
  }

  availableTeachers = [];

  ngOnInit() {
    this.sidePanel.deactivate();
    this.loginGuard.activate();
    this.loginGuard.runHealthCheck();
    this.activeClassroom = null;
    this.route.queryParams.subscribe((queryParams) => {
      this.currentQueryParams = {
        school: queryParams['school']
      }
    });
    
    this.classroomService.reloadSchool(true);
    this.classroomService.sub().subscribe(data => {
      if (data){
        this.classrooms = this.classroomService.classrooms;
        // console.log('isFreeStart', this.classroomService.isFreeStart(0))
        this.schoolName = this.classroomService.getSchoolName();
        this.checkPrivateSchool();
        this.loadClassrooms();
        this.loadRoute();
        //this.loadInvigilators()
        this.initQuestionnaire();

        if(this.showResponsibilityAgreement()) { // Remove if ABED has a responsibility agreement in the future
          this.checkAgreement(ProfResponsibilityTeacherAgreement);
        }
        if(this.isABED()) {
          this.haveUserAgreedOnConfidentiality();
        }
      }
    })
    if (this.whitelabelService.isABED()) {
      this.pageModal = this.pageModalService.defineNewPageModal();
    }
  }

  ngOnDestroy() {
    //this.signinSub.unsubscribe();
  }

  isPJ() {
    return this.activeClassroom.curricShort == "EQAO_G3" || this.activeClassroom.curricShort == "EQAO_G6";
  }

  activeStudents(){
    return this.activeClassroom.currentStudents.list
  }

  getActiveClassroomDepth(){
    if (this.classroomId){
      return 'SETTINGS';
    }
    else{
      return 'ADD_REMOVE';
    }
  }

  isFreeStart(classroomId:number | string){
    if (this.activeClassroom){
      return this.classroomService.isFreeStart(+classroomId)
    }
  }

  checkPrivateSchool(){
    const schoolInfo = this.classroomService.getSchoolByClassroomId(0);
    this.isPrivateSchool = !!schoolInfo.is_private;
  }
  createNewAssessmentSession($event){
    this.sessionDetails = $event;
      // this.getActiveSessions(config);
    // console.log('active classroom', this.activeClassroom, config);
    if (this.config.isScheduled){
      this.openAssessmentSchedulerModal();
    }
    else{
      if (!this.sessionDetails.isStudentsPresent) {
        this.loginGuard.quickPopup('msg_scheduling_no_students');
      } else {
        this.finalizeAssessmentCreation(this.config)
      }
    }
    console.log(this.sessionDetails)
  }

  isLanguageVisibleForFIClass(): boolean {
    const isFI = this.activeClassroom?.is_fi == 1 ? true : false;
    if(this.g9demoService.schoolDist[0].fi_option === 'C' && isFI) {
      return false;
    }
    return true;
  }

  checkAgreement(agreement: IAgreementConfirmConfig, callback?: (agreement: IAgreementConfirmConfig) => void) {
    const nonAcceptedWindows = this.g9demoService.totalNonAcceptedRecords;
    if(this.isPrivateSchool || (nonAcceptedWindows.length<=0)) {
      return;
    }

    this.displayAgreement(agreement, () => this.setAgreementConfirmed());
  }

  setAgreementConfirmed() {
    this.auth.apiCreate(this.routes.EDUCATOR_RESPONSIBILITY_AGREEMENTS, this.g9demoService.totalNonAcceptedRecords, { query: {schl_group_id: this.g9demoService.schoolData.group_id}})
    this.g9demoService.totalNonAcceptedRecords = [];
  }

  displayAgreement(agreement: IAgreementConfirmConfig, callback?: (agreement: IAgreementConfirmConfig) => void): Promise<any> {
    return new Promise<void>((resolve, reject) => {
      const caption = this.lang.tra(agreement.captionSlug);
      const checkBoxText = this.lang.tra(agreement.checkboxSlug);
      const btnSlug = this.lang.tra(agreement.btnSlug);
      this.loginGuard.confirmationReqActivate({
        caption,
        requireCheckboxInput: {checkboxCaption: checkBoxText},
        btnProceedConfig: {
          caption: btnSlug
        },
        btnCancelConfig: {
          hide: true
        },
        width: '43em',
        confirm: () => {
          callback(agreement);
          resolve();
        }
      });
    })
  }

  isDateTimeInTheFuture(UTCDateTime: string): boolean
  {
    const inputDTMoment = moment(UTCDateTime).utc(); // in UTC
    const nowDTMoment = moment().utc(); // in UTC
    return inputDTMoment.isAfter(nowDTMoment);
  }

  validateSchedulingRange(input, startDate, endDate, error) {
    const userInput = moment(input).tz(moment.tz.guess());
    const startM    = moment(startDate).utc();
    const endM      = moment(endDate).utc();
    const userInputUTC = userInput.utc();
    if(userInputUTC.isBefore(startM) || userInputUTC.isAfter(endM)){
      this.loginGuard.quickPopup(error);
      throw new Error();
    }
    return userInputUTC;
  }
  
  // async getLatestTestDesignId(itemSetId: number) {
  //   const testDesigns = await this.auth.apiFind(this.routes.TEST_AUTH_TEST_DESIGNS, { 
  //     query: { source_item_set_id: itemSetId } 
  //   });
  //   if (testDesigns.length > 0){
  //     return testDesigns[0].id;
  //   }
  //   return null;
  // }

  async finalizeAssessmentCreation($event){
    const config =  $event;
    const school_class_id = +this.activeClassroom.id
    const params = <any> this.classroomService.constructClassGroupIdQuery(this.classroomId); // can be null, missing some error handling from before
    const isFI = !this.isLanguageVisibleForFIClass();
    let slug = config.slug;
    let isScheduled = config.isScheduled;
    let scheduled_time = config.scheduled_time;
    let date_time_end;
    let sessionName = config.sessionName;
    let duration_m = config.duration_m;
    let capacity = config.capacity;
    const isCustomAssessment = config.isCustomAssessment;
    const customItemSetId = config.custom_item_set_id;

    const sc = this.activeClassroom; // why isn't this used?
    const classroom = this.g9demoService.classrooms.find(classroom => classroom.id === +this.classroomId)
    const semester = this.g9demoService.semesters.list.find(semester => semester.id == +(classroom?.semester || -1));
    const testWindow = this.g9demoService.testWindows.find(testWindow => testWindow.id === semester?.testWindowId);
    
    if (!classroom || !testWindow) {
      this.loginGuard.quickPopup('Error creating session with currently selected classroom. If problem persists after refreshing th page, please contact support.');
      throw new Error();
    }
    if (!slug) {
      this.loginGuard.quickPopup(this.whitelabelService.isABED() ? this.lang.tra("abed_select_ass") : this.lang.tra('sa_asses_type_invalid'));
      throw new Error();
    }
    if(!scheduled_time){
      if (this.isDateTimeInTheFuture(testWindow.date_start)) {
        alert(this.lang.tra("abed_future_schedule_error"));
        throw new Error();
      }
    }
    // if(!isScheduled && sc.openAssessments.length){
    //   alert(this.lang.tra("abed_one_asmt_msg"));
    //   throw new Error();
    // }

    if(scheduled_time){
      date_time_end = testWindow.date_end;
    }

    this.pageModal.saveStart();
    this.classroomService
      .createAssessmentSession({
        school_class_id,
        slug: slug,
        isScheduled: isScheduled,
        scheduled_time: scheduled_time,
        date_time_end,
        sessionName,
        duration_m,
        capacity,
        is_fi: isFI,
        isCustomAssessment,
        custom_item_set_id: customItemSetId,
      }, params)
      .then(session => {
        if (isScheduled){
          const sessionEntry = {
            test_session_id:session.test_session_id,
            date_time_start:session.date_time_start,
            school_class_id:session.school_class_id,
            slug:session.slug,
            asmt_design_caption: session.asmt_design_caption,
            // name // see g9demoService.processSessionName
            timeDirectStart:this.renderDay(session.date_time_start),
            hourDirectStart:this.renderTime(session.date_time_start),
            access_code:session.access_code
          }
          this.g9demoService.processSessionName(sessionEntry);
          this.activeClassroom.scheduledAssessments.splice(0,0, sessionEntry);
          this.pageModal.closeModal();
          this.closeActiveOverlay()
        }
       else{
          this.classroomService.navigateToAssessmentSession(''+school_class_id, session.test_session_id)
       }
      })
      .catch(e => {
        if (e.message === 'TECH_READI_PENDING'){
          this.loginGuard.disabledPopup(this.lang.tra('technical_readiness_warning'));
        }
        this.pageModal.saveEnd();
      })
  }

  private validateTestDatesWindow(a_string, b_string, windowStart, windowEnd, alertSlug) {
    const timezone = this.whitelabelService.getTimeZone();
    const currDateTime = moment().tz(timezone); // with time
    const a: moment.Moment | null = a_string ? moment(a_string.split("T")[0]) : null; // no time
    const b: moment.Moment | null = b_string ? moment(b_string.split("T")[0]) : null; // no time
    const aWithTime: moment.Moment | null = a_string ? moment(a_string).tz(timezone) : null; // with time
    const bWithTime: moment.Moment | null = a_string ? moment(a_string).tz(timezone) : null; // with time
    const windowStartDate: moment.Moment = moment(windowStart[0]); // no time
    const windowEndDate: moment.Moment = moment(windowEnd[0]); // no time
    const windowStartFormatted: string = windowStartDate.format("YYYY-MM-DD");
    const windowEndFormatted: string = windowEndDate.format("YYYY-MM-DD");
    const pastDateWarningSlug = "msg_abed_administration_window_past_warning"; // reuseable for EQAO and NBED too

    if (!a && b) {

      // todo: clarify conditions where we want to block creation of past sessions
      // if (bWithTime.diff(currDateTime) < 0){
      //   alert(this.lang.tra(pastDateWarningSlug));
      //   throw new Error();
      // }

      if (b.isBefore(windowStartDate)){
        alert(this.lang.tra(alertSlug, undefined,
        {
          start: windowStartFormatted,
          end: windowEndFormatted
        }));
        throw new Error();
      }

      if (b.isAfter(windowEndDate)){
        alert(this.lang.tra(alertSlug, undefined,
        {
          start: windowStartFormatted,
          end: windowEndFormatted
        }));
        throw new Error();
      }

    }

    else if(!b && a){ // todo: why is this duplicated?

      // if (aWithTime.diff(currDateTime) < 0){
      //   alert(this.lang.tra(pastDateWarningSlug));
      //   throw new Error();
      // }

      if (a.isBefore(windowStartDate))
      {
        alert(this.lang.tra(alertSlug, undefined,
        {
          start: windowStartFormatted,
          end: windowEndFormatted
        }));
        throw new Error();
      }

      if (a.isAfter(windowEndDate))
      {
        alert(this.lang.tra(alertSlug, undefined,
        {
          start: windowStartFormatted,
          end: windowEndFormatted
        }));
        throw new Error();
      }
    }

    else if(a && b) { // todo: why duplication?

      // if (aWithTime.diff(currDateTime) < 0 || bWithTime.diff(currDateTime) < 0)      {
      //   alert(this.lang.tra(pastDateWarningSlug));
      //   throw new Error();
      // }

      if (a.isBefore(windowStartDate) || b.isBefore(windowStartDate))
      {
        alert(this.lang.tra(alertSlug, undefined,
        {
          start: windowStartFormatted,
          end: windowEndFormatted
        }));
        throw new Error();
      }

      else if (a.isAfter(windowEndDate) || b.isAfter(windowEndDate))
      {
        alert(this.lang.tra(alertSlug, undefined,
        {
          start: windowStartFormatted,
          end: windowEndFormatted
        }));
        throw new Error();
      }
    }
  }

  getPayload(config){
    const slug = config.payload.data.slug;
    const slugType = config.payload.data.slugType;
    if(config.payload.data.schedule === SCHEDULER.LATER){
      if(this.isABED()){
        const date = config.payload.data.sessionAStartDate;
        const time = config.payload.data.sessionAStartTime;
        if(!date){
          alert(this.lang.tra("abed_teach_invalid_date"));
          throw new Error();
        }
        if(!time){
          alert(this.lang.tra("abed_teach_invalid_time"));
          throw new Error();
        }
      }
      if(slug === ASSESSMENT.G9_SAMPLE){
        return {
          school_class_id: config.payload.data.classId,
          slug: slug,
          isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
          scheduled_time: [`${config.payload.data.sessionAStartDate}T${this.timeConvert(config.payload.data.sessionAStartTime)}`]
        }
      }

      else if (slug.includes('ABED_') && slugType !== AssessmentTypeOptions.OPERATIONAL)
      {
        return {
          school_class_id: config.payload.data.classId,
          slug: slug,
          isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
          scheduled_time: `${config.payload.data.sessionAStartDate}T${this.timeConvert(config.payload.data.sessionAStartTime)}`
        }
      }

      else if (slug.includes('ABED_') && slugType === AssessmentTypeOptions.OPERATIONAL)
      {
        return {
          school_class_id: config.payload.data.classId,
          slug: slug,
          isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
          scheduled_time: config.payload.data.sessionAStartDateTime
        }
      }

      else if(slug === ASSESSMENT.G9_OPERATIONAL || slug === ASSESSMENT.OSSLT_SAMPLE || slug === ASSESSMENT.OSSLT_OPERATIONAL){
        return {
          school_class_id: config.payload.data.classId,
          slug: slug,
          isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
          scheduled_time: [
            `${config.payload.data.sessionAStartDate}T${this.timeConvert(config.payload.data.sessionAStartTime)}`,
            `${config.payload.data.sessionBStartDate}T${this.timeConvert(config.payload.data.sessionBStartTime)}`
          ]
        }
      }
      else if(slug === ASSESSMENT.PRIMARY_SAMPLE || slug === ASSESSMENT.PRIMARY_OPERATIONAL || slug === ASSESSMENT.JUNIOR_SAMPLE || slug === ASSESSMENT.JUNIOR_OPERATIONAL){
        let sessionLangStartDate =  config.payload.data.sessionLangStartDate ? `${config.payload.data.sessionLangStartDate}` : null;
        let sessionLangEndDate =  config.payload.data.sessionLangEndDate ? `${config.payload.data.sessionLangEndDate}` : null;
        let sessionMathStartDate =  config.payload.data.sessionMathStartDate ? `${config.payload.data.sessionMathStartDate}` : null;
        let sessionMathEndDate =  config.payload.data.sessionMathEndDate ? `${config.payload.data.sessionMathEndDate}` : null;
        return {
          school_class_id: config.payload.data.classId,
          slug: slug,
          isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
          scheduled_time: [
            sessionLangStartDate,
            sessionLangEndDate,
            sessionMathStartDate,
            sessionMathEndDate,
          ],
          isLangScheduled: config.payload.data.sessionLangStartDate || config.payload.data.sessionLangEndDate ? true : false,
          isMathScheduled: config.payload.data.sessionMathStartDate || config.payload.data.sessionMathEndDate ? true : false,
        }
      }
    }
    return {
      school_class_id: config.payload.data.classId,
      slug: config.payload.data.slug,
      isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
    }
  }

  timeConvert(tm) {
    let time;
    if (tm.includes('pm')){
      time = tm.replace('pm',' PM')
    }
    else{
      time = tm.replace('am', ' AM')
    }
    var hours = Number(time.match(/^(\d+)/)[1]);
    var minutes = Number(time.match(/:(\d+)/)[1]);
    var AMPM = time.match(/\s(.*)$/)[1];
    if (AMPM == "PM" && hours < 12) hours = hours + 12;
    if (AMPM == "AM" && hours == 12) hours = hours - 12;
    var sHours = hours.toString();
    var sMinutes = minutes.toString();
    if (hours < 10) sHours = "0" + sHours;
    if (minutes < 10) sMinutes = "0" + sMinutes;
    return (sHours + ":" + sMinutes);
  }

  getClassroomGroupId(){
    const classroom = this.classroomService.getClassroomById(this.classroomId)
    return classroom.group_id;
  }

  getActiveSessions(config){
    const classroom = this.classroomService.getClassroomById(this.classroomId)
    const params = {
      query: {
        school_class_group_id: classroom.group_id,
        school_class_id:this.classroomId,
        slug:config.slug
      }
    }
    this.classroomService
    .getActiveAssessments(params)
    .then(res => {
      this.completedSubSessions = res.completedSubSessions;
      this.subSessions = res.subSessions;
      this.activeSubSessions = res.activeSubSessions;
      // if(this.completedSubSessions.length > 0 || this.activeSubSessions.length > 0){
      //   this.config = config;
      //   this.openActiveAssessmentModal();
      // }
      // else{
      //   this.finalizeAssessmentCreation(config)
      // }
      this.isActiveLoaded = true;
      this.config = config;
      this.openActiveAssessmentModal();
    })
    }

  selectClassroom(classroom:IClassroomSlot){
    this.selectClassroomById(classroom.id);
  }

  getSelectClassroomLabel(classroom: IClassroom): string {
    switch(classroom.curricShort) {
      case 'EQAO_G10':
      case 'NBED_TCLE':
      case 'NBED_TCN':
      case 'NBED_SCIENCES8':
        return 'g9_select_group'
      case 'EQAO_G3':
      case 'EQAO_G6':
      case 'EQAO_G9':
      default:
        return this.whitelabelService.isABED() ? "abed_select_class": 'g9_select_class'
    }
  }

  getSchoolByClassroomId

  getInvigilateRouteLink(entry){
    return `/${this.lang.c()}/educator/assessment/${this.activeClassroom.id}/${entry.test_session_id}/`;
  }
  getScheduledInvigilateRouteLink(entry){
    this.classroomService.navigateToAssessmentSession(''+this.activeClassroom.id, entry.test_session_id, this.currentQueryParams)
  }
  getStudentRouteLink(){
    return `/${this.lang.c()}/educator/students/${this.activeClassroom.id}`;
  }

  renderDay(dateTime){
    const m = moment.tz(dateTime, moment.tz.guess());
    return m.format(this.lang.tra('datefmt_day_month'));
  }

  renderTime(dateTime){
    const m = moment.tz(dateTime, moment.tz.guess());
    return m.format('h:mm A')
  }

  selectClassroomById(classroomId:string){
    this.classroomService.navigateToClassroom(classroomId, this.currentQueryParams)
    setTimeout(()=>{
      //time delay is for aesthetic reasons, but if we decide to remove it, we should take it out
      const el = this.selectedClassroomPanel.nativeElement;
      if (el){
        el.scrollIntoView({behavior: 'smooth'});
      }
    }, 700);
  }

  loadRoute(){
    if (this.routeSub){ this.routeSub.unsubscribe(); }
    this.routeSub = this.route.params.subscribe(params => {
      this.classroomId = this.userSiteContext.handleClassroomRouteParam(params['classroomId']);
      if (!this.classroomId){
        this.activeClassroom = null;
        this.classroomService.setActiveClassroom(this.activeClassroom)
        this.sidePanel.classroomId = null;
        this.updateBreadcrumb();
        return;
      }
      this.sidePanel.classroomId = this.classroomId;
      this.loadClassroom(this.classroomId);
      this.updateBreadcrumb();
    });
  }

  updateBreadcrumb() {
    let teacherClass = this.lang.tra('lbl_classes_groups_ABED'); // todo:WHITELABEL
    const basePath = `/${this.lang.c()}/educator/classrooms`;
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT(teacherClass, basePath, this.currentQueryParams)
    ];
    if (this.activeClassroom){
      this.breadcrumb.push(
        this.breadcrumbsService._CURRENT( this.activeClassroom.name, basePath+'/'+this.classroomId, this.currentQueryParams)
      );
    }
  }

  

  getRolloverName(tw_id:number){
    const next_tw_id = this.nextTwIds[tw_id] 
    return this.twNames[next_tw_id]
  }
  isRolloverCandidate(tw_id:number){
    if (this.nextTwIds[tw_id]){
      return true;
    }
    return false;
  }

  isRollingOver = false 
  async rolloverClass(cloneScId: number){
    this.isRollingOver = true
    try {
      await this.auth.apiCreate('public/educator/class-rollover', {cloneScId})
      window.location.reload(); //todo: should just add the class...
    }
    catch (e){
      this.loginGuard.quickPopup(e.message)
    }
    this.isRollingOver = false
  }

  selectedAcademicYear:string;
  academicYears:any[] = [];
  isArchiving:boolean = false;
  isViewingArchive:boolean = false;
  isClassroomsLoaded:boolean = false;
  async loadClassrooms(){
    this.initClassroomsAndTwSlots();
    this.initAcademicYears();
    this.isClassroomsLoaded = true;
  }

  initClassroomsAndTwSlots(){
    this.classroomSlots = [];
    this.testWindowSlots = [];
    const twRef:Map<number, TwSlot> = new Map()
    for (let tw of this.g9demoService.testWindows){
      this.twNames[tw.id] = this.parseTwTitle(tw.title);
      this.nextTwIds[tw.id] = tw.next_tw_id
      if(tw.is_teacher_creation){
        const twSlot = {
          tw_id: tw.id, 
          academic_year: tw.academic_year, 
          sortOrder: tw.date_start, 
          classes: [], 
          is_teacher_creation: true
        }
        this.testWindowSlots.push(twSlot)
        twRef.set(tw.id, twSlot)
      }
    }
    this.classrooms.forEach(classroom =>{
      const classroomSearch = this.g9demoService.classrooms.find( c => c.id === +classroom.id);
      const classSemester = this.g9demoService.semesters.map[classroomSearch.semester]
      const tw = this.g9demoService.testWindows.find(tw => tw.id === classSemester.testWindowId)
      let twSlot:TwSlot = twRef.get(tw.id)
      if (!twSlot){
        twSlot = {
          tw_id: tw.id, 
          academic_year: tw.academic_year, 
          sortOrder: tw.date_start,
          classes: [],
        }
        this.testWindowSlots.push(twSlot)
        twRef.set(tw.id, twSlot)
      }
      twSlot.classes.push({
        id:classroom.id,
        name:classroom.name,
        isAssigned:classroom.isAssigned,
        is_archived:classroom.is_archived,
        curricShort:classroom.curricShort,
        classCode:classroom.classCode,
        numStudents:classroom.currentStudents.list.length,
        numTeachers:classroom.currentTeachers.length,
        is_fi:classroom.is_fi,
      })
    })
    this.testWindowSlots = sortBy(this.testWindowSlots, 'sortOrder', true);

  }
  initAcademicYears(){
    this.academicYears = this.g9demoService.academicYears;
    // console.log('academicYears', this.academicYears)
    let activePreviewClass;
    let activeAcademicYear;
    for (let ay of this.academicYears){
      const academic_year = ay.code
      if (ay.is_active == 1 && !activeAcademicYear){
        activeAcademicYear = academic_year
      }
      if (ay.is_preview == 1 && !activePreviewClass){
        for (let twSlot of this.testWindowSlots){
          if (twSlot.academic_year === academic_year && twSlot.classes.length > 0){
            activePreviewClass = {academic_year, classroom: twSlot.classes[0]};
          }
        }
      }
    }

    if (activePreviewClass){
      this.selectedAcademicYear = this.classroomService.setAcademicYear(activePreviewClass.academic_year, true);  
    }
    else if (activeAcademicYear){
      this.selectedAcademicYear = this.classroomService.setAcademicYear(activeAcademicYear, true);  
    }

  }

  parseTwTitle(twTitle:string){
    try {
      const title = JSON.parse(twTitle) || {};
      const titleStr = title[this.lang.c()] || title['en'] || '';
      if (titleStr){
        return titleStr;
      }
    }
    catch (e) { }
    return '--'
  }

  filterClassesInActiveTestWindow(classroom: any){
    const classroomSearch = this.g9demoService.classrooms.find( c => c.id === +classroom.id);
    const classSemester = this.g9demoService.semesters.map[classroomSearch.semester]
    const classTestWindow = this.g9demoService.testWindows.find(tw => tw.id === classSemester.testWindowId)
    //return (new Date(classTestWindow.date_end) > new Date ())
    // return classTestWindow.is_archived != 1
    return true;
  }

  loadInvigilators(){
    if(!this.activeClassroom || !this.g9demoService.invigilators){
      return
    }
    this.availableTeachers = [];
    //const invigilators = this.g9demoService.invigilators.filter(invig => invig.group_id == null || +invig.group_id == +this.activeClassroom.group_id)
    let invigilators = [];
    this.g9demoService.invigilators.forEach(g9invig => {
      const in_invigilator = invigilators.find(invig => invig.contact_email === g9invig.contact_email)
      if(!in_invigilator){ // if g9invig's email is not in invigilators list
        let theInvig = this.g9demoService.invigilators.find( invig => invig.contact_email === g9invig.contact_email && +invig.group_id == +this.activeClassroom.group_id)
        if(theInvig){
          invigilators.push(theInvig)
        }else{
          invigilators.push(g9invig)
        }
      }
    })
    invigilators.forEach(invig => {
      const avaiableInvig ={
        uid:invig.uid,
        name: invig.first_name+" "+invig.last_name,
        email: invig.contact_email,
        selected: +invig.group_id == +this.activeClassroom.group_id
      }
      this.availableTeachers.push(avaiableInvig);
    });
    this.availableTeachers.sort((a, b) => a.name.localeCompare(b.name));
  }

  loadClassroom(classroomId:string){
    this.activeClassroom = this.classrooms.find(classroom => (''+classroom.id) === classroomId);
    // console.log('activeClassroom', this.activeClassroom)
    this.classroomService.setActiveClassroom(this.activeClassroom)
    this.loadInvigilators();
    this.loadScanProgress()
  }

  scanProgress:ScanSessionInfo = {};
  async loadScanProgress(){
    const scInfo = this.activeClassroom
    const test_session_ids = [
      ... scInfo.openAssessments.map(s => s.test_session_id),
      ... scInfo.recentAssessments.map(s => s.test_session_id),
      // ... scInfo.scheduledAssessments.map(s => s.test_session_id),
    ]
    if (test_session_ids.length){
      const scanProgressPayload = await this.auth.apiCreate('public/school-admin/session-scan-progress', {test_session_ids});
      this.scanProgress = scanProgressPayload?.scanProgress || {};
    }
    else {
      this.scanProgress = {};
    }
  }
  hasSessionScanProgress(sessionId:number){
    const progress = this.scanProgress[sessionId];
    return progress?.is_scan_session;
  }
  getSessionScanProgressDisplay(sessionId:number){
    const progress = this.scanProgress[sessionId];
    if (progress.n_scans_expected){
      return `${progress.n_scans_received || 0}/${progress.n_scans_expected || 0} response sheets uploaded`; // todo: needs translation
    }
    return '';
  }
  isSessionScanProgressComplete(sessionId:number){
    const progress = this.scanProgress[sessionId];
    return (progress.n_scans_received >= progress.n_scans_expected)
  }

  getReportRouteForClass(){
    this.router.navigate([this.lang.c(),
      AccountType.EDUCATOR,
      'results-report',
      this.classroomId,
      this.activeClassroom.group_id
    ], {
      queryParams: this.currentQueryParams
    });
  }

  openAssessmentReport(session:{school_class_id:number, test_session_id:number, slug: string}){
    if (this.isScoreEntrySession(session)) {
      this.router.navigate([`/${this.lang.c()}/educator/assessment/view-responses/${this.activeClassroom.id}/${session.test_session_id}/`]);
      return;
    }
    const {school_class_id, test_session_id} = session;
    this.router.navigate([`${this.lang.c()}/educator/session-report/${school_class_id}/${test_session_id}`]);
    // this.router.navigate(['teacher','classroom',this.classroomId,'assignment', assignmentId, 'report']);
  }

  openClassroomNameEditor(){
    if (!this.isEditingClassroomName){
      this.isEditingClassroomName = true;
      this.classroomNameForm.setValue({name: this.activeClassroom.name});
    }
  }
  closeClassroomNameEditor(){
    this.isEditingClassroomName = false;
  }
  changeCurrentClassroomName(){
    this.activeClassroom.name = this.classroomNameForm.controls.name.value;
    this.closeClassroomNameEditor();
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }

  activeOverlay?:string;
  openNewClassroomModal(){
    this.activeOverlay = Overlays.NEW_CLASSROOM;
  }

  PREVENT_SIMUL_SCHED = true
  openNewAssessmentModal(sessionConfig?: any){
    try {
      if (this.PREVENT_SIMUL_SCHED){
        const sc = this.activeClassroom;
        // console.log('sc.openAssessments',sc.openAssessments  )
        if (!this.isABED() && (sc.openAssessments.length || sc.scheduledAssessments.length) ){
          this.whitelabelService.isABED() ? this.loginGuard.quickPopup('abed_text_active') : this.loginGuard.quickPopup('txt_t_active_asmt');
          return;
        }else if(this.isABED()){
          const nonScoreEntry = sc.openAssessments.filter(asmt => asmt.is_score_entry === 0);
          if(nonScoreEntry.length){
            this.loginGuard.quickPopup(this.lang.tra('abed_one_asmt_msg'));
          }
        }
      }
    }
    catch(e){}

    const classroom = this.g9demoService.classrooms.find(classroom => classroom.id === +this.classroomId);
    const semester = this.g9demoService.semesters.list.find(semester => semester.id == +(classroom?.semester || -1));
    const testWindow = this.g9demoService.testWindows.find(testWindow => testWindow.id === semester?.testWindowId);
    let assessmentsToCompare = [
      ...this.activeClassroom.scheduledAssessments || [], 
      ...this.activeClassroom.openAssessments || []
    ];
    let assessments:any[] = [];
    for(let assmnt of testWindow.assessments){
      const check = assessmentsToCompare.findIndex(a => a.slug == assmnt.asmt_type_slug);
      if(check == -1){
        assessments.push(assmnt);
      }
    }
    testWindow.assessments = assessments;
    const config = sessionConfig || {
      filter: classroom?.course_type,
      assessment_def_id: 5,
      testWindow: testWindow,
      classId: classroom?.id
    };
    this.pageModal.newModal({
      type: SessionModal.NEW_SESSION,
      config,
      isExplicitClose: true,
      saveCaption: 'Creating...',
      finish: this.newSessionModalFinish
    });

  }

  newSessionModalFinish = (config: { payload }) => {
    this.finalizeAssessmentCreation(config)
  }

  isReportsEnabled(){
    return IS_REPORTS_ENABLED
  }


  closeActiveOverlay(){
    this.activeOverlay = undefined;
  }
  openActiveAssessmentModal(){
    this.activeOverlay = Overlays.ACTIVE_ASSESSMENT;
  }
  openAssessmentSchedulerModal(){
    this.activeOverlay = Overlays.ASSESSMENT_SCHEDULER;
  }
  renderOverlayName(overlayId:string){
    switch(overlayId){
      case Overlays.NEW_CLASSROOM: return this.lang.tra('g9_create_new_group');
      case Overlays.NEW_ASSESSMENT:return this.lang.tra('g9_create_new_assess');
      case Overlays.ACTIVE_ASSESSMENT:return this.lang.tra('title_active_completed');
      default: return overlayId;
    }

  }

  getNumStudents(){

    return this.activeStudents().length;
  }

  getEntryProps(entry){
    let momentTime = moment.tz(entry.date_time_start, moment.tz.guess());
    let time = momentTime.format('h:mm');
    let timezone = momentTime.zoneAbbr();
    timezone = momentTime.format('z');
    // timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label')
    return{
      date:entry.timeDirectStart,
      time:`${time} ${timezone}`
    }
  }
  activateClickBuffer(){
    this.isClickBufferActive = true;
    setTimeout(()=>{
      this.isClickBufferActive = false;
    }, 800)
  }
  
  async classroomArchiveToggle(classroomSlot:IClassroomSlot){
    const classId = classroomSlot.id;
    let is_archived = classroomSlot.is_archived;
    if (is_archived == 1){
      is_archived = 0
    }
    else { 
      is_archived = 1
    }
    if (is_archived == 1){
      if (!confirm(`Are you sure you want to archive ${classroomSlot.name}?`)){
        return;
      }
    }
    await this.auth.apiPatch('public/educator/class-archive', classId, {is_archived})
    classroomSlot.is_archived = is_archived // todo: this is likely not going to be refreshed far up enough in the data payload
    if (is_archived == 0){
      this.isViewingArchive = false
      this.selectClassroom(classroomSlot)
    }
  }

  createNewSession(){
    // this.activeClassroom.openAssessments.push()
  }

  isShowClass(classroomSlot:IClassroomSlot){
    if (this.isViewingArchive){
      return classroomSlot.is_archived == 1
    }
    else {
      return classroomSlot.is_archived == 0
    }
    return false;
  }

  studentsDynamicTranslation(num: number){
    const singular = this.lang.tra('teacher_students_singular');
    const plural = this.lang.tra('teacher_students_plural');
    return `${num} ${(num === 1) ? singular : plural}`;
  }
  // goToAssessmentScheduler($event){
  //   this.openAssessmentSchedulerModal()
  // }
  createNewClassroom(){
    return this.loginGuard.disabledPopup();
    this.isAddingNewClassroom = true;
    // this.classroomService
    //   .createNewClassroom('cambridge-stage6', this.newClassName.value)
    //   .then(res => {
    let id = ''+randInt(1,100000000)
     let name = this.newClassName.value;
     let cCode = generateAccessCode(8);
      let classroom = {
        id:id,
        name:name,
        classCode:cCode,
        owner: 'Test',
        enableClassListingByCC: true,
        currentStudents: {list:[], map:{}},
        currentTeachers: this.dummyTeacherlist,
        timeCreated: 'Oct 19,2020.1:15pm',
        timeLastTouched: 'Oct 19,2020.1:15pm'
    }
    let classSlot = {
      id:id,
      name:name,
      curricShort:'',
      classCode:cCode,
      numStudents: classroom.currentStudents.list.length,
      numTeachers: classroom.currentTeachers.length
    }
    this.isAddingNewClassroom = false;
    this.newClassName.reset();
    this.closeActiveOverlay();
    //this.classroomClassCodeCache.set('classroom1', 'ABC');
    //this.selectClassroomById('classroom1');
  // })

    this.classroomService.saveClassroom(classroom)
    this.classroomSlots.push(classSlot)
  }

  inValidTestWindow(){
    const classroom = this.g9demoService.classrooms.find( classroom => classroom.id === +this.classroomId)
    const classSemester = this.g9demoService.semesters.map[classroom.semester]
    const classTestWindow = this.g9demoService.testWindows.find(tw => tw.id === classSemester.testWindowId)
    return (new Date(classTestWindow.date_end) < new Date ())
  }

  isPlaceHolder(){
    const classroom = this.g9demoService.classrooms.find( classroom => classroom.id === +this.classroomId)
    return classroom?.is_placeholder
  }

  isCreateSessionOverlays(){
    return this.activeOverlay == Overlays.NEW_CLASSROOM || this.activeOverlay == Overlays.NEW_ASSESSMENT || this.activeOverlay == Overlays.ACTIVE_ASSESSMENT || this.activeOverlay == Overlays.ASSESSMENT_SCHEDULER;
  }

  isAddRemoveInvigilatorOverlays(){
    return this.activeOverlay == Overlays.ADD_REMOVE_INVIGILATORS
  }

  openAddRemoveInvigilatorsModal(){
    this.activeOverlay = Overlays.ADD_REMOVE_INVIGILATORS;
  }

  showAddRemoveSchoolTeachersBtn(){
    if(this.availableTeachers.length < 1){
      this.loadInvigilators();
    }
    return !this.activeClassroom.is_invigilating;
  }

  toggleAddRemoveInvigilator(teacher){
    teacher.selected = !teacher.selected
    //const theTeacher = this.g9demoService.invigilators.find(invig => invig.contact_email == teacher.email)
    if(teacher.selected){ //add teacher as invigilator
      const theTeacher = this.g9demoService.invigilators.find(invig => invig.contact_email == teacher.email)
      this.auth.apiCreate( this.routes.EDUCATOR_INVIGILATORS,
        {
          invig_uid: teacher.uid,
        },
        this.configureQueryParams()
      ).then( res =>{
        if(theTeacher.group_id == null){
          theTeacher.group_id = this.activeClassroom.group_id
        }else{
          this.g9demoService.invigilators.push({
            uid:theTeacher.uid,
            contact_email:theTeacher.contact_email,
            first_name:theTeacher.first_name,
            last_name:theTeacher.last_name,
            group_id:this.activeClassroom.group_id
          })
        }
      })
    }else{//remove teacher from invigilator
      const theTeacher = this.g9demoService.invigilators.find(invig => invig.contact_email == teacher.email && invig.group_id == this.activeClassroom.group_id)
      this.auth.apiRemove( this.routes.EDUCATOR_INVIGILATORS,-1,this.configureQueryParams({ data: { invig_uid:teacher.uid }}))
        .then( res =>{
          theTeacher.group_id = null;
        })
    }
  }

  configureQueryParams(query?:any){
    if(query){
      return {
        query:{
          school_class_group_id: this.activeClassroom.group_id,
          ...query
        }
      }
    }
    return {
      query:{
        school_class_group_id: this.activeClassroom.group_id
      }
    }
  }

  getClassInvigilators(){
    let classInvigilatorsName=""
    const classInvigilators = this.availableTeachers.filter(teacher => teacher.selected)
    classInvigilators.forEach( invig => classInvigilatorsName = classInvigilatorsName.concat(" "+invig.name+","))
    return classInvigilatorsName;
  }

  showStartOrScheduleButton() {
    return !this.isNBED() && !this.isMBED()
  }

  isNBED() {
    return this.whitelabelService.getSiteFlag('IS_NBED');
  }

  isMBED = () => this.whitelabelService.getSiteFlag('IS_MBED');
  isEQAO = () => this.whitelabelService.getSiteFlag('IS_EQAO');
  isABED = () => this.whitelabelService.getSiteFlag('IS_ABED');

  getAccessScopeSlug(){
    if(this.isABED()){
      return 'abed_accesscose_for_sessions'
    }
    return 'for_class_section'
  }

  getTAClassInvigilatorsLabel() {
    if(this.isABED()){
      return 'ta_class_invigilators_label_ABED'
    }
    return 'ta_class_invigilators_label'
  }

  getClassHeader() {
    if (this.isNBED()) {
      return 'sa_educators_classes2_header_NBED'
    }
    if(this.isABED()){
      return 'sa_educators_classes2_header_ABED'
    }
    return 'sa_educators_classes2_header'
  }

  reset_flag:boolean = true
  detectChanges(){
    this.reset_flag = false
    this.changeDetector.detectChanges();
    this.reset_flag = true;
  }

  // Questionnaire session

  getQuestionnaireRouteLink(test_session_id) {
    return `${this.lang.c()}/educator/questionnaire/${test_session_id}`
  }

  initQuestionnaire = () => {
    this.taQuestionnaire
    .findQuestionnaireSession(AccountType.EDUCATOR, ITypeSlug.TEACHER_QUESTIONNAIRE, this.g9demoService.schoolData.group_id)
    .then((session) => {
      if(session){
        // there will be only one session for all the educators
        this.questionnaireSessionId = session[0].id
        this.questionnaireDueOn = moment(session[0].due_on).format('LL')
        this.detectChanges(); //tra-md not detecting changes
        this.haveQuestionnaireSession = true;
      }
    })
  }

  onClickQuestionnaire = () => {

    if(!this.questionnaireSessionId){
      alert("No Questionnaire session found");
      return;
    }

    this.router.navigate([this.getQuestionnaireRouteLink(this.questionnaireSessionId)]);

  }

  getQuestionnaireDueDate = () => this.questionnaireDueOn ? this.questionnaireDueOn : ''

  showResponsibilityAgreement() {
    const showRespAgreementForSlugs = ['IS_EQAO']
    const isVisible = showRespAgreementForSlugs?.some(slug => this.whitelabelService.getSiteFlag(slug));
    return isVisible;
  }

  getTeacherClassSlug(){
    if(this.whitelabelService.isABED()){
      return "abed_this_is_your_class"
    }
    return "teacher_class_this_is_your"
  }

  getStartOrScheduleSlug(){
    if(this.whitelabelService.isABED()){
      return "abed_sched_assessment"
    }
    return "g9_start_or_sched"
  }

  getActiveAssessmentSlug(){
    if(this.whitelabelService.isABED()){
      return "abed_active_assessment"
    }
    return "lbl_sessions"
  }

  getCompletedAssessmentSlug(){
    if(this.whitelabelService.isABED()){
      return "abed_completed_assessment"
    }
    return "recently_completed"
  }

  getGuideSlug() {
    return this.isABED() ? 'abed_tech_guides_public' : 'abed_tech_guides_logged_in'
  }
  getClickText() {
    return "abed_login_click_txt"
  }

  async haveUserAgreedOnConfidentiality(){
    if (this.whitelabelService.getSiteFlag('IS_SCH_CONFAGR')){
      const agreementStatus = await this.g9demoService.haveUserAgreedOnConfidentiality(this.isABED());
      console.log("agreementStatus: ", agreementStatus);
      if(agreementStatus.userHaveAccepted == false){
        let confidentialityName = '';
        let confidentialityDate = formatDate(new Date(), 'yyyy-MM-dd', 'en-US')
        const config = {
          confidentialityChecks: this.g9demoService.getConfidentialityChecks(),
          confidentialityName,
          confidentialityDate
        };
        this.pageModal.newModal({
          type: ConfidentialityAgreement.HAVE_NOT_AGREED,
          config,
          isProceedOnly: true,
          confirmationCaption: "Send Attestations",
          finish: (e) => this.acceptConfidentialityAgreement(e)
        });
      }
    }
  }

  async acceptConfidentialityAgreement(config){
    await this.g9demoService.acceptConfidentialityAgreement(this.isABED(), config.confidentialityName, config.confidentialityDate).catch(
      (e) => {
        alert("Error");
      }
    )
    await this.haveUserAgreedOnConfidentiality();
  }

  disableConfidentiality(){
    return this.g9demoService.disableConfidentiality();
  }
  
  handleCreateNewClassroom(tw_id){
      const config = {
        test_window_id: tw_id,
        key: this.whitelabelService.getSiteText('studentIdProp'),
        schl_group_id: this.classroomService.getSchlGpId(),
        className: ''
      };
      this.pageModal.newModal({
        type: ClassroomModal.NEW_CLASS,
        config,
        finish: this.newClassrommFinish
      });
  }

  newClassrommFinish = async(config: { test_window_id, className, schl_group_id, key}) => {
    if(config.className.trim() == ''){
      this.loginGuard.confirmationReqActivate({
        caption: "teach_new_class_validation",
        confirm: () => this.handleCreateNewClassroom(config.test_window_id),
        btnCancelConfig: {
          hide: true
        }
      });
      return;
    }
    try{
      const newClass = await this.auth.apiCreate(this.routes.EDUCATOR_CLASSROOM, config);
      await this.classroomService.reloadSchool();
      this.selectClassroomById(newClass.id);
    }catch(e){
      if(e.message == "DUPL_CLASS_CODE"){
        this.loginGuard.quickPopup('Could not create new grouping. (there is likely already grouping with the same name)')
      }
    }
  }
  //TODO: Have columns for is_score_entry at test session + twtar level. Test session creation will add this value based off twtar.
  isScoreEntrySession(session) {
     return session.is_score_entry == 0 ? false : true
  }
  isReportView = false;

  async toggleViewReport(){
    this.isReportView = !this.isReportView;
    if(!this.scoringReportRecords && this.isReportView){
      await this.loadScoringReports();
    }
  }

  async loadScoringReports(){
    const scoringReportRecords = await this.auth.apiFind(this.routes.EDUCATOR_LOCAL_SCORING_REPORT,{
      query: {
        schl_group_id: this.classroomService.getSchlGpId(),
        academic_year: this.selectedAcademicYear
      }
    }).catch((e)=>{
      this.loginGuard.quickPopup(e.message);
    });
    scoringReportRecords.map((record)=>{
      const titleObject = JSON.parse(record.test_window_title);
      const lang = this.lang.getCurrentLanguage();
      if(titleObject[lang] !== undefined){
        record._test_window_title = titleObject[lang];
      }
      else{
        record._test_window_title = titleObject['en'];
      }
    })
    this.scoringReportRecords = scoringReportRecords;
  }

  onGridReady(params) {
    this.gridApi = params.api;
  }

  onSelectedAcademicYear(event){
    this.classroomService.setAcademicYear(this.selectedAcademicYear);
    if(this.isReportView){
      this.loadScoringReports();
    }
  }
  
  onSelected($event: RowNodeEvent){
    const selectedRows = this.scoringReportGridOptions.api.getSelectedRows();
    if (selectedRows.length > 0){
      const selectedReport = selectedRows[0];
      this.selectedReport = selectedReport;
    }
    else {
      this.selectedReport = null;
    }
  }

  async downloadScoringReport(){
    const mw_id = this.selectedReport.mw_id;
    const tw_id = this.selectedReport.tw_id;
    const scoringReportExcel = await this.auth.apiGet(this.routes.EDUCATOR_LOCAL_SCORING_REPORT, mw_id,{
      query: {
        tw_id: tw_id,
        schl_group_id: this.classroomService.getSchlGpId()
      }
    }).catch((e)=>{
      this.loginGuard.quickPopup(e.message);
    });

    this.downFileFromBuffer(scoringReportExcel.buffer, scoringReportExcel.contentType, scoringReportExcel.filename);
  }

  downFileFromBuffer(buffer: any, contentType: string,  filename: string){
    const byteCharacters = atob(buffer); // Decode base64 string
    const byteNumbers = new Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    
    // Create a Blob from the byte array
    const blob = new Blob([byteArray], { type: contentType });

    // Create a Blob URL and open it in a new tab
    const blobUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = `${filename}.xlsx`; // The filename for downloading
    link.target = '_blank';  // Open in a new tab
    document.body.appendChild(link);
    link.click();

    // Cleanup
    document.body.removeChild(link);
    window.URL.revokeObjectURL(blobUrl); // Revoke the Blob URL after the file is opened/downloaded
  }

  isMultiSession(): boolean {
     if(!this.activeClassroom.openAssessments){
      return false;
     }
    const astms = this.activeClassroom.openAssessments.filter(asmt => asmt.is_score_entry === 0);
    return astms && astms.length > 1;
  }
}
