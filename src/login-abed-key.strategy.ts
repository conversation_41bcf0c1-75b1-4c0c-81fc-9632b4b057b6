import { ServiceAddons, Params,Paginated, Query } from '@feathersjs/feathers';
import { AuthenticationBaseStrategy } from '@feathersjs/authentication';
import { Application } from './declarations';
import { Errors } from './errors/general';
import { dbRawRead, dbRawReadReporting, dbRawWrite } from './util/db-raw';
import { STUDENT_ROLE_TYPES } from './services/public/educator/walk-in-students/walk-in-students.class';
import { SelfRegPayload } from './services/public/student/self-reg/self-reg.class';
import { dbDateNow } from './util/db-dates';
import { ABED_IS_ALLOW_DIP_CROSS_WRITING } from './constants/site-flags';
import { SQL_DIP_SC_BY_SC_AC, SQL_PASI_EXAM_PERIOD, SQL_ROLETYPES_BY_UID, SQL_SC_ACCESS_CODE, SQL_SC_FROM_AC, SQL_SELECT_USER_DOB, S<PERSON>_STU_ALIVE_CHECK, SQL_STU_DEACTIVE_CHECK, SQL_STU_SC_BY_AC, SQL_STU_SC_BY_AC_GUEST, SQL_STU_TW_UM_DIP, SQL_USERMETA_AND_ROLES_BY_STUNUM, SQL_USERMETA_BY_STUNUM, SQL_USERMETA_LANG_BY_UID } from './sql/auth';

interface Data {
  strategy:string,
  studentNumber:string,
  accessCode:string,
  abed_dob:string,
}

export const GRADE12Class = "ABED_GRADE_12";

export class RequestContext {
  
  private timestampStart:number = -1;
  private timestampLast:number = -1;
  private deltas:{slug:string, timeDiff:number}[] = [];

  constructor(){
    this.timestampLast = this.timestampStart = this.getTimestamp()
  }

  public log(slug:string){
    const t = this.getTimestamp()
    const timeDiff = t - this.timestampLast;
    this.deltas.push({slug, timeDiff});
    this.timestampLast = t
  }
  public getDeltas(){
    return this.deltas;
  }

  private getTimestamp(){
    return +(new Date())
  }

}

export class LoginABEDKeyStrategy extends AuthenticationBaseStrategy {

  app: Application;

  constructor ( app: Application) {
    super();
    this.app = app;
  }

  static getInstance (app: Application) {
    const authService = app.service('authentication');
    const instance = authService.getStrategies().find(
        (strategy) => strategy instanceof LoginABEDKeyStrategy
      ) as LoginABEDKeyStrategy;
    if (instance === undefined) {
      throw new Error('could not find an instance of the LoginABEDKeyStrategy');
    }
    return instance;
  }

  async authenticate (data:Data) {
    let user;
    const ctx = new RequestContext()
    const {isSelfReg} = <any> data;
    if (!isSelfReg){
      user = await this.getIdentity(<any> data, ctx)
    }
    else {
      const registrationPayload:SelfRegPayload = (<any> data).registrationPayload;
      const abed_dob = registrationPayload.dob
      registrationPayload.dob = registrationPayload.dob.split('-').join('')
      await this.app.service('public/student/self-reg').addSelfRegStudent(registrationPayload, ctx)
      // these students are not real students at this point, but we should still be able to identify them through
      const {studentNumber, accessCode, isSasnLogin} = registrationPayload
      user = await this.getIdentity({studentNumber, accessCode, abed_dob}, ctx)
    }
    if (user){
      return {
        authentication: { strategy: this.name },
        user
      };
    }
    else {
      throw new Errors.GeneralError('USER_UNDEFINED');
    }
  }

  async getIdentity(data: {studentNumber:string, accessCode:string, abed_dob:string, isSasnLogin?:boolean}, ctx:RequestContext){
    const {studentNumber, accessCode, abed_dob, isSasnLogin} = data;
    const nowTime = dbDateNow(this.app);

    try {
      if (!isSasnLogin){
        LoginABEDKeyStrategy.validateStudentNumber(studentNumber, ctx); // throws error if invalid
      }
  
      const { school_group_id, user_meta_keys, semester_id } = await LoginABEDKeyStrategy.getClassesFromAccessCode(this.app, accessCode);
      const uids = await LoginABEDKeyStrategy.getUidsFromStunum(this.app, ctx, school_group_id, user_meta_keys, studentNumber, false, isSasnLogin);
      let studentUid: number;
  
      if (uids.length === 0) {
        const schoolYearInfo = await this.app.service("public/abed-pasi").getPASISchoolYearFromSemesterId(semester_id);
        ctx.log('service/public/abed-pasi/getPASISchoolYearFromSemesterId')
        if (schoolYearInfo != null) {
          studentUid = await this.app.service("public/abed-pasi").trySyncASNFromPASISchema(studentNumber, nowTime, schoolYearInfo.PASISchoolYear, schoolYearInfo.testWindowId, semester_id, schoolYearInfo.typeSlug, schoolYearInfo.pasiGradeCode);
          ctx.log('service/public/abed-pasi/trySyncASNFromPASISchema')
        }
  
        else {
          // TODO: what do we throw here? Make a slug for it.
          throw new Errors.Forbidden("No corresponding SchoolYear found.", ctx.getDeltas());
        }
      }
      else {
        studentUid = uids[0];
      }
  
      // Once we reach this point, we know for a fact that this student is real, in current school or guesting from another school.
      if ( !isSasnLogin){
        await this.confirmASNIsNotASecondaryASN(studentNumber, ctx);
        await this.confirmStudentDOB(studentUid, abed_dob, ctx);
        await this.confirmStudentIsAlive(studentUid, ctx);
        await this.confirmStudentIsActivated(studentUid, ctx);
      }
      await this.confirmDiplomaRegistration(accessCode, studentUid, ctx);
  
      let accessCodeToCheck:string = accessCode;
      let classes = await LoginABEDKeyStrategy.getStudentClasses(this.app, ctx, studentUid, accessCode)
      if (classes.length === 0){
        classes = await LoginABEDKeyStrategy.processNewWalkinAndGetClasses(this.app, ctx, studentUid, school_group_id, accessCodeToCheck, semester_id);
      }
      //once we are done processing the student's connection to the class we select the classroom. Different paths:
      //a) current student is already a regular student within the class.
      //b) current student is already a walk-in student requesting to enter the class. And any previous access to another class is revoked.
      //c) current student just walked into this class for the first time.
      const classroom = classes[0];
      const uid = classroom.uid; // todo: clarify why this is needed as an override on the previously identified studentUid
      const userRecord = await LoginABEDKeyStrategy.getUserByStuUid(this.app, uid);
      ctx.log('LoginABEDKeyStrategy.getUserByStuUid')
  
      const lang = await LoginABEDKeyStrategy.getLangByStuUid(this.app, uid);
      ctx.log('LoginABEDKeyStrategy.getLangByStuUid')

      //the rest follows the same wrap-up process of login as the `login-key.strategy`
      const assistiveTech = await this.app.service('public/student/session').verifyAssistedTechAccomodation(uid, classroom.group_type);
      ctx.log('service/public/student/session/verifyAssistedTechAccomodation')
      
      const linear = await this.app.service('public/student/session').getLinear(uid, classroom.group_type);
      ctx.log('service/public/student/session/getLinear')
      
      const assessmentType = await this.app.service('public/student/session').getAssessmentType(uid, classroom.id);
      ctx.log('service/public/student/session/getAssessmentType')
      
      // const accountTypes = await dbRawRead(this.app, {uid}, SQL_ROLETYPES_BY_UID());
      const accountTypes: any[] = [];
      ctx.log('SQL_ROLETYPES_BY_UID')

      console.log({action: 'ctxTimeline', timeline: ctx.getDeltas(), data})

      const result ={
          ... userRecord,
          uid: userRecord.id,
          accountType: userRecord.account_type,
          accountTypes: accountTypes,
          sch_class_group_id: classroom.group_id,
          sch_class_id: classroom.id,
          sch_class_group_type: classroom.group_type,
          assistive_tech: assistiveTech,
          linear: linear,
          lang,
          assessmentType: assessmentType.length > 0 ? (assessmentType[0].slug ?? ''): '',
      }
      return result
    }
    catch (e:any){
      throw e;
      // throw new Errors.GeneralError(e.message, ctx.getDeltas());
    }
    throw new Errors.BadRequest();
  }


  static validateStudentNumber(studentNumber: string, ctx:RequestContext){
    if (studentNumber === '*********' || (+studentNumber === 0) ){
      throw new Errors.Forbidden('INVALID_USER_ID');
    }
    ctx.log('validateStudentNumber')
  }

  static async getClassesFromAccessCode(app: Application, accessCode: string){
    // get key from school-classes to validate if the class exists
    const schoolClassKeys  = await dbRawRead(app, {accessCode}, SQL_SC_FROM_AC())
    //if the class doesn't exist we return an error
    if(schoolClassKeys.length === 0){
      throw new Errors.NotFound('ACCESS_CODE_NOT_FOUND');
    }
    // we pull the school group id from the school_class data so that we can enforce school-wide uniqueness

    // there should only be one schoolClass found as it is by accessCode
    const scFirst = schoolClassKeys[0];
    const school_group_id = scFirst.schl_group_id;
    const sc_id = scFirst.id;
    const sc_group_id = scFirst.group_id;
    const semester_id = scFirst.semester_id;

    //we get the current user's meta records
    const user_meta_keys = schoolClassKeys.map(schlclass => schlclass.key).filter(key => key != null);
    return {
      sc_id,
      semester_id,
      sc_group_id,
      school_group_id,
      user_meta_keys,
    }
  }

  static async getStudentClasses(app: Application, ctx:RequestContext, studentUid:number, accessCodeToCheck:string){
    //first we check if this is a regular student
    let classes = await LoginABEDKeyStrategy.getStudentClassroomsByAccessCode(app, studentUid, accessCodeToCheck);
    ctx.log('LoginABEDKeyStrategy.getStudentClassroomsByAccessCode')
    if (classes.length === 0){
      // we check if the student have already been a walk-in student of the class the access code belongs to.
      //since the student can logout and log back in. We don't need to create new user roles in that case.
      classes = await LoginABEDKeyStrategy.getStudentClassroomsByAccessCode(app, studentUid, accessCodeToCheck, true);
      ctx.log('LoginABEDKeyStrategy.getStudentClassroomsByAccessCode::walkin')
    }
    return classes;
  }

  static async getStudentClassroomsByAccessCode(app: Application, uid: any, accessCodeToCheck: any, forWalkIn: boolean = false){
    const records = await dbRawRead(app, {uid, accessCodeToCheck}, SQL_STU_SC_BY_AC(forWalkIn) );
    if (records.length){
      return records
    }
    else {
      //if this student is real but we can't find them within the school, then we check if they are a guest
      return await LoginABEDKeyStrategy.getGuestStudentClassroomsByAccessCode(app, uid, accessCodeToCheck);
    }
  }

  static async getGuestStudentClassroomsByAccessCode(app: Application, uid: any, accessCodeToCheck: any){
    const records = await dbRawRead(app, {uid, accessCodeToCheck}, SQL_STU_SC_BY_AC_GUEST());
    return <any[]> records;
  }

  static async processNewWalkinAndGetClasses(app: Application, ctx:RequestContext, studentUid:number, school_group_id:number, accessCodeToCheck:string, semesterId: number){
    //by now, we have already confirmed the student is enrolled in the current school.
    //and by entering this IF clause, means we cannot find a 'schl_student' user role linking to this specific class.
    //that means this student does not have access to this class. However they are allowed to request to walk into it.
    //so we first revoke any access they might have to any other class.
    //1 student can be active in 1 class at any given point in time.
    const scRolesToRevoke = await app.service('public/educator/walk-in-students').previousStudentRoles(studentUid, school_group_id, semesterId);
    ctx.log('service/public/educator/walk-in-students/previousStudentRoles')
    //at this point, we have confirmed the student is attempting to walk into a class they have no regular access to.
    //if the student have already been a walk-in student into this class. Then nothing will be revoked.
    //if the student is attempting to walk into this class for the first time, then any previous access they have had to another class gets revoked.
    const revokedByUid = studentUid; // todo:STREAMLINE_LOGIC we wouldnt need to do this if the walkin was tracked at the class level
    for (let studentScRole of scRolesToRevoke){
      const {uid, group_id, role_type} = studentScRole;
      await app
        .service('auth/user-role-actions')
        .removeUserRoleFromGroup(uid, group_id, role_type, revokedByUid);
    }
    ctx.log('service/auth/user-role-actions/removeUserRoleFromGroup')
    //if the length is zero, that means this student has never accessed the waiting room of this class
    //so we set up a walk in user_role linking current student to the class they are requesting to enter
    const classDataFromAccessCode = await dbRawRead(app, {accessCodeToCheck}, SQL_SC_ACCESS_CODE())
    const sc = classDataFromAccessCode[0]
    await LoginABEDKeyStrategy.addWalkinStudentToClass(app, sc.id, studentUid);
    ctx.log('removeUserRoleFromGroup')
    const classes = await LoginABEDKeyStrategy.getStudentClassroomsByAccessCode(app, studentUid, accessCodeToCheck, true);
    ctx.log('LoginABEDKeyStrategy.getStudentClassroomsByAccessCode')
    return classes;
  }

  static async getUidsFromStunum(app: Application, ctx:RequestContext, school_group_id:number, user_meta_keys:string[], studentNumber:string, isStrictSchSearch:boolean = false, isSasnLogin:boolean = false){
    let userStudentNumberMetaRecords = await dbRawRead(app, {school_group_id, user_meta_keys, studentNumber}, SQL_USERMETA_AND_ROLES_BY_STUNUM())
    ctx.log('SQL_USERMETA_AND_ROLES_BY_STUNUM')
    //if we can't find the ASN meta in the school the student is attempting to login into then we check if they are enrolled anywhere else
    if (!isStrictSchSearch){
      if(userStudentNumberMetaRecords.length === 0){
        userStudentNumberMetaRecords = await dbRawRead(app, {user_meta_keys, studentNumber}, SQL_USERMETA_BY_STUNUM());
        ctx.log('SQL_USERMETA_BY_STUNUM')
        //if we can't find the ASN at all, then we throw an error later
      }
    }
    const uids = userStudentNumberMetaRecords.map(userMeta => userMeta.uid);
    if (uids.length === 0 && isSasnLogin){
      throw new Errors.NotFound('INVALID_USER_ID', ctx.getDeltas());
    }
    return uids;
  }

  static async getUserByStuUid(app: Application, uid:number){
    const user = <Paginated<any>> await app
    .service('db/read/users')
    .find({
      query: {
        id: uid
      }
    })
    return user.data[0];
  }

  static async getLangByStuUid(app: Application, uid:number){
    let lang = 'en';
    // console.log(uid);
    const langRows = await dbRawRead(app, {uid}, SQL_USERMETA_LANG_BY_UID())
    if(langRows.length > 0) {
      lang = langRows[0].value; // todo: check if case sensitive?
    }
    return lang;
  }

  static async addWalkinStudentToClass(app: Application, school_class_id:number, student_uid:number){
    const newSchoolClass = await app
      .service('db/read/school-classes')
      .get(school_class_id);
    if(newSchoolClass) {
      const newClassGroupId = newSchoolClass.group_id;
      //this is the student requesting to enter a class, meaning they are assiging themselves as a possible student, so created_by_uid is the students
      await app.service('auth/user-role-actions').assignUserRoleToGroup({
        role_type: STUDENT_ROLE_TYPES.walk_in_student_role,
        uid: student_uid,
        group_id: newClassGroupId,
        created_by_uid: student_uid,
      })
      await app.service('public/school-admin/classes').updateStudentFrenchImm(school_class_id, student_uid, student_uid);
    }
  }

  private async confirmASNIsNotASecondaryASN(ASN: string, ctx:RequestContext) {
    // do not allow login if the ASN is a secondary ASN
    const isASNSecondaryASN = await this.app.service("public/abed-pasi").isASNASecondaryASN(ASN);
    if (isASNSecondaryASN) {
      // TODO: confirm slug/error message
      // console.log("Secondary ASN found on actual login (not PASI sync).")
      throw new Errors.Forbidden("This ASN is a secondary ASN.");
    }
  }

  private async confirmStudentDOB(uid: any, inputDOB: any, ctx:RequestContext){
    //This block is for checking if the student dob is correct.
    //convert the date from (YYYY-MM-DD) format to (YYYYMMDD)
    const dob = inputDOB.split('-').join('');
    const dobRow = await dbRawRead(this.app, {uid}, SQL_SELECT_USER_DOB()) 
    ctx.log('SQL_SELECT_USER_DOB::'+uid )
    //only allow the student to move forward if the dob record was found and was matched
    if (dobRow && dobRow.length) {
      if (dobRow[0].value != dob){
        throw new Errors.NotFound('STUDENT_DOB_NOT_FOUND', ctx.getDeltas());
      }
    }
    else {
      // if no records, than its not that dob is not matching, its that the provided has no associated um
      throw new Errors.NotFound('STUDENT_META_NOT_FOUND', ctx.getDeltas());
    }
  }

  /**
   * Using the school class's accessCode and the student uid. This method checks if this class is hosting a diploma.
   * If the class does host a diploma exam then the student enrollments will be checked to see if
   *  they are registered to any diploma the class hosts.
   * If the class does not host any diploma exams then the regular logic flow continues.
   *
   * @param accessCode the accessCode of the school class to be checked for diploma exams
   * @param uid the student uid
   * @throws an 'abed_missing_registration' erorr which gets translated on WC and displays:
   *  "You are not registered for a diploma this class is hosting."
   */
  private async confirmDiplomaRegistration(accessCode: string, uid: number, ctx:RequestContext){
    const class_diplomas = await dbRawReadReporting(this.app, {accessCode, group_type:GRADE12Class}, SQL_DIP_SC_BY_SC_AC());
    await examRegistrationCheck(this.app, class_diplomas, uid);
  }

  /**
   * This function is used to lookup the `IsDeceased` pasi-sourced user_meta.
   * If we can find a user_meta with that key, then we check if the status is 1 -meaning true
   * In that case we throw an error which blocks the student from logging in.
   * In any other case -aka value of 0 or there is no matching user_meta at all- we take no action.
   * @param {number} uid - the uid of the student attempting to login.
   * @throws {Forbidden} - when the student is flagged as deceased then we cannot accept the login request.
  */
  private async confirmStudentIsAlive(uid: number, ctx:RequestContext){
    const aliveCheckRows = await dbRawRead(this.app, {uid}, SQL_STU_ALIVE_CHECK())
    if(aliveCheckRows && aliveCheckRows.length){
      aliveCheckRows.forEach((row) => {
        if(+row.value){
          throw new Errors.Forbidden('abed_deceased_student');
        }
      })
    }
  }

  /**
   * This function is used to lookup the `IsDeactivated` pasi-sourced user_meta.
   * If we can find a user_meta with that key, then we check if the status is 1 - meaning true.
   * In that case we throw an error which blocks the student from logging in.
   * In any other case - i.e. value of 0 or there is no matching user_meta at all - we take no action.
   * No action means we let the student continue.
   * @param uid - the uid of the student attempting to login.
   * @throws {} - when the student is flagged as deactivated then we cannot accept the login request.
  */
  private async confirmStudentIsActivated(uid: number, ctx:RequestContext){
    const activatedRows = await dbRawRead(this.app, {uid}, SQL_STU_DEACTIVE_CHECK())
    if(activatedRows && activatedRows.length){
      activatedRows.forEach((row) => {
        if(+row.value){
          throw new Errors.Forbidden('abed_deactivated_student');
        }
      })
    }
  }

}


export const examRegistrationCheck = async (app: Application, class_diplomas: any  [], uid: number) => {
  if(class_diplomas.length){
    // const mappedDiplomas = class_diplomas.map(dip => dip.asmt_type_slug);
    //once we find dilomas the class is hosting, that means the student that is attempting to login needs to be registered
    const matchingRegistrationsCheck = await dbRawRead(app, {uid}, SQL_STU_TW_UM_DIP() )
    //if we don't get any match from the previous query that means the student is not registration in an exam the class is hosting.
    if(matchingRegistrationsCheck.length == 0 && !ABED_IS_ALLOW_DIP_CROSS_WRITING){
      throw new Errors.Forbidden('abed_missing_registration');
    }
    else{
      for(let registration of matchingRegistrationsCheck){
        const meta = registration.meta;
        if(!meta){
          continue;
        }
        const examRefId = JSON.parse(meta).ExamRefId;
        const examPeriodCheck = await dbRawRead(app, {examRefId}, SQL_PASI_EXAM_PERIOD());
        if(examPeriodCheck.length){
          const examPeriod = examPeriodCheck[0].ExamPeriod;
          const registrationConfirmation = class_diplomas.findIndex((cd) => cd.asmt_type_slug == registration.asmt_type_slug && cd.pasi_exam_period == examPeriod);
          //if we find a match for the asmt_type_slug and examPeriod then this student should be allowed in, meaning we need to end the function here.
          if(registrationConfirmation != -1){
            return;
          }
        }
      }
      //if we finish processing all of the student's registrations and none of them match, we will return the missing registration error.
      if (!ABED_IS_ALLOW_DIP_CROSS_WRITING){
        throw new Errors.Forbidden('abed_missing_registration');
      }
    }
  }
}
