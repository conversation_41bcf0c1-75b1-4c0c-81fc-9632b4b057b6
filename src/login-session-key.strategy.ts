import { ServiceAdd<PERSON>, Params, Paginated, Query, Id } from '@feathersjs/feathers';
import { AuthenticationBaseStrategy } from '@feathersjs/authentication';
import { Application } from './declarations';
import { Errors } from './errors/general';
import { Knex } from 'knex';
import { dbRawRead, dbRawWrite } from './util/db-raw';
import moment from 'moment';
import { dbDateNow } from './util/db-dates';
import { generateSecretCode } from './util/secret-codes';
import { ITestAttempt } from './services/db/schemas/test_attempts.schema';
import { EDeliveryOption } from './services/db/schemas/test_sessions.schema';
import { randArrEntry } from './util/random';
import { generateS3DownloadUrl } from './services/upload/upload.listener';
import Axios from 'axios';
import _, { attempt } from 'lodash';
import { AssessmentType, getAssessmentTypeFromStr } from './services/public/bc-admin-coordinator/test-window/assessment-type';
import logger from './logger';

interface Data {
    strategy: string,
    stage: number,
    studentNumber: string,
    accessCode: string,
    districtSlug?: string,
    schoolSlug?: string,
    assessmentType?: string,
}

interface ITestDesignInfo {
    test_design_id: Id,
    slug: string,
    source_item_set_id: Id,
    test_form_id: Id,
    alloc_id: Id,
    famework: string,
    delivery_format: EDeliveryOption,
}

const ALLOW_LOGIN_WITHOUT_REGISTRATION = false;

const SHOW_LOGS = true;

export class LoginSessionKeyStrategy extends AuthenticationBaseStrategy {

    app: Application;

    constructor(app: Application) {
        super();
        this.app = app;
    }

    async authenticate(data: Data) {
        const user = await getIdentity(this.app, data);

        return {
            authentication: { strategy: this.name },
            user
        };
    }
}

const verifySessionPassword = async (app: Application, assessmentType: AssessmentType, testWindow: any, sessionPassword: string): Promise<string> => {
    const testSessionPasswords = <any[]>await app.service('db/read/test-session-passwords').find({
        query: {
            $select: assessmentType === AssessmentType.GRAD ? ['td_alloc_rule_id', 'valid_on'] : ['td_alloc_rule_id'],
            test_window_id: testWindow.id,
            password: sessionPassword,
        },
        paginate: false,
    })
    if (testSessionPasswords.length === 0) {
        throw new Errors.NotFound('SESSION_PASSWORD_NOT_FOUND');
    }

    let allocRuleId = null;
    if (assessmentType === AssessmentType.GRAD) {
        for (const password of testSessionPasswords) {
            if (Math.abs(moment().diff(moment(password.valid_on), 'days')) < 1) {
                allocRuleId = password.td_alloc_rule_id as number;
            }
        }
    } else {
        allocRuleId = testSessionPasswords[0].td_alloc_rule_id as number;
    }

    if (allocRuleId === null) {
        throw new Errors.NotFound('SESSION_PASSWORD_NOT_FOUND');
    }
    const allocRule = await app.service('db/read/test-window-td-alloc-rules').get(allocRuleId);
    const testSessionSlug = allocRule.type_slug as string;
    logger.debug({ testSessionSlug });
    return testSessionSlug;
}

const getRegisteredClassByPen = async (app: Application, pen: string, testSessionSlug: string, testWindow: any): Promise<any> => {
    const uidsMatchingPenRecords = <any[]>await app.service('db/read/user-metas').find({
        query: {
            $select: ['uid'],
            key_namespace: 'bced',
            key: 'StudentPEN',
            value: pen,
            $limit: 10, // todo: remove this?
        },
        paginate: false,
    })
    if (uidsMatchingPenRecords.length === 0) {
        throw new Errors.NotFound('STUDENT_NUMBER_NOT_FOUND');
    }
    const uidsMatchingPen = uidsMatchingPenRecords.map(umpr => umpr.uid) as number[];
    logger.debug({ pen, uidsMatchingPen });

    // if we have multiple uids of the same pen (duplicate), we need to pick the uid that is registered for the assessment (test session slug)
    let schoolClassRecord = null;
    for (const uid of uidsMatchingPen) {
        const schoolClasses = await dbRawRead(app, [testSessionSlug, testWindow.id, uid], `
            SELECT
                sc.id,
                sc.group_id,
                sc.schl_group_id,
                sc.schl_dist_group_id,
                urt.uid
            FROM
                school_classes sc
                    JOIN
                school_class_test_sessions scts ON sc.id = scts.school_class_id
                    AND scts.slug = ?
                    JOIN
                test_sessions ts ON ts.id = scts.test_session_id
                    AND ts.test_window_id = ?
                    JOIN
                user_roles urt ON urt.group_id = sc.group_id
                    AND urt.role_type = 'schl_student'
                    AND urt.is_revoked != 1
                    AND urt.uid = ?
            LIMIT 1
        `);
        if (schoolClasses.length === 0) {
            continue;
        }
        schoolClassRecord = schoolClasses[0];
        break;
    }

    return schoolClassRecord;

}

const addStudentToClass = async (app: Application, classGroupId: number, uid: number) => {
    const queryFields = {
        role_type: 'schl_student',
        uid,
        group_id: classGroupId,
        is_revoked: 0,
    }
    const roles = <any[]>await app.service('db/read/user-roles').find({
        query: queryFields,
        paginate: false
    });
    if (roles.length === 0) {
        await app.service('db/write/user-roles').create({
            ...queryFields,
            created_on: moment().utc().format(),
        });
    }
}

// const createTestSession = async (app: Application, classId: number, slug: string, uid: number) => {
//     const testSession = <any>await app.service('public/educator/session').createSessionForSchool(uid, {
//         isScheduled: false,
//         scheduled_time: [],
//         school_class_id: classId,
//         slug,
//         caption: 'Test Session',
//     }, AssessmentType.GRAD);
//     return testSession;
// }

const hasTestAttempt = async (app: Application, uid: number, test_session_id: number): Promise<boolean> => {
    const testAttempts = <any[]>await app.service('db/read/test-attempts').find({
        query: {
            uid,
            test_session_id,
            is_invalid: 0
        },
        paginate: false,
    });
    return testAttempts.length !== 0;
}

const hasSubmittedTestAttempt = async (app: Application, uid: number, test_window_id: number, slug: string): Promise<boolean> => {
    // todo: allow check for classroom assessment, in initial implementation, it is okay to assume that none of them will support carry over of attempts between test sessions 
    // todo: review the role of this function and document it (guessing this login strategy is only used in BCED)
    const attempts = await dbRawRead(app, {uid, test_window_id, slug}, `
        select 
            count(*) counts
        from test_attempts ta
        join test_sessions ts 
            on ta.uid = :uid
            and ta.is_submitted = 1 
            and ts.id = ta.test_session_id 
            and ts.test_window_id = :test_window_id
        join test_window_td_alloc_rules 
            twtar on twtar.type_slug = :slug
            and twtar.id = ta.twtdar_id
            and twtar.is_custom = 0 
    `);
    const count = attempts[0].counts;
    return count !== 0;
}

const createTestAttempt = async (app: Application, uid: number, test_window_id: number, test_session_id: number, lang: string | null, is_present: number = 1, twtdar_order = 0, is_invalid = 0) => {
    // const test_form_id = await this.getRandomTestFormId(test_session_id);
    const testDesign = await getTestDesignInfo(app, test_window_id, test_session_id);
    const test_form_id = <number>testDesign.test_form_id;
    const test_form_cache = await generateTestFormCache(app, testDesign);
    const createFields: Partial<ITestAttempt> = {
        uid,
        test_session_id,
        lang: <any>lang,
        section_index: 0,
        question_index: 0,
        test_form_id,
        test_form_cache,
        attempt_key: generateSecretCode(5),
        is_present,
        is_absent: 0,
        is_invalid,
        twtdar_order,
        twtdar_id: <number>testDesign.alloc_id
    }
    if (testDesign.delivery_format === EDeliveryOption.SCHOOL) {
        createFields.is_identity_verified = 1;
    }
    createFields.started_on = dbDateNow(app);
    return app
        .service('db/write/test-attempts')
        .create(createFields)
}

const createRejectedTestAttempt = async (app: Application, uid: number, test_session_id: number, lang: string | null) => {
    const createFields: Partial<ITestAttempt> = {
        uid,
        test_session_id,
        lang: <any>lang,
        attempt_key: generateSecretCode(5),
        is_invalid: 1,
    }
    createFields.started_on = dbDateNow(app);
    return app
        .service('db/write/test-attempts')
        .create(createFields)
}

const getTestDesignInfo = async (app: Application, test_window_id: number, test_session_id: number): Promise<ITestDesignInfo> => {
    const testSession = await app.service('db/read/test-sessions').get(test_session_id)
    const schoolClassTestSessions = <any[]>await app
        .service('db/read/school-class-test-sessions')
        .find({
            query: {
                test_session_id,
                is_custom: 0 
            },
            paginate: false,
        });
    logger.debug({ schoolClassTestSessions });
    const sctsRecord = schoolClassTestSessions[0]
    let slug = sctsRecord.slug;

    const allocRules = <any[]>await app.service('db/read/test-window-td-alloc-rules').find({
        query: {
            test_window_id,
            is_active: 1,
            type_slug: slug,
        },
        paginate: false,
    });
    logger.debug({
        test_window_id,
        is_active: 1,
        type_slug: slug,
    })
    if (allocRules.length === 0) {
        throw new Errors.BadGateway('NO_TEST_DESIGN_RULE_MATCH');
    }
    logger.debug({ allocRules });

    const test_design_id = allocRules[0].test_design_id;
    const testDesignRecord = await app.service('db/read/test-designs').get(test_design_id);
    if (!testDesignRecord) {
        throw new Errors.NotFound('NO_TEST_DESIGN_FOUND');
    }
    const testFormRefs = <Paginated<any>>await app
        .service('db/read/test-forms')
        .find({
            query: {
                $select: ['id', 'lang'],
                test_design_id,
                is_revoked: 0,
                $limit: 1000,
            }
        });
    const testFormSelection = randArrEntry(testFormRefs.data);
    return {
        test_design_id,
        slug,
        source_item_set_id: testDesignRecord.source_item_set_id,
        test_form_id: testFormSelection.id,
        alloc_id: allocRules[0].id,
        famework: testDesignRecord.framework,
        delivery_format: testSession.delivery_format,
    }
}

const getTestDesign = async (app: Application, file_path: string) => {
    const testDesign = await getTestDesignPromise(app, file_path);
    return <any>testDesign.data;
}

const getTestDesignPromise = async (app: Application, file_path: string) => {
    const url = generateS3DownloadUrl(file_path, 60);
    return Axios.get(url, {});
}

const loadTestFormById = async (app: Application, test_form_id: Id) => {
    const testForm = await app.service('db/read/test-forms').get(test_form_id);
    return getTestDesign(app, testForm.file_path);
}

const generateTestFormCache = async (app: Application, testDesign: ITestDesignInfo): Promise<string> => {
    let cache: { [key: string]: any } = {};
    try {
        // temp: these are temporary... just need to cycle out the forms after completing this ticket: https://bubo.vretta.com/vea/platform/vea-web-client/-/issues/2246
        const framework = JSON.parse(testDesign.famework);
        cache.testFormType = framework.testFormType;
        cache.isTimerDisabled = framework.isTimerDisabled;
        cache.referenceDocumentPages = framework.referenceDocumentPages;
        cache.helpPageId = framework.helpPageId;
    } catch (e) { }
    const testFormData = await loadTestFormById(app, testDesign.test_form_id);
    try {
        testFormData.panelModules.forEach((panelModule: { questions: number[] }) => {
            panelModule.questions = _.shuffle(panelModule.questions);
        })
        cache.panelModules = testFormData.panelModules
    } catch (e) { }
    return JSON.stringify(cache);
}

// const getAssessmentCodeFromTestSessionSlug = (slug: string): string => {
//     const assessments = ['GRAD', 'FSA'];
//     for (const assessment of assessments) {
//         if (slug.startsWith(assessment)) {
//             return slug.slice(assessment.length)
//         }
//     }
//     return '';
// }

const checkIfStudentInDistrict = async (app: Application, uid: number, districtSlug: string): Promise<boolean> => {
    const districts = await dbRawRead(app, [districtSlug], `
        select group_id from school_districts where ? like concat('%', name, '%') limit 1;
    `);
    if (districts.length === 0) return false;

    const districtGroupId = districts[0].group_id;
    const exist = <1 | 0>(await dbRawRead(app, [uid, districtGroupId], `
        select exists (
            select * from user_roles urt
            join schools s on urt.uid = ?
                and urt.is_revoked != 1
                and urt.role_type = 'schl_student'
                and urt.group_id = s.group_id
                and s.schl_dist_group_id = ?
        ) exist
    `))[0].exist;
    return exist === 1;
}

const checkIfStudentInSchool = async (app: Application, uid: number, schoolSlug: string): Promise<boolean> => {
    const schools = await dbRawRead(app, [schoolSlug], `
        select group_id from schools where ? like concat('%', name, '%') limit 1;
    `);
    if (schools.length === 0) return false;

    const schoolGroupId = schools[0].group_id;
    const exist = <1 | 0>(await dbRawRead(app, [uid, schoolGroupId], `
        select exists (
            select * from user_roles urt
            join schools s on urt.uid = ?
                and urt.is_revoked != 1
                and urt.role_type = 'schl_student'
                and urt.group_id = s.group_id
                and s.group_id = ?
        ) exist
    `))[0].exist;
    return exist === 1;
}

const isDistrictFrench = async (app: Application, districtSlug: string): Promise<boolean> => {
    const districts = await dbRawRead(app, [districtSlug], `
        select group_id, foreign_id from school_districts where ? like concat('%', name, '%') limit 1;
    `);
    if (districts.length === 0) return false;
    if (districts[0].foreign_id === 93) return true;
    return false;
}

const countValidAttemptsInAllTestWindows = async (app: Application, assessmentType: AssessmentType, uid: number, testSessionSlug: string): Promise<number> => {
    const testWindows = await app.service('public/bc-admin-coordinator/test-window').findAllTestWindows(assessmentType);
    if (testSessionSlug == "GRAD_NME10" || testSessionSlug == "GRAD_NMF10") {
      const test_window_ids = testWindows.map(tw => tw.id)
      const attempts = await dbRawRead(app, {uid,test_window_ids}, `
          select
          count(*) counts
          from test_attempts ta
          join test_sessions ts 
            on ta.uid = :uid and ta.is_invalid = 0 
            and ts.id = ta.test_session_id 
            and ts.test_window_id in (:test_window_ids)
          join test_window_td_alloc_rules twtar 
            on twtar.type_slug in ("GRAD_NME10", "GRAD_NMF10") 
            and twtar.id = ta.twtdar_id
      `);
      const count = attempts[0].counts;
      return count;
    }
    else {
      const test_window_ids = testWindows.map(tw => tw.id)
      const attempts = await dbRawRead(app, {uid, test_window_ids, testSessionSlug}, `
          select
            count(*) counts
          from test_attempts ta
          join test_sessions ts 
            on ta.uid = :uid
            and ta.is_invalid = 0 
            and ts.id = ta.test_session_id 
            and ts.test_window_id in (:test_window_ids)
          join test_window_td_alloc_rules twtar 
            on twtar.type_slug = :testSessionSlug
            and twtar.id = ta.twtdar_id
      `);
      const count = attempts[0].counts;
      return count;
    }
}

const hasDoneGradLtpWritten = async (app: Application, uid: number, grade: 10 | 12, testWindowId: number): Promise<boolean> => {
    return (await dbRawRead(app, ['GRAD_LTP' + grade + ' WRITTEN', testWindowId, uid], `
        select exists (select * from test_sessions ts join school_class_test_sessions scts on ts.id = scts.test_session_id and scts.slug = ? and ts.test_window_id = ?
        join test_attempts ta on ts.id = ta.test_session_id and ta.is_invalid != 1 and ta.is_closed = 1 and ta.uid = ?) has_attempt
    `))[0].has_attempt == 1;
}

export const getIdentity = async (app: Application, data: Data) => {
    const { studentNumber: pen, accessCode: sessionPassword, districtSlug, schoolSlug } = data;
    const assessmentType = getAssessmentTypeFromStr(data.assessmentType || 'grad');

    if (!districtSlug && !schoolSlug) throw new Errors.NotFound("DISTRICT_OR_SCHOOL_NOT_FOUND");

    // verify session password
    const testWindows = <any[]>await app.service('public/bc-admin-coordinator/test-window').findCurrentTestWindows(assessmentType);
    if (testWindows.length === 0) {
        throw new Errors.NotFound('TEST_WINDOW_NOT_FOUND');
    }
    const testWindow = testWindows[0];
    SHOW_LOGS && logger.silly({ testWindow });

    const testSessionSlug = await verifySessionPassword(app, assessmentType, testWindow, sessionPassword)

    const uidsMatchingPenRecords = <any[]>await app.service('db/read/user-metas').find({
        query: {
            $select: ['uid'],
            key_namespace: 'bced',
            key: 'StudentPEN',
            value: pen,
            $limit: 1,
        },
        paginate: false,
    })
    if (uidsMatchingPenRecords.length === 0) {
        throw new Errors.NotFound('STUDENT_NUMBER_NOT_FOUND');
    }
    let firstUid = uidsMatchingPenRecords[0].uid;


    const userMetasLang = ((<any[]>await app.service('db/read/user-metas').find({
        query: {
            uid: firstUid,
            key: 'Lang',
            key_namespace: 'bced',
            $limit: 1,
        },
        paginate: false,
    }))[0] || { value: 'en' }).value;

    // logged event CSF
    SHOW_LOGS && logger.debug({ testSessionSlug });
    SHOW_LOGS && logger.debug({ districtSlug });
    if (['GRAD_LTF12', 'GRAD_LTF12 WRITTEN', 'GRAD_LTF12 ORAL'].includes(testSessionSlug) && districtSlug && await isDistrictFrench(app, districtSlug)) {
        const ret = await createRejectedTestAttempt(app, firstUid, testWindow.id, userMetasLang);
        SHOW_LOGS && logger.debug('rejected test attempt', ret);
        let sql = `
          INSERT INTO test_window_student_codes
          (test_window_id, uid, code, code_type, created_on, test_attempt_id)
          VALUES
          (?, ?, 'ER', 'log', now(), ?)
        `;
        await dbRawWrite(app, [testWindow.id, firstUid, ret.id], sql);
        sql = `
          INSERT INTO test_window_student_codes
          (test_window_id, uid, code, code_type, created_on, test_attempt_id)
          VALUES
          (?, ?, 'CSF', 'log', now(), ?)
        `;
        await dbRawWrite(app, [testWindow.id, firstUid, ret.id], sql);
        throw new Errors.BadRequest("CSF");
    }

    // identify student by PEN
    const registeredClass = await getRegisteredClassByPen(app, pen, testSessionSlug, testWindow);
    let uid = null;
    let schlClassTestSession = null;
    let finalClassId: number = -1;
    let finalClassGroupId: number = -1;
    let schoolGroupId: number = -1;
    let districtGroupId: number = -1;

    if (ALLOW_LOGIN_WITHOUT_REGISTRATION) {
        SHOW_LOGS && logger.debug('allow login without registration');
        if (!registeredClass) {

            SHOW_LOGS && logger.debug('class does not exist, creating new class')

            throw new Errors.NotFound("Creating new class on login is temporarily disabled.");

            // // create new class for this school and this component
            // const classGroup = await this.app.service('db/write/u-groups').create({
            //     group_type: 'school_class',
            //     created_on: moment().utc().format(),
            // });
            // finalClassGroupId = classGroup.id;
            // const schoolClass = await this.app.service('db/write/school-classes').create({
            //     group_id: classGroup.id,
            //     schl_group_id: schoolGroupId,
            //     schl_dist_group_id: districtGroupId,
            //     name: schoolName + '-' + testSessionSlug,
            //     is_active: 1,
            //     created_on: moment().utc().format(),
            // });
            // finalClassId = schoolClass.id;
            // await this.createTestSession(schoolClass.id, testSessionSlug, uid);
            // await this.addStudentToClass(finalClassGroupId, uid);

        } else {
            finalClassId = registeredClass.id;
            finalClassGroupId = registeredClass.group_id;
            uid = registeredClass.uid;
            schoolGroupId = registeredClass.schl_group_id;
            districtGroupId = registeredClass.schl_dist_group_id;

            SHOW_LOGS && logger.debug('class exists, adding student to class');
            await addStudentToClass(app, finalClassGroupId, uid);
        }
    } else {
        // reject if student is not registered for this assessment
        if (!registeredClass) {
            const ret = await createRejectedTestAttempt(app, firstUid, testWindow.id, userMetasLang);
            logger.debug(ret);
            const sql = `
              INSERT INTO test_window_student_codes
              (test_window_id, uid, code, code_type, created_on, test_attempt_id)
              VALUES
              (?, ?, 'ER', 'log', now(), ?)
            `;
            await dbRawWrite(app, [testWindow.id, firstUid, ret.id], sql);
            throw new Errors.BadRequest("ER");
        }
        SHOW_LOGS && logger.debug('WK check');
        SHOW_LOGS && logger.debug(registeredClass);

        finalClassId = registeredClass.id;
        finalClassGroupId = registeredClass.group_id;
        uid = registeredClass.uid;
        schoolGroupId = registeredClass.schl_group_id;
        districtGroupId = registeredClass.schl_dist_group_id;

        schlClassTestSession = (<any[]>await app.service('db/read/school-class-test-sessions').find({
            query: {
                school_class_id: finalClassId,
                $limit: 1,
            },
            paginate: false
        }))[0];
        logger.debug('school class test session', schlClassTestSession);
    }

    if (districtSlug && !(await checkIfStudentInDistrict(app, uid, districtSlug))
        || schoolSlug && !(await checkIfStudentInSchool(app, uid, schoolSlug))) {
        throw new Errors.NotFound("NOT_ENROLLED");
    }

    if (await hasSubmittedTestAttempt(app, uid, testWindow.id, testSessionSlug)) {
        const ret = await createTestAttempt(app, uid, testWindow.id, schlClassTestSession.test_session_id, userMetasLang, 1, 0, 1);
        // throw new Errors.BadRequest("Assessment already submitted");
    }

    // logged event EX
    if ((await countValidAttemptsInAllTestWindows(app, assessmentType, uid, testSessionSlug)) >= 3) {
        const ret = await createTestAttempt(app, uid, testWindow.id, schlClassTestSession.test_session_id, userMetasLang, 1, 0, 1);
        logger.debug(ret);
        await app.service('public/bc-admin-coordinator/validation-code').createValidationCode(testWindow.id, uid, schlClassTestSession.school_class_id, 'EX', 'log', ret.id);
        // const sql = `
        //   INSERT INTO test_window_student_codes
        //   (test_window_id, uid, code, code_type, created_on, test_attempt_id)
        //   VALUES
        //   (?, ?, 'EX', 'log', now(), ?)
        // `;
        // await dbRawWrite(app, [testWindow.id, uid, ret.id], sql);
        throw new Errors.BadRequest("EX");
    }

    // logged event LFW
    if (['GRAD_LTP10 ORAL', 'GRAD_LTP12 ORAL'].includes(testSessionSlug)) {
        const grade = parseInt(testSessionSlug.substring(8, 10)) as 10 | 12;
        if (!(await hasDoneGradLtpWritten(app, uid, grade, testWindow.id))) {
            const ret = await createTestAttempt(app, uid, testWindow.id, schlClassTestSession.test_session_id, userMetasLang, 1, 0, 1);
            await app.service('public/bc-admin-coordinator/validation-code').createValidationCode(testWindow.id, uid, schlClassTestSession.school_class_id, 'LFW', 'log', ret.id);
            throw new Errors.BadRequest("LFW");
        }
    }

    logger.debug('passed all checks');

    // create test attempt
    const _hasTestAttempt = await hasTestAttempt(app, uid, schlClassTestSession.test_session_id);
    logger.debug('has test attempt', _hasTestAttempt);
    if (!_hasTestAttempt) {
        await createTestAttempt(app, uid, testWindow.id, schlClassTestSession.test_session_id, userMetasLang);
        logger.debug('test attempt')
        const subSessions = <any>await app.service('public/educator/session-sub').getSubSessions(schlClassTestSession.test_session_id);
        logger.debug('sub sessions', subSessions)
        if (subSessions.subSessionRecords.length !== 0) {
            if (!subSessions.studentStates[uid] || subSessions.studentStates[uid].active_sub_session_id === null) {
                await app.service('public/educator/session-sub').patchSession(schlClassTestSession.test_session_id, {
                    subSessionId: subSessions.subSessionRecords[0].id,
                    twtdarOrder: 0,
                    isClosing: false,
                    studentUids: [uid],
                    classId: finalClassId,
                }, {
                    query: {
                        school_class_group_id: finalClassGroupId,
                    }
                });
            } else {
                await app.service('public/educator/session-sub').patchSession(schlClassTestSession.test_session_id, {
                    subSessionId: subSessions.studentStates[uid].active_sub_session_id,
                    twtdarOrder: 0,
                    isClosing: false,
                    studentUids: [uid],
                    classId: finalClassId,
                }, {
                    query: {
                        school_class_group_id: finalClassGroupId,
                    }
                });
            }
        }
    }
    else {

    }

    const user = <Paginated<any>>await app
        .service('db/read/users')
        .find({
            query: {
                id: uid
            }
        });
    const userRecord = user.data[0];

    const langRow = await app.service('db/read/user-metas')
        .db()
        .where('key_namespace', 'bced')
        .where('key', 'lang')
        .where('uid', uid)
        .select('value');

    let lang = "en";
    if (langRow && langRow.length > 0 && langRow[0]) {
        lang = langRow[0].value;
    };

    const result = {
        ...userRecord,
        uid: userRecord.id,
        accountType: userRecord.account_type,
        sch_class_group_id: finalClassGroupId,
        lang,
        assessmentSlug: testSessionSlug,
        test_window_id: testWindow.id,
    };
    return result;
}
