import { Application } from "@feathersjs/express";

export interface IEmailOptions {
  whitelabel?: string;
  emailAddress: string;
  subject: string;
  emailTemplate: string;
  parameterMapping: { [key: string]: any };
  replyTo?: string;
}

export interface IReplyToConfig {
  defaultReplyTo: string;
  customReplyTo?: string;
}

export const DEFAULT_REPLY_TO = '<EMAIL>';

export function getReplyToAddress(config?: IReplyToConfig): string {
  if (!config) {
    return DEFAULT_REPLY_TO;
  }
  return config.customReplyTo || config.defaultReplyTo;
}

export async function sendStandardizedEmail(app: Application, options: IEmailOptions) {
  const mailCore = app.service('mail/core');
  const replyTo = getReplyToAddress({
    defaultReplyTo: DEFAULT_REPLY_TO,
    customReplyTo: options.replyTo
  });
  
  return mailCore.sendEmail({
    whitelabel: options.whitelabel,
    emailAddress: options.emailAddress,
    subject: options.subject,
    emailTemplate: options.emailTemplate,
    parameterMapping: options.parameterMapping,
    replyTo
  });
}

export async function sendReportedIssueCommentEmail(
  app: Application,
  options: {
    ric_id: number;
    contact_email: string;
    email_subject: string;
    comment: string;
  }
) {
  const { ric_id, contact_email, email_subject, comment,  } = options;

  return sendStandardizedEmail(app, {
    whitelabel: 'abed-api.vretta.com',
    emailAddress: contact_email,
    subject: `[VT#${ric_id}] `+ email_subject,
    emailTemplate: `
**A new comment has been added to your reported issue**

---

${comment}

--- 

If you have any questions, please contact us at [${getReplyToAddress()}](${getReplyToAddress()}).
    `,
    parameterMapping: {}
  });
} 