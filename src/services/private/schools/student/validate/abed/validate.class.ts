import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import moment from 'moment';
import { Application } from '../../../../../../declarations';
import { Errors } from '../../../../../../errors/general';
import {dbRawRead } from "../../../../../../util/db-raw";
import {startUnitTest} from "./../unit-tests";
import { STUDENT_ROLE_TYPES } from '../../../../../public/educator/walk-in-students/walk-in-students.class';

interface Data {}
interface ServiceOptions {}
interface Constraint {
  isSub : boolean,
  type : (value: any, data:any) => Promise<boolean>, 
  data : any,
  targetVal : any,
  bypassNull : boolean,
  warning : boolean,
  errMsg : string
}

export class Validate implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();

    // //curl "localhost:3030/private/schools/student/validate"  
    // //return await startUnitTest(this.app);

    // //OEN generation

    // const fs = require('fs');

    // let writeStream = fs.createWriteStream('OENs.txt');

    
    // for(let i =0; i <1000; i++){
    //   let result  = '000000001';
    //   const characters = '012346789';
    //   const charactersLength = characters.length;
    //   while(!(await this.C_OEN_DIGIT_CHECK(result,'')) || Number(result) <= 60000000){
    //     result  = '';
    //     for ( var j = 0; j < 9; j++ ) {
    //         result += characters.charAt(Math.floor(Math.random() * charactersLength));
    //     }
    //     //this.C_OEN_DIGIT_CHECK(result,'')
    //   }
    //   writeStream.write(result, 'utf-8');
    //   writeStream.write('\n', 'utf-8');
    //   console.log(result)  
    // }

    // // the finish event is emitted when all data has been flushed from the stream
    // writeStream.on('finish', () => {
    //   console.log('wrote all data to file');
    // });

    // // close the stream
    // writeStream.end();
    
    // return [];  
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    //reset environment
    var studentData:any = {};
    var ErrorMessages:string[]  = [];
    var warningMessages:string[] = [];

    //load data
    studentData = data;

    //init and execute constraints
    await this.initAndExecConstraints(studentData,ErrorMessages,warningMessages);

    //return results
    return {"ErrorMessages": ErrorMessages, "warningMessages": warningMessages};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async initAndExecConstraints(studentData:any, ErrorMessages:any, warningMessages:any){
    //run through all fieldname
    var constraints:{[key:number]:Constraint};

    //for all g36 g9 g10 en and fr
    constraints = await this.initConstraints('FirstName',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('LastName',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('StudentType',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('NBED_UserId',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('ABED_UserId',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('StudentGrade', studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('StudentIdentificationNumber', studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('SASN',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('DateofBirth',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('Gender',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('LearningFormat',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    
    constraints = await this.initConstraints('DateEnteredSchool',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('DateEnteredBoard',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IndigenousType',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('FirstLanguage',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('EnrolledOntario',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('OutOfProvinceResidence',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('StatusInCanada',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('Refugee',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('BornOutsideCanada',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('TimeInCanada',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IEP',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IPRCBehaviour',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IPRCAutism',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IPRCDeaf',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IPRCBlind',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IPRCGifted',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IPRCIntellectual',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IPRCDevelopmental',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IPRCMultiple',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IPRCPhysical',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IPRCSpeech',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IPRCLanguage',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IPRCLearning',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('SpecPermTemp',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('SpecPermMoved',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('SpecProvBreaks',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    //for both g3 and g6
    if(studentData['Namespace'] == 'eqao_g3'||studentData['Namespace'] == 'eqao_g6'){
      constraints = await this.initConstraints('Grade',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('JrKindergarten',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      
      constraints = await this.initConstraints('SrKindergarten',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccAssistiveTech',studentData,'Reading');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccAssistiveTech',studentData,'Writing');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccAssistiveTech',studentData,'Mathematics');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccBraille',studentData,'Reading');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccBraille',studentData,'Wrting');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccBraille',studentData,'Mathematics');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccAudioVersion',studentData,'Reading');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccAudioVersion',studentData,'Wrting');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccAudioVersion',studentData,'Mathematics');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccSign',studentData,'Reading');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccSign',studentData,'Wrting');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccSign',studentData,'Mathematics');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccScribing',studentData,'Reading');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccScribing',studentData,'Wrting');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccScribing',studentData,'Mathematics');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }
    
     //EQAO: g3, g6 and g9 only, ABED: currently all assessment types
     //ABED: removed the abed-related Namespace so that we don't require 'ClassCode' when creating an ABED student since they might be walk-in (aka with no 'ClassCode')
    if(studentData['Namespace'] == 'eqao_g3'||studentData['Namespace'] == 'eqao_g6'||studentData['Namespace'] == 'eqao_g9'){
      constraints = await this.initConstraints('ClassCode',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    } 

    //for both g9 and g10
    if(studentData['Namespace'] == 'eqao_g9'||studentData['Namespace'] == 'eqao_g10'){
      constraints = await this.initConstraints('AccAssistiveTech',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccBraille',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccAudioVersion',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccBreaks',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccSign',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccAudioResponse',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccScribing',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }

    //for g9 only
    if(studentData['Namespace'] == 'eqao_g9'){
      constraints = await this.initConstraints('MathClassWhen',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }  

    //for g10 only
    if(studentData['Namespace'] == 'eqao_g10'){
      constraints = await this.initConstraints('Grouping',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('Homeroom',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('EligibilityStatus',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('SemesterIndicator',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('LevelofStudyLanguage',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('DateOfFTE',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('Graduating',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      //constraints = await this.initConstraints('AccVideotapeResponse');
      //await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccOther',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('SpecPermIEP',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('NonParticipationStatus',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }

    //en only
    if(studentData['Language'] == 'en'){
      constraints = await this.initConstraints('ESLELD',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }

    //fr only
    if(studentData['Language'] == 'fr'){
      constraints = await this.initConstraints('ALFPANA',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }
  }

  async initConstraints(valueName:string, studentData:any, subValueName =''){
    //reset constraints list
    let constraints:{[key:number]:Constraint} = {};

    //Make the variables clear for local use.
    const Namespace = studentData['Namespace'];
    const StudentID =  studentData['StudentID'];
    const StudentGrade = studentData['StudentGrade'];
    const GroupType = studentData['GroupType'];
    const StudentIdentificationNumber = studentData['StudentIdentificationNumber'];
    const Language = studentData["Language"];
    const IsPenExemption = studentData["IsPenExemption"];
    const SchGroupID =   studentData['SchGroupID'];
    const IsPrivate  = studentData['IsPrivate']
    const FirstName =  studentData['FirstName'];
    const LastName =  studentData['LastName'];
    const StudentType =  studentData['StudentType'];
    const SASN =  studentData['SASN'];
    const DateofBirth =  studentData['DateofBirth'];
    const Gender =  studentData['Gender'];
    const ClassCode =  studentData['ClassCode'];
    const MathClassWhen =  studentData['MathClassWhen'];
    const LearningFormat =  studentData['LearningFormat'];
    const Grouping =  studentData['Grouping'];
    const Grade =  studentData['Grade'];
    const Homeroom =  studentData['Homeroom'];
    const DateEnteredSchool =  studentData['DateEnteredSchool'];
    const DateEnteredBoard =  studentData['DateEnteredBoard'];
    const JrKindergarten =  studentData['JrKindergarten'];
    const SrKindergarten =  studentData['SrKindergarten'];
    const EligibilityStatus =  studentData['EligibilityStatus'];
    const SemesterIndicator =  studentData['SemesterIndicator'];
    const LevelofStudyLanguage =  studentData['LevelofStudyLanguage'];
    const DateOfFTE =  studentData['DateOfFTE'];
    const Graduating =  studentData['Graduating'];
    const IndigenousType =  studentData['IndigenousType'];
    const FirstLanguage =  studentData['FirstLanguage'];
    const EnrolledOntario =  studentData['EnrolledOntario'];
    const OutOfProvinceResidence =  studentData['OutOfProvinceResidence'];
    const StatusInCanada =  studentData['StatusInCanada'];
    const Refugee =  studentData['Refugee'];
    const BornOutsideCanada =  studentData['BornOutsideCanada'];
    const TimeInCanada =  studentData['TimeInCanada'];
    const IEP =  studentData['IEP'];
    const IPRCBehaviour =  studentData['IPRCBehaviour'];
    const IPRCAutism =  studentData['IPRCAutism'];
    const IPRCDeaf =  studentData['IPRCDeaf'];
    const IPRCBlind =  studentData['IPRCBlind'];
    const IPRCGifted =  studentData['IPRCGifted'];
    const IPRCIntellectual =  studentData['IPRCIntellectual'];
    const IPRCDevelopmental =  studentData['IPRCDevelopmental'];
    const IPRCMultiple =  studentData['IPRCMultiple'];
    const IPRCPhysical =  studentData['IPRCPhysical'];
    const IPRCSpeech =  studentData['IPRCSpeech'];
    const IPRCLanguage =  studentData['IPRCLanguage'];
    const IPRCLearning =  studentData['IPRCLearning'];
    const AccAssistiveTech =  studentData['AccAssistiveTech'+subValueName];
    const AccAssistiveTechRead =  studentData['AccAssistiveTechRead'];
    const AccAssistiveTechWriting =  studentData['AccAssistiveTechWriting'];
    const AccAssistiveTechMathematics =  studentData['AccAssistiveTechMathematics'];
    const AccBraille =  studentData['AccBraille'+subValueName];
    const AccAudioVersion =  studentData['AccAudioVersion'+subValueName];
    const AccBreaks =  studentData['AccBreaks'];
    const AccSign =  studentData['AccSign'+subValueName];
    const AccAudioResponse =  studentData['AccAudioResponse'];
    const AccVideotapeResponse =  studentData['AccVideotapeResponse'];
    const AccScribing =  studentData['AccScribing'+subValueName];
    const AccOther =  studentData['AccOther'];
    const SpecPermIEP =  studentData['SpecPermIEP'];
    const SpecPermTemp =  studentData['SpecPermTemp'];
    const SpecPermMoved =  studentData['SpecPermMoved'];
    const ESLELD =  studentData['ESLELD'];
    const ALFPANA =  studentData['ALFPANA'];
    const SpecProvBreaks =  studentData['SpecProvBreaks'];
    const NonParticipationStatus =  studentData['NonParticipationStatus'];

    //init valueName constraint
    switch(valueName){
      case 'FirstName':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,       data: null,   targetVal: FirstName,  bypassNull: false,  warning: false,  errMsg: 'FirstName_NOT_NULL'};
        constraints [2]  = {isSub: false, type: this.C_CHAR_LEN_RANGE, data: [1,50], targetVal: FirstName,  bypassNull: false,  warning: false,  errMsg: 'FirstName_CHAR_LEN_RANGE'};
        break;
      case 'LastName':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,       data: null,   targetVal: LastName,  bypassNull: false,  warning: false,  errMsg: 'LastName_NOT_NULL'};
        constraints [2]  = {isSub: false, type: this.C_CHAR_LEN_RANGE, data: [1,50], targetVal: LastName,  bypassNull: false,  warning: false,  errMsg: 'LastName_CHAR_LEN_RANGE'};
        break;
      case 'StudentType':
        constraints [1]  = {isSub: false, type: this.C_NULL_OK,        data: null,  targetVal: StudentType,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [2]  = {isSub: false, type: this.C_CHAR_LEN_RANGE, data: [1,1], targetVal: StudentType,  bypassNull: true,   warning: false,  errMsg: 'StudentType_CHAR_LEN_RANGE'};
        constraints [3]  = {isSub: false, type: this.C_VAL_RANGE,      data: [1,7], targetVal: StudentType,  bypassNull: true,   warning: false,  errMsg: 'StudentType_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,        data: [11,12], targetVal: constraints,   bypassNull: false,  warning: false, errMsg: 'StudentType_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,      data: [1,1],   targetVal: IsPrivate,        bypassNull: false,  warning: true,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,             data: [13,14], targetVal: constraints,  bypassNull: false,  warning: true,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_VAL_RANGE,      data: [3,4],   targetVal: StudentType,  bypassNull: false,  warning: true,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,        data: null,   targetVal: StudentType,  bypassNull: false,  warning: true,  errMsg: ''};
        break;    
      case 'StudentIdentificationNumber':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,                data: null,                       targetVal: StudentIdentificationNumber,          bypassNull: false,  warning: false, errMsg: 'Student_Identification_Number_NOT_NULL'};
        constraints [2]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,          data: [9, 9],                     targetVal: StudentIdentificationNumber,          bypassNull: false,  warning: false, errMsg: 'Student_Identification_Number_CHAR_LEN_RANGE'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_PATTERN,            data: /^\d{9}$/,                  targetVal: StudentIdentificationNumber,          bypassNull: false,  warning: false, errMsg: 'Student_Identification_Number_CHAR_LEN_RANGE'};
        constraints [18] = {isSub: false, type: this.C_ABED_STUDENT_ID_UNIQUE,  data: {StudentID, GroupType, SchGroupID, Namespace},targetVal: StudentIdentificationNumber,          bypassNull: false,  warning: false, errMsg: 'Student_Identification_Number_UNIQUE'};
        break;
      case 'StudentGrade':
        constraints [1]  = {isSub: false,  type: this.C_NOT_NULL,        data: null,        targetVal: StudentGrade,  bypassNull: false,   warning: false,  errMsg: 'StudentGrade_NOT_NULL'};
        constraints [2] =  {isSub: false,  type: this.C_VAL_RANGE,       data: [1, 12],     targetVal: StudentGrade,  bypassNull: false,   warning: false,  errMsg: 'StudentGrade_VAL_RANGE'};
        break;
      case 'ClassCode':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,       data: null,   targetVal: ClassCode,  bypassNull: false,   warning: false,  errMsg: 'ClassCode_NOT_NULL'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE, data: [1,20], targetVal: ClassCode,  bypassNull: false,   warning: false,  errMsg: 'ClassCode_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VALIDE_CLASS,   data: null,   targetVal: ClassCode,  bypassNull: false,   warning: false,  errMsg: 'ClassCode_VALIDE_CLASS'};
        break;
      case 'Grouping':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,        data: null,   targetVal: Grouping,  bypassNull: false,   warning: false,  errMsg: 'Grouping_NOT_NULL'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,25], targetVal: Grouping,  bypassNull: false,   warning: false,  errMsg: 'Grouping_CHAR_LEN_RANGE'};
        constraints [13] = {isSub: false, type: this.C_VALIDE_GRPING,   data: null,   targetVal: Grouping,  bypassNull: false,   warning: false,  errMsg: 'Grouping_VALIDE_GRPING'};
        constraints [14] = {isSub: false, type: this.C_VALIDE_GRPING_2, data: null,   targetVal: Grouping,  bypassNull: false,   warning: false,  errMsg: 'Grouping_VALIDE_GRPING_2'};
        break;
      case 'DateofBirth':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,          data: null,  targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_NOT_NULL'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,    data: [8,8], targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_CHAR_LEN_RANGE'};
        constraints [10] = {isSub: false, type: this.C_DATE_VALIDATE,     data: null,  targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_DATE_VALIDATE'};

        // We have no hard requirements for the student's age right now. For now, we will check that the student's age is between 0 and 150.
        constraints [11] = {isSub: false, type: this.C_DATE_DIFF_GREATER, data: 0,     targetVal: DateofBirth,  bypassNull: false,  warning: true,  errMsg: 'DateofBirth_DATE_DIFF_GREATER_ABED'};
        constraints [12] = {isSub: false, type: this.C_DATE_DIFF_SMALLER, data: 150,   targetVal: DateofBirth,  bypassNull: false,  warning: true,  errMsg: 'DateofBirth_DATE_DIFF_SMALLER_ABED'};
        break;
      case 'IEP':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,     targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],    targetVal: IEP,               bypassNull: true,   warning: false,  errMsg: 'IEP_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: true,   warning: false,  errMsg: 'IEP_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [20] = {isSub: false, type: this.C_IF_THEN,         data: [21,22],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        constraints [21] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [30] = {isSub: false, type: this.C_IF_THEN,         data: [31,32],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        constraints [31] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [32] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [40] = {isSub: false, type: this.C_IF_THEN,         data: [41,42],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        constraints [41] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [42] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [50] = {isSub: false, type: this.C_IF_THEN,         data: [51,52],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        constraints [51] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [52] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [60] = {isSub: false, type: this.C_IF_THEN,         data: [61,62],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        constraints [61] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [62] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [70] = {isSub: false, type: this.C_IF_THEN,         data: [71,72],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        constraints [71] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [72] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [80] = {isSub: false, type: this.C_IF_THEN,         data: [81,82],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        constraints [81] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [82] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [90] = {isSub: false, type: this.C_IF_THEN,         data: [91,92],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        constraints [91] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [92] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [100] ={isSub: false, type: this.C_IF_THEN,         data: [101,102],targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        constraints [101] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [102] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [110] ={isSub: false, type: this.C_IF_THEN,         data: [111,112],targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        constraints [111] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [112] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [120] ={isSub: false, type: this.C_IF_THEN,         data: [121,122],targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        constraints [121] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [122] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [130] ={isSub: false, type: this.C_IF_THEN,         data: [131,132],targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE_2'};
        constraints [131] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: SpecPermIEP,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [132] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [140] ={isSub: false, type: this.C_IF_THEN,         data: [141,142],targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE_3'};
        constraints [141] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: SpecPermTemp,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [142] ={isSub: true,  type: this.C_IS_NULL,         data: null,     targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [150] ={isSub: false, type: this.C_IF_THEN,         data: [151,152],targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE_4'};
        constraints [151] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: SpecPermMoved,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [152] ={isSub: true,  type: this.C_IS_NULL,         data: null,     targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''}; 
        break;
      case 'IPRCBehaviour':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCBehaviour,     bypassNull: true,   warning: false,  errMsg: 'IPRCBehaviour_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCBehaviour,     bypassNull: true,   warning: false,  errMsg: 'IPRCBehaviour_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRCBehaviour_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'IPRCAutism':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCAutism,        bypassNull: true,   warning: false,  errMsg: 'IPRCAutism_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCAutism,        bypassNull: true,   warning: false,  errMsg: 'IPRCAutism_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRCAutism_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'IPRCDeaf':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCDeaf,          bypassNull: true,   warning: false,  errMsg: 'IPRCDeaf_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCDeaf,          bypassNull: true,   warning: false,  errMsg: 'IPRCDeaf_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRCDeaf_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'IPRCBlind':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCBlind,         bypassNull: true,   warning: false,  errMsg: 'IPRCBlind_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCBlind,         bypassNull: true,   warning: false,  errMsg: 'IPRCBlind_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRCBlind_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'IPRCGifted':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCGifted,        bypassNull: true,   warning: false,  errMsg: 'IPRCGifted_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCGifted,        bypassNull: true,   warning: false,  errMsg: 'IPRCGifted_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRCGifted_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'IPRCIntellectual':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCIntellectual,  bypassNull: true,   warning: false,  errMsg: 'IPRCIntellectual_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCIntellectual,  bypassNull: true,   warning: false,  errMsg: 'IPRCIntellectual_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRCIntellectual_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'IPRCDevelopmental':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCDevelopmental, bypassNull: true,   warning: false,  errMsg: 'IPRCDevelopmental_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCDevelopmental, bypassNull: true,   warning: false,  errMsg: 'IPRCDevelopmental_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRCDevelopmental_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'IPRCMultiple':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCMultiple,      bypassNull: true,   warning: false,  errMsg: 'IPRCMultiple_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCMultiple,      bypassNull: true,   warning: false,  errMsg: 'IPRCMultiple_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRCMultiple_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'IPRCPhysical':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCPhysical,      bypassNull: true,   warning: false,  errMsg: 'IPRCPhysical_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCPhysical,      bypassNull: true,   warning: false,  errMsg: 'IPRCPhysical_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRCPhysical_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'IPRCSpeech':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCSpeech,        bypassNull: true,   warning: false,  errMsg: 'IPRCSpeech_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCSpeech,        bypassNull: true,   warning: false,  errMsg: 'IPRCSpeech_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRCSpeech_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'IPRCLanguage':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCLanguage,      bypassNull: true,   warning: false,  errMsg: 'IPRCLanguage_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCLanguage,      bypassNull: true,   warning: false,  errMsg: 'IPRCLanguage_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRCLanguage_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'IPRCLearning':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCLearning,      bypassNull: true,   warning: false,  errMsg: 'IPRCLearning_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCLearning,      bypassNull: true,   warning: false,  errMsg: 'IPRCLearning_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRCLearning_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'AccAssistiveTech':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccAssistiveTech,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccAssistiveTech,  bypassNull: true,   warning: false,  errMsg: 'AccAssistiveTech_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,2],         targetVal: AccAssistiveTech,  bypassNull: true,   warning: false,  errMsg: 'AccAssistiveTech_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: 'AccAssistiveTech_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccAssistiveTech,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,       bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,     bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'AccBraille':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccBraille,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccBraille,        bypassNull: true,   warning: false,  errMsg: 'AccBraille_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,4],         targetVal: AccBraille,        bypassNull: true,   warning: false,  errMsg: 'AccBraille_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: 'AccBraille_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccBraille,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,       bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [20] = {isSub: false, type: this.C_IF_THEN,         data: [21,22],       targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: 'AccBraille_VAL_RANGE_2'};
        constraints [21] = {isSub: true,  type: this.C_VAL_RANGE,       data: [3,4],         targetVal: AccBraille,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_LANG_IS_ENG,     data: null,          targetVal: Language,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [30] = {isSub: false, type: this.C_IF_THEN,         data: [31,32],       targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: 'AccBraille_VAL_RANGE_2'};
        constraints [31] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,2],         targetVal: AccBraille,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [32] = {isSub: true,  type: this.C_LANG_IS_FRN,     data: null,          targetVal: Language,          bypassNull: false,  warning: false,  errMsg: ''}; 
        break;
      case 'AccAudioVersion':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccAudioVersion,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccAudioVersion,  bypassNull: true,   warning: false,  errMsg: 'AccAudioVersion_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccAudioVersion,  bypassNull: true,   warning: false,  errMsg: 'AccAudioVersion_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints, bypassNull: false,  warning: false,  errMsg: 'AccAudioVersion_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccAudioVersion,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,     bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,    bypassNull: false,  warning: false,  errMsg: ''};  
        break;  
      case 'AccBreaks':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccBreaks,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccBreaks,        bypassNull: true,   warning: false,  errMsg: 'AccBreaks_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccBreaks,        bypassNull: true,   warning: false,  errMsg: 'AccBreaks_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: 'AccBreaks_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccBreaks,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,     bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,    bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'AccSign':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccSign,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccSign,          bypassNull: true,   warning: false,  errMsg: 'AccSign_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccSign,          bypassNull: true,   warning: false,  errMsg: 'AccSign_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: 'AccSign_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccSign,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,     bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,    bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'AccAudioResponse':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccAudioResponse, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccAudioResponse, bypassNull: true,   warning: false,  errMsg: 'AccAudioResponse_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccAudioResponse, bypassNull: true,   warning: false,  errMsg: 'AccAudioResponse_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: 'AccAudioResponse_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccAudioResponse, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,     bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,    bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'AccVideotapeResponse':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccVideotapeResponse, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccVideotapeResponse, bypassNull: true,   warning: false,  errMsg: 'AccVideotapeResponse_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccVideotapeResponse, bypassNull: true,   warning: false,  errMsg: 'AccVideotapeResponse_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'AccVideotapeResponse_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccVideotapeResponse, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,                  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,         bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,        bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'AccScribing':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccScribing,          bypassNull: true,   warning: false,  errMsg: 'AccScribing_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccScribing,          bypassNull: true,   warning: false,  errMsg: 'AccScribing_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'AccScribing_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,                  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,         bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,        bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'AccOther':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccOther,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccOther,          bypassNull: true,   warning: false,  errMsg: 'AccOther_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccOther,          bypassNull: true,   warning: false,  errMsg: 'AccOther_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'AccOther_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccOther,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,       bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,      bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,     bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'SpecPermIEP':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                      targetVal: SpecPermIEP,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                     targetVal: SpecPermIEP,          bypassNull: true,   warning: false,  errMsg: 'SpecPermIEP_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                     targetVal: SpecPermIEP,          bypassNull: true,   warning: false,  errMsg: 'SpecPermIEP_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                   targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'SpecPermIEP_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: SpecPermIEP,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16,17,18,19,20,21,22,23,24],  targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBraille,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBreaks,            bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSign,              bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioResponse,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccVideotapeResponse, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccOther,             bypassNull: false,  warning: false,  errMsg: ''};
        constraints [20] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersion,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [21] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTech,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechRead, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [23] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechWriting,       bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [24] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechMathematics, bypassNull: false,  warning: false,  errMsg: ''};    
        break;
      case 'SpecPermTemp':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                      targetVal: SpecPermTemp,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                     targetVal: SpecPermTemp,         bypassNull: true,   warning: false,  errMsg: 'SpecPermTemp_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                     targetVal: SpecPermTemp,         bypassNull: true,   warning: false,  errMsg: 'SpecPermTemp_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                   targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'SpecPermTemp_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: SpecPermTemp,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16,17,18,19,20,21,22,23,24], targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBraille,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBreaks,            bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSign,              bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioResponse,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccVideotapeResponse, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccOther,             bypassNull: false,  warning: false,  errMsg: ''};
        constraints [20] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersion,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [21] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTech,      bypassNull: false,  warning: false,  errMsg: ''};   
        constraints [22] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechRead, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [23] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechWriting,       bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [24] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechMathematics, bypassNull: false,  warning: false,  errMsg: ''}; 
        break;
      case 'SpecPermMoved':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                      targetVal: SpecPermMoved,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                     targetVal: SpecPermMoved,        bypassNull: true,   warning: false,  errMsg: 'SpecPermMoved_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                     targetVal: SpecPermMoved,        bypassNull: true,   warning: false,  errMsg: 'SpecPermMoved_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                   targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'SpecPermMoved_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: SpecPermMoved,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16,17,18,19,20,21,22,23,24], targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBraille,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBreaks,            bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSign,              bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioResponse,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccVideotapeResponse, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccOther,             bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersion,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [21] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTech,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechRead, bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [23] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechWriting,       bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [24] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechMathematics, bypassNull: false,  warning: false,  errMsg: ''}; 
        break;
      case 'ESLELD':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                   targetVal: ESLELD,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                  targetVal: ESLELD,        bypassNull: true,   warning: false,  errMsg: 'ESLELD_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                  targetVal: ESLELD,        bypassNull: true,   warning: false,  errMsg: 'ESLELD_VAL_RANGE'};                 
        break;
      case 'ALFPANA':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                   targetVal: ALFPANA,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                  targetVal: ALFPANA,        bypassNull: true,   warning: false,  errMsg: 'ALFPANA_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,2],                  targetVal: ALFPANA,        bypassNull: true,   warning: false,  errMsg: 'ALFPANA_VAL_RANGE'};                 
        break;
      case 'SpecProvBreaks':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                   targetVal: SpecProvBreaks,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                  targetVal: SpecProvBreaks,       bypassNull: true,   warning: false,  errMsg: 'SpecProvBreaks_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                  targetVal: SpecProvBreaks,       bypassNull: true,   warning: false,  errMsg: 'SpecProvBreaks_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'SpecProvBreaks_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                   targetVal: SpecProvBreaks,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14],                targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                   targetVal: ESLELD,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                   targetVal: ALFPANA,              bypassNull: false,  warning: false,  errMsg: ''};  
        break;
      case 'NonParticipationStatus':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                       targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                      targetVal: NonParticipationStatus,  bypassNull: true,   warning: false,  errMsg: 'NonParticipationStatus_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,3],                      targetVal: NonParticipationStatus,  bypassNull: true,   warning: false,  errMsg: 'NonParticipationStatus_VAL_RANGE'};                 
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [12,13],                    targetVal: constraints,        bypassNull: false,  warning: true,   errMsg: 'NonParticipationStatus_VAL_VALIDATE'};
        constraints [12] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                       targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_AND,             data: [14,15,16,17,18,19,20,21],  targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccAssistiveTech,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccBraille,              bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccBreaks,               bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccSign,                 bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccAudioResponse,        bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccVideotapeResponse,    bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccScribing,             bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccOther,                bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [30] = {isSub: false, type: this.C_IF_THEN,         data: [31,32],                    targetVal: constraints,             bypassNull: false,  warning: true,   errMsg: 'NonParticipationStatus_VAL_VALIDATE_2'};
        constraints [31] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                       targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [32] = {isSub: true,  type: this.C_AND,             data: [33,34,35],                 targetVal: constraints,             bypassNull: false,  warning: false,  errMsg: ''};
        constraints [33] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: SpecPermIEP,             bypassNull: false,  warning: false,  errMsg: ''};
        constraints [34] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: SpecPermTemp,            bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [35] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: SpecPermMoved,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [40] = {isSub: false, type: this.C_IF_THEN,         data: [41,42],                    targetVal: constraints,             bypassNull: false,  warning: true,   errMsg: 'NonParticipationStatus_VAL_VALIDATE_3'};
        constraints [41] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                       targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [42] = {isSub: true,  type: this.C_AND,             data: [45],                       targetVal: constraints,             bypassNull: false,  warning: false,  errMsg: ''};
        //constraints [43] = {isSub: true,  type: this.C_IS_NULL,       data: null,                       targetVal: ESLELD,                  bypassNull: false,  warning: false,  errMsg: ''};
        //constraints [44] = {isSub: true,  type: this.C_IS_NULL,       data: null,                       targetVal: ALFPANA,                 bypassNull: false,  warning: false,  errMsg: ''};  
        constraints [45] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: SpecProvBreaks,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [50] = {isSub: false, type: this.C_IF_THEN,         data: [51,52],                    targetVal: constraints,             bypassNull: false,  warning: false,  errMsg: 'NonParticipationStatus_VAL_VALIDATE_4'};
        constraints [51] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],                      targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [52] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],                      targetVal: IEP,                     bypassNull: false,  warning: false,  errMsg: ''};  
        break;                                           
      default:
        break;
    }
    return constraints;
  }

  async executeConstraints(constraints:any,ErrorMessages:any, warningMessages:any){ 
    for (let index in constraints){
      const indexNum = Number(index);
      const constraint = constraints[indexNum]
      if(!constraint.isSub){ // execute constraint if not sub
        if(!(await this.executeConstraint(constraint))){//constraint check does not pass
          if(constraint.warning){ //add to warning message if its a warning
            warningMessages.push (constraint.errMsg)
          }else{ //add to error message if its a error
            ErrorMessages.push (constraint.errMsg)
            return; //skip the rest of constraint check. since an error occure already
          }
        }  
      } 
    } 
  }

  async executeConstraint(constraint : Constraint):Promise<boolean>{
    if(await this.C_IS_NULL(constraint.targetVal, constraint.data) && constraint.bypassNull){ //bypass if null
      return true;
    }
    try{
      return await constraint.type(constraint.targetVal, constraint.data);
    }catch(e){
      return false;
    }  
  }

  C_NOT_NULL = async (value:any, data:any ) :Promise<boolean> => {
    return value != null && value.length > 0;
  }

  C_IS_NULL = async (value:any, data:any ) :Promise<boolean> => {
    return value == undefined || value == null || value.length == 0;
  }

  C_NULL_OK = async (value:any, data:any ) :Promise<boolean> => {
    return value == undefined || value != null || value == null;
  }
  
  C_CHAR_LEN_RANGE = async (value:any, data:any ) :Promise<boolean> => {
    return value.length >= data[0] && value.length <= data[1]; 
  }

  C_VAL_RANGE = async (value:any, data:any ) :Promise<boolean> => {
    return Number(value) >= data[0] && Number(value) <= data[1];
  }

  C_CHAR_PATTERN = async (value:any, data:any ) :Promise<boolean> => {
    return data.test(value);
  }
  
  C_OR = async (value:any, data:any ) :Promise<boolean> => {
    for (let i =0; i<data.length;i++){
      const index = data[i];
      const constraint = value[index];
      if(await this.executeConstraint(constraint)){ //constraint pass check
        return true
      } 
    }
    return false;
  }

  C_AND = async (value:any, data:any ) :Promise<boolean> => {
    for (let i =0; i<data.length;i++){
      const index = data[i];
      const constraint = value[index];
      if(! (await this.executeConstraint(constraint))){ //constraint does not pass check
        return false;
      } 
    }
    return true;
  }

  C_IF_THEN = async (value:any, data:any ) :Promise<boolean> => {
    const firstConstraint = value[data[0]];
    const secondConstraint = value[data[1]];
    if(await this.executeConstraint(firstConstraint) && ! await this.executeConstraint(secondConstraint)){ 
      return false;
    }
    return true;
  }

  C_DATE_VALIDATE = async (value:any, data:any ) :Promise<boolean> => {
    return moment(value, "YYYYMMDD", true).isValid() && moment(value, "YYYYMMDD", true) < moment()
  }

  C_DATE_DIFF_GREATER = async (value:any, data:any ) :Promise<boolean> => {
    if(value == undefined || value == null || value == ''){
      return true;
    }
    // return Math.floor((Date.now() - moment(value, 'YYYYMMDD').toDate().getTime()) / 3.15576e+10) > data
    let diff = moment().diff(moment(value, 'YYYYMMDD'), 'year', true);
    return moment().diff(moment(value, 'YYYYMMDD'), 'year', true) > data;
  }

  C_IS_PEN_EXEMPTION = async (value:any, data:any ) :Promise<boolean> => {
    return +value === 1;
  }

  C_DATE_DIFF_SMALLER = async (value:any, data:any ) :Promise<boolean> => {
    if(value == undefined || value == null || value == ''){
      return true;
    }
    // return Math.floor((Date.now() - moment(value, 'YYYYMMDD').toDate().getTime()) / 3.15576e+10) < data
    let diff = moment().diff(moment(value, 'YYYYMMDD'), 'year', true);
    return moment().diff(moment(value, 'YYYYMMDD'), 'year', true) < data;
  }
  C_DATE_SMALLER = async (value:any, data:any ) :Promise<boolean> => {
    if(value == undefined || value == null || value == ''|| data == undefined || data == null || data == ''){
      return true;
    }
    return moment(value, 'YYYYMMDD').toDate().getTime() <= moment(data, 'YYYYMMDD').toDate().getTime()
  }  

  C_OEN_DIGIT_CHECK = async (value:any, data:any ) :Promise<boolean> => {
    if(value == '000000000'){
      return true;
    }
    /*
    from  OENCheckDigit.sql  code source: https://bubo.vretta.com/vea/project-management/eqao-project-deliverables/-/issues/126#note_191642
    set @retValue = 10 - (dbo.OENMask(convert(int,substring(@prmOEN,1,2))+1)+
		                      dbo.OENMask(convert(int,substring(@prmOEN,3,2))+1)+
		                      dbo.OENMask(convert(int,substring(@prmOEN,5,2))+1)+
		                      dbo.OENMask(convert(int,substring(@prmOEN,7,2))+1)) % 10
		  if @retValue = 10 set @retValue = 0
    */
    var retValue = 10 - (await this.OENMast(Number(value.substring(0,2))+1)+
                           await this.OENMast(Number(value.substring(2,4))+1)+
                           await this.OENMast(Number(value.substring(4,6))+1)+
                           await this.OENMast(Number(value.substring(6,8))+1)) % 10
    if(retValue == 10){
      retValue =0;
    }
    const comparValue = Number(value.substring(8,9));
    if(retValue != comparValue){
      return false;
    }
    return true;
  }

  C_VALIDE_GRPING = async (value:any, data:any ) :Promise<boolean> => {
    const studentID = Number(data);
    var records = await dbRawRead(this.app, [
      value
    ], `
        select id
        from school_classes as sc
        where sc.name = ?
    `)
    if(records.length > 0){
      return true;
    }
    return false;
  }

  C_VALIDE_GRPING_2 = async (value:any, data:any ) :Promise<boolean> => {
    const studentID = Number(data);
    var records = await dbRawRead(this.app, [
      value
    ], `
      select sc.id
      from school_classes sc
      join school_semesters sm
        on sm.id = sc.semester_id
      join test_windows tw
        on tw.id =  sm.test_window_id
      and tw.date_end > now() 
      where sc.name = ?
    `)
    if(records.length > 0){
      return true;
    }
    return false;
  }

  C_VALIDE_CLASS = async (value:any, data:any ) :Promise<boolean> => {
    const studentID = Number(data);
    var records = await dbRawRead(this.app, [
      value
    ], `
        select id
        from school_classes as sc
        where sc.name = ?
    `)
    if(records.length > 0 ){
      return true;
    }
    return false;
  }

  C_VALIDE_CLASS_2 = async (value:any, data:any ) :Promise<boolean> => {
    const studentID = Number(data);
    var records = await dbRawRead(this.app, [
      value
    ], `
      select sc.id
        from school_classes sc
        join school_semesters sm
          on sm.id = sc.semester_id
        join test_windows tw
          on tw.id =  sm.test_window_id
         and tw.date_end > now() 
       where sc.name = ?
    `)
    if(records.length > 0 ){
      return true;
    }
    return false;
  }

  C_VALIDE_HOMEROOM = async (value:any, data:any ) :Promise<boolean> => {
    //need to implement this
    if(value == 'Invalid HomeRoom Name')
      return false;
    return true;
  }

  C_LANG_IS_ENG = async (value:any, data:any ) :Promise<boolean> => {
    if(value === 'en'){
      return true;
    }
    return false;
  }
  
  C_LANG_IS_FRN = async (value:any, data:any ) :Promise<boolean> => {
    if(value === 'fr'){
      return true;
    }
    return false;
  } 

  C_ABED_STUDENT_ID_UNIQUE = async (value: any, data: any) : Promise<boolean> => {
    const studentID = Number(data.StudentID);
    const GroupType = data.GroupType;
    const Value = value;
    const SchGroupID = Number(data.SchGroupID);

    const records = await dbRawRead(this.app, [
      Value,
      SchGroupID
    ], `
      select um.uid as uid, um.value, IFNULL(um2.value , 0) as isSameGrade
        from user_metas as um
        join user_roles ur
          on ur.uid = um.uid
          and ur.role_type = 'schl_student'
          and ur.is_revoked = 0
        join school_classes sc
          on sc.group_id = ur.group_id
          and sc.is_active = 1
        join school_semesters sm
          on sm.id = sc.semester_id
        join test_windows tw
          on tw.id = sm.test_window_id
          and tw.is_active = 1  
        LEFT JOIN user_metas AS um2
          ON um.uid = um2.uid 
        where um.key = 'StudentIdentificationNumber'
          and um.value = ?
          and sc.schl_group_id = ?    
      `)

    // we don't need to check for class-less students anymore, we only care to check for class-ed students within a given school

    // const walkin_records = await dbRawRead(this.app, [
    //   Value,
    //   SchGroupID
    // ], `
    //   select um.uid as uid, um.value
    //     from user_metas as um
    //     join user_roles ur
    //       on ur.uid = um.uid
    //       and ur.role_type in ('${STUDENT_ROLE_TYPES.walk_in_student_role}', '${STUDENT_ROLE_TYPES.regular_student_role}')
    //       and ur.is_removed = 0
    //     where um.key = 'StudentIdentificationNumber'
    //       and um.value = ?
    //       and ur.group_id = ?    
    //   `)
    // const combinedResults = [...records, ...walkin_records];
    
    if (value === '000000000' || records.length == 0) {
      return true;
    }

    if (records.filter(record => record.uid != studentID).length == 0) {
      return true;
    }

    return false;
  }

  C_OEN_UNIQUE = async (value:any, data:any ) :Promise<boolean> => {
    //need to update this to each school 
    const studentID = Number(data.StudentID);
    const SchGroupID = Number(data.SchGroupID);
    //const Namespace = data.Namespace === 'eqao_g10' ? 'IS_G10':'IS_G9'
    let group_type;
    let Namespace;
    switch(data.Namespace){
      case 'eqao_g3':
        Namespace = 'IS_G3'
        group_type = 'EQAO_G3'
        break;
      case 'eqao_g6':
        Namespace = 'IS_G6'
        group_type = 'EQAO_G6'
        break;
      case 'eqao_g9':
        Namespace = 'IS_G9'
        group_type = 'EQAO_G9'
        break;
      case 'eqao_g10':
        Namespace = 'IS_G10'
        group_type = 'EQAO_G10'
        break;
      default:
        Namespace = 'IS_G3'
        group_type = 'EQAO_G3'
        break;  
    }
    
    var records = await dbRawRead(this.app, [
      group_type,
      Namespace,
      value,
      SchGroupID
    ], `
        select um.uid as uid, IFNULL(um2.value , 0) as isSameGrade
          from user_metas as um
          join user_roles ur
            on ur.uid = um.uid
           and ur.role_type = 'schl_student'
           and ur.is_revoked = 0
          join school_classes sc
            on sc.group_id = ur.group_id
           and sc.is_active = 1
           and group_type = ?
          join school_semesters sm
            on sm.id = sc.semester_id
          join test_windows tw
            on tw.id = sm.test_window_id
           and tw.is_active = 1  
     LEFT JOIN user_metas AS um2
            ON um.uid = um2.uid
           AND um2.key_namespace = 'eqao_sdc'
           AND um2.key = ?
           AND um2.value = '1'      
         where um.key = 'NBED_UserId'
           and um.key_namespace = 'eqao_sdc'
           and um.value = ?
           and sc.schl_group_id = ?    
    `)



    /*
    var records = await dbRawRead(this.app, [
      Namespace,
      value,
      SchGroupID
    ], `
        select um.uid as uid, IFNULL(um2.value , 0) as isSameGrade
          from user_metas as um
     left join user_roles ur
            on ur.uid = um.uid
           and ur.role_type = 'schl_student'
           and ur.is_revoked = 0
     left join school_classes sc
            on sc.group_id = ur.group_id
           and sc.is_active = 1
     LEFT JOIN user_metas AS um2
            ON um.uid = um2.uid
           AND um2.key_namespace = 'eqao_sdc'
           AND um2.key = ?
           AND um2.value = '1'      
         where um.key = 'StudentOEN'
           and um.key_namespace = 'eqao_sdc'
           and um.value = ?
           and sc.schl_group_id = ?    
    `)
    */                                                
    if(value === '000000000'||records.length == 0 || 
       records.filter(record => record.uid != studentID).length == 0 || 
       records.filter(record =>record.isSameGrade != 0).length == 0){
      return true;
    }
    return false;
  }

  OENMast = async (value:number):Promise<number> =>{
    /*
    from  OENMask.sql  code source: https://bubo.vretta.com/vea/project-management/eqao-project-deliverables/-/issues/126#note_191642
    set @retValue = case @prmIndex
      when 1  then 0 when 2  then 2 when 3  then 4 when 4  then 6 when 5  then 8 when 6  then 1 when 7  then 3 when 8  then 5 when 9  then 7 when 10 then 9
      when 11 then 1 when 12 then 3 when 13 then 5 when 14 then 7 when 15 then 9 when 16 then 2 when 17 then 4 when 18 then 6 when 19 then 8 when 20 then 0
      when 21 then 2 when 22 then 4 when 23 then 6 when 24 then 8 when 25 then 0 when 26 then 3 when 27 then 5 when 28 then 7 when 29 then 9 when 30 then 1
      when 31 then 3 when 32 then 5 when 33 then 7 when 34 then 9 when 35 then 1 when 36 then 4 when 37 then 6 when 38 then 8 when 39 then 0 when 40 then 2
      when 41 then 4 when 42 then 6 when 43 then 8 when 44 then 0 when 45 then 2 when 46 then 5 when 47 then 7 when 48 then 9 when 49 then 1 when 50 then 3
      when 51 then 5 when 52 then 7 when 53 then 9 when 54 then 1 when 55 then 3 when 56 then 6 when 57 then 8 when 58 then 0 when 59 then 2 when 60 then 4
      when 61 then 6 when 62 then 8 when 63 then 0 when 64 then 2 when 65 then 4 when 66 then 7 when 67 then 9 when 68 then 1 when 69 then 3 when 70 then 5
      when 71 then 7 when 72 then 9 when 73 then 1 when 74 then 3 when 75 then 5 when 76 then 8 when 77 then 0 when 78 then 2 when 79 then 4 when 80 then 6
      when 81 then 8 when 82 then 0 when 83 then 2 when 84 then 4 when 85 then 6 when 86 then 9 when 87 then 1 when 88 then 3 when 89 then 5 when 90 then 7
      when 91 then 9 when 92 then 1 when 93 then 3 when 94 then 5 when 95 then 7 when 96 then 0 when 97 then 2 when 98 then 4 when 99 then 6 when 100 then 8
      end
      Return @retValue
    */
    var mask:any ={};
    mask[1]=0;  mask[2]=2;  mask[3]=4;  mask[4]=6;  mask[5]=8;  mask[6]=1;  mask[7]=3;  mask[8]=5;  mask[9]=7;  mask[10]=9
    mask[11]=1; mask[12]=3; mask[13]=5; mask[14]=7; mask[15]=9; mask[16]=2; mask[17]=4; mask[18]=6; mask[19]=8; mask[20]=0
    mask[21]=2; mask[22]=4; mask[23]=6; mask[24]=8; mask[25]=0; mask[26]=3; mask[27]=5; mask[28]=7; mask[29]=9; mask[30]=1
    mask[31]=3; mask[32]=5; mask[33]=7; mask[34]=9; mask[35]=1; mask[36]=4; mask[37]=6; mask[38]=8; mask[39]=0; mask[40]=2
    mask[41]=4; mask[42]=6; mask[43]=8; mask[44]=0; mask[45]=2; mask[46]=5; mask[47]=7; mask[48]=9; mask[49]=1; mask[50]=3
    mask[51]=5; mask[52]=7; mask[53]=9; mask[54]=1; mask[55]=3; mask[56]=6; mask[57]=8; mask[58]=0; mask[59]=2; mask[60]=4
    mask[61]=6; mask[62]=8; mask[63]=0; mask[64]=2; mask[65]=4; mask[66]=7; mask[67]=9; mask[68]=1; mask[69]=3; mask[70]=5
    mask[71]=7; mask[72]=9; mask[73]=1; mask[74]=3; mask[75]=5; mask[76]=8; mask[77]=0; mask[78]=2; mask[79]=4; mask[80]=6
    mask[81]=8; mask[82]=0; mask[83]=2; mask[84]=4; mask[85]=6; mask[86]=9; mask[87]=1; mask[88]=3; mask[89]=5; mask[90]=7
    mask[91]=9; mask[92]=1; mask[93]=3; mask[94]=5; mask[95]=7; mask[96]=0; mask[97]=2; mask[98]=4; mask[99]=6; mask[100]=8

    return mask[value];
  }

}
