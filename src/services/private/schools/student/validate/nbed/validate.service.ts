// Initializes the `private/schools/student/validate` service on path `/private/schools/student/validate`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../../declarations';
import { Validate } from './validate.class';
import hooks from './validate.hooks';

// Add this service to the service type index
declare module '../../../../../../declarations' {
  interface ServiceTypes {
    'private/schools/student/validate/nbed': Validate & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/private/schools/student/validate/nbed', new Validate(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('private/schools/student/validate/nbed');

  service.hooks(hooks);
}
