import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { BadRequest, NotAcceptable } from '@feathersjs/errors';
import { dbRawRead } from '../../../util/db-raw';

interface Data {}

enum RequestType
{
  getAllSchools = "getAllSchools",
  getExistingStudents = "getExistingStudents"
}

interface ServiceOptions {}

export class AbedDataImport implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> 
  {
    const requestType = params?.query?.requestType as RequestType;
    console.log(params);
    const includeSample = params?.query?.includeSample;
    if (requestType == null)
    {
      throw new BadRequest("Please specify the request type.");
    }

    if (requestType === RequestType.getAllSchools)
    {
      const parsedIncludeSample: boolean = includeSample == 1 ? true : false;
      return await dbRawRead(this.app, [], 
      `
      SELECT s.*
      FROM schools s
      INNER JOIN school_districts sd on sd.group_id = s.schl_dist_group_id
      ${!parsedIncludeSample ? 'WHERE sd.is_sample = 0' : ''}
      ;
      `);
    }

    else if (requestType === RequestType.getExistingStudents)
    {
      const studentASNs = params?.query?.studentASNs;

      if (studentASNs == null)
      {
        throw new BadRequest("Please include ASNs to get existing students.");
      }
      console.log(studentASNs, typeof(studentASNs));
      return await dbRawRead(this.app, [studentASNs], 
      `
        SELECT um.uid, um.value as ASN
        FROM user_metas um
        JOIN users u 
        ON u.id = um.uid
        WHERE um.key_namespace = 'abed_course'
        AND um.key = 'StudentIdentificationNumber'
        AND um.value IN (?)
        GROUP by um.uid;
      ;
      `);
    }

    throw new NotAcceptable("Unacceptable request type.");
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
