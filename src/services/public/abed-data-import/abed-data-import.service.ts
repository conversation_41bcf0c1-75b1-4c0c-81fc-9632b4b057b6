// Initializes the `public/abed-data-import` service on path `/public/abed-data-import`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { AbedDataImport } from './abed-data-import.class';
import hooks from './abed-data-import.hooks';

// Add this service to the service type index
declare module '../../../declarations' {
  interface ServiceTypes {
    'public/abed-data-import': AbedDataImport & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/abed-data-import', new AbedDataImport(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/abed-data-import');

  service.hooks(hooks);
}
