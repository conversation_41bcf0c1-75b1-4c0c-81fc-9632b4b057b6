import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { BadRequest } from '@feathersjs/errors';
import { dbRawRead, dbRawWrite } from '../../../util/db-raw';
import { Knex } from 'knex';
import { dbDateNow } from '../../../util/db-dates';
import { currentUid } from '../../../util/uid';
import moment from 'moment';
import { DBD_U_GROUP_TYPES } from '../../../constants/db-extracts';
import { generateAccessCode } from '../../../util/secret-codes';
import { createHash } from 'crypto';
import { getSysConstNumeric } from '../../../util/sys-const-numeric';
import { Errors } from '../../../errors/general';
import { STUDENT_ROLE_TYPES } from '../educator/walk-in-students/walk-in-students.class';
import { PasiImportTestWindow } from './types';
const AWS = require('aws-sdk');

const AWS_CONFIG =
{
  secretAccessKey: 'Czd414/T9Agn0+d4P2xo1Z6ejQxNdaURBdf6WmAF',
  accessKeyId: '********************',
  region: 'ca-central-1'
};
const s3 = new AWS.S3(
{
  ... AWS_CONFIG
});

const baseAWSPath = "to/be/decided";
const bucketDomain = "storage.mathproficiencytest.ca";

/*
// only used for students, diploma exam marks and student enrollments
// right now, as other services do not have too many insertions/updates/etc
*/
const maxNumRowsToRetrieveFromDB = 20000;
const maxIterationsAtOnce = 10000;
const diplomaExamSittingsChunkSize = 1000;

// TODO: change to include proper set up for all grades, for later (e.g. grade 08)
// Check the `test_windows.is_for_pasi`, `test_windows.pasi_exam_period` and `test_windows.PASI_school_year` columns
const allowedStudentSchoolEnrolmentGrades: string[] = ["06", "09"];
// const allowedStudentSchoolEnrolmentGrades: string[] = ["disabled"];
const allowedDiplomaGrades: string[] = ["12"];
const allowedDiplomaGroupType: string = "ABED_GRADE_12";
// TODO: change to include proper set up for all possible years, for later (e.g. grade 05)
// NOTE: The allowedDiplomaExamSchoolYear is the same as allowedStudentSchoolEnrolmentYears
const allowedStudentSchoolEnrolmentYears: string[] = ["2024"];
const defaultSchoolLang = "en";
const twumDiplomaKeyName = "StudentDiplomaExamInfo";
export const twumGradeKeyName = "StudentGrade";
const userMetasPasiKeyNamespace = "abed_pasi_sync";
const ASNKey = "StudentIdentificationNumber";
const pasiStatusSlug = "pasi_status";

// for temp solution of unsyncable SSE records (mainly diploma_exam_mark cases)
const isTempSolnForUnsyncedSSEEnabled: boolean = false;
const latestSchoolYearG12TWID: number = 115;

const enum PASIStatuses {
  noAccess = "NoAccess",
  unknown = "Unknown",
  normal = "Normal",
};

interface Data {}

interface ServiceOptions {}


export class AbedPasi implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  numToUint8Array(num:number):Uint8Array {
    let arr = new Uint8Array(8);

    for (let i = 0; i < 8; i++) {
      arr[i] = num % 256;
      num = Math.floor(num / 256);
    }

    return arr;
  }

  async hashVersionsForTable (tableName:string, year:string|null) {
    const knex:Knex = this.app.get('knexClientRead');
    const startTime = Date.now();
    console.log(`starting hash at ${startTime}`);
    let query = knex
      .select('PASICoreVersion')
      .hintComment('MAX_EXECUTION_TIME(4294967295)')
      .from(`pasi_data.${tableName}`);
    if (year) {
      query = query.where('SchoolYear', year)
    }
    const stream = query
      .orderBy('PASICoreVersion')
      .stream();

    const hasher = createHash('md5');
    for await (const row of stream) {
      hasher.update(this.numToUint8Array(row.PASICoreVersion))
    }
    const result = hasher.digest('hex');
    console.log(`completed in ${Date.now() - startTime} ms`);
    console.log(result);
    return result;
  }

  async getLatestVersions (withVersionHash:boolean = false): Promise<Data[] | Paginated<Data>> {
    const allLatestVersions = await dbRawRead(this.app, [],
      `
        SELECT psv.*
        FROM pasi_data.pasi_latest_versions psv
        WHERE psv.is_revoked = 0
        ORDER BY psv.order_to_sync_to_mpt_dev ASC;
      `);
    if (!withVersionHash) {
      return allLatestVersions;
    }

    for (const versionRow of allLatestVersions) {
      if (!versionRow.is_for_IDA) {
        continue;
      }
      const { type, version, last_hash, last_hash_version } = versionRow;
      let year = null;
      if (versionRow.requires_year_in_request){
        year = type.substring(type.length - 4, type.length);
      }
      if (version > last_hash_version) {
        const knex:Knex = this.app.get('knexClientWrite');
        versionRow.versionHash = await this.hashVersionsForTable(versionRow.pasi_data_schema_table, year);
        await knex('pasi_data.pasi_latest_versions')
          .where('id', versionRow.id)
          .update({
            last_hash: versionRow.versionHash,
            last_hash_version: version
          });
      } else {
        versionRow.versionHash = last_hash
      }
    }
    return allLatestVersions;
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params?.query == null) {
      throw new BadRequest();
    }

    if (params?.query?.getAllPASILatestVersions == 1) {
      // get latest PASI API versions numbers here
      return this.getLatestVersions(!!params?.query?.withHashes);
    }

    return ["Successful response test"];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: NullableId, params?: Params): Promise<Data> {
    if (params?.query == null) {
      throw new BadRequest();
    }

    if (params?.query?.getStudentDiplomaExams == 1) {
      if (params?.query?.schoolGroupId == null) {
        throw new Error("Missing school group ID from diploma exam fetch for students.");
      }

      if (params?.query?.uids == null) {
        return {};
      }

      try {
        let uids = Object.values(params.query.uids) as string[] | number[];
        for (let i = 0; i < uids.length; i++) {
          uids[i] = +uids[i];
        }

        if (uids.length === 0) {
          return {};
        }

        let relevantMetas: any = {}; // filtered by current school being looked at
        const metas = await dbRawRead(this.app, [twumDiplomaKeyName, userMetasPasiKeyNamespace, uids],
        `
          SELECT
            twum.uid,
            twum.asmt_type_slug,
            twum.test_window_id,
            twum.meta,
            ac.course_name_short,
            ac.course_name_full
          FROM
            tw_user_metas twum
                LEFT JOIN
            assessment_courses ac ON twum.asmt_type_slug = ac.course_code_foreign
                AND ac.is_revoked = 0
          WHERE
            twum.key = ?
                AND twum.key_namespace = ?
                AND twum.uid in (?)
          ;
        `);

        // console.log(metas.length);
        for (let info of metas) {
          // console.log(info);
          const schoolInfo = JSON.parse(info.meta).schoolInfo as any[];
          const isDEMDeleted = +JSON.parse(info.meta).IsDeleted as 0 | 1;
          const matchFound = schoolInfo.find(sInfo => +sInfo.schoolGroupId === +params?.query?.schoolGroupId) != undefined;
          
          // console.log(matchFound, schoolInfo);
          // filter out deleted diploma exam marks, and marks not relevant to this particular school
          if (matchFound && isDEMDeleted !== 1) {
            let studentMeta = relevantMetas[info.uid];
            if (studentMeta == null) {
              // we will return all information to be comprehensive, in case it is needed elsewhere on client side
              // however, we only display on the students table the display_course_name for now
              relevantMetas[info.uid] = [{
                type_slug: info.asmt_type_slug,
                test_window_id: info.test_window_id,
                course_name_short: info.course_name_short,
                course_name_full: info.course_name_full,
                display_course_name: info.course_name_short == null ? info.asmt_type_slug : info.course_name_short
              }];
            }

            else {
              studentMeta.push({
                type_slug: info.asmt_type_slug,
                test_window_id: info.test_window_id,
                course_name_short: info.course_name_short,
                course_name_full: info.course_name_full,
                display_course_name: info.course_name_short == null ? info.asmt_type_slug : info.course_name_short
              });
            }
            // console.log(relevantMetas[info.uid]);
          }
        }

        return relevantMetas;

      }

      catch(e) {
        throw (e);
      }
    }

    return {};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: any[], params?: Params): Promise<Data> {
    // if (Array.isArray(data)) {
    //   return Promise.all(data.map(current => this.create(current, params)));
    // }

    let reqType = "";
    let isUnknown: boolean = false;
    let isNoAccess: boolean = false;
    if (params?.query?.type == null) {
      throw new BadRequest();
    }

    else {
      reqType = params?.query?.type;
    }

    // store modified PASI data here (we know what changed based on what PASI returns)

    let dataToUpsert = [];
    let columnsToMerge: string[] = [];
    let maxKnownPasiVersionForType = 0;
    let colToMergeOn = "";
    let tableName = "";
    const pasiSchema = "pasi_data";

    if (reqType === "CodeValues") {
      columnsToMerge =
      [
        `CodeClassName`,
        `CodeText`,
        `CodeTextPlusCodeClassName`,
        `Description`,
        `DisplaySequence`,
        `EffectiveSchoolYear`,
        `ExpirySchoolYear`,
        `FrenchDescription`,
        `FrenchShortDescription`,
        `PASICoreVersion`,
        `ShortDescription`
      ];

      // this column is CodeClassName + CodeText; we will merge on this, as this seems to be guranteed
      // to be unique, even if CodeClassText and CodeClassName individually are not guranteed to be unique
      colToMergeOn = "CodeTextPlusCodeClassName";
      tableName = "code_values";

      for (let i = 0; i < data.length; i++) {

        let objToUpsert = {
          CodeClassName: data[i].CodeClassName || "",
          CodeText: data[i].CodeText || "",
          CodeTextPlusCodeClassName: data[i].CodeClassName + data[i].CodeText,
          Description: data[i].Description || "",
          DisplaySequence: data[i].DisplaySequence || 0,
          EffectiveSchoolYear: data[i].EffectiveSchoolYear || null,
          ExpirySchoolYear: data[i].ExpirySchoolYear || null,
          FrenchDescription: data[i].FrenchDescription || "",
          FrenchShortDescription: data[i].FrenchShortDescription || "",
          PASICoreVersion: data[i].PASICoreVersion || 0,
          ShortDescription: data[i].ShortDescription || ""
        };
        dataToUpsert.push(objToUpsert);

        if (objToUpsert.PASICoreVersion > maxKnownPasiVersionForType) {
          maxKnownPasiVersionForType = objToUpsert.PASICoreVersion;
        }
      }
    }

    else if (reqType === "School") {
      columnsToMerge =
      [
        `AvailabilityStatus`,
        `Classifications`,
        `DeliveryAddress`,
        `EmailAddress`,
        `FaxNumber`,
        `GoverningAuthorities`,
        `MailingAddress`,
        `Names`,
        `PasiCoreVersion`,
        `PhoneNumber`,
        `Statuses`,
        `synced_to_mpt_dev`
      ];

      colToMergeOn = "OrganizationCode";
      tableName = "pasi_schools";

      for (let i = 0; i < data.length; i++) {
        const school = data[i].School;

        if (data[i].AvailabilityStatus === PASIStatuses.noAccess) {
          isNoAccess = true;
        }

        else if (data[i].AvailabilityStatus === PASIStatuses.unknown) {
          isUnknown = true;
        }

        let objToUpsert = {
          AvailabilityStatus: data[i].AvailabilityStatus || "",
          Classifications: this.objectToJSON(school?.Classifications),
          DeliveryAddress: this.objectToJSON(school?.DeliveryAddress),
          EmailAddress: school?.EmailAddress || "",
          FaxNumber: school?.FaxNumber || "",
          GoverningAuthorities: this.objectToJSON(school?.GoverningAuthorities),
          MailingAddress: this.objectToJSON(school?.MailingAddress),
          Names: this.objectToJSON(school?.Names),
          OrganizationCode: school?.OrganizationCode || "",
          PasiCoreVersion: school?.PasiCoreVersion || 0,
          PhoneNumber: school?.PhoneNumber || "",
          Statuses: this.objectToJSON(school?.Statuses),
          synced_to_mpt_dev: 0
        };
        dataToUpsert.push(objToUpsert);

        if (objToUpsert.PasiCoreVersion > maxKnownPasiVersionForType) {
          maxKnownPasiVersionForType = objToUpsert.PasiCoreVersion;
        }
      }
    }

    else if (reqType === "SchoolAuthority") {
      columnsToMerge =
      [
        `AvailabilityStatus`,
        `Classifications`,
        `DeliveryAddress`,
        `EmailAddress`,
        `FaxNumber`,
        `MailingAddress`,
        `Names`,
        `PasiCoreVersion`,
        `PhoneNumber`,
        `Statuses`,
        `synced_to_mpt_dev`
      ];

      colToMergeOn = "OrganizationCode";
      tableName = "school_authorities";

      for (let i = 0; i < data.length; i++) {
        const schoolAuthority = data[i].SchoolAuthority;

        if (data[i].AvailabilityStatus === PASIStatuses.noAccess) {
          isNoAccess = true;
        }

        else if (data[i].AvailabilityStatus === PASIStatuses.unknown) {
          isUnknown = true;
        }

        let objToUpsert = {
          AvailabilityStatus: data[i].AvailabilityStatus || "",
          Classifications: this.objectToJSON(schoolAuthority?.Classifications),
          DeliveryAddress: this.objectToJSON(schoolAuthority?.DeliveryAddress),
          EmailAddress: schoolAuthority?.EmailAddress || "",
          FaxNumber: schoolAuthority?.FaxNumber || "",
          MailingAddress: this.objectToJSON(schoolAuthority?.MailingAddress),
          Names: this.objectToJSON(schoolAuthority?.Names),
          OrganizationCode: schoolAuthority?.OrganizationCode || "",
          PasiCoreVersion: schoolAuthority?.PasiCoreVersion || 0,
          PhoneNumber: schoolAuthority?.PhoneNumber || "",
          Statuses: this.objectToJSON(schoolAuthority?.Statuses),
          synced_to_mpt_dev: 0
        };
        dataToUpsert.push(objToUpsert);

        if (objToUpsert.PasiCoreVersion > maxKnownPasiVersionForType) {
          maxKnownPasiVersionForType = objToUpsert.PasiCoreVersion;
        }
      }
    }

    else if (reqType === "Student") {
      columnsToMerge =
      [
        `AvailabilityStatus`,
        // `CitizenshipStatuses`, // not required to be saved at this point, so commented out
        `CreatedOnUtc`,
        // `DateOfDeath`, // not required to be saved at this point, so commented out
        `DisclosureRestrictions`, // this column will be modified, but using the property "HasDisclosureRestrictions" from PASI's response directly
        // `EmailAddresses`, // not required to be saved at this point, so commented out
        // `Gender`, // not required to be saved at this point, so commented out
        `IdentificationRecord`, // this column will be modified, but using the property "BirthDate" from PASI's response directly
        `IsDeactivated`,
        `IsDeceased`,
        `LastUpdateUtcTime`,
        `MaturityDate`,
        // `MaturityReason`, // not required to be saved at this point, so commented out
        // `MedicalAlerts`, // not required to be saved at this point, so commented out
        `OriginalNameRefId`,
        // `OtherPhoneNumbers`, // not required to be saved at this point, so commented out
        // `ParentGuardians`, // not required to be saved at this point, so commented out
        `PasiCoreVersion`,
        `PreferredNameRefId`,
        // `PreferredPhoneNumber`, // not required to be saved at this point, so commented out
        // `PrimaryLanguageSpokenAtHome`, // not required to be saved at this point, so commented out
        `PrimaryStateProvinceId`,
        `ScheduledRecordDisposalDate`,
        `SecondaryStateProvinceIds`,
        // `Section23Eligibility`, // not required to be saved at this point, so commented out
        // `StudentAddresses`, // not required to be saved at this point, so commented out
        `StudentNames`,
        `synced_to_mpt_dev`
      ];

      colToMergeOn = "StateProvinceId";
      tableName = "students";

      for (let i = 0; i < data.length; i++) {
        const student = data[i].Student;

        if (data[i].AvailabilityStatus === PASIStatuses.noAccess) {
          isNoAccess = true;
        }

        else if (data[i].AvailabilityStatus === PASIStatuses.unknown) {
          isUnknown = true;
        }

        let objToUpsert = {
          AvailabilityStatus: data[i].AvailabilityStatus || "",
          // CitizenshipStatuses: this.objectToJSON(student?.CitizenshipStatuses), // not required to be saved at this point, so commented out
          CreatedOnUtc: student?.CreatedOnUtc || "",
          // DateOfDeath: student?.DateOfDeath || "", // not required to be saved at this point, so commented out
          DisclosureRestrictions: this.objectToJSON(this.formDisclosureRestrictionsFromHDR(student?.HasDisclosureRestrictions)), // this column will be modified, but using the property "HasDisclosureRestrictions" from PASI's response directly
          // EmailAddresses: this.objectToJSON(student?.EmailAddresses),  // not required to be saved at this point, so commented out
          // Gender: student?.Gender || "", // not required to be saved at this point, so commented out
          IdentificationRecord: this.objectToJSON(this.formIdentificationRecordFromDOB(student?.BirthDate)), // this column will be modified, but using the property "BirthDate" from PASI's response directly
          IsDeactivated: this.boolToNum(student?.IsDeactivated),
          IsDeceased: this.boolToNum(student?.IsDeceased),
          LastUpdateUtcTime: student?.LastUpdateUtcTime || "",
          MaturityDate: student?.MaturityDate || "",
          // MaturityReason: student?.MaturityReason || "", // not required to be saved at this point, so commented out
          // MedicalAlerts: this.objectToJSON(student?.MedicalAlerts), // not required to be saved at this point, so commented out
          OriginalNameRefId: student?.OriginalNameRefId || 0,
          // OtherPhoneNumbers: this.objectToJSON(student?.OtherPhoneNumbers), // not required to be saved at this point, so commented out
          // ParentGuardians: this.objectToJSON(student?.ParentGuardians), // not required to be saved at this point, so commented out
          PasiCoreVersion: student?.PASICoreVersion || 0,
          PreferredNameRefId: student?.PreferredNameRefId || 0,
          // PreferredPhoneNumber: this.objectToJSON(student?.PreferredPhoneNumber), // not required to be saved at this point, so commented out
          // PrimaryLanguageSpokenAtHome: student?.PrimaryLanguageSpokenAtHome || "", // not required to be saved at this point, so commented out
          PrimaryStateProvinceId: student?.PrimaryStateProvinceId || "",
          ScheduledRecordDisposalDate: student?.ScheduledRecordDisposalDate || "",
          SecondaryStateProvinceIds: this.objectToJSON(student?.SecondaryStateProvinceIds),
          // Section23Eligibility: student?.Section23Eligibility || "", // not required to be saved at this point, so commented out
          StateProvinceId: student?.StateProvinceId || "",
          // StudentAddresses: this.objectToJSON(student?.StudentAddresses), // not required to be saved at this point, so commented out
          StudentNames:  this.objectToJSON(student?.StudentNames),
          synced_to_mpt_dev: 0
        };
        dataToUpsert.push(objToUpsert);

        if (objToUpsert.PasiCoreVersion > maxKnownPasiVersionForType) {
          maxKnownPasiVersionForType = objToUpsert.PasiCoreVersion;
        }
      }
    }

    else if (/^StudentSchoolEnrolment\.?[0-9]{4}/.test(reqType)) {
      columnsToMerge =
      [
        `AvailabilityStatus`,
        `EffectiveUtc`,
        `ExitDateType`,
        // `ExtendedSchoolEnrolmentInfo`, // not required to be saved at this point, so commented out
        `Grade`,
        `IsDeleted`,
        `PASICoreVersion`,
        `ProgrammingTimeframe`,
        `ProgrammingType`,
        `RegistrationExitDate`,
        `RegistrationStartDate`,
        `SchoolCode`,
        `SchoolProvidedProgramPercentage`,
        `SchoolYear`,
        `StateProvinceId`,
        `synced_to_mpt_dev`
      ];

      colToMergeOn = "RefId";
      tableName = "student_school_enrolments";

      for (let i = 0; i < data.length; i++) {
        const enrolment = data[i].Enrolment;

        if (data[i].AvailabilityStatus === PASIStatuses.noAccess) {
          isNoAccess = true;
        }

        else if (data[i].AvailabilityStatus === PASIStatuses.unknown) {
          isUnknown = true;
        }

        let objToUpsert = {
          AvailabilityStatus: data[i].AvailabilityStatus || "",
          EffectiveUtc: enrolment?.EffectiveUtc || "",
          ExitDateType: enrolment?.ExitDateType || "",
          // ExtendedSchoolEnrolmentInfo: this.objectToJSON(enrolment?.ExtendedSchoolEnrolmentInfo), // not required to be saved at this point, so commented out
          Grade: enrolment?.Grade || "",
          IsDeleted: this.boolToNum(enrolment?.IsDeleted),
          PASICoreVersion: enrolment?.PASICoreVersion || 0,
          ProgrammingTimeframe: enrolment?.ProgrammingTimeframe || "",
          ProgrammingType: enrolment?.ProgrammingType || "",
          RefId: enrolment?.RefId || "",
          RegistrationExitDate: enrolment?.RegistrationExitDate || "",
          RegistrationStartDate: enrolment?.RegistrationStartDate || "",
          SchoolCode: enrolment?.SchoolCode || "",
          SchoolProvidedProgramPercentage: enrolment?.SchoolProvidedProgramPercentage	|| "",
          SchoolYear: enrolment?.SchoolYear || "",
          StateProvinceId: enrolment?.StateProvinceId || "",
          synced_to_mpt_dev: 0
        };
        dataToUpsert.push(objToUpsert);

        if (objToUpsert.PASICoreVersion > maxKnownPasiVersionForType) {
          maxKnownPasiVersionForType = objToUpsert.PASICoreVersion;
        }
      }
    }

    else if (/^DiplomaExam\.?[0-9]{4}/.test(reqType)) {
      columnsToMerge =
      [
        `AvailabilityStatus`,
        `CourseCode`,
        `EffectiveUtc`,
        `EnglishFootnote`,
        `ExamComponents`,
        `ExamLanguage`,
        `ExamPeriod`,
        `ExamType`,
        `FrenchFootnote`,
        `IsDeleted`,
        `MarkFormat`,
        `PASICoreVersion`,
        `RegistrationDeadlineUtc`,
        `RescoreDeadlineDate`,
        `SchoolYear`,
        `synced_to_mpt_dev`
      ];

      colToMergeOn = "RefId";
      tableName = "diploma_exams";

      for (let i = 0; i < data.length; i++) {
        const exam = data[i].Exam;

        if (data[i].AvailabilityStatus === PASIStatuses.noAccess) {
          isNoAccess = true;
        }

        else if (data[i].AvailabilityStatus === PASIStatuses.unknown) {
          isUnknown = true;
        }

        let objToUpsert = {
          AvailabilityStatus: data[i].AvailabilityStatus || "",
          CourseCode: exam?.CourseCode || "",
          EffectiveUtc: exam?.EffectiveUtc || "",
          EnglishFootnote: exam?.EnglishFootnote || "",
          ExamComponents: this.objectToJSON(exam?.ExamComponents),
          ExamLanguage: exam?.ExamLanguage || "",
          ExamPeriod: exam?.ExamPeriod || "",
          ExamType: exam?.ExamType || "",
          FrenchFootnote: exam?.FrenchFootnote || "",
          IsDeleted: this.boolToNum(exam?.IsDeleted),
          MarkFormat: exam?.MarkFormat || "",
          PASICoreVersion: exam?.PASICoreVersion || 0,
          RefId: exam?.RefId || "",
          RegistrationDeadlineUtc: exam?.RegistrationDeadlineUtc || "",
          SchoolYear: exam?.SchoolYear || "",
          synced_to_mpt_dev: 0
        };
        dataToUpsert.push(objToUpsert);

        if (objToUpsert.PASICoreVersion > maxKnownPasiVersionForType) {
          maxKnownPasiVersionForType = objToUpsert.PASICoreVersion;
        }
      }
    }

    else if (reqType === "Course") {
      columnsToMerge =
      [
        `AvailabilityStatus`,
        `AccumulatedCreditMaximum`,
        `CourseAuthorizations`,
        `CourseCompletionMethods`,
        `CoursePermissions`,
        `CourseRelationships`,
        `CourseSeries`,
        `DevelopedBy`,
        `DisciplineCode`,
        `EnglishFullName`,
        `EnglishShortName`,
        `EvaluationMethod`,
        `FirstPilotSchoolYear`,
        `FirstSchoolYear`,
        `FrenchFullName`,
        `FrenchShortName`,
        `FundingTier`,
        `InstructionalLevel`,
        `IntendedGradeLevel`,
        `LastPilotSchoolYear`,
        `LastPublishedUtcTime`,
        `LastPublishedVersion`,
        `LastSchoolYear`,
        `PasiCoreVersion`,
        `SubjectCode`,
        `synced_to_mpt_dev`
      ];

      colToMergeOn = "CourseCode";
      tableName = "courses";

      for (let i = 0; i < data.length; i++) {
        const course = data[i].Course;

        if (data[i].AvailabilityStatus === PASIStatuses.noAccess) {
          isNoAccess = true;
        }

        else if (data[i].AvailabilityStatus === PASIStatuses.unknown) {
          isUnknown = true;
        }

        let objToUpsert = {
          AvailabilityStatus: data[i].AvailabilityStatus || "",
          AccumulatedCreditMaximum: course?.AccumulatedCreditMaximum || null,
          CourseAuthorizations: this.objectToJSON(course?.CourseAuthorizations),
          CourseCode: course?.CourseCode || "",
          CourseCompletionMethods: this.objectToJSON(course?.CourseCompletionMethods),
          CoursePermissions: this.objectToJSON(course?.CoursePermissions),
          CourseRelationships: this.objectToJSON(course?.CourseRelationships),
          CourseSeries: course?.CourseSeries || "",
          DevelopedBy: course?.DevelopedBy || "",
          DisciplineCode: course?.DisciplineCode || "",
          EnglishFullName: course?.EnglishFullName || "",
          EnglishShortName: course?.EnglishShortName || "",
          EvaluationMethod: course?.EvaluationMethod || "",
          FirstPilotSchoolYear: course?.FirstPilotSchoolYear || null,
          FirstSchoolYear: course?.FirstSchoolYear || 0,
          FrenchFullName: course?.FrenchFullName || "",
          FrenchShortName: course?.FrenchShortName || "",
          FundingTier: course?.FundingTier || "",
          InstructionalLevel: course?.InstructionalLevel || "",
          IntendedGradeLevel: course?.IntendedGradeLevel || "",
          LastPilotSchoolYear: course?.LastPilotSchoolYear || null,
          LastPublishedUtcTime: course?.LastPublishedUtcTime || "",
          LastPublishedVersion: course?.LastPublishedVersion || 0,
          LastSchoolYear: course?.LastSchoolYear || null,
          PASICoreVersion: course?.PASICoreVersion || 0,
          SubjectCode: course?.SubjectCode || "" ,
          synced_to_mpt_dev: 0
        };
        dataToUpsert.push(objToUpsert);

        if (objToUpsert.PASICoreVersion > maxKnownPasiVersionForType) {
          maxKnownPasiVersionForType = objToUpsert.PASICoreVersion;
        }
      }
    }

    else if (/^DiplomaExamSitting\.?[0-9]{4}/.test(reqType)) {
      columnsToMerge =
      [
        `AvailabilityStatus`,
        `ComponentCode`,
        `ExamRefId`,
        `IsDeleted`,
        `IsSpecial`,
        `LastUpdateUtcTime`,
        `LocationName`,
        `OrganizationCode`,
        `PasiCoreVersion`,
        `ScheduledDtoTime`,
        `StudentCapacity`,
        `synced_to_mpt_dev`,
        'SchoolYear'
      ];
      const SchoolYear = parseInt(reqType.substring(reqType.length - 4, reqType.length), 10);
      colToMergeOn = "RefId";
      tableName = "diploma_exam_sittings";

      for (let i = 0; i < data.length; i++) {
        const diplomaExamSitting = data[i].DiplomaExamSitting;

        if (data[i].AvailabilityStatus === PASIStatuses.noAccess) {
          isNoAccess = true;
        }

        else if (data[i].AvailabilityStatus === PASIStatuses.unknown) {
          isUnknown = true;
        }

        let objToUpsert = {
          AvailabilityStatus: data[i].AvailabilityStatus || "",
          ComponentCode: diplomaExamSitting?.ComponentCode || "",
          ExamRefId: diplomaExamSitting?.ExamRefId || "",
          IsDeleted: this.boolToNum(diplomaExamSitting?.IsDeleted),
          IsSpecial: this.boolToNum(diplomaExamSitting?.IsSpecial),
          LastUpdateUtcTime: diplomaExamSitting?.LastUpdateUtcTime || "",
          LocationName: diplomaExamSitting?.LocationName || "",
          OrganizationCode: diplomaExamSitting?.OrganizationCode || "",
          PASICoreVersion: diplomaExamSitting?.PASICoreVersion || 0,
          RefId: diplomaExamSitting?.RefId || "",
          ScheduledDtoTime: this.objectToJSON(diplomaExamSitting?.ScheduledDtoTime),
          StudentCapacity: diplomaExamSitting?.StudentCapacity || null,
          synced_to_mpt_dev: 0,
          SchoolYear
        };
        dataToUpsert.push(objToUpsert);

        if (objToUpsert.PASICoreVersion > maxKnownPasiVersionForType) {
          maxKnownPasiVersionForType = objToUpsert.PASICoreVersion;
        }
      }
    }

    else if (/^DiplomaExamMark\.?[0-9]{4}/.test(reqType)) {
      columnsToMerge =
      [
        `AvailabilityStatus`,
        `ComponentMarks`,
        `EffectiveUtc`,
        `ExamMarkStatus`,
        `ExamRefId`,
        `IsDeleted`,
        `IsRescored`,
        `MarkValue`,
        `PASICoreVersion`,
        `StateProvinceId`,
        `synced_to_mpt_dev`,
        'SchoolYear'
      ];
      const SchoolYear = parseInt(reqType.substring(reqType.length - 4, reqType.length), 10);
      colToMergeOn = "RefId";
      tableName = "diploma_exam_marks";

      for (let i = 0; i < data.length; i++) {
        const examMark = data[i].ExamMark;

        if (data[i].AvailabilityStatus === PASIStatuses.noAccess) {
          isNoAccess = true;
        }

        else if (data[i].AvailabilityStatus === PASIStatuses.unknown) {
          isUnknown = true;
        }

        let objToUpsert = {
          AvailabilityStatus: data[i].AvailabilityStatus || "",
          ComponentMarks: this.objectToJSON(examMark?.ComponentMarks),
          EffectiveUtc: examMark?.EffectiveUtc || "",
          ExamMarkStatus: examMark?.ExamMarkStatus || "",
          ExamRefId: examMark?.ExamRefId || "",
          IsDeleted: this.boolToNum(examMark?.IsDeleted),
          IsRescored: this.boolToNum(examMark?.IsRescored),
          MarkValue: examMark?.MarkValue || "",
          PASICoreVersion: examMark?.PASICoreVersion || 0,
          RefId: examMark?.RefId || "",
          StateProvinceId: examMark?.StateProvinceId || "",
          synced_to_mpt_dev: 0,
          SchoolYear
        };
        dataToUpsert.push(objToUpsert);

        if (objToUpsert.PASICoreVersion > maxKnownPasiVersionForType) {
          maxKnownPasiVersionForType = objToUpsert.PASICoreVersion;
        }
      }
    }

    else {
      throw new BadRequest("Unsupported request type.");
    }

    try {
      const knex:Knex = this.app.get('knexClientWrite');
      var result =
      await knex(pasiSchema+"."+tableName)
      .insert(dataToUpsert)
      .onConflict(colToMergeOn)
      .merge(columnsToMerge);

      // console.log('about to storelatestersion\n\n\n');
      await this.storeLatestPASIVersion(this.app, maxKnownPasiVersionForType, reqType);
      await this.updatePASIStatus(isNoAccess, isUnknown, true);
    }

    catch(e) {
      console.log("Error in upserting", e);
      throw(e);
    }

    return result;
  }

  async storeLatestPASIVersion(app: Application, latestPASIVersion: number, type: string) {
    // based on the response we get from PASI when we get updates, the latest version should be stored
    // for next time
    type = type.replace(/\.([0-9]{4})/, '$1')
    // there is 1 row per type in psv table
    let rowsFound = await dbRawRead(app, [type],
    `
      SELECT *
      FROM pasi_data.pasi_latest_versions psv
      WHERE psv.type = ? AND psv.is_revoked = 0
      ;
    `);

    // console.log(rowsFound[0]);
    if (rowsFound.length === 0) {
      // console.log('new row', type);
      await dbRawWrite(app, [latestPASIVersion, type],
      `
        INSERT INTO
        pasi_data.pasi_latest_versions (version, type)
        values
        (?, ?)
        ;
      `);
    }

    else {
      // console.log("latestVersionFound", latestPASIVersion, rowsFound);
      if (rowsFound[0].version < latestPASIVersion) {
        // console.log("latestVersionFound", latestPASIVersion, rowsFound);
        await dbRawWrite(app, [latestPASIVersion, rowsFound[0].id],
          `
            UPDATE pasi_data.pasi_latest_versions
            SET version = ?, last_synced_to_pasi_data_schema_on = now()
            WHERE id = ?
            ;
          `);
      }
    }
  }

  objectToJSON(obj: undefined | Object | "null"): string {
    // returns stringified JSON
    if (obj == "null" || obj == null || obj == "") {
      return "{}";
    }

    return JSON.stringify(obj);
  }

  boolToNum(val: null | undefined | boolean ): 0 | 1 {
    if (val == null) {
      return 0;
    }

    return (val ? 1 : 0);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: any, params?: Params): Promise<Data> {
    // used for syncing data from `pasi_data` to `mpt_dev`

    if (params == null || params.query == null) {
      throw new BadRequest();
    }

    const adminUid = await currentUid(this.app, params);
    const pasiSchema = "pasi_data";
    const nowTime = dbDateNow(this.app);
    const limit = {
      limitStart: 0,
      numRowsToFetch: maxNumRowsToRetrieveFromDB
    };

    // DEPRECATED
    // if (params?.query?.S3ToDB == 1) {
    //   await this.syncPASIFromS3ToDB();
    // }

    if (params?.query?.logPASIErrors == 1) {
      await this.logPASIErrors(data[0], adminUid);
    }

    else if (params?.query?.noIDADataFound == 1) {
      // if no data found from IDA, mark PASI as available, with known status and with access
      await this.updatePASIStatus(false, false, true);
    }

    else if (params?.query?.syncAcrossSchemas == 1) {

      if (params?.query?.type == null) {
        throw new BadRequest("Missing type from syncAcrossSchemas call.");
      }
      const type = params.query.type.replace(/\.([0-9]{4})/, '$1')
      // there should only be 1 service for each type from the params?.query?.type and psv.type
      const service = (await dbRawRead(this.app, [type],
      `
        SELECT psv.*
        FROM pasi_data.pasi_latest_versions psv
        WHERE psv.is_revoked = 0
        AND psv.pasi_data_schema_table IS NOT NULL
        AND psv.type = ?
        LIMIT 1
        ;
      `))[0];

      console.log(`Entry into schema sync for type ${service.type}.`);

      try {
        if (service.type === "Student") {
          // 1. every student record that gets attempted to be synced will be synced 100%
          // thus, there is no need to account ones that may not get synced

          let students = [];
          const stateProvinceIds = await this.getStateProvinceIdsToSync();

          if (stateProvinceIds.length === 0) {
            console.log("There are no students (ASNs) to sync.\n");
            return [];
          }

          const studentsWhereClauseSQL = this.generateStudentsWhereClause(stateProvinceIds);
          // console.log(studentsWhereClauseSQL);
          // console.log(stateProvinceIds.length, stateProvinceIds[0], typeof(stateProvinceIds[0]));

          do {
            console.log("Getting students data.\n");
            students = await this.getRelevantData(pasiSchema, service.pasi_data_schema_table, limit,
            undefined, studentsWhereClauseSQL, undefined, false);
            // console.log(students.length);
            await this.syncIteratedData(students, pasiSchema, service,
            "students", this.upsertIntoUserMetasAndUsers, [adminUid, nowTime]);
            // console.log(counter, limit);

            /*
            // only attempt to fetch more records as long as the number of records retrieved is actually
            // at the limit. Any less than the limit, and there's no need to fetch anymore as
            // the next time records are fetched, the # is guranteed to be 0, as we got LESS
            // records than the SQL limit.
            */
          } while (students.length === limit.numRowsToFetch);
        }

        else if (service.type === "SchoolAuthority") {
          // 1. every school authority record will be synced 100%
          // 2.school authority data does not need to be synced in iterations as there is not much
          // thus, there is no need to account ones that may not get synced

          console.log("Getting school authorities data.\n");
          let schoolAuthorities = await this.getRelevantData(pasiSchema, service.pasi_data_schema_table);
          /* Steps:
          1. Go through all school authorities that need syncing to mpt_dev schema.
          2. If the school authority does not exist in schools table, we need to create it
          3. If the school authority does exist, we simply need to update its information
          */

          console.log("Unsynced school authorities #: ", schoolAuthorities.length);
          console.log("Upserting into school_districts.\n");
          schoolAuthorities = await this.upsertIntoSchoolAuthorities(schoolAuthorities, adminUid, nowTime);

          console.log("Len2: ", schoolAuthorities.length, "\n");

          if (schoolAuthorities.length > 0) {
            console.log(schoolAuthorities.length + " school authorities successfully synced to mpt_dev schema.\n");
            await this.turnOnSyncedFlag(pasiSchema, service.pasi_data_schema_table,
              schoolAuthorities.map(schoolAuthority => schoolAuthority.id));
          }
        }

        else if (service.type === "School") {
          // 1. every school record will be synced 100%
          // 2.school data does not need to be synced in iterations as there is not much
          // thus, there is no need to account ones that may not get synced
          console.log("Getting schools data.\n");
          let schools = await this.getRelevantData(pasiSchema, service.pasi_data_schema_table);
          /*
          // Note 1: pasi_data.school_authorities should be synced before schools, as we need valid
          // school_dist_group_id before making a school, but the code is written as such if the
          // school district of a school does not exist, the school being synced is skipped from the sync
          */

          /* Steps:
          1. Go through all schools that need syncing to mpt_dev schema.
          2. If school does not exist in schools table, we need to create it and create
          the placeholder classes for the grades we care about right now (see  variable allowedStudentSchoolEnrolmentGrades)
          3. If school does exist, we simply need to update its information
          */

          console.log("Unsynced schools #: ", schools.length);
          console.log("Upserting into schools.\n");
          schools = await this.upsertIntoSchools(schools, adminUid, nowTime);

          console.log("Len2: ", schools.length,  "\n");

          if (schools.length > 0) {
            console.log(schools.length + " schools successfully synced to mpt_dev schema.\n");
            await this.turnOnSyncedFlag(pasiSchema, service.pasi_data_schema_table,
              schools.map(school => school.id))
          }
        }

        else if (/^StudentSchoolEnrolment\.?[0-9]{4}/.test(service.type)) {
          /*
          // Note 1: This is associated with the StudentSchoolEnrolments service, as the Students service itself
          // does not have any School/School Authority associated data in PASI.

          // Note 2: At this point we SHOULD have an uid for EVERY student in the `pasi_data.students` table, but
          // I will write the code such that if there is no uid, the code still works

          // Note 3: we should update schools and school authorities tables information before we do this, as we will need
          // school class group IDs (which need schools) for this section. At this point, we should have all the schools,
          // school authorities and school_classes that we need for inserting students into user_roles based for school classes.
          // But again I will write the code such that if there is no school existing yet, the code still works

          Steps:
          // 0. Create placeholder classes if needed
          // 1. Get all students from `pasi_data.students` with the ASNs from `pasi_data.student_school_enrolment` table
            This yields us the students' uids.
            Note we make changes in `user_roles` for the rows in `student_school_enrolments`.
          // 2. find user_roles with students' uid
          // 3. revoke if any found in step 2.
          // 4. Insert new user_roles directly
          */

          // dynamically create any placeholder classes that are not already created
          await this.createPlaceholderSchoolClassesIfNeeded(adminUid);

          let studentEnrolments = [];
          let studentEnrolmentIdsFailedToSync: number[] = [];

          do {
            console.log("Getting student school enrolment data.\n");
            studentEnrolments = await this.getDataForStudentSchoolEnrolments(limit, studentEnrolmentIdsFailedToSync);
            const failedIds = await this.syncIteratedData(studentEnrolments, pasiSchema, service, "student enrollments",
            this.upsertForStudentEnrollments, [adminUid, nowTime]);
            studentEnrolmentIdsFailedToSync = studentEnrolmentIdsFailedToSync.concat(failedIds);
          } while (studentEnrolments.length !== 0);
        }

        else if (/^DiplomaExamMark\.?[0-9]{4}/.test(service.type)) {
          /*
          // Important Points/Assumptions/Explanation of Code
          //
          // Sync all diploma exam marks. The key thing is that DiplomaExamMarks allow us to
          // make user_roles for G12 students based on the information provided (but putting them in
          // placeholder classes for now).
          //
          // The 3 diploma exam related services allow us information to filter the marks we care about,
          // and then process them and turn them into enrolments.

          // Note that we will have school_classes made, based on the school and course.
          // When doing diploma_exam_mark -> user_role, we will however only need to ensure a placeholder G12
          // school class exists for that SchoolYear, to create that user_role
          //
          // One final note:  for each diploma exam mark, we need a placeholder school_class linked user_role
          // There can only be one of these max: per school per student.
          // But each unique ASN in the diploma exam marks (that is, 1 student), only gets
          // one school linked user_role, based on the student_school_enrolments table.
          // That is, each student who is in G12 and writing diploma exams will get only ONE school user_role
          // based on student_school_enrolments, EVEN if they write their exams in different schools.
          //
          // Additinoally, every school has one school class for each course that has a diploma exam mark for it.
          // This is through creating diploma exam sittings as required based on the marks.
          //
          // Important fields from `pasi_data.diploma_exams`:
          // CourseCode, RefId (unique), IsDeleted (deletions), SchoolYear

          // Important fields from `pasi_data.diploma_exam_marks`:
          // ExamRefId (foreign ID to diploma_exams), RefId (unique), IsDeleted (deletions), StateProvinceId (ASN),
          // ComponentMarks (ExamSittingRefId - indicates which school the mark belongs to)

          // Important fields from `pasi_data.diploma_exam_sittings`:
          // ExamRefId (foreign ID to diploma_exams), RefId (unique), IsDeleted (deletions),
          // OrganizationCode (foreign ID to school.foreignID, once parsed properly)
          */

          let diplomaExamMarks = [];
          let failedToSyncDiplomaExamMarkIds: number[] = [];
          let foundNewFails = false;
          do {
            console.log("Getting diploma exam mark data.\n");
            let diplomaTestWindows = (await this.getTestWindowsForPASIImport())
              .filter(tw => tw.type_slug === allowedDiplomaGroupType);
            diplomaExamMarks = await this.getDataForDiplomaExamMarks(
              pasiSchema,
              service.pasi_data_schema_table,
              limit,
              failedToSyncDiplomaExamMarkIds,
              diplomaTestWindows
            );
            const failedIds = await this.syncIteratedData(
              diplomaExamMarks,
              pasiSchema,
              service,
              "diploma exam marks",
              this.diplomaExamMarksToUserRoles,
              [ adminUid, nowTime, diplomaTestWindows ]
            );
            foundNewFails = !!failedIds.length;
            failedToSyncDiplomaExamMarkIds = failedToSyncDiplomaExamMarkIds.concat(failedIds);
          } while (diplomaExamMarks.length !== 0 || foundNewFails);
        }

        else if (/^DiplomaExamSitting\.?[0-9]{4}/.test(service.type)) {
            // sync only the deleted diploma exam sittings for now

          let diplomaExamSittings = [];
          let failedToSyncDiplomaExamSittingIds: number[] = [];

          do {
            console.log("Getting deleted diploma exam sittings data.\n");
            diplomaExamSittings = await this.getDataForDeletedDiplomaExamSittings(pasiSchema, service.pasi_data_schema_table,
            limit, failedToSyncDiplomaExamSittingIds);
            const failedIds = await this.syncIteratedData(diplomaExamSittings, pasiSchema, service, "deleted diploma exam sittings",
            this.handleDeletedDiplomaExamSittings, [adminUid, nowTime]);
            failedToSyncDiplomaExamSittingIds = failedToSyncDiplomaExamSittingIds.concat(failedIds);
          } while (diplomaExamSittings.length !== 0);

        }

        else if (/^DiplomaExam\.?[0-9]{4}/.test(service.type)) {
          // sync only the deleted diploma exams for now

          let diplomaExams = [];
          let diplomaExamsFailedToSync: any[] = [];

          do {
            console.log("Getting deleted diploma exams data.\n");
            diplomaExams = await this.getDataForDeletedDiplomaExams(pasiSchema, service.pasi_data_schema_table,
            limit, diplomaExamsFailedToSync);
            const failedIds = await this.syncIteratedData(diplomaExams, pasiSchema, service, "deleted diploma exams",
            this.handleDeletedDiplomaExams, [adminUid, nowTime]);
            diplomaExamsFailedToSync = diplomaExamsFailedToSync.concat(failedIds);
          } while (diplomaExams.length !== 0);
        }

        else if (service.type === "Course") {
          // do nothing for now
          // not implemented for now, might be in the future
        }

        else {
          throw new BadRequest("Invalid request. The service " + service.type + " is not yet configured.");
        }
      }

      catch(e) {
        console.log("Unexpected error while schema syncing, error: " + e);
        throw("Unexpected error while schema syncing, error: " + e);
      }
    }

    else {
      throw new BadRequest("Unacceptable request into patch call of abed-pasi.");
    }

    return [];
  }

  private async syncPASIFromS3ToDB() {

    const services = (await dbRawRead(this.app, [],
    `
      SELECT psv.*
      FROM pasi_data.pasi_latest_versions psv
      WHERE psv.is_revoked = 0
      ;
    `));

    for (let service of services) {
      let S3FetchInfo =
      {
        operation: "getObject",
        bucket: bucketDomain,
        key: baseAWSPath + service + ".json"
      };

      try {
        const fileURL = await this.generateS3SignedUrl(S3FetchInfo);
        const response = await fetch(fileURL);
        let data = await response.json();

        // data should be exactly how this.create() expects it for the various different types
        // if this is not the case, data will need to be modified here to be made like this

        // sync from S3 to pasi_data schema
        await this.create(data,
        {
          query: {
            type: service
          }
        });

        // sync from pasi_data to mpt_dev schema
        await this.patch(null, [], {
          query: {
            type: service,
            syncAcrossSchemas: 1
          }
        });
      }

      catch(e) {
        throw(e);
      }
    }
  }

  async generateS3SignedUrl({ operation, bucket, key, expires }:
    {
      operation: string,
      bucket: string,
      key: string,
      expires?: number
    }): Promise<string> {
      return await s3.getSignedUrlPromise(operation, {
        Key: key,
        Bucket: bucket,
        Expires: expires || 900
      });
    }

  private async syncIteratedData(data: any[], pasiSchema: string,
  service: any, syncType: string, syncFunction: Function, functionArgs: any[]): Promise<number[]> {
    // note: functionArgs should always look like: [adminUid, nowTime] when passed in
    // note: it's important the syncFunction always returns the ids to sync with the key "id"

    console.log("Total batch of " + syncType + " #: ", data.length);

    /*
    // batch is an array of 2 arrays;
    // the 1st array is successfully synced records of the service
    // the 2nd array is records of the service that failed to sync
    */
    let batch: (any[])[] = [[], []];
    let idsFailedToSync: number[] = [];

    if (data.length === 0) {
      console.log(`No more ${syncType} required to sync.`);
    }

    else {
      console.log(`Starting next nested batch (batch of batch of data retrieved) sync process for ${syncType}...`);
      console.log("This is " + maxIterationsAtOnce + " at a time.");
    }

    for (let i = 0; i < data.length; i++) {
      batch[0].push(data[i]);
      // make batches for the data upsertion, as the # of data CAN be in millions for which this function is called
      if (((i + 1) % maxIterationsAtOnce === 0 ) || (i === data.length - 1)) {

        let modFunctionArgs = [batch[0], ... functionArgs];
        batch = await syncFunction.apply(this, modFunctionArgs);

        if (batch[0].length !== 0) {
          await this.turnOnSyncedFlag(pasiSchema, service.pasi_data_schema_table,
          batch[0].map((record: any) => record.id));
        }

        idsFailedToSync = idsFailedToSync.concat(batch[1]);

        batch = [[], []];
        console.log(`${(i + 1)} out of ${data.length} ${syncType} processed (may or may not be successfully synced).`);
        console.log("Current Time: " + moment().format() + "\n\n");
      }
    }

    return idsFailedToSync;
  }

  generateStudentsWhereClause(stateProvinceIds: string[]): string {
    // only restrict students by StateProvinceId

    let stateProvinceIdsStr = "";
    for (let i = 0; i < stateProvinceIds.length; i++) {
      if (i != 0) {
        stateProvinceIdsStr+=", ";
      }

      stateProvinceIdsStr+=this.enquoteVal(stateProvinceIds[i]);
    }

    return ` AND StateProvinceId in (${stateProvinceIdsStr})`;
  }

  public enquoteVal(val: string | number): string {
    return '"' + val + '"';
  }

  async getStateProvinceIdsToSync(): Promise<string[]> {
    const DEMStateProvinceIds = await dbRawRead(this.app, [allowedStudentSchoolEnrolmentYears],
    `
        SELECT DISTINCT
            dem.StateProvinceId
        FROM
            pasi_data.diploma_exam_marks dem
                JOIN
            pasi_data.diploma_exams de ON de.RefId = dem.ExamRefId
                JOIN
            pasi_data.courses c ON c.CourseCode = de.CourseCode
                JOIN
            pasi_data.students AS s ON dem.StateProvinceId = s.StateProvinceId
        WHERE
                s.uid IS NULL
                AND de.SchoolYear IN (?)
                AND c.course_in_scope = 1
                AND (dem.synced_to_mpt_dev = 0 OR s.synced_to_mpt_dev = 0)
    ;
    `);

    const SSEStateProvinceIds = await dbRawRead(this.app,
    [allowedStudentSchoolEnrolmentGrades, allowedStudentSchoolEnrolmentYears],
      `
          SELECT DISTINCT
          sse.StateProvinceId
      FROM
          pasi_data.student_school_enrolments AS sse
            JOIN pasi_data.students AS s ON sse.StateProvinceId = s.StateProvinceId
      WHERE
              s.uid IS NULL
              AND Grade IN (?)
              AND SchoolYear IN (?)
              AND (sse.synced_to_mpt_dev = 0 OR s.synced_to_mpt_dev = 0)
      ;
      `);

    const uidStateProvinceIds = await dbRawRead(
      this.app,
      [],
        `
        SELECT DISTINCT
            s.StateProvinceId
        FROM
            pasi_data.students AS s
        WHERE
            s.uid IS NOT NULL AND s.synced_to_mpt_dev = 0
        ;`
    );
    const ASNs: string[] = ([] as any[]).concat(
      DEMStateProvinceIds.map(row => row.StateProvinceId),
      SSEStateProvinceIds.map(row => row.StateProvinceId),
      uidStateProvinceIds.map(row => row.StateProvinceId)
    );
    return Array.from(new Set(ASNs)).sort();
  }

  generateStudentSchoolEnrolmentWhereClause(): string {
    // done based on current requirements, timeline and scope
    let allowedSchoolYearsStr = "";
    let allowedGradesStr = "";
    let SQL = "";

    for (let i = 0; i < allowedStudentSchoolEnrolmentYears.length; i++) {
      if (i != 0) {
        allowedSchoolYearsStr+=", ";
      }

      allowedSchoolYearsStr+=`'${allowedStudentSchoolEnrolmentYears[i]}'`;
    }

    for (let i = 0; i < allowedStudentSchoolEnrolmentGrades.length; i++) {
      if (i != 0) {
        allowedGradesStr+=", ";
      }

      allowedGradesStr+=`'${allowedStudentSchoolEnrolmentGrades[i]}'`;
    }

    if (allowedGradesStr !== "") {
      SQL+=` AND Grade in (${allowedGradesStr})`;
    }

    if (allowedSchoolYearsStr !== "") {
      SQL+=` AND SchoolYear in (${allowedSchoolYearsStr})`;
    }

    return SQL;
  }

  async turnOnSyncedFlag(pasiSchema: string, pasiDataSchemaTable: string, IDsSynced: number[]): Promise<void> {
    await dbRawWrite(this.app, [pasiSchema, pasiDataSchemaTable, IDsSynced],
    `
      UPDATE ??.??
      SET synced_to_mpt_dev = 1
      WHERE id in (?)
    ;
    `);
  }

  async getDataForDeletedDiplomaExams(pasiSchema: string, pasiDataSchemaTable: string,
  limitOnRecordSet: {limitStart: number, numRowsToFetch: number},
  idsToIgnore: number[]): Promise<any[]> {
    /*
    // only used for getting deleted diploma exams for now, as that's all we sync
    // note we still look at only specific school years
    */

    let params: any[] = [allowedStudentSchoolEnrolmentYears];
    let params2: any[] = [limitOnRecordSet.limitStart, limitOnRecordSet.numRowsToFetch];

    if (idsToIgnore.length !== 0) {
      params.push(idsToIgnore);
    }

    params = params.concat(params2);

    return await dbRawRead(this.app, params,
    `
    SELECT
        c.CourseCode,
        de.*
    FROM
        pasi_data.diploma_exams de
            INNER JOIN
        pasi_data.courses c ON c.CourseCode = de.CourseCode
    WHERE
        de.SchoolYear IN (?)
            AND c.course_in_scope = 1
            AND de.synced_to_mpt_dev = 0
            AND de.IsDeleted = 1
            ${idsToIgnore.length !== 0 ? 'AND de.id not in (?)' : ''}
    ORDER BY de.CourseCode DESC
    LIMIT ?, ?
    ;
    `);
  }

  async getDataForDeletedDiplomaExamSittings(pasiSchema: string, pasiDataSchemaTable: string,
  limitOnRecordSet: {limitStart: number, numRowsToFetch: number}, idsToIgnore: number[]): Promise<any[]> {
    /*
    // only used for getting deleted DES for now, as that's all we sync
    // note we still look at only specific school years, and we don't ignore deleted diploma exams, as need all such deleted DES
    */

    let params: any[] = [allowedStudentSchoolEnrolmentYears];
    let params2: any[] = [limitOnRecordSet.limitStart, limitOnRecordSet.numRowsToFetch];

    if (idsToIgnore.length !== 0) {
      params.push(idsToIgnore);
    }

    params = params.concat(params2);

    return await dbRawRead(this.app, params,
    `
    SELECT
        des.*,
        c.CourseCode,
        de.SchoolYear
    FROM
        pasi_data.diploma_exam_sittings des
            INNER JOIN
        pasi_data.diploma_exams de ON de.RefId = des.ExamRefId
            INNER JOIN
        pasi_data.courses c ON c.CourseCode = de.CourseCode
    WHERE
        de.SchoolYear IN (?)
            AND c.course_in_scope = 1
            AND des.synced_to_mpt_dev = 0
            AND des.IsDeleted = 1
            ${idsToIgnore.length !== 0 ? 'AND des.id not in (?)' : ''}
    ORDER BY de.CourseCode DESC
    LIMIT ?, ?
    ;
    `);
  }

  async getDataForDiplomaExamMarks(
    pasiSchema: string,
    pasiDataSchemaTable: string,
    limitOnRecordSet: {limitStart: number, numRowsToFetch: number},
    idsToIgnore: number[],
    diplomaTestWindows: PasiImportTestWindow[]
  ): Promise<any[]> {
    /*
    // only used for getting diploma exam marks to sync, as the query is very specific, as opposed to other
    // services.
    // Note: we are ignoring deleted diploma exams. If a DiplomaExam IsDeleted, we also ignore
    // the corresponding diploma exam mark and do not sync it at all.
    */

    const examPeriodsFound = Array.from(new Set(diplomaTestWindows.map(tw => tw.pasi_exam_period)));
    let params: any[] = [allowedStudentSchoolEnrolmentYears, examPeriodsFound];
    let params2: any[] = [limitOnRecordSet.limitStart, limitOnRecordSet.numRowsToFetch];

    if (idsToIgnore.length !== 0) {
      params.push(idsToIgnore);
    }

    params = params.concat(params2);

    return await dbRawRead(this.app, params,
    `
        SELECT
        dem.*,
        c.CourseCode,
        de.SchoolYear,
        de.ExamPeriod
    FROM
        pasi_data.diploma_exam_marks dem
            INNER JOIN
        pasi_data.diploma_exams de ON de.RefId = dem.ExamRefId
            INNER JOIN
        pasi_data.courses c ON c.CourseCode = de.CourseCode
    WHERE
        de.SchoolYear in (?)
            AND de.ExamPeriod IN (?)
            AND c.course_in_scope = 1
            AND dem.synced_to_mpt_dev = 0
            AND de.IsDeleted = 0
            ${idsToIgnore.length !== 0 ? 'AND dem.id not in (?)' : ''}
    ORDER BY dem.StateProvinceId, dem.IsDeleted ASC
    LIMIT ?,?
    ;
    `);
  }

  async getRelevantData(
    pasiSchema: string,
    pasiDataSchemaTable: string,
    limitOnRecordSet?: { limitStart: number, numRowsToFetch: number },
    orderBySQL?: string,
    whereClauseSQL?: string,
    idsToIgnore?: number[],
    includeSynced?: boolean
  ): Promise<any[]> {
    let params: any[] = [pasiSchema, pasiDataSchemaTable];

    if (idsToIgnore != null && idsToIgnore.length !== 0) {
      params.push(idsToIgnore);
    }

    if (limitOnRecordSet != null) {
      params.push(limitOnRecordSet.limitStart);
      params.push(limitOnRecordSet.numRowsToFetch);
    }

    return await dbRawRead(this.app, params,
    `
      SELECT *
      FROM ??.??
      WHERE ${idsToIgnore != null && idsToIgnore.length !== 0 ? 'id not in (?) AND' : ''}
      ${includeSynced ? '1' : 'synced_to_mpt_dev = 0'} ${whereClauseSQL != null ? whereClauseSQL : ''}
      ${orderBySQL != null ? orderBySQL : ''}
      ${limitOnRecordSet != null ? 'LIMIT ?, ?' : ''}
      ;
    `);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }

  async getRequiredUids(studentASNs: string[]): Promise<any[]> {
    return await dbRawRead(this.app, [studentASNs],
    `
      SELECT s.uid, s.StateProvinceId
      FROM pasi_data.students s
      WHERE s.StateProvinceId in (?)
      ;
    `);
  }

  async upsertIntoSchools(schools: any[], adminUid: number, nowTime: any): Promise<any[]> {
    const currSchoolsInDB = await this.getAllCurrSchools();
    const currDistrictsinDB = await this.getAllCurrDistricts();
    let schoolsUpdatedSuccessfully: any[] = [];
    const knex: Knex = this.app.get('knexClientWrite');
    let schoolDataToUpsert = [];
    let uGroupsDataToCreate = [];
    let newSchoolDataToInsert = [];
    let schoolsFailedToSync = [];

    // foreign_id not included here as for updating school rows, the foreign_id would be the same
    // that's how we find the row to update
    let schoolsTableColumnsToMerge: string[] =
    [
      "schl_dist_group_id",
      "name",
      "availability_status",
      "classifications",
      "delivery_address",
      "email_address",
      "fax_number",
      "city",
      "address",
      "country",
      "postal_code",
      "province",
      "phone_number",
      "statuses",
      "lang"
    ];

    for (let school of schools) {
      // console.log(school);
      // see which schools are existing and not existing already in the DB
      const schoolAuthorityCode =
      this.findCurrentGoverningSchoolAuthorityCode(school?.GoverningAuthorities?.GoverningSchoolAuthority || []);
      const schoolName = this.findCurrentName(school?.Names?.OrganizationName || []);

      if (schoolAuthorityCode == null) {
        // skip schools without a school district; this behaviour can be changed if needed later
        schoolsFailedToSync.push(school);
        continue;
      }

      // both the schools.foreign_id and OrganizationCode as it comes from PASI should be strings,
      // but we can convert them to numbers and compare, it will still work (e.g. 0009 turns into 9)
      // this is to ensure equality
      // this is also done below when setting schoolInDB
      const district = currDistrictsinDB.find(DBDistrict =>
      +this.parseOrganizationCode(DBDistrict.foreign_id) === +schoolAuthorityCode);

      if (district == null) {
        // skip schools without a school district which is not in the mpt_dev school_districts table
        // districts should be synced first to mpt_dev in order to prevent any such cases anyways
        schoolsFailedToSync.push(school);
        continue;
      }
      school.schl_dist_group_id = district.group_id;

      const schoolInDB = currSchoolsInDB.find(DBSchool =>
      +this.parseOrganizationCode(DBSchool.foreign_id) === +school.OrganizationCode);

      // console.log(schoolInDB, schoolAuthorityCode, schoolName, +school.OrganizationCode);
      let schoolDataObjToUpsert: any = {
        schl_dist_group_id: school.schl_dist_group_id,
        foreign_id: this.formPASIStyleOrgCode(true, school.OrganizationCode || ""),
        name: schoolName || "",
        availability_status: school.AvailabilityStatus || "",
        classifications: this.objectToJSON(school.Classifications),
        delivery_address: this.objectToJSON(school.DeliveryAddress),
        email_address: school.EmailAddress || "",
        fax_number: school.FaxNumber || "",
        lang: defaultSchoolLang,
        // storing mailing address in existing schools table columns, delivery address as a separate column
        city: school?.MailingAddress?.City || "",
        address: school?.MailingAddress?.Street || "",
        country: school?.MailingAddress?.Country || "",
        postal_code: school?.MailingAddress?.PostalCode || "",
        province: school?.MailingAddress?.StateProvince || "",
        phone_number: school?.PhoneNumber || "",
        statuses: this.objectToJSON(school?.Statuses)
      };

      if (schoolInDB == null) {
        uGroupsDataToCreate.push(
        {
          group_type: "school",
          description: schoolName,
          created_on: nowTime,
          created_by_uid: adminUid
        });

        newSchoolDataToInsert.push(schoolDataObjToUpsert);
      }

      else {
        schoolDataObjToUpsert.id = schoolInDB.id;
        schoolDataToUpsert.push(schoolDataObjToUpsert);
      }

      schoolsUpdatedSuccessfully.push(school);
    }

    if (uGroupsDataToCreate.length > 0) {
      console.log("schools creation/update in progress, plus u_groups.\n");

      // length of this array is always 1
      const firstUGroupIdInserted: number[] =
      await knex("mpt_dev"+"."+"u_groups")
      .insert(uGroupsDataToCreate);
      let firstUGroupId: number = firstUGroupIdInserted[0];

      // note that the # of u_groups inserted above HAS to be equal to the length of newSchoolDataToInsert
      for (let i = 0; i < newSchoolDataToInsert.length; i++) {
        newSchoolDataToInsert[i].group_id = firstUGroupId;
        newSchoolDataToInsert[i].created_on = nowTime;
        newSchoolDataToInsert[i].created_by_uid = adminUid;
        schoolDataToUpsert.push(newSchoolDataToInsert[i]);
        firstUGroupId++;
      }
    }

    else {
      console.log("No schools to insert. Updates being done if required!\n");
    }

    console.log("Len1: ", schoolDataToUpsert.length);

    if (schoolDataToUpsert.length > 0) {
      await knex("mpt_dev"+"."+"schools")
      .insert(schoolDataToUpsert)
      .onConflict("id")
      .merge(schoolsTableColumnsToMerge);
    }

    // after new schools have been inserted, we set up some more tables required for schools to work
    // properly on ABED. We need the new school IDs for these, so this has to be done here.

    if (newSchoolDataToInsert.length !== 0) {
      const assDefToCon = await this.getAssessmentDefsToConfigure();
      const newSchools = await this.findNewSchoolsInserted(newSchoolDataToInsert.map(newSchool => newSchool.group_id));
      let schoolAssessmentMapsInsertions = [];
      let schoolTypeAssessmentSettingsInsertions = [];

      for (let i = 0; i < newSchools.length; i++) {
        for (let assessmentDef of assDefToCon) {
          schoolAssessmentMapsInsertions.push(
            {
              assessment_def_id: assessmentDef.id,
              school_id: newSchools[i].id,
              created_by_uid: adminUid,
              created_on: nowTime,
              updated_by_uid: adminUid,
              updated_on: nowTime
            });

            schoolTypeAssessmentSettingsInsertions.push(
            {
              assessment_def_id: assessmentDef.id,
              settings_type_slug: "ABED",
              school_id: newSchools[i].id,
              created_by_uid: adminUid,
              created_on: nowTime,
              updated_by_uid: adminUid,
              updated_on: nowTime
            });
        }
      }

      await knex("mpt_dev.school_assessments_map")
      .insert(schoolAssessmentMapsInsertions);

      await knex("mpt_dev.school_type_assessment_settings")
      .insert(schoolTypeAssessmentSettingsInsertions);
    }

    return schoolsUpdatedSuccessfully;
  }

  async findNewSchoolsInserted(newSchoolGroupIds: number[]): Promise<any[]> {
    return await dbRawRead(this.app, [newSchoolGroupIds],
    `
      SELECT id, group_id, foreign_id
      FROM schools
      WHERE group_id in (?)
    `);
  }

  async getAssessmentDefsToConfigure(): Promise<any[]> {
    return await dbRawRead(this.app, [],
    `
      SELECT *
      FROM assessment_def
      WHERE is_for_pasi_sync = 1
      ;
    `);
  }

  async upsertIntoSchoolAuthorities(schoolAuthorities: any[], adminUid: number, nowTime: any): Promise<any[]> {
    const currDistrictsinDB = await this.getAllCurrDistricts();
    let schoolAuthoritiesUpdatedSuccessfully: any[] = [];
    const knex: Knex = this.app.get('knexClientWrite');
    let schoolAuthorityDataToUpsert = [];
    let uGroupsDataToCreate = [];
    let newSchoolAuthorityDataToInsert = [];

    // foreign_id not included here as for updating school_districts rows, the foreign_id would be the same
    // that's how we find the row to update
    let schoolDistrictsTableColumnsToMerge: string[] =
    [
      "name",
      "availability_status",
      "classifications",
      "delivery_address",
      "email",
      "fax_number",
      "city",
      "address",
      "country",
      "postal_code",
      "province",
      "phone_number",
      "statuses"
    ];

    for (let schoolAuthority of schoolAuthorities) {
      // see which school authorities are existing and not existing already in the DB
      // note I am turning both the foreign ID and OrganizationCode to numbers to ensure equality
      const schoolAuthorityName = this.findCurrentName(schoolAuthority?.Names?.OrganizationName || []);
      const schoolAuthorityInDB = currDistrictsinDB.find(DBDistrict =>
      +this.parseOrganizationCode(DBDistrict.foreign_id) === +schoolAuthority.OrganizationCode);


      let schoolAuthorityDataObjToUpsert: any = {
        foreign_id: this.formPASIStyleOrgCode(false, schoolAuthority?.OrganizationCode || ""),
        name: schoolAuthorityName || "",
        availability_status: schoolAuthority?.AvailabilityStatus || "",
        classifications: this.objectToJSON(schoolAuthority?.Classifications),
        delivery_address: this.objectToJSON(schoolAuthority?.DeliveryAddress),
        email: schoolAuthority?.EmailAddress || "",
        fax_number: schoolAuthority?.FaxNumber || "",
        // storing mailing address in school_districts existing columns, delivery address as a separate column
        city: schoolAuthority?.MailingAddress?.City || "",
        address: schoolAuthority?.MailingAddress?.Street || "",
        country: schoolAuthority?.MailingAddress?.Country || "",
        postal_code: schoolAuthority?.MailingAddress?.PostalCode || "",
        province: schoolAuthority?.MailingAddress?.StateProvince || "",
        phone_number: schoolAuthority?.PhoneNumber || "",
        statuses: this.objectToJSON(schoolAuthority?.Statuses)
      };

      if (schoolAuthorityInDB == null) {
        uGroupsDataToCreate.push(
        {
          group_type: "school_district",
          description: schoolAuthorityName,
          created_on: nowTime,
          created_by_uid: adminUid
        });

        newSchoolAuthorityDataToInsert.push(schoolAuthorityDataObjToUpsert);
      }

      else {
        schoolAuthorityDataObjToUpsert.id = schoolAuthorityInDB.id;
        schoolAuthorityDataToUpsert.push(schoolAuthorityDataObjToUpsert);
      }

      schoolAuthoritiesUpdatedSuccessfully.push(schoolAuthority);
    }

    if (uGroupsDataToCreate.length > 0) {
      console.log("school_districts creation/update in progress, plus u_groups.\n");
      // length of this array is always 1
      const firstUGroupIdInserted: number[] =
      await knex("mpt_dev"+"."+"u_groups")
      .insert(uGroupsDataToCreate);
      let firstUGroupId: number = firstUGroupIdInserted[0];

      // note that the # of u_groups inserted above HAS to be equal to the length of newSchoolAuthorityDataToInsert
      for (let i = 0; i < newSchoolAuthorityDataToInsert.length; i++) {
        newSchoolAuthorityDataToInsert[i].group_id = firstUGroupId;
        newSchoolAuthorityDataToInsert[i].created_on = nowTime;
        newSchoolAuthorityDataToInsert[i].created_by_uid = adminUid;
        schoolAuthorityDataToUpsert.push(newSchoolAuthorityDataToInsert[i]);
        firstUGroupId++;
      }
    }

    else {
      console.log("No school_districts to insert. Updates being done if required!\n");
    }

    console.log("Len1: ", schoolAuthorityDataToUpsert.length);

    if (schoolAuthorityDataToUpsert.length > 0) {
      await knex("mpt_dev"+"."+"school_districts")
      .insert(schoolAuthorityDataToUpsert)
      .onConflict("id")
      .merge(schoolDistrictsTableColumnsToMerge);
    }

    return schoolAuthoritiesUpdatedSuccessfully;
  }

  async createPlaceholderSchoolClassesIfNeeded(adminUid: number) {
    // if not already existing, creates placeholder classes for ALL schools (sample or non-sample)
    // for all required PASI test windows (marked by test_windows.is_for_pasi)

    console.log("\nStarting placeholder PASI school classes creation process, if required.\n");
    const defaultLang = defaultSchoolLang;
    const testWindows = await this.getTestWindowsForPASIImport();
    const currPlaceholderSchoolClasses = await this.getAllPlaceholderSchoolClassesForCurrentTW();
    const allSchools = await this.getAllCurrSchools();
    const defaultPlaceholderSlug = "lb_class_placeholder_default";
    const placeHolderClassName = await this.app.service('public/translation').getOneBySlug(defaultPlaceholderSlug, defaultLang);
    let bulkInsertData: any[] = [];
    console.log("Num schools: " + allSchools.length);
    console.log("Num test_windows: " + testWindows.length);

    for (let school of allSchools) {
      for (let testWindow of testWindows) {
        /*
        Note: the below condition - matching on testWindow.id and school.group_id, should be
        enough to ensure the creation of the required placeholder classes for all schools and
        all test_windows that exist. For G6 and G9, there should be 1 test_window for each PASI SchoolYear and
        PASI grade that are in scope, and corresponding type_slug_mappings and school_semesters as well.
        For G12, there should be 1 test_window for each PASI SchoolYear and ExamPeriod in scope, as specified
        by test_windows.pasi_exam_period.
        This way, all the required placeholder classes are created based off of the test_windows
        that are present for each SchoolYear and Grade, and in the case of G12, ExamPeriod.
        The test window is what allows us to match the right Grade and SchoolYear and even ExamPeriod.
        */
        const matchingPHSC = currPlaceholderSchoolClasses.find(cpsc => cpsc.tw_id === testWindow.id &&
        cpsc.schl_group_id === school.group_id);

        if (matchingPHSC == null) {
          let data = {
            schoolGroupId: school.group_id,
            testWindowId: testWindow.id,
            semesterId: testWindow.semester_id,
            schoolDistGroupId: school.schl_dist_group_id,
            groupType: testWindow.type_slug
          };
          bulkInsertData.push(data);
        }
      }
    }

    if (bulkInsertData.length === 0) {
      console.log("There are no school_classes or u_groups needed to be created for placeholder PASI classes. Done.\n");
    }

    else {
      await this.bulkCreatePlaceholderClass(bulkInsertData, placeHolderClassName, adminUid);
      console.log("Required school_classes for all schools without a placeholder PASI class are made.\n");
    }
  }

  public async logPASIErrors(errors: any, adminUid: number): Promise<void> {
    await this.updatePASIStatus(false, false, false);
    await this.logToDB(errors, adminUid);
  }

  public async logToDB(errors: any, adminUid: number): Promise<void> {
    await dbRawWrite(this.app, [errors, adminUid],
    `
      INSERT INTO pasi_error_logs
      (error, created_on, created_by_uid)
      values
      (?, now(), ?)
      ;
    `);
  }

  public async bulkCreatePlaceholderClass(requiredData: {schoolGroupId: number, testWindowId: number,
    schoolDistGroupId: number, groupType: string, semesterId: number}[],
    placeholderClassName: string, createdByUid: number)
    {
      try {
        let uGroupsInsertionData = [];
        let schoolClassesInsertionData = [];
        const knex: Knex = this.app.get('knexClientWrite');

        for (let data of requiredData) {
          uGroupsInsertionData.push({
            group_type: DBD_U_GROUP_TYPES.school_class,
            description: placeholderClassName,
            created_on: dbDateNow(this.app),
            created_by_uid: createdByUid
          });

          schoolClassesInsertionData.push({
            schl_group_id: data.schoolGroupId,
            schl_dist_group_id: data.schoolDistGroupId,
            name: placeholderClassName,
            semester_id: data.semesterId,
            key: ASNKey,
            dedicated_test_window_id: data.testWindowId,
            is_grouping: 1,
            group_type: data.groupType,
            created_by_uid: createdByUid,
            created_on: dbDateNow(this.app),
            access_code: null,  //leave it to null so it can not create session
            group_id: 0, // is set below, when u_groups are created
            is_active: 1,
            is_placeholder: 1,
            notes: "PASI related school classes - check test_windows.is_for_pasi for associated TWs."
          });
        }

        // length of this array is always 1
        const firstUGroupIdInserted: number[] =
        await knex("mpt_dev"+"."+"u_groups")
        .insert(uGroupsInsertionData);
        let firstUGroupId: number = firstUGroupIdInserted[0];

        // note that the # of u_groups inserted above HAS to be equal to the length of schoolClassesInsertionData
        // which HAS to equal to the length of uGroupsInsertionData
        for (let i = 0; i < schoolClassesInsertionData.length; i++) {
          schoolClassesInsertionData[i].group_id = firstUGroupId;
          firstUGroupId++;
        }

        await knex("mpt_dev"+"."+"school_classes")
        .insert(schoolClassesInsertionData);
      }

      catch(e) {
        console.log("Failed to create placeholder class");
        throw new Error("Failed to create placeholder class");
      }
    }

  findCurrentGoverningSchoolAuthorityCode(authorities: any[]): null | string {
    // input is the value of the key GoverningSchoolAuthority in `pasi_data.schools.GoverningAuthorities`
    // based on: https://extranet.education.alberta.ca/pasidevnet/Docs/Technical%20API/html/6c18d501-1772-ebbe-14cb-98e9d012594e.htm
    // this is with the assumption a school can only have 1 governing authority at any one point in time

    const foundAuthority = authorities.find(authority => {
      return moment.utc(authority.EffectiveDate).isBefore() &&
      moment.utc(authority.ExpiryDate).isAfter();
    });

    return foundAuthority == null ? null : foundAuthority.AuthorityCode;
  }

  findCurrentName(names: any[]): string {
    // input is the value of the key OrganizationName in `pasi_data.pasi_schools.Names` or `pasi_data.school_authorities.Names`
    // e.g. https://extranet.education.alberta.ca/pasidevnet/Docs/Technical%20API/html/a3c7b60e-f020-c332-84fc-899cdaa48960.htm
    // this is with the assumption a school and school authority can only have 1 name at any one point in time

    const foundName = names.find(name => {
      return moment.utc(name.EffectiveDate).isBefore() &&
      moment.utc(name.ExpiryDate).isAfter();
    });

    return foundName == null ? "" : foundName.Name;
  }

  async upsertIntoUserRolesAndUserMetas(studentEnrolments: any[], uidASNPairs: any[],
  adminUid: number, nowTime: any): Promise<any[]> {
    /*
    // if an ASN doesn't have an associated uid with it in pasi_data.students table,
    // we can't do anything in terms of user_roles, and so we do not mark these kinds of rows as synced successfully
    // note we also mark these kinds of rows as not synced successfully if the school or school class
    // of the enrolment row is not present in our DB already, and we do not sync it

    // Note we also want to upsert into user_metas for key "Lang", based on the school's language here
    // when we do the insertion into user_roles. Nothing in PASI indicates a school's language as of now, so
    // we will assume all school languages to be English for now (proposed by Zach).
    // Lastly, we also upsert into user_metas for key "GroupType"
    */

    if (studentEnrolments.length === 0) {
      return [[], []];
    }

    console.log("Original length of student school enrolment rows to sync: " + studentEnrolments.length);
    const validUidASNPairs: any[] = uidASNPairs.filter(uidASN => uidASN.uid != null);
    const invalidUidASNPairs: any[] = uidASNPairs.filter(uidASN => uidASN.uid == null || uidASN.uid == 0);
    const validUids: number[] = validUidASNPairs.map(uidASN => uidASN.uid);
    const PASIKeyNamespace = userMetasPasiKeyNamespace;
    const sseTWUMKey = "StudentSchoolEnrolmentInfo";
    const gradeTWUMKey = twumGradeKeyName;

    let studentEnrolmentsToSync: any[] = [];
    let studentsMissingUids: string[] = [];
    let sseRowIdsFailedToSync: number[] = []

    for (let i = 0; i < studentEnrolments.length; i++) {
      // there is only 1 unique ASN max in validUidASNPairs
      const uidASNPair = validUidASNPairs.find(each => studentEnrolments[i].StateProvinceId === each.StateProvinceId);
      if (uidASNPair != null) {
        studentEnrolmentsToSync.push(
        {
          ...studentEnrolments[i],
          uid: uidASNPair.uid
        });
      }

      else {
        studentsMissingUids.push(studentEnrolments[i].StateProvinceId);
        sseRowIdsFailedToSync.push(studentEnrolments[i].id);
      }
    }

    if (studentsMissingUids.length !== 0) {
      console.log("pasi_data.students missing ASNs rows themselves found. Failed student_school_enrolments IDs of such rows: "
      + sseRowIdsFailedToSync);
    }

    if (invalidUidASNPairs.length !== 0) {
      console.log("ASNs from student enrollments are containing NULL uids in pasi_data.students. Such ASNs:" +
      invalidUidASNPairs.map(each => each.StateProvinceId));
    }

    console.log(`There were ${validUidASNPairs.length} student uid-ASN pairs found.`);
    console.log("Fetching user_roles for users.");
    const userRoles = await this.getAllUserRolesExisting(validUids);
    console.log(`Fetching user_roles for users done, ${userRoles.length} roles found.`);

    const twUserMetas = await this.getAllTwUserMetasExisting(validUids, PASIKeyNamespace, sseTWUMKey);
    console.log(`Fetching tw_user_metas for users done, ${twUserMetas.length} tw metas found.`);

    const existingGradeTWUserMetas = await this.getAllTwUserMetasExisting(validUids, PASIKeyNamespace, gradeTWUMKey);
    console.log(`Fetching GRADE tw_user_metas for SSE (when syncing SSE) for users done, ${existingGradeTWUserMetas.length} tw metas found.`);

    const schoolClasses: any[] = await this.getAllPlaceholderSchoolClassesForCurrentTW();
    return await this.insertNewUserRolesAndUpsertUserMetas(studentEnrolmentsToSync, sseRowIdsFailedToSync, nowTime, adminUid,
    schoolClasses, userRoles, twUserMetas, existingGradeTWUserMetas, PASIKeyNamespace, sseTWUMKey, gradeTWUMKey);
  }

  async getAllCurrSchools() {
    return await dbRawRead(this.app, [],
    `
      SELECT id, group_id, schl_dist_group_id, foreign_id, name
      FROM schools s;
      ;
    `);
  }

  async getAllCurrDistricts() {
    return await dbRawRead(this.app, [],
    `
      SELECT id, group_id, foreign_id
      FROM school_districts sd;
      ;
    `);
  }

  async getAllPlaceholderSchoolClassesForCurrentTW() {
    return await dbRawRead(this.app, [],
    `
          SELECT
          ss.id AS 'semester_id',
          tw.PASI_school_year,
          tw.pasi_exam_period,
          sc.id,
          sc.group_id,
          s.group_id AS schl_group_id,
          s.foreign_id,
          tsm.pasi_grade_code,
          tw.id AS tw_id,
          sc.created_on,
          sc.created_by_uid
      FROM
          school_classes sc
              INNER JOIN
          school_semesters ss ON ss.id = sc.semester_id
              INNER JOIN
          test_windows tw ON tw.id = ss.test_window_id
              INNER JOIN
          type_slug_mappings tsm ON tsm.type_slug = tw.type_slug
              INNER JOIN
          schools s ON s.group_id = sc.schl_group_id
      WHERE
          tw.is_for_pasi = 1
              AND tsm.is_qa = 0
              AND sc.is_active = 1
              AND sc.is_placeholder = 1
      ;
    `);
  }

  async getStudentSchoolEnrolmentsForDiploma(ASNs: string[]): Promise<any[]> {
    return await dbRawRead(this.app, [ASNs, allowedStudentSchoolEnrolmentYears],
    `
      SELECT sse.* -- id, IsDeleted, SchoolCode, StateProvinceId, SchoolYear, Grade
      FROM pasi_data.student_school_enrolments sse
      WHERE StateProvinceId in (?)
      AND SchoolYear in (?)
      ORDER BY StateProvinceId, Grade, IsDeleted ASC, RegistrationExitDate DESC, RegistrationStartDate DESC
      ;
    `);
  }

  async getAllNonPlaceholderSchoolClassesForG12TWs(): Promise<any[]> {
    // for diploma_exam_marks, diploma exam sittings and diploma exams sync purposes
    // TODO: currently we only care about 1 school year, so this works, but this assumption
    // and code might fail once we take into account more than 1 school year and more importantly,
    // more than 1 test window and more than 1 semester ID
    return await dbRawRead(this.app, [allowedDiplomaGroupType],
    `
          SELECT
          ss.id AS "semester_id",
          sc.id,
          sc.group_id,
          s.group_id AS "schl_group_id",
          s.foreign_id,
          tsm.pasi_grade_code,
          tw.id AS tw_id,
          tw.PASI_school_year,
          tw.pasi_exam_period,
          sc.foreign_id AS "course_code",
          s.name as "school_name"
      FROM
          school_classes sc
              INNER JOIN
          school_semesters ss ON ss.id = sc.semester_id
              INNER JOIN
          test_windows tw ON tw.id = ss.test_window_id
              INNER JOIN
          type_slug_mappings tsm ON tsm.type_slug = tw.type_slug
              INNER JOIN
          schools s ON s.group_id = sc.schl_group_id
      WHERE
          tw.is_for_pasi = 1
              AND tsm.type_slug = ?
              AND tsm.is_qa = 0
              AND sc.is_active = 1
              AND sc.is_placeholder = 0
      ;
    `);
  }

  async getTestWindowsForPASIImport():Promise<PasiImportTestWindow[]> {
    // should return all the test_windows in general, including the ones associated with G12
    // for all SchoolYears and ExamPeriods
    return await dbRawRead(this.app, [],
    `
      SELECT
        tw.id,
        tw.type_slug,
        tsm.pasi_grade_code,
        ss.id as "semester_id",
        tw.PASI_school_year,
        tw.pasi_exam_period
      FROM test_windows tw
      INNER JOIN type_slug_mappings tsm on tsm.type_slug = tw.type_slug
      INNER JOIN school_semesters ss on tw.id = ss.test_window_id
      WHERE tw.is_for_pasi = 1
      AND tsm.is_qa = 0
      ;
    `);
  }

  async revokeUserRoles(uids: number[], adminUid: number): Promise<void> {
    // DEPRECATED FUNCTION; use if really needed for something, but it is a bad idea
    // in most/all cases, from what I have learned and found while working on PASI
    return;

    // revokes all user_roles, including both school and school_classes related user_roles
    await dbRawWrite(this.app, [adminUid, uids],
    `
      UPDATE user_roles
      SET is_revoked = 1, revoked_on = now(), revoked_by_uid = ?
      WHERE uid in (?)
      AND is_revoked = 0
      ;
    `);
  }

  async revokeUserRolesByGroupId(groupIds: number[], adminUid: number) {
    // DEPRECATED FUNCTION; use if really needed for something, but it is a bad idea
    // in most/all cases, from what I have learned and found while working on PASI
    // group IDs contain for example an array of school class group IDs, for
    // which roles are to be revoked

    return;
    await dbRawWrite(this.app, [adminUid, groupIds],
      `
        UPDATE user_roles
        SET is_revoked = 1, revoked_on = now(), revoked_by_uid = ?
        WHERE group_id in (?)
        AND is_revoked = 0
        ;
      `);
  }

  async getAllTwUserMetasExisting(uids: number[], PASIKeyNamespace: string, metaKey: string): Promise<any[]> {
    if (uids.length === 0) {
      return [];
    }

    return await dbRawRead(this.app, [uids, PASIKeyNamespace, metaKey],
    `
    SELECT
      twum.id,
      twum.uid,
      twum.key_namespace,
      twum.key,
      twum.value,
      twum.test_window_id,
      twum.meta,
      twum.sse_id,
      twum.dem_id
    FROM
      tw_user_metas twum
    WHERE
      twum.uid in (?)
      AND twum.key_namespace = ?
      AND twum.key = ?
    ;
    `);
  }

  async getAllUserRolesExisting(uids: number[]): Promise<any[]> {
    if (uids.length === 0) {
      return [];
    }

    return await dbRawRead(this.app, [uids],
    `
      SELECT
        ur.id,
        ur.role_type,
        ur.uid,
        ur.group_id
      FROM
        user_roles ur
      WHERE
        ur.uid in (?)
        AND ur.is_revoked = 0
        AND ur.role_type = "schl_student"
      ;
    `);
  }

  findUserRoles(group_id: number, uid: number, userRoles: any[]): any[] {
    // user_roles always contain schl_student group_type user_roles which are is_revoked = 0
    // there should only be 1 such unrevoked user_role of the below constraint being returned at any one time
    // but in case there is more than 1, we will handle it accordingly by using filter and not find
    return userRoles.filter(userRole => userRole.uid === uid && userRole.group_id === group_id);
  }

   findTWUserMetas(uniqueId: number, twUserMetas: any[], uniqueColName: string): any[] {
    // there can only be 1 record max (return array length === 1) returned from this function
    // twum.meta should be an object (even if empty - {})
    // can also use meta directly for this, but using the unique id for now

    return twUserMetas.filter(twum => twum[uniqueColName] === uniqueId);
  }

  findSSETWUserMetasByUidAndSchoolYear(uid: number, sseTWUserMetas: any[], schoolYear: string): any[] {
    // find student based on uid and SchoolYear
    // the idea is that in SSE, there is only 1 grade amongst rows per student per SchoolYear, no matter what.
    return sseTWUserMetas.filter(row => {
      const meta = JSON.parse(row.meta) || {};
      return row.uid === uid && meta?.SchoolYear === schoolYear;
    });
  }

  findStudentGradeTWUserMetas(twUserMetas: any[], testWindowId: number, uid: number) {
    return twUserMetas.filter(row => +row.test_window_id === testWindowId && +row.uid === +uid);
  }

  async insertNewUserRolesAndUpsertUserMetas(studentEnrolmentsToSync: any[], sseRowIdsFailedToSync: number[], nowTime: any, adminUid: number,
  placeholderSchoolClasses: any[], existingUserRoles: any[], existingTwUserMetas: any[], existingGradeTWUserMetas: any[],
  PASIKeyNamespace: string, sseTWUMKey: string, gradeTWUMKey: string): Promise<any[]> {
    if (studentEnrolmentsToSync.length === 0) {
      return [[], sseRowIdsFailedToSync];
    }

    const knex: Knex = this.app.get('knexClientWrite');
    let groupTypeUserMetaKey = "GroupType";
    let groupTypeUserMetaKeyNamespace = "abed_course";
    let langUserMetaKey = "Lang";
    let langUserMetaKeyNamespace = "abed_dyn";
    let dataToUpsert: any[] = [];
    let twUserMetasToUpsert: any[] = [];
    let schoolEnrolmentsSuccessful: any[] = [];
    let uidSchoolGroupIds: Map<number, Set<number>> = new Map();
    let uidSchoolClassGroupIds: Map<number, Set<number>> = new Map();
    let userMetasToUpsert: any[] = [];
    let schoolEnrolmentsFailed: number[] = [];
    let deletedStudentSchoolEnrolmentRecords: number[] = [];
    let gradeTWUserMetasToInsert: any[] = [];
    let grade6TWUmIdsToUpdate: number[] = [];
    let grade9TWUmIdsToUpdate: number[] = [];
    let seenTwumGradeInsertions: Map<number, Set<number>> = new Map();

    // for non g6 and non g9 grades only.
    let dynamicGradeTWumUpdateQueryParams: {
      grade: string,
      ids: number[]
    }[] = [];

    const gradeTWumUpdateQuery =
    `
      UPDATE tw_user_metas
      SET value = ?, updated_by_uid = ?, updated_on = ?
      WHERE id in (?)
      ;
    `;
    const schema = "mpt_dev";
    const userMetaTableName = "user_metas";
    const twUserMetaTableName = "tw_user_metas";
    const twUserMetasColumnsToMergeOn = ["sse_id"];
    const twUserMetaColumnsToMerge = ["uid", "test_window_id", "meta", "updated_by_uid", "updated_on"];
    const userMetasColumnsToMergeOn = ['uid', 'key'];
    const userMetasColumnsToMerge = ['value', 'updated_on', 'updated_by_uid', 'key_namespace'];
    const userRolesColumnsToMergeOn = ['id'];
    const userRolesColumnsToMerge = ['is_revoked', 'revoked_on', 'revoked_by_uid'];

    for (let i = 0; i < studentEnrolmentsToSync.length; i++) {
      /*
      // At this point, any student that has a grade NOT IN allowedStudentSchoolEnrolmentGrades,
      // and is NOT Grade 12, we know for sure is a case where the Grade changed for the same SchoolYear, and they were
      // previously synced as a G6/G9 student, because to retrieve student enrollments to sync
      // we use synced students with pasi_data.students != NULL, which are based on G6/G9 cases in
      // the allowed SchoolYears anyways.

      // So any student that has a grade not in the allowed student school enrolment grades and not G12,
      // we don't want to do any user_metas or user_role changes to; just updating (or in cases where isTempSolnForUnsyncedSSEEnabled = true, inserting) the sse_id row
      // in tw_user_metas, and updating the StudentGrade in tw_user_metas is all we need to do.

      // We can simply do that in the beginning once we detect a non allowedStudentSchoolEnrolmentGrades
      // grade, and then mark the id as synced and continue.

      // This doesn't account for G6 -> G12 and G9 -> G12 direct grade changes in the SchoolYear,
      // but having diploma exam marks for the student in the same SchoolYear will handle that in the DEM sync.
      */

      if (!allowedStudentSchoolEnrolmentGrades.includes(studentEnrolmentsToSync[i].Grade)
      && studentEnrolmentsToSync[i].IsDeleted === 0) {

        // 1. First, find any sse_row matching the SchoolYear of the current sse_row,
        // from tw_user_metas for the student, as this is a Grade case change.
        // If we cannot find one at all, and isTempSolnForUnsyncedSSEEnabled is false, do not sync this row.
        // If we cannot find one at all, and isTempSolnForUnsyncedSSEEnabled is true, create a sse row based on test window inference.
        const meta = studentEnrolmentsToSync[i];
        const twUserMetasFound = this.findSSETWUserMetasByUidAndSchoolYear(meta.uid, existingTwUserMetas, meta.SchoolYear);
        let studentGradeChangeOldTWID: number = 0;

        if (twUserMetasFound.length === 0 && !isTempSolnForUnsyncedSSEEnabled) {
          // console.log("No existing tw user metas found, cannot sync SSE id " + studentEnrolmentsToSync[i].id);
          schoolEnrolmentsFailed.push(studentEnrolmentsToSync[i].id);
          continue;
        }

        else if (twUserMetasFound.length === 0 && isTempSolnForUnsyncedSSEEnabled) {
          studentGradeChangeOldTWID = this.getStudentOldTWID(existingGradeTWUserMetas, meta.uid);
        }

        else {
          // isTempSolnForUnsyncedSSEEnabled only applies if twUserMetasFound.length === 0
          // so it does not matter if it is true or false in this case
          studentGradeChangeOldTWID = twUserMetasFound[0].test_window_id;
        }

        // 2. if there is 1 matching tw_user_meta row found which has the same sse_id as the current sse row,
        // update its information of the old row in tw_user_meta. if there is no such sse row found to update, insert it
        // this is done using the upsert functionality

        // all the rows retrieved from findSSETWUserMetasByUidAndSchoolYear should have only 1 test window ID and SchoolYear associated with it
        // this is the test window ID associated with the student's original grade, for that SchoolYear

        // there should only be 1 row max matching the sse_id
        const matchingSSERow = twUserMetasFound.find(row => row.sse_id === meta.id);

        let twUserMetaRecord: any;
        if (matchingSSERow != null) {
          twUserMetaRecord = {
            uid: meta.uid,
            sse_id: meta.id,
            test_window_id: studentGradeChangeOldTWID,
            meta: JSON.stringify(meta),
            updated_by_uid: adminUid,
            updated_on: nowTime
          };
        }

        else {
          twUserMetaRecord = {
            uid: meta.uid,
            key_namespace: PASIKeyNamespace,
            key: sseTWUMKey,
            value: 1,
            sse_id: meta.id,
            test_window_id: studentGradeChangeOldTWID,
            meta: JSON.stringify(meta),
            created_by_uid: adminUid,
            created_on: nowTime
          };
        }

        twUserMetasToUpsert.push(twUserMetaRecord);

        // 3. change tw_user_meta grade. We know which test_window_id to change for the StudentGrade
        // key in tw_user_metas because any old sse_row directly reveals the test_window_id of the student's original grade
        // in the case of there being an old sse_row. If there is not a sse_row and isTempSolnForUnsyncedSSEEnabled
        // is true, we still have a test window ID we infer by getStudentOldTWID.

        // we only want to have 1 uid-tw pair inserted into tw_user_metas max
        let gradeTwumUpsertNeeded: boolean = false;
        const studentFound = seenTwumGradeInsertions.get(studentEnrolmentsToSync[i].uid);

        if (studentFound == null) {
          seenTwumGradeInsertions.set(studentEnrolmentsToSync[i].uid, new Set([studentGradeChangeOldTWID]));
          gradeTwumUpsertNeeded = true;
        }

        else {
          if (!studentFound.has(studentGradeChangeOldTWID)) {
            studentFound.add(studentGradeChangeOldTWID);
            gradeTwumUpsertNeeded = true;
          }
        }

        if (gradeTwumUpsertNeeded) {
          // StudentGrade tw_user_metas inserts/updates
          const studentGradetwUserMetasFound = this.findStudentGradeTWUserMetas(
          existingGradeTWUserMetas, studentGradeChangeOldTWID, studentEnrolmentsToSync[i].uid);

            if (studentGradetwUserMetasFound.length === 0) {
              // this is a grade change case, so this case should not happen
              // it can POSSIBLY happen if isTempSolnForUnsyncedSSEEnabled = true and no old sse rows were found
              // for the student, but even in those case, there should be some existing and found.
              // As a safety check though, we should insert a record just to be safe anyways, in this case

              let gradeTwUserMetaRecord: any = {
                uid: studentEnrolmentsToSync[i].uid,
                key_namespace: PASIKeyNamespace,
                key: gradeTWUMKey,
                value: studentEnrolmentsToSync[i].Grade,
                test_window_id: studentGradeChangeOldTWID,
                created_by_uid: adminUid,
                created_on: nowTime
              };

              gradeTWUserMetasToInsert.push(gradeTwUserMetaRecord);
            }

            else {
              // change student grade
              // in this scope, we have to be in a non G6 and non G9 grade

              const relevantQuery = dynamicGradeTWumUpdateQueryParams.find(each => each.grade === studentEnrolmentsToSync[i].Grade)
              if (relevantQuery != null) {
                relevantQuery.ids.push(...studentGradetwUserMetasFound.map(record => record.id));
              }

              else {
                dynamicGradeTWumUpdateQueryParams.push({
                  grade: studentEnrolmentsToSync[i].Grade,
                  ids: [...studentGradetwUserMetasFound.map(record => record.id)]
                });
              }
            }
        }

        // 4. mark as synced and continue to next student enrolment
        schoolEnrolmentsSuccessful.push(studentEnrolmentsToSync[i]);
        continue;
      }

      let shouldStudentBeRevoked: boolean = false;

      // note I am turning both the foreign ID and SchoolCode to numbers, as PASI stores
      // the SchoolCode as "0009" but our DB can store it as 9, so we want to ensure equality
      // this is also done below when setting schoolInDB
      const schoolClass = placeholderSchoolClasses.find(schoolClass => {
        return schoolClass.pasi_grade_code === studentEnrolmentsToSync[i].Grade
        && +this.parseOrganizationCode(schoolClass.foreign_id) === +studentEnrolmentsToSync[i].SchoolCode
        && +schoolClass.PASI_school_year === +studentEnrolmentsToSync[i].SchoolYear
      });

      if (schoolClass == null) {
        // cannot find a school class, do not do user_roles and user_meta insertions
        // this should never be the case, but might be if there are some missing test_windows/schools/etc.
        schoolEnrolmentsFailed.push(studentEnrolmentsToSync[i].id);
        continue;
      }

      if (studentEnrolmentsToSync[i].IsDeleted === 1 && !allowedDiplomaGrades.includes(studentEnrolmentsToSync[i].Grade)) {
        shouldStudentBeRevoked = true;
        deletedStudentSchoolEnrolmentRecords.push(studentEnrolmentsToSync[i].id);
      }

      else if (studentEnrolmentsToSync[i].IsDeleted === 0 && !allowedDiplomaGrades.includes(studentEnrolmentsToSync[i].Grade)) {
        /* Pseudocode
        // For every non-deleted `student_school_enrolments` (note this links to a SCHOOL user_role), insert 1 row into `tw_user_meta` accordingly.
        // Store `student_school_enrolment` record information into the `meta` column.
        // We know if the row already exists in `tw_user_meta` or not (for upsert purposes) based on `tw_user_meta.sse_id` field.
        //`test_window_id` and `uid` are not needed for the upsert check, because `student_school_enrolment_id` indirectly covers both aspects anyways
        // (`SchoolYear` to `test_window_id`, and `StateProvinceId` to `uid`. In the event either of those fields change, the `tw_user_meta` row should be updated with the latest information anyways).

        // Do the same - an insertion - into tw_user_metas for key StudentGrade
        */

        // we only want to have 1 uid-tw pair inserted into tw_user_metas max
        let gradeTwumUpsertNeeded: boolean = false;
        const studentFound = seenTwumGradeInsertions.get(studentEnrolmentsToSync[i].uid);

        if (studentFound == null) {
          seenTwumGradeInsertions.set(studentEnrolmentsToSync[i].uid, new Set([schoolClass.tw_id]));
          gradeTwumUpsertNeeded = true;
        }

        else {
          if (!studentFound.has(schoolClass.tw_id)) {
            studentFound.add(schoolClass.tw_id);
            gradeTwumUpsertNeeded = true;
          }
        }

        if (gradeTwumUpsertNeeded) {
          // StudentGrade tw_user_metas inserts/updates
          const studentGradetwUserMetasFound = this.findStudentGradeTWUserMetas(
          existingGradeTWUserMetas, schoolClass.tw_id, studentEnrolmentsToSync[i].uid);

            if (studentGradetwUserMetasFound.length === 0) {
              let gradeTwUserMetaRecord: any = {
                uid: studentEnrolmentsToSync[i].uid,
                key_namespace: PASIKeyNamespace,
                key: gradeTWUMKey,
                value: studentEnrolmentsToSync[i].Grade,
                test_window_id: schoolClass.tw_id,
                created_by_uid: adminUid,
                created_on: nowTime
              };

              gradeTWUserMetasToInsert.push(gradeTwUserMetaRecord);
            }

            else {
              // in this scope, we are not doing a grade change, so only 2 Grades for now for non diploma PASI sync
              if (allowedStudentSchoolEnrolmentGrades[0] === studentEnrolmentsToSync[i].Grade) {
                // G6 grade
                grade6TWUmIdsToUpdate.push(...studentGradetwUserMetasFound.map(record => record.id));
              }

              else {
                // G9 grade
                grade9TWUmIdsToUpdate.push(...studentGradetwUserMetasFound.map(record => record.id));
              }
            }
        }

        // SSE tw_user_metas insertions
        const twUserMetasFound = this.findTWUserMetas(studentEnrolmentsToSync[i].id, existingTwUserMetas, "sse_id");
        const meta = studentEnrolmentsToSync[i];

        if (twUserMetasFound.length === 0) {
          let twUserMetaRecord: any = {
            uid: meta.uid,
            key_namespace: PASIKeyNamespace,
            key: sseTWUMKey,
            value: 1,
            sse_id: meta.id,
            test_window_id: schoolClass.tw_id,
            meta: JSON.stringify(meta),
            created_by_uid: adminUid,
            created_on: nowTime
          };

          twUserMetasToUpsert.push(twUserMetaRecord);
        }

        else {
          let twUserMetaRecord: any = {
            uid: meta.uid,
            test_window_id: schoolClass.tw_id,
            sse_id: meta.id,
            meta: JSON.stringify(meta),
            updated_by_uid: adminUid,
            updated_on: nowTime
          };

          twUserMetasToUpsert.push(twUserMetaRecord);
        }
      }

      // we only want to have 1 uid-schoolClass group id pair inserted into user_roles max, and update
      // the existing such school linked user_roles only once
      let schoolClassUserRoleUpsertNeeded: boolean = false;
      const studentFoundSchlClass = uidSchoolClassGroupIds.get(studentEnrolmentsToSync[i].uid);

      if (studentFoundSchlClass == null) {
        uidSchoolClassGroupIds.set(studentEnrolmentsToSync[i].uid, new Set([schoolClass.group_id]));
        schoolClassUserRoleUpsertNeeded = true;
      }

      else {
        if (!studentFoundSchlClass.has(schoolClass.group_id)) {
          studentFoundSchlClass.add(schoolClass.group_id);
          schoolClassUserRoleUpsertNeeded = true;
        }
      }

      if (schoolClassUserRoleUpsertNeeded) {
        const userRolesFound = this.findUserRoles(schoolClass.group_id, studentEnrolmentsToSync[i].uid, existingUserRoles);

        if (userRolesFound.length === 0) {
          // insert school_class user_role
          let schoolClassUserRole: any = {
            role_type: "schl_student",
            uid: studentEnrolmentsToSync[i].uid,
            group_id: schoolClass.group_id,
            created_on: nowTime,
            created_by_uid: adminUid,
            is_revoked: shouldStudentBeRevoked ? 1 : 0
          };

          if (shouldStudentBeRevoked) {
            schoolClassUserRole.revoked_on = nowTime;
            schoolClassUserRole.revoked_by_uid = adminUid;
          }

          dataToUpsert.push(schoolClassUserRole);
        }

        else {
          for (let userRole of userRolesFound) {
            /*
            // important: note the IsDeleted = 1 rows will always be sorted first (for the same ASN and other conditions)
            // so if the same group ID associated with the row is used for multiple rows of diploma_exam_marks
            // or student_school_enrolments, it will always ensure the last one being upserted into user_roles
            // is always the unrevoked (is_revoked = 0) row for that group_id, if any IsDeleted = 0 rows exist
            // This is ideal and what we want, as it ensures a non-revoked user_role row whre needed
            */
            if (shouldStudentBeRevoked) {
              let schoolClassUserRoleToUpdate: any = {
                id: userRole.id,
                is_revoked: 1,
                revoked_on: nowTime,
                revoked_by_uid: adminUid
              };
              dataToUpsert.push(schoolClassUserRoleToUpdate);
            }

            else {
              let schoolClassUserRoleToUpdate: any = {
                id: userRole.id,
                is_revoked: 0,
                revoked_on: null,
                revoked_by_uid: null
              };
              dataToUpsert.push(schoolClassUserRoleToUpdate);
            }
          }
        }
      }

      // we only want to have 1 uid-school group id pair inserted into user_roles max, and update
      // the existing such school linked user_roles only once
      let schoolUserRoleUpsertNeeded: boolean = false;
      const studentFound = uidSchoolGroupIds.get(studentEnrolmentsToSync[i].uid);

      if (studentFound == null) {
        uidSchoolGroupIds.set(studentEnrolmentsToSync[i].uid, new Set([schoolClass.schl_group_id]));
        schoolUserRoleUpsertNeeded = true;
      }

      else {
        if (!studentFound.has(schoolClass.schl_group_id)) {
          studentFound.add(schoolClass.schl_group_id);
          schoolUserRoleUpsertNeeded = true;
        }
      }

      if (schoolUserRoleUpsertNeeded) {
        const userRolesFound = this.findUserRoles(schoolClass.schl_group_id, studentEnrolmentsToSync[i].uid, existingUserRoles);

        if (userRolesFound.length === 0) {
          // inserts school user_role
          let schoolUserRole: any = {
            role_type: "schl_student",
            uid: studentEnrolmentsToSync[i].uid,
            group_id: schoolClass.schl_group_id,
            created_on: nowTime,
            created_by_uid: adminUid,
            is_revoked: shouldStudentBeRevoked ? 1 : 0
          };

          if (shouldStudentBeRevoked) {
            schoolUserRole.revoked_on = nowTime;
            schoolUserRole.revoked_by_uid = adminUid;
          }

          dataToUpsert.push(schoolUserRole);
        }

        else {
          for (let userRole of userRolesFound) {
            /*
            // important: note the IsDeleted = 1 rows will always be sorted first (for the same ASN and other conditions)
            // so if the same group ID associated with the row is used for multiple rows of diploma_exam_marks
            // or student_school_enrolments, it will always ensure the last one being upserted into user_roles
            // is always the unrevoked (is_revoked = 0) row for that group_id, if any IsDeleted = 0 rows exist
            // This is ideal and what we want, as it ensures a non-revoked user_role row whre needed
            */
            if (shouldStudentBeRevoked) {
              // you just let the user_roles exist with no changes if no existing user roles to revoke
              let schoolUserRoleToUpdate: any = {
                id: userRole.id,
                is_revoked: 1,
                revoked_on: nowTime,
                revoked_by_uid: adminUid
              };
              dataToUpsert.push(schoolUserRoleToUpdate);
            }

            else {
              let schoolUserRoleToUpdate: any = {
                id: userRole.id,
                is_revoked: 0,
                revoked_on: null,
                revoked_by_uid: null
              };
              dataToUpsert.push(schoolUserRoleToUpdate);
            }
          }
        }

        let groupTypeUserMetaToUpsert: any = {
          uid: studentEnrolmentsToSync[i].uid,
          key: groupTypeUserMetaKey,
          value: this.parseGroupType(studentEnrolmentsToSync[i]?.Grade),
          updated_by_uid: adminUid,
          updated_on: nowTime,
          created_by_uid: adminUid,
          created_on: nowTime,
          key_namespace: groupTypeUserMetaKeyNamespace
        };

        let langUserMetaToUpsert: any = {
          uid: studentEnrolmentsToSync[i].uid,
          key: langUserMetaKey,
          value: defaultSchoolLang,
          updated_by_uid: adminUid,
          updated_on: nowTime,
          created_by_uid: adminUid,
          created_on: nowTime,
          key_namespace: langUserMetaKeyNamespace
        }

        userMetasToUpsert.push(groupTypeUserMetaToUpsert);
        userMetasToUpsert.push(langUserMetaToUpsert);
      }

      schoolEnrolmentsSuccessful.push(studentEnrolmentsToSync[i]);
    }

    console.log("# of records being synced marked as IsDeleted in student_school_enrolments: " + deletedStudentSchoolEnrolmentRecords.length);

    if (dataToUpsert.length > 0) {
      try {
        console.log("Updating new school and school_classes user_roles now.")
        const resultsUserRoles =
        await knex("mpt_dev"+"."+"user_roles")
        .insert(dataToUpsert)
        .onConflict(userRolesColumnsToMergeOn)
        .merge(userRolesColumnsToMerge)
        console.log("Updating new school and school_classes user_roles done.");
      }

      catch(e) {
        console.log(e);
        throw(e);
      }
    }

    if (gradeTWUserMetasToInsert.length > 0) {
      try {
        // no updates with this logic with this, just inserts
        console.log("Inserting grade tw_user_metas for SSE now.");
        const results =
        await knex("mpt_dev"+"."+twUserMetaTableName)
        .insert(gradeTWUserMetasToInsert)
        console.log("Inserting grade tw_user_metas for SSE done.");
      }

      catch(e) {
        console.log(e);
        throw(e);
      }
    }

    // for non grade change cases
    if (grade6TWUmIdsToUpdate.length > 0) {
      try {
        console.log("Updating tw_user_metas for SSE G6 now.");
        await dbRawWrite(this.app, [allowedStudentSchoolEnrolmentGrades[0], adminUid, nowTime, grade6TWUmIdsToUpdate],
        gradeTWumUpdateQuery);
        console.log("Updating tw_user_metas for SSE G6 done.");
      }

      catch(e) {
        console.log(e);
        throw(e);
      }
    }

    // for non grade change cases
    if (grade9TWUmIdsToUpdate.length > 0) {
      try {
        console.log("Updating tw_user_metas for SSE G9 now.");
        await dbRawWrite(this.app, [allowedStudentSchoolEnrolmentGrades[1], adminUid, nowTime, grade9TWUmIdsToUpdate],
        gradeTWumUpdateQuery);
        console.log("Updating tw_user_metas for SSE G9 done.");
      }

      catch(e) {
        console.log(e);
        throw(e);
      }
    }

    // for grade change cases
    for (let query of dynamicGradeTWumUpdateQueryParams) {
      console.log(`Updating tw_user_metas for SSE Grade ${query.grade} now.`);
      await dbRawWrite(this.app, [query.grade, adminUid, nowTime, query.ids],
      gradeTWumUpdateQuery);
      console.log(`Updating tw_user_metas for SSE Grade ${query.grade} done.`);
    }

    if (twUserMetasToUpsert.length > 0) {
      try {
        console.log("Inserting/updating tw_user_metas now.")
        const results =
        await knex("mpt_dev"+"."+twUserMetaTableName)
        .insert(twUserMetasToUpsert)
        .onConflict(twUserMetasColumnsToMergeOn)
        .merge(twUserMetaColumnsToMerge)
        console.log("Inserting/updating tw_user_metas done.")
      }

      catch(e) {
        console.log(e);
        throw(e);
      }
    }

    if (userMetasToUpsert.length > 0) {
      try {
        console.log("Creating/updating user_metas started.");
        const resultsUserMetas =
        await knex(schema+"."+userMetaTableName)
        .insert(userMetasToUpsert)
        .onConflict(userMetasColumnsToMergeOn)
        .merge(userMetasColumnsToMerge);
        console.log("Creating/updating user_metas done.")
      }

      catch(e) {
        console.log(e);
        throw(e);
      }
    }

    if (schoolEnrolmentsFailed.length !== 0) {
      console.log("Failed enrollment IDs, because of missing school class or missing twum when doing a grade change: " + schoolEnrolmentsFailed);
    }

    return [schoolEnrolmentsSuccessful, schoolEnrolmentsFailed.concat(sseRowIdsFailedToSync)];
  }

  async updatePASIStatus(isNoAccess: boolean, isUnknown: boolean, isPasiAvailable: boolean) {
    let pasiStatusJSON = {
      noAccess: isNoAccess,
      unknown: isUnknown,
      isPasiAvailable
    };

    // only 1 record should be returned from this query, always
    const pasiStatusRecord = await dbRawRead(this.app, [pasiStatusSlug],
    `
      SELECT *
      FROM pasi_statuses
      WHERE slug = ?
      ;
    `);

    if (pasiStatusRecord.length === 0) {
      await dbRawWrite(this.app, [pasiStatusSlug, JSON.stringify(pasiStatusJSON)],
      `
        INSERT INTO pasi_statuses (slug, data)
        values (?, ?)
        ;
      `);
    }

    else {
      await dbRawWrite(this.app, [JSON.stringify(pasiStatusJSON), pasiStatusRecord[0].id],
      `
        UPDATE pasi_statuses
        SET data = ?
        WHERE id = ?
        ;
      `);
    }
  }

  async updateUsers(students: any[], usersColumnsToMergeOn: string[], usersColumnsToMerge: string[],
  mainSchema: string, usersTable: string, nowTime: any, adminUid: number) {
    if (students.length > 0) {
      const knex: Knex = this.app.get('knexClientWrite');
      let usersDataToUpsert = this.getUsersNamesToUpsert(students, nowTime, adminUid, false);

      try {
        var results =
        await knex(mainSchema+"."+usersTable)
        .insert(usersDataToUpsert)
        .onConflict(usersColumnsToMergeOn)
        .merge(usersColumnsToMerge);
      }

      catch(e) {
        console.log("Error in upserting", e);
        throw(e);
      }
    }
  }

  async createUsers(students: any[], nowTime: any, adminUid: number, mainSchema: string,
  pasiSchema: string, usersTable: string) {
    // after this finishes, we are guranteed to have an uid for each ASN in the original input (to be updated)
    // students is an array of the new users needed to be created
    if (students.length > 0) {
      const knex: Knex = this.app.get('knexClientWrite');
      let usersDataToUpsert = this.getUsersNamesToUpsert(students, nowTime, adminUid, true);
      let pasiStudentsToUpdate: any[] = [];

      // length of the firstUidInserted array is always 1
      const firstUidInserted: number[] =
      await knex(mainSchema+"."+usersTable)
      .insert(usersDataToUpsert);
      let firstUid: number = firstUidInserted[0];

      // note that the # of users inserted above HAS to be equal to the length of students
      // as students is the new students to be created. Also, the length of usersDataToUpsert will always
      // be equal to the length of the students array.
      for (let i = 0; i < students.length; i++) {
        students[i].uid = firstUid;
        pasiStudentsToUpdate.push(
        {
          id: students[i].id, // note, this id is `pasi_data.students.id`
          uid: firstUid
        });
        firstUid++;
      }

      await knex(pasiSchema+"."+"students")
      .insert(pasiStudentsToUpdate)
      .onConflict("id")
      .merge(["uid"]);
    }
  }

  getUsersNamesToUpsert(students: any[], nowTime: any, adminUid: number, areNewUsers: boolean): any[] {
    let usersDataToUpsert = [];
    for (let i = 0; i < students.length; i++) {
      const studentNames: any[] = students[i].StudentNames.Name;
      const preferredName = studentNames.find(sName => sName.RefId === students[i].PreferredNameRefId);
      let firstName, lastName, middleName = "";

      if (preferredName != null) {
        firstName = preferredName.FirstName; // has to exist
        lastName = preferredName.LastName; // has to exist
        middleName = preferredName?.MiddleName || "" // may not exist;
      }

      else {
        // fallback, in case there is an error in PASI and the PreferredNameRefID has no matches
        // pick 1st student name (most students have only 1 name in this array anyways)
        firstName = studentNames[0].FirstName; // has to exist
        lastName = studentNames[0].LastName; // has to exist
        middleName = studentNames[0].MiddleName || "" // may not exist;
      }

      // note: both new or existing students should be marked as is_PASI_student = 1
      // this is because they are both still linked to pasi_data.students
      let userObjToUpsert: any = {
        account_type: "student",
        first_name: firstName,
        last_name: lastName,
        middle_name: middleName,
        created_on: nowTime,
        created_by_uid: adminUid,
        is_PASI_student: 1
      };

      if (!areNewUsers) {
        userObjToUpsert.id = students[i].uid;
      }

      usersDataToUpsert.push(userObjToUpsert);
    }

    return usersDataToUpsert;
  }

  async findUidsFromASNs(ASNs: string[], ASNKey: string, ASNKeyNamespace: string) {
    if (!ASNs?.length) {
      return [];
    }
    return await dbRawRead(this.app, [ASNKey, ASNs, ASNKeyNamespace],
      `
        SELECT um.uid, um.value as "ASN", um.id
        FROM mpt_dev.user_metas um
        WHERE um.key = ?
        AND um.value in (?)
        AND um.key_namespace = ?
        ;
      `);
  }

  async findDiplomaExamMarksFromSittings(examSittingRefIds: string[]): Promise<any> {
    let SQLREGEXPVal = "";
    for (let i = 0; i < examSittingRefIds.length; i++) {
      SQLREGEXPVal+=examSittingRefIds[i];

      if (i !== (examSittingRefIds.length - 1)) {
        SQLREGEXPVal+="|";
      }
    }

    return await dbRawRead(this.app, [],
    `
    SELECT
        dem.*,
        JSON_EXTRACT(ComponentMarks,
                '$.ComponentMarkInfo[*].ExamSittingRefId') AS ExamSittingRefIds
    FROM
        pasi_data.diploma_exam_marks dem
    WHERE
        JSON_EXTRACT(ComponentMarks,
                '$.ComponentMarkInfo[*].ExamSittingRefId') REGEXP '${SQLREGEXPVal}'
    ;
    `);
  }

  async handleDeletedDiplomaExamSittings(deletedDiplomaSittings: any[], adminUid: number, nowTime: any): Promise<any[]> {
    if (deletedDiplomaSittings.length === 0) {
      return [[], []];
    }

    /*
    There are 3 steps needed:
    1. For each deleted diploma exam sitting, go through all the diploma exam marks associated with the
    particular diploma exam sitting through the ExamSittingRefId. If every single associated diploma exam mark is deleted,
    we can proceed with the deletion of the diploma sitting.

    2. For each associated diploma mark with the diploma exam sittings which can be deleted, go through
    each mark and revoke any user_roles found associated with that mark (and ASN and uid) and school.

    3. For each mark row found, delete the mark row if the
    the deleted sitting is the only sitting associated with the mark. If there is more than 1 sittings associated with the mark,
    keep the mark row but delete one of the sitting ref ID from the JSON.

    Important note: we do not sync deleted diploma exam sitting or process them if the DES associated diploma exam marks
    are not also marked as IsDeleted = 1.
    */

    console.log("Getting relevant information and data for deleted DES sync.");

    const schoolClasses: any[] = await this.getAllNonPlaceholderSchoolClassesForG12TWs();
    const schools = await this.getAllCurrSchools();
    const diplomaExamColumnsToMergeOn = ['id'];
    const diplomaExamColumnsToMerge = ['ComponentMarks'];
    const knex: Knex = this.app.get('knexClientWrite');
    const diplomaExamMarkSchemaTableName = "pasi_data.diploma_exam_marks";
    const userRolesColumnsToMergeOn = ['id'];
    const userRolesColumnsToMerge = ['is_revoked', 'revoked_on', 'revoked_by_uid'];
    const allAssociatedDiplomaExamMarks: any[] =
    await this.findDiplomaExamMarksFromSittings(deletedDiplomaSittings.map(record => record.RefId));

    let diplomaSittingsToProcess: any[] = [];
    let diplomaExamMarkIdsToDelete: number[] = [];
    let diplomaExamMarkDataToPatch: any[] = [];
    let syncedDeletedDiplomaExamSittingIds: any[] = [];
    let skippedDESIdsAsNonDeletedDEM: number[] = [];
    let relevantDiplomaExamMarks: any[] = [];
    let userRolesToRevoke = [];
    let uidASNPairs: any[] = [];
    let existingUserRoles: any[] = [];

    // does 1. above
    console.log("Finding valid deleted diploma sittings to sync and process.");
    for (let i = 0; i < deletedDiplomaSittings.length; i++) {
      // note: DS = diploma exam sitting here
      const currDS = deletedDiplomaSittings[i];

      // all DEM associated with the DES HAVE to be deleted in order to be processed successfully
      if (this.areAllRelevantDEMMarkedAsDeleted(currDS.RefId, allAssociatedDiplomaExamMarks)) {
        syncedDeletedDiplomaExamSittingIds.push(currDS);
        diplomaSittingsToProcess.push(currDS);
      }

      else {
        skippedDESIdsAsNonDeletedDEM.push(currDS.id);
        continue;
      }
    }

    console.log("Finding associated DEM from the DES rows, and processing.");

    if (diplomaSittingsToProcess.length > 0) {
      relevantDiplomaExamMarks =
      await this.findDiplomaExamMarksFromSittings(diplomaSittingsToProcess.map(record => record.RefId));
    }

    if (relevantDiplomaExamMarks.length > 0) {
      uidASNPairs = await this.getRequiredUids(relevantDiplomaExamMarks.map(record => record.StateProvinceId));
      console.log(`Getting required uids for students of diploma exam marks done,` +
      `${uidASNPairs.length} uid-ASN pairs found.`);
    }

    if (uidASNPairs.length > 0) {
      existingUserRoles = await this.getAllUserRolesExisting(uidASNPairs.map(uidASN => uidASN.uid));
      console.log(`Fetching user_roles for users done, ${existingUserRoles.length} roles found.`);
    }

    // does 2. above
    for (let i = 0; i < relevantDiplomaExamMarks.length; i++) {
      const currDEM = relevantDiplomaExamMarks[i];

      // uidASNPairs is guranteed to have each ASN appear only once max (unique) in the array
      const uid = uidASNPairs.find(uidASNPair => uidASNPair.StateProvinceId === currDEM.StateProvinceId);

      if (uid !== null) {
        // if uid is null, do nothing; nothing to revoke; we still consider the diploma exam sitting wow
        // synced, even if this is the case
        for (let mark of currDEM.ComponentMarks.ComponentMarkInfo) {

          // guranteed to only find 1 diploma exam sitting, as ExamSittingRefId is unique
          let diplomaExamSittingFound = diplomaSittingsToProcess.find((des:any) => des.RefId === mark.ExamSittingRefId);
          if (diplomaExamSittingFound != null) {
            const school = schools.find(school => {
              return !(["", " "].includes(this.parseSchoolOrgCode(diplomaExamSittingFound.OrganizationCode)))
              && +this.parseOrganizationCode(school.foreign_id) === +this.parseSchoolOrgCode(diplomaExamSittingFound.OrganizationCode)
            });

            // if no school found, or no school class found, just skip the revokation completely
            // if no school/school_class found, it means the user_role does not exist, that's acceptable
            if (school !== null) {
              const schoolClass = schoolClasses.find(schoolClass => {
                return schoolClass.course_code === diplomaExamSittingFound.CourseCode
                && !(["", " "].includes(this.parseSchoolOrgCode(diplomaExamSittingFound.OrganizationCode)))
                && +this.parseOrganizationCode(schoolClass.foreign_id) === +this.parseSchoolOrgCode(diplomaExamSittingFound.OrganizationCode)
                && +schoolClass.PASI_school_year === +diplomaExamSittingFound.SchoolYear
                && schoolClass.pasi_exam_period === diplomaExamSittingFound.ExamPeriod;
              });

              if (schoolClass != null) {

                // find all non-revoked user roles associated with this school class and uid.
                // if any are found, mark them as needing to be revoked
                const userRolesFound = this.findUserRoles(schoolClass.group_id, uid, existingUserRoles);

                for (let userRole of userRolesFound) {
                  const userRoleToUpdate = {
                    id: userRole.id,
                    is_revoked: 1,
                    revoked_on: nowTime,
                    revoked_by_uid: adminUid
                  };
                  userRolesToRevoke.push(userRoleToUpdate);
                }
              }
            }
          }
        }
      }

      // does 3. above
      if (currDEM.ExamSittingRefIds.length === 0) {
        // note: currDem.ExamSittingRefIds.length can never be 0, because of the way the SQL query is
        // which retrieved these marks. But just in case. Failproof code.
        continue;
      }

      if (currDEM.ExamSittingRefIds.length === 1) {
        // if the DEM only has 1 exam sitting ref ID associated with it, delete the DEM row completely
        diplomaExamMarkIdsToDelete.push(currDEM.id);
      }

      else {
        // if the DEM only more than 1 exam sitting ref ID associated with it
        // we want to update the DEM row such that the JSON does not contain this exam sitting ref ID anymore

        // this function will return how the new ComponentMarks column should look like of the DEM row.
        // if ComponentMarks.ComponentMarkInfo is empty, that means all diploma exam sitting IDs were actually removed
        // even if there were more than one, as they were deleted.
        // if this is the case, delete the row, otherwise update the JSON.
        let newDEM =
        this.removeExamSittingRefIdsFromComponentMarks(diplomaSittingsToProcess.map(record => record.RefId), currDEM);

        if (newDEM.ComponentMarkInfo.length === 0) {
          diplomaExamMarkIdsToDelete.push(currDEM.id);
        }

        else {
          diplomaExamMarkDataToPatch.push({
            id: currDEM.id,
            ComponentMarks: JSON.stringify(newDEM)
          });
        }
      }
    }

    console.log("Revoking the user_roles associated with diploma exam marks needing to be deleted, if any.");
    console.log("# such roles found: " + userRolesToRevoke.length);

    if (userRolesToRevoke.length > 0) {
      // this always updates, not inserts, as every single record in userRolesToRevoke has an user_role.id
      // associated with it
      await knex("mpt_dev"+"."+"user_roles")
      .insert(userRolesToRevoke)
      .onConflict(userRolesColumnsToMergeOn)
      .merge(userRolesColumnsToMerge);
    }

    console.log("Deleting diploma exam mark rows if needed. # num rows found: " + diplomaExamMarkIdsToDelete.length);
    // delete diploma exam marks required

    if (diplomaExamMarkIdsToDelete.length > 0) {
      await knex(diplomaExamMarkSchemaTableName)
      .whereIn("id", diplomaExamMarkIdsToDelete)
      .del();
    }

    console.log("Patching diploma exam mark rows if needed. # num rows found: " + diplomaExamMarkDataToPatch.length);
    if (diplomaExamMarkDataToPatch.length > 0) {
    // inserts 0 rows, only updates (patches) rows as there is a valid ID for each diploma exam mark to patch
      await knex(diplomaExamMarkSchemaTableName)
      .insert(diplomaExamMarkDataToPatch)
      .onConflict(diplomaExamColumnsToMergeOn)
      .merge(diplomaExamColumnsToMerge);
    }

    console.log("Skipped diploma exam sittings as non-deleted diploma exam mark rows were found"
    + ", which are associated with the diploma exam sittings. # of such cases: "
    + skippedDESIdsAsNonDeletedDEM.length);

    console.log("Synced deleted diploma exam ids. # of such cases: " + syncedDeletedDiplomaExamSittingIds.length);

    return [syncedDeletedDiplomaExamSittingIds, skippedDESIdsAsNonDeletedDEM];
  }

  areAllRelevantDEMMarkedAsDeleted(DSRefId: string, diplomaExamMarks: any[]): boolean {
    for (let mark of diplomaExamMarks) {
      let marksInfo: any[] = mark.ComponentMarks.ComponentMarkInfo;
      for (let mInfo of marksInfo) {
        if (mInfo.ExamSittingRefId === DSRefId && mark.IsDeleted !== 1) {
          return false;
        }
      }
    }
    return true;
  }

  async handleDeletedDiplomaExams(deletedDiplomaExams: any[], adminUid: number, nowTime: any): Promise<any[]> {
    if (deletedDiplomaExams.length === 0) {
      return [[], []];
    }

    let syncedDiplomaExams: any[] = [];
    let skippedDiplomaExamIds: number[] = [];
    let diplomaExamSittingsToProcess: any[] = [];

    // get ALL diploma exam sittings associated with the deleted diploma exam ref IDs, no matter what
    const associatedDiplomaExamSittings: any[] =
    await this.findDiplomaExamSittingsFromDiplomaExams(deletedDiplomaExams.map(record => record.RefId));

    // We only want to process the deleted diploma exam sittings
    for (let diplomaExamSitting of associatedDiplomaExamSittings) {
      if (diplomaExamSitting.IsDeleted === 1) {
        diplomaExamSittingsToProcess.push(diplomaExamSitting);
      }
    }

    /*
    // POSSIBLE FUTURE TODO: assumption that there is not too many deleted diploma exams and thus
    // deleted diploma exam sittings associated, so it is okay to process them all at once.
    // If this is not the case, we need to iterate through a number like maxIterationsAtOnce
    // at a time to process these. I highly doubt we would need to do this.
    */
    const data = await this.handleDeletedDiplomaExamSittings(diplomaExamSittingsToProcess, adminUid, nowTime);
    const syncedDESIds = data[0];
    await this.turnOnSyncedFlag("pasi_data", "diploma_exam_sittings", syncedDESIds);

    /*
    // we compare the expected DES IDs to be synced to the ones that actually got synced
    // If a DES related to a deleted diploma exam did not get processed because it is not itself
    // marked as IsDeleted = 1, or if there was some further error (e.g. non-deleted DEM) in the handleDeletedDiplomaExamSittings
    // function, the actual DES that were synced will not contain the DES IDs that associatedDiplomaExamSittings
    // contains, which is ALL the related DES IDs with the exams, regardless of anything.
    */
    for (let deletedDiplomaExam of deletedDiplomaExams) {
      const expectedDESIdsToBeSynced = associatedDiplomaExamSittings
      .filter(record => record.ExamRefId === deletedDiplomaExam.RefId)
      .map(record => record.id);

      let deletedDiplomaExamSuccessfullySynced: boolean = true;
      for (let DESId of expectedDESIdsToBeSynced) {
        if (!syncedDESIds.includes(DESId)) {
          skippedDiplomaExamIds.push(deletedDiplomaExam.id);
          deletedDiplomaExamSuccessfullySynced = false;
          break;
        }
      }

      if (deletedDiplomaExamSuccessfullySynced) {
        syncedDiplomaExams.push(deletedDiplomaExam);
      }
    }

    console.log(`When processing deleted diploma exams, there were ${skippedDiplomaExamIds.length} `
    + `diploma exams skipped, because either the` +
    `associated diploma exam sitting with the deleted exam was not itself marked as diploma_exam_sitting.IsDeleted = 1 `
    + `, or there were issues, such as diploma exam marks tied to the diploma exam sitting and diploma exam` +
    ` not itself being marked as diploma_exam_marks.IsDeleted = 1.`);

    console.log(`When processing deleted diploma exams, there were ${syncedDiplomaExams.length} ` +
    `diploma exams successfully synced`);
    return [syncedDiplomaExams, skippedDiplomaExamIds];
  }

  async findDiplomaExamSittingsFromDiplomaExams(diplomaExamRefIds: string[]) {
    return await dbRawRead(this.app, [allowedStudentSchoolEnrolmentYears, diplomaExamRefIds],
    `
        SELECT
        des.*,
        c.CourseCode,
        de.SchoolYear,
        de.ExamPeriod
    FROM
        pasi_data.diploma_exam_sittings des
            INNER JOIN
        pasi_data.diploma_exams de ON de.RefId = des.ExamRefId
            INNER JOIN
        pasi_data.courses c ON c.CourseCode = de.CourseCode
    WHERE
        de.SchoolYear IN (?)
            AND c.course_in_scope = 1
            AND des.ExamRefId IN (?)
    ORDER BY de.CourseCode DESC
    ;
    `);
  }

  removeExamSittingRefIdsFromComponentMarks(deletedDESRefIds: string[], currDEM: any) {
    let newDEM: any = {
      ComponentMarkInfo: []
    };

    for (let deletedRefId of deletedDESRefIds) {
      for (let mark of currDEM.ComponentMarks.ComponentMarkInfo) {
        if (mark.ExamSittingRefId !== deletedRefId) {
          newDEM.ComponentMarkInfo.push(mark);
        }
      }
    }

    return newDEM;
  }

  async diplomaExamMarksToUserRoles(diplomaExamMarks: any[], adminUid: number, nowTime: any): Promise<any[]> {
    // turn marks into user_roles; revoke the old user_roles for these uids
    // skip any marks which have ASNs found without an valid uid in `pasi_data.students`
    // Important Note: every school has one school class for each course that has a diploma exam mark for it.
    if (diplomaExamMarks.length === 0) {
      return [[], []];
    }

    // get all required uids from pasi_data.students
    console.log("Getting required uids for students of diploma exam marks.");
    const uidASNPairs = await this.getRequiredUids(diplomaExamMarks.map(mark => mark.StateProvinceId));
    return await this.upsertMarksToUserRoles(diplomaExamMarks, uidASNPairs, adminUid, nowTime);
  }

  async upsertMarksToUserRoles(allDiplomaExamMarks: any, uidASNPairs: any[], adminUid: number, nowTime: any): Promise<any[]> {
    const diplomaExamMarksFailedToSync: any[] = [];
    const diplomaExamMarks = allDiplomaExamMarks.filter((dem:any) => {
      if (!dem.ComponentMarks?.ComponentMarkInfo?.length) {
        diplomaExamMarksFailedToSync.push(dem);
        return false;
      }
      const demRegistration = dem.ComponentMarks?.ComponentMarkInfo?.find((markInfo:any) => {
        return markInfo.ComponentMarkStatus === "RE" || markInfo.ComponentMarkStatus === "RG"
      });
      if (!demRegistration) {
        diplomaExamMarksFailedToSync.push(dem);
        return false;
      }
      return true;
    })
    if (diplomaExamMarks.length === 0) {
      return [[], diplomaExamMarksFailedToSync.map(record => record.id)];
    }

    // revoke all these uids in user_roles
    console.log("Original length of diploma exam marks rows to sync: " + diplomaExamMarks.length);
    const validUidASNPairs: any[] = uidASNPairs.filter(uidASN => uidASN.uid != null && uidASN.uid !== 0);
    const invalidUidASNPairs: any[] = uidASNPairs.filter(uidASN => uidASN.uid == null || uidASN.uid == 0);
    const PASIKeyNamespace = userMetasPasiKeyNamespace;
    const sseTWUMKey = "StudentSchoolEnrolmentInfo";
    const DEMTWUMKey = twumDiplomaKeyName;
    const gradeTWUMKey = twumGradeKeyName;
    const validUids = validUidASNPairs.map(uidASN => uidASN.uid);

    let diplomaExamMarksToSync: any[] = [];
    let studentsMissingUids: string[] = [];
    let dipomaExamSittingRefIds: string[] = [];

    for (let i = 0; i < diplomaExamMarks.length; i++) {

      // there is only 1 unique ASN max in validUidASNPairs
      const uidASNPair = validUidASNPairs.find(each => diplomaExamMarks[i].StateProvinceId === each.StateProvinceId);
      if (uidASNPair != null) {
        diplomaExamMarksToSync.push(
        {
          ...diplomaExamMarks[i],
          uid: uidASNPair.uid
        });
      }

      else {
        studentsMissingUids.push(diplomaExamMarks[i].StateProvinceId);
        diplomaExamMarksFailedToSync.push(diplomaExamMarks[i]);
      }

      this.extractDiplomaExamSittings(dipomaExamSittingRefIds, diplomaExamMarks[i]?.ComponentMarks?.ComponentMarkInfo || []);
    }

    // this is the main thing we care about; indicates which diploma exam marks cannot be synced to start with
    if (studentsMissingUids.length !== 0) {
      console.log("pasi_data.students missing ASNs rows themselves found. Failed diploma_exam_marks IDs of such rows: "
      + diplomaExamMarksFailedToSync.map(record => record.id));
    }

    if (invalidUidASNPairs.length !== 0) {
      console.log("ASNs from student enrollments are containing NULL uids in pasi_data.students. Such ASNs:" +
      invalidUidASNPairs.map(each => each.StateProvinceId));
    }

    console.log(`There were ${validUidASNPairs.length} student uid-ASN pairs found.`);
    console.log("Fetching user_roles for users.");
    const userRoles = await this.getAllUserRolesExisting(validUids);
    console.log(`Fetching user_roles for users done, ${userRoles.length} roles found.`);

    const sseTWUserMetas = await this.getAllTwUserMetasExisting(validUids, PASIKeyNamespace, sseTWUMKey);
    console.log(`Fetching tw_user_metas for SSE (when syncing DEM) for users done, ${sseTWUserMetas.length} tw metas found.`);

    const DEMTWUserMetas = await this.getAllTwUserMetasExisting(validUids, PASIKeyNamespace, DEMTWUMKey);
    console.log(`Fetching tw_user_metas for DEM (when syncing DEM) for users done, ${DEMTWUserMetas.length} tw metas found.`);

    const existingGradeTWUserMetas = await this.getAllTwUserMetasExisting(validUids, PASIKeyNamespace, gradeTWUMKey);
    console.log(`Fetching GRADE tw_user_metas for DEM (when syncing DEM) for users done, ${existingGradeTWUserMetas.length} tw metas found.`);

    return await this.modifyUserRolesAndUserMetasForDiplomaExamMarks(diplomaExamMarksToSync, nowTime,
    adminUid, dipomaExamSittingRefIds, userRoles, diplomaExamMarksFailedToSync,
    sseTWUserMetas, DEMTWUserMetas, existingGradeTWUserMetas, PASIKeyNamespace, sseTWUMKey, DEMTWUMKey, gradeTWUMKey);
  }

  async extractDiplomaExamSittings(refIds: string[], componentMarkInfo: any[]) {
    for (let markInfo of componentMarkInfo) {
      if (markInfo.ComponentMarkStatus === "RE" || markInfo.ComponentMarkStatus === "RG")
      // If valid component mark, track the diploma exam sitting
      // https://extranet.education.alberta.ca/pasidevnet/Docs/Technical%20API/PASI%20Techincal%20Concepts%20Overview/CodeValues.html#ComponentMarkStatus
      refIds.push(markInfo.ExamSittingRefId);
    }
  }

  async modifyUserRolesAndUserMetasForDiplomaExamMarks(diplomaExamMarks: any[],
  nowTime: any, adminUid: number, diplomaExamSittingsRefIds: string[],
  existingUserRoles: any[], existingDiplomaExamMarksFailedToSync: any[],
  existingTwUserMetasForSSE: any[], existingTWUserMetasForDEM: any[], existingGradeTWUserMetas: any[],
  PASIKeyNamespace: string, sseTWUMKey: string, DEMTWUMKey: string, gradeTWUMKey: string): Promise<any[]> {
    // ensure all diploma exam sittings of the marks have classes already; if not create them, in bulk:

    // get all relevant diploma exam sittings
    // for each diploma exam mark, find relevant diploma exam sitting information, and importantly
    // the OrganizationCode. We also have the course code for each such row now. So:

    // ensure the school_classes needed exist.
    // If not, create them. If created, mark that particular diploma_exam_sitting as synced_to_mpt_dev, as
    // technically, we are creating a school class for that sitting and syncing it

    // Now, we can insert into user_roles and the user metas (not upsert), with the school_class_group_id from above

    // console.log("Existing DEMFTS: ", existingDiplomaExamMarksFailedToSync);
    const existingFailedDiplomaExamMarksIds: number[] = existingDiplomaExamMarksFailedToSync.map(record => record.id);
    if (diplomaExamMarks.length === 0) {
      return [[], existingFailedDiplomaExamMarksIds];
    }

    console.log("user_roles and user_metas sync process starting...");

    const knex: Knex = this.app.get('knexClientWrite');
    const groupTypeUserMetaKey = "GroupType";
    const groupTypeUserMetaKeyNamespace = "abed_course";
    const langUserMetaKey = "Lang";
    const langUserMetaKeyNamespace = "abed_dyn";
    const schema = "mpt_dev";
    const userMetaTableName = "user_metas";
    const userMetasColumnsToMergeOn = ['uid', 'key'];
    const userMetasColumnsToMerge = ['value', 'updated_on', 'updated_by_uid', 'key_namespace'];
    const userRolesColumnsToMergeOn = ['id'];
    const userRolesColumnsToMerge = ['is_revoked', 'revoked_on', 'revoked_by_uid'];
    const twUserMetaTableName = "tw_user_metas";
    const twUserMetasColumnsToMergeOnForSSE = ["sse_id"];
    const twUserMetasColumnsToMergeOnForDEM = ["dem_id"];
    const twUserMetaColumnsToMerge = ["uid", "test_window_id", "meta", "updated_by_uid", "updated_on", "asmt_type_slug"];
    const gradeTWumUpdateQuery =
    `
      UPDATE tw_user_metas
      SET value = ?, updated_by_uid = ?, updated_on = ?
      WHERE id in (?)
      ;
    `;

    let dynamicGradeTWumUpdateQueryParams: {
      grade: string,
      ids: number[]
    }[] = [];
    let userRolesToUpsert: any[] = [];
    let userMetasToUpsert: any[] = [];
    let schoolClassesToInsert: any[] = [];
    let uGroupsToInsert: any[] = [];
    let diplomaExamMarksSyncedSuccessfully: any[] = [];
    let uidsSeen: Set<number> = new Set<number>();
    let uidSchoolClassGroupIds: Map<number, Set<number>> = new Map();
    let uidSchoolGroupIds: Map<number, Set<number>> = new Map();
    let diplomaExamMarksFailedToSync: number[] = existingFailedDiplomaExamMarksIds;
    let syncedDiplomaStudentSchoolEnrolmentIds: number[] = [];
    let deletedDiplomaStudentSchoolEnrolmentRecords: number[] = [];
    let deletedDiplomaExamMarkRecords: number[] = [];
    let deletedDiplomaExamSittingRecords: number[] = [];
    let SSETWUserMetasToUpsert: any[] = [];
    let DEMTWUSerMetasToUpsert: any[] = [];
    let gradeTWUserMetasToInsert: any[] = [];
    let seenTwumGradeInsertions: Map<number, Set<number>> = new Map();
    let seenMissingTWErrors: Map<string, Set<string>> = new Map();

    // entries in this array are des.OrganizationCode + de.CourseCode + des.SchoolYear + des.ExamPeriod
    let seenSchoolCourseSchoolYearExamPeriodPairs: string[] = [];

    let diplomaTestWindows = (await this.getTestWindowsForPASIImport()).
    filter(tw => tw.type_slug === allowedDiplomaGroupType);
    const placeholderSchoolClasses: any[] = await this.getAllPlaceholderSchoolClassesForCurrentTW();
    let nonPlaceholderSchoolClasses: any[] = await this.getAllNonPlaceholderSchoolClassesForG12TWs();
    let uniqueASNs = Array.from(new Set(diplomaExamMarks.map(mark => mark.StateProvinceId)));
    let diplomaExamSittings = await this.getAllRelevantDiplomaExamSittings(uniqueASNs);
    let studentSchoolEnrolments: any[] = await this.getStudentSchoolEnrolmentsForDiploma(uniqueASNs);
    const schools = await this.getAllCurrSchools();

    if (diplomaTestWindows.length === 0) {
      console.log("Not able to find a single G12 test window for diploma exam sync. Cannot proceed.");
      return [[], existingFailedDiplomaExamMarksIds];
    }

    // commented out because of support request: https://bubo.vretta.com/vea/project-management/vretta-project-notes/vea-abed/-/issues/4024
    // await this.createDiplomaSchoolClassesForDEM(diplomaExamSittings, diplomaTestWindows, seenMissingTWErrors,
    // seenSchoolCourseSchoolYearExamPeriodPairs, schools, nonPlaceholderSchoolClasses, uGroupsToInsert, nowTime,
    // adminUid, schoolClassesToInsert);

    // now, we are guranteed to have school_classes for all the diploma exam sittings in scope
    // if we didn't before, we created them above.
    // Fetching the school classes again now will get us ALL such school_classes for all sittings
    // There will be no sitting without a school class
    // (Note we still don't use school classes, as we are using placeholder school classes for storing students right now)
    nonPlaceholderSchoolClasses = await this.getAllNonPlaceholderSchoolClassesForG12TWs();
    console.log(`School classes retrieved after creation of required ones, length: ${nonPlaceholderSchoolClasses.length}`);

    let schoolInfo: any[] = [];
    for (let i = 0; i < diplomaExamMarks.length; i++) {
      let isThereANonDeletedSameTypeDEM: boolean = false;
      let nonDeletedDEMs: any[] = [];
      let isNonDeletedSameTypeDEMAlreadyRetrieved: boolean = false;

      nonDeletedDEMs = await this.getNonDeletedDEMs(diplomaExamMarks[i]);
      isThereANonDeletedSameTypeDEM = await this.isThereANonDeletedSameTypeDEM(diplomaExamMarks[i], diplomaExamSittings, nonDeletedDEMs);
      isNonDeletedSameTypeDEMAlreadyRetrieved = true;
      // console.log("entry 1", nonDeletedDEMs, isThereANonDeletedSameTypeDEM);

      let diplomaTestWindowForSchoolYear = diplomaTestWindows.find(dtw => {
        return +dtw.PASI_school_year === +diplomaExamMarks[i].SchoolYear
        && dtw.pasi_exam_period === diplomaExamMarks[i].ExamPeriod;
      });

      if (diplomaTestWindowForSchoolYear == null) {
        // this diploma exam mark cannot be synced as no relevant diploma test window has been found
        console.log("Associated test window is not found for DEM with id " + diplomaExamMarks[i].id + ", error.")
        diplomaExamMarksFailedToSync.push(diplomaExamMarks[i].id);
        continue;
      }

      // get all the student's student_school_enrolment records. Do not restrict on grade
      // There should be ideally only 1 unique Grade per SchoolYear per ASN, even if there are  multiple SSE records
      // but this can be false in the case of grade changes, which also need to be accounted for
      // it is possible for there to be no student diploma enrolments (len 0) for a student with a mark

      const studentDiplomaEnrolments =
      studentSchoolEnrolments.filter(enrolment => enrolment.SchoolYear === diplomaExamMarks[i].SchoolYear
      && enrolment.StateProvinceId === diplomaExamMarks[i].StateProvinceId);

      // get nonDeletedEnrolments and the latest of the student school enrolments of the student
      // use this for the latest grade to store into tw_user_metas (in case, somehow, there's more than 1 grade)
      const nonDeletedEnrolments = studentDiplomaEnrolments.filter(enrolment => enrolment.IsDeleted === 0);

      // this is undefined if there are no non deleted enrolments for a student with a mark
      // in this case, the below code will pick the default grade for DEM - G12
      const latestEnrolment = nonDeletedEnrolments.sort((e1, e2) => {return (e2.id - e1.id)})[0];
      const TWUMGrade = latestEnrolment?.Grade == null ? allowedDiplomaGrades[0] : latestEnrolment.Grade;
      // console.log("\nLATEST ENROLMENT: ", latestEnrolment);

      let shouldUserRoleBeRevoked: boolean = false;
      if (diplomaExamMarks[i].IsDeleted === 1) {
        /*
        // simply turn the shouldUserRoleBeRevoked flag to true, and handle their enrolment record from
        // student_school_enrolment records as required below (to handle the SCHOOL associated user_role insertion)
        // All we are doing here is inserting/updating revoked SCHOOL CLASS associated user_roles
        // THIS WILL MARK RECORD AS synced_to_mpt_dev = 1
        // We will not push a deleted DEM record to `tw_user_metas`, because there is no `is_revoked` field
        // in `tw_user_metas`, and it makes more sense to not include them.
        */        

        if (!isThereANonDeletedSameTypeDEM) {
          shouldUserRoleBeRevoked = true;
        };

        deletedDiplomaExamMarkRecords.push(diplomaExamMarks[i].id);
      }

      const componentMarkInfo = diplomaExamMarks[i]?.ComponentMarks?.ComponentMarkInfo || [];
      let currentExamSittingRefIds: string[] = [];

      // console.log("shouldUserRoleBeRevokedCheck", shouldUserRoleBeRevoked, isThereANonDeletedSameTypeDEM);

      // console.log(diplomaExamMarks[i]);
      try {
        schoolInfo = [];
        for (let markInfo of componentMarkInfo) {
          // console.log(markInfo);
          // use the mark status to decide whether or not a school_class user_role should be revoked or not
          // If valid component mark, track the diploma exam sitting as an unrevoked user_role
          // https://extranet.education.alberta.ca/pasidevnet/Docs/Technical%20API/PASI%20Techincal%20Concepts%20Overview/CodeValues.html#ComponentMarkStatus
          // DO NOT revoke user_role no matter what, if there is at least 1 non deleted same type DEM

          shouldUserRoleBeRevoked = 
          !isThereANonDeletedSameTypeDEM &&
            (shouldUserRoleBeRevoked || 
              !(markInfo.ComponentMarkStatus === "RE" || markInfo.ComponentMarkStatus === "RG"
            ));

          // guranteed to find only 1 row because RefId is UNIQUE in the diploma exam sittings table
          const foundDES = diplomaExamSittings.find(sitting => sitting.SittingRefId === markInfo.ExamSittingRefId);
          // console.log(foundDES);

          // note I am turning both the foreign ID and OrganizationCode to numbers, as PASI stores
          // the SchoolCode as "0009" but our DB can store it as 9, so wb e want to ensure equality
          // this is also done below when setting schoolInDB
          if (foundDES) {
            if (foundDES.IsDesDeleted === 1) {
              /*
              // simply mark DES record as deleted for logging, and skip this specific DES
              // associated with the student (so no school_class user_role insertions for this DES).
              // All we are doing here is skipping this PARTICULAR SCHOOL CLASS associated user_role insertion.
              //
              // If this means no non-deleted, linked DES record is matched with this particular DEM
              // then no user_roles insertions (SCHOOL or SCHOOL CLASS) will be done, nor any user_meta insertions will be done.
              // THIS WILL NOT MARK RECORD AS synced_to_mpt_dev = 1, as this is not expected behaviour
              // We find a deleted DES for a non-deleted DEM, which is unusual.
              */
              deletedDiplomaExamSittingRecords.push(foundDES.id);
            }

            else {
              const school = schools.find(school => {
                return !(["", " "].includes(this.parseSchoolOrgCode(foundDES.OrganizationCode)))
                && +this.parseOrganizationCode(school.foreign_id) === +this.parseSchoolOrgCode(foundDES.OrganizationCode)
              });

              schoolInfo.push(
              {
                schoolGroupId: school?.group_id || null
              });

              if (school == null) {
                // cannot even find such a school in our DB, skip (need to do a school sync again to avoid this)
                continue;
              }

              /*
              // Psuedocode
              // we do not care about finding schoolClass through all this anymore
              // instead, we will search placeholder classes with the G6/G9 placeholder logic
              // to find if a placeholder class exists for the associated school or not. If it does not, we will skip.
              // We simply insert them in this placeholder class, ONCE, regardless of how many
              // times this particular student - uid - further comes up into DEM for the SAME school.
              // A student being in DIFFERENT schools' placeholder classes for G12 is fine, I don't see an issue with it.
              // Then they will be in multiple placeholder classes for the multiple schools.
              // This means we will upsert into user_roles, simply based on the uid and placeholder group ID
              // If it's already done, we just leave it.
              */

              // allowedDiplomaGrade will always only be 1 - "12"
              const schoolClass = placeholderSchoolClasses.find(schoolClass => {
                return schoolClass.pasi_grade_code === allowedDiplomaGrades[0]
                && !(["", " "].includes(this.parseSchoolOrgCode(foundDES.OrganizationCode)))
                && +this.parseOrganizationCode(schoolClass.foreign_id) === +this.parseSchoolOrgCode(foundDES.OrganizationCode)
                && +schoolClass.PASI_school_year === +diplomaExamMarks[i].SchoolYear
                && schoolClass.pasi_exam_period === diplomaExamMarks[i].ExamPeriod;
              });

              schoolInfo[schoolInfo.length - 1].schoolClassGroupId = schoolClass?.group_id || null;

              if (schoolClass == null) {
                // this should never happen as diploma exam marks are always synced after student school enrolments sync
                // and the student school enrolments sync creates the required placeholder classes
                // just in case it does, somehow happen, skip syncing of this mark
                console.log("Warning: School class found to be null, even after creation. Skipped.")
                continue;
              }

              else {
                let shouldSchoolClassUserRoleBeModified: boolean = true;
                if (uidSchoolClassGroupIds.has(diplomaExamMarks[i].uid)) {
                  const schoolClassGrpIdsEnrolledTo = uidSchoolClassGroupIds.get(diplomaExamMarks[i].uid)!;
                  if (schoolClassGrpIdsEnrolledTo.has(schoolClass.group_id)) {
                    shouldSchoolClassUserRoleBeModified = false;
                  }

                  else {
                    schoolClassGrpIdsEnrolledTo.add(schoolClass.group_id);
                  }
                }

                else {
                  uidSchoolClassGroupIds.set(diplomaExamMarks[i].uid, new Set([schoolClass.group_id]));
                }

                if (shouldSchoolClassUserRoleBeModified) {
                  // school user_roles

                  // we only want to have 1 uid-school group id pair inserted into user_roles max, and update
                  // the existing such school linked user_roles only once
                  let schoolUserRoleUpsertNeeded: boolean = false;
                  const studentFound = uidSchoolGroupIds.get(diplomaExamMarks[i].uid);

                  if (studentFound == null) {
                    uidSchoolGroupIds.set(diplomaExamMarks[i].uid, new Set([schoolClass.schl_group_id]));
                    schoolUserRoleUpsertNeeded = true;
                  }

                  else {
                    if (!studentFound.has(schoolClass.schl_group_id)) {
                      studentFound.add(schoolClass.schl_group_id);
                      schoolUserRoleUpsertNeeded = true;
                    }
                  }

                  if (schoolUserRoleUpsertNeeded) {
                    const userRolesFound = this.findUserRoles(schoolClass.schl_group_id, diplomaExamMarks[i].uid, existingUserRoles);

                    if (userRolesFound.length === 0) {
                      // inserts school user_role
                      let schoolUserRole: any = {
                        role_type: "schl_student",
                        uid: diplomaExamMarks[i].uid,
                        group_id: schoolClass.schl_group_id,
                        created_on: nowTime,
                        created_by_uid: adminUid,
                        is_revoked: shouldUserRoleBeRevoked ? 1 : 0
                      };

                      if (shouldUserRoleBeRevoked) {
                        schoolUserRole.revoked_on = nowTime;
                        schoolUserRole.revoked_by_uid = adminUid;
                      }

                      userRolesToUpsert.push(schoolUserRole);
                    }

                    else {
                      for (let userRole of userRolesFound) {
                        /*
                        // important: note the IsDeleted = 1 rows will always be sorted first (for the same ASN and other conditions)
                        // so if the same group ID associated with the row is used for multiple rows of diploma_exam_marks
                        // or student_school_enrolments, it will always ensure the last one being upserted into user_roles
                        // is always the unrevoked (is_revoked = 0) row for that group_id, if any IsDeleted = 0 rows exist
                        // This is ideal and what we want, as it ensures a non-revoked user_role row whre needed
                        */
                        if (shouldUserRoleBeRevoked) {
                          // you just let the user_roles exist with no changes if no existing user roles to revoke
                          let schoolUserRoleToUpdate: any = {
                            id: userRole.id,
                            is_revoked: 1,
                            revoked_on: nowTime,
                            revoked_by_uid: adminUid
                          };
                          userRolesToUpsert.push(schoolUserRoleToUpdate);
                        }

                        else {
                          let schoolUserRoleToUpdate: any = {
                            id: userRole.id,
                            is_revoked: 0,
                            revoked_on: null,
                            revoked_by_uid: null
                          };
                          userRolesToUpsert.push(schoolUserRoleToUpdate);
                        }
                      }
                    }
                  }

                  // school class user_roles
                  const userRolesFound = this.findUserRoles(schoolClass.group_id, diplomaExamMarks[i].uid, existingUserRoles);

                  if (userRolesFound.length === 0) {
                    // insert school_class user_role
                    let schoolClassUserRole: any = {
                      role_type: "schl_student",
                      uid: diplomaExamMarks[i].uid,
                      group_id: schoolClass.group_id,
                      created_on: nowTime,
                      created_by_uid: adminUid,
                      is_revoked: shouldUserRoleBeRevoked ? 1 : 0
                    };

                    if (shouldUserRoleBeRevoked) {
                      schoolClassUserRole.revoked_on = nowTime;
                      schoolClassUserRole.revoked_by_uid = adminUid;
                    }

                    userRolesToUpsert.push(schoolClassUserRole);
                  }

                  else {
                    for (let userRole of userRolesFound) {
                      /*
                      // important: note the IsDeleted = 1 rows will always be sorted first (for the same ASN and other conditions)
                      // so if the same group ID associated with the row is used for multiple rows of diploma_exam_marks
                      // or student_school_enrolments, it will always ensure the last one being upserted into user_roles
                      // is always the unrevoked (is_revoked = 0) row for that group_id, if any IsDeleted = 0 rows exist
                      // This is ideal and what we want, as it ensures a non-revoked user_role row whre needed
                      */
                      if (shouldUserRoleBeRevoked) {
                        // you just let the user_roles exist with no changes if no existing user roles to revoke
                        let schoolClassUserRoleToUpdate: any = {
                          id: userRole.id,
                          is_revoked: 1,
                          revoked_on: nowTime,
                          revoked_by_uid: adminUid
                        };
                        userRolesToUpsert.push(schoolClassUserRoleToUpdate);
                      }

                      else {
                        let schoolClassUserRoleToUpdate: any = {
                          id: userRole.id,
                          is_revoked: 0,
                          revoked_on: null,
                          revoked_by_uid: null
                        };
                        userRolesToUpsert.push(schoolClassUserRoleToUpdate);
                      }
                    }
                  }
                }

                currentExamSittingRefIds.push(foundDES);
              }
            }
          }
        }
      }

      catch(e) {
        console.log(e);
        throw(e);
      }

      if (currentExamSittingRefIds.length === 0) {
        // this diploma exam mark has not been able to be synced in terms of school_class user_roles,
        // so skip the remaining steps
        console.log("CurrentExamSittingRefIds not found for " + diplomaExamMarks[i].id + ", error.")
        diplomaExamMarksFailedToSync.push(diplomaExamMarks[i].id);
        continue;
      }

      // if DEM is non deleted
      if (diplomaExamMarks[i].IsDeleted === 0) {
        // Only sync StudentGrade into tw_user_metas if the DEM is not deleted
    
        // we only want to have 1 uid-tw pair inserted into tw_user_metas max
        const studentFound = seenTwumGradeInsertions.get(diplomaExamMarks[i].uid);
        let gradeTwumUpsertNeeded: boolean = false;
        // console.log(studentFound);
        if (studentFound == null) {
          // console.log("student not found", diplomaExamMarks[i].uid, diplomaTestWindowForSchoolYear.id);
          seenTwumGradeInsertions.set(diplomaExamMarks[i].uid, new Set([diplomaTestWindowForSchoolYear.id]));
          gradeTwumUpsertNeeded = true;
        }

        else {
          // console.log("student found", diplomaTestWindowForSchoolYear.id, studentFound);
          // console.log(studentFound.has(diplomaTestWindowForSchoolYear.id));
          if (!studentFound.has(diplomaTestWindowForSchoolYear.id)) {
            studentFound.add(diplomaTestWindowForSchoolYear.id);
            gradeTwumUpsertNeeded = true;
          }
        }

        if (gradeTwumUpsertNeeded) {
          // StudentGrade tw_user_meta inserts/updates
          // console.log("starting meta insertions");
          const studentGradetwUserMetasFound = this.findStudentGradeTWUserMetas(
          existingGradeTWUserMetas, diplomaTestWindowForSchoolYear.id, diplomaExamMarks[i].uid);

          if (studentGradetwUserMetasFound.length === 0) {
            let gradeTwUserMetaRecord: any = {
              uid: diplomaExamMarks[i].uid,
              key_namespace: PASIKeyNamespace,
              key: gradeTWUMKey,
              value: TWUMGrade,
              test_window_id: diplomaTestWindowForSchoolYear.id, // DEM SchoolYear is used to pull appropriate TW ID
              created_by_uid: adminUid,
              created_on: nowTime
            };

            gradeTWUserMetasToInsert.push(gradeTwUserMetaRecord);
          }

          else {
            const relevantQuery = dynamicGradeTWumUpdateQueryParams.find(each => each.grade === TWUMGrade);
            if (relevantQuery != null) {
              relevantQuery.ids.push(...studentGradetwUserMetasFound.map(record => record.id));
            }

            else {
              dynamicGradeTWumUpdateQueryParams.push({
                grade: TWUMGrade,
                ids: [...studentGradetwUserMetasFound.map(record => record.id)]
              });
            }
          }
        }
      }

      // DEM tw_user_metas upserts
      // do these upserts, regardless of whether the diploma exam mark is deleted or not - we want this information
      // we want something similar for SSE done too (save them into tw_user_metas, deleted or not) - not sure if this happens right now for SSE
      
      // note: deleted DEM will be stored as such into `tw_user_metas` with meta.IsDeleted = 1 or meta.IsDeleted = 0
      // we will use this to our advantage

      // Only sync StudentGrade into tw_user_metas if the DEM is not deleted
      let meta = diplomaExamMarks[i];
      meta.schoolInfo = schoolInfo;
      const twUserMetasFound = this.findTWUserMetas(meta.id, existingTWUserMetasForDEM, "dem_id");

      if (twUserMetasFound.length === 0) {
        let twUserMetaRecord: any = {
          uid: diplomaExamMarks[i].uid,
          key_namespace: PASIKeyNamespace,
          key: DEMTWUMKey,
          value: 1,
          dem_id: meta.id,
          test_window_id: diplomaTestWindowForSchoolYear.id,
          asmt_type_slug: meta.CourseCode,
          meta: JSON.stringify(meta),
          created_by_uid: adminUid,
          created_on: nowTime
        };

        DEMTWUSerMetasToUpsert.push(twUserMetaRecord);
      }

      else {
        let twUserMetaRecord: any = {
          uid: diplomaExamMarks[i].uid,
          test_window_id: diplomaTestWindowForSchoolYear.id,
          value: 1,
          dem_id: meta.id,
          asmt_type_slug: meta.CourseCode,
          meta: JSON.stringify(meta),
          updated_by_uid: adminUid,
          updated_on: nowTime
        };

        DEMTWUSerMetasToUpsert.push(twUserMetaRecord);
      }

      // it does not matter how many diploma exam marks there are, as soon as we see a
      // student's ASN and uid, we find their enrollment information and use it to make
      // school user_roles as needed. It will always be for grade 12, as all diploma exams are for grade 12 (valid assumption)

      if (!uidsSeen.has(diplomaExamMarks[i].uid) && diplomaExamMarks[i].IsDeleted === 0) {
        // do user_metas insertions regardless of if an user_role school linked insertion is done or not
        let groupTypeUserMetaToUpsert: any = {
          uid: diplomaExamMarks[i].uid,
          key: groupTypeUserMetaKey,
          value: this.parseGroupType(allowedDiplomaGrades[0]), // always Grade 12
          updated_by_uid: adminUid,
          updated_on: nowTime,
          created_by_uid: adminUid,
          created_on: nowTime,
          key_namespace: groupTypeUserMetaKeyNamespace
        };

        let langUserMetaToUpsert: any = {
          uid: diplomaExamMarks[i].uid,
          key: langUserMetaKey,
          value: defaultSchoolLang,
          updated_by_uid: adminUid,
          updated_on: nowTime,
          created_by_uid: adminUid,
          created_on: nowTime,
          key_namespace: langUserMetaKeyNamespace
        };

        userMetasToUpsert.push(groupTypeUserMetaToUpsert);
        userMetasToUpsert.push(langUserMetaToUpsert);
        uidsSeen.add(diplomaExamMarks[i].uid);
      }

      /*
      // note enrolment.Grade is always going to be 12 below, so we don't need to check for that
      // Diplomas are always associated with Grade 12 students, and so the query finding the enrolments
      // are also all Grade 12 rows. These are fair assumptions.

      // allowedStudentSchoolEnrolmentYears are the years for which marks and enrolment records are pulled
      // note: we order student school enrolments such that the most recent and relevant
      // enrollments come first. In the case a student has multiple student school enrolments for G12
      // we pick their most recent (up to date) enrolment first. If a isDeleted = 1 row is encountered,
      // we add a revoked user_role, but don't mark the uid as seen as to keep looking for a valid
      // isDeleted = 0 student school enrolment row to make an unrevoked user_role for the student
      // for the same grade (G12) and school year.
      */

      // console.log(studentSchoolEnrolments);
      // console.log(diplomaExamMarks[i]);
      // console.log(studentDiplomaEnrolments);

      for (let studentDiplomaEnrolment of studentDiplomaEnrolments) {

        if (studentDiplomaEnrolment.synced_to_mpt_dev === 1) {
          // if a SSE row related to the diploma exam mark is already synced, no need to do anything again
          continue;
        }

        // console.log("Unskipped uid for G12 sync entry");

        if (studentDiplomaEnrolment == null) {
          // if a Grade 12 enrolment for the student in the school year of the diploma exam mark
          // is not present, skip just the school user_role insertion, and only do the user_metas required insertions

          // important: note we make this specific exam mark as successfully synced if this were to happen
          // even if a successful school user_role has not been inserted, since the user_meta and school_class user_roles are inserted

          // do nothing
          // leaving this as its own block, if modifications are needed in this block for the future
        }

        else {
          if (studentDiplomaEnrolment.IsDeleted === 1) {
            // THIS WILL MARK RECORD AS synced_to_mpt_dev = 1 for both DEM and student_school_enrolment rows
            // This is okay
            // We find a deleted student_school_enrolment record for a non-deleted DEM, which can happen.
            if (!isNonDeletedSameTypeDEMAlreadyRetrieved) {
              nonDeletedDEMs = await this.getNonDeletedDEMs(diplomaExamMarks[i]); 
              isThereANonDeletedSameTypeDEM = await this.isThereANonDeletedSameTypeDEM(diplomaExamMarks[i], diplomaExamSittings, nonDeletedDEMs);
              isNonDeletedSameTypeDEMAlreadyRetrieved = true;
              // console.log("SDE entry into retrieval of non deleted DEms");
            }

            const nonDeletedDEMOrgs: string[] = [];
            for (let dem of nonDeletedDEMs) {
              nonDeletedDEMOrgs.push(...this.getExistingDemOrgCodes(dem.ComponentMarks, diplomaExamSittings));
            }

            // console.log(nonDeletedDEMs, isThereANonDeletedSameTypeDEM);
            // console.log(nonDeletedDEMOrgs, studentDiplomaEnrolment.SchoolCode, studentDiplomaEnrolment.id);

            if (nonDeletedDEMOrgs.includes(this.formPASIStyleOrgCode(true, studentDiplomaEnrolment.SchoolCode)) 
            && isThereANonDeletedSameTypeDEM) {
              // case 1: if there is at least 1 non deleted DEM
              // AND the current student diploma enrolment is linked to one of the non deleted DEM
              shouldUserRoleBeRevoked = false;
            }

            else {
            // rest of the cases:
            // if simply there is no non deleted DEM, or the current student diploma enrolment is not linked to one of the non deleted DEM
            shouldUserRoleBeRevoked = true;
          }

          deletedDiplomaStudentSchoolEnrolmentRecords.push(studentDiplomaEnrolment.id);
        }

          else {
            /* Pseudocode
            // For every non-deleted `student_school_enrolments` (note this links to a SCHOOL user_role), insert 1 row into `tw_user_meta` accordingly.
            // Store `student_school_enrolment` record information into the `meta` column.
            // We know if the row already exists in `tw_user_meta` or not (for upsert purposes) based on `tw_user_meta.sse_id` field.
            //`test_window_id` and `uid` are not needed for the upsert check, because `student_school_enrolment_id` indirectly covers both aspects anyways
            // (`SchoolYear` to `test_window_id`, and `StateProvinceId` to `uid`. In the event either of those fields change, the `tw_user_meta` row should be updated with the latest information anyways).
            */

            const twUserMetasFound = this.findTWUserMetas(studentDiplomaEnrolment.id, existingTwUserMetasForSSE, "sse_id");
            const meta = studentDiplomaEnrolment;

            if (twUserMetasFound.length === 0) {
              let twUserMetaRecord: any = {
                uid: diplomaExamMarks[i].uid,
                key_namespace: PASIKeyNamespace,
                key: sseTWUMKey,
                value: 1,
                sse_id: meta.id,
                test_window_id: diplomaTestWindowForSchoolYear.id, // DEM SchoolYear is used to pull appropriate TW ID
                meta: JSON.stringify(meta),
                created_by_uid: adminUid,
                created_on: nowTime
              };

              SSETWUserMetasToUpsert.push(twUserMetaRecord);
            }

            else {
              let twUserMetaRecord: any = {
                uid: diplomaExamMarks[i].uid,
                test_window_id: diplomaTestWindowForSchoolYear.id, // DEM SchoolYear is used to pull appropriate TW ID
                value: 1,
                sse_id: meta.id,
                meta: JSON.stringify(meta),
                updated_by_uid: adminUid,
                updated_on: nowTime
              };

              SSETWUserMetasToUpsert.push(twUserMetaRecord);
            }
          }

          const school = schools.find(school => +this.parseOrganizationCode(school.foreign_id) === +studentDiplomaEnrolment.SchoolCode);

          if (school == null) {
            // school should never be null here, so this should never happen but just in case.
            // cannot even find such a school in our DB, skip (need to do a school sync again to avoid this)

            /*
            // Important: note we don't make this specific exam mark as successfully synced if this were to happen
            // as a successful school user_role has not been inserted, even if the user_meta and school_class user_roles
            // have been. We want the school user_role to be inserted after doing a pasi_data -> mpt_dev schema sync for school.
            // Note: we don't mark the uid as seen if such a case is encountered, as we don't want to stop altogether
            // looking at enrolments and trying to find a IsDeleted = 0 enrolment to make a unrevoked user_role
            // this may cause 2 school user_roles to be inserted for the user, but this is better than no
            // school user_roles inserted at all for the user.
            */
            console.log("School not found for DEM ID " + diplomaExamMarks[i].id + ", error.");
            diplomaExamMarksFailedToSync.push(diplomaExamMarks[i].id);
            continue;
          }

          // school user_roles

          // we only want to have 1 uid-school group id pair inserted into user_roles max, and update
          // the existing such school linked user_roles only once
          let schoolUserRoleUpsertNeeded: boolean = false;
          const studentFound = uidSchoolGroupIds.get(diplomaExamMarks[i].uid);

          if (studentFound == null) {
            uidSchoolGroupIds.set(diplomaExamMarks[i].uid, new Set([school.group_id]));
            schoolUserRoleUpsertNeeded = true;
          }

          else {
            if (!studentFound.has(school.group_id)) {
              studentFound.add(school.group_id);
              schoolUserRoleUpsertNeeded = true;
            }
          }

          if (schoolUserRoleUpsertNeeded) {
            const userRolesFound = this.findUserRoles(school.group_id, diplomaExamMarks[i].uid, existingUserRoles);

            if (userRolesFound.length === 0) {
              // insert school user_role
              let schoolUserRole: any = {
                role_type: "schl_student",
                uid: diplomaExamMarks[i].uid,
                group_id: school.group_id,
                created_on: nowTime,
                created_by_uid: adminUid,
                is_revoked: shouldUserRoleBeRevoked ? 1 : 0
              };

              if (shouldUserRoleBeRevoked) {
                schoolUserRole.revoked_on = nowTime;
                schoolUserRole.revoked_by_uid = adminUid;
              }

              userRolesToUpsert.push(schoolUserRole);
            }

            else {
              for (let userRole of userRolesFound) {
                /*
                // important: note the IsDeleted = 1 rows will always be sorted first (for the same ASN and other conditions)
                // so if the same group ID associated with the row is used for multiple rows of diploma_exam_marks
                // or student_school_enrolments, it will always ensure the last one being upserted into user_roles
                // is always the unrevoked (is_revoked = 0) row for that group_id, if any IsDeleted = 0 rows exist
                // This is ideal and what we want, as it ensures a non-revoked user_role row whre needed
                */
                if (shouldUserRoleBeRevoked) {
                  // you just let the user_roles exist with no changes if no existing user roles to revoke
                  let schoolUserRoleToUpdate: any = {
                    id: userRole.id,
                    is_revoked: 1,
                    revoked_on: nowTime,
                    revoked_by_uid: adminUid
                  };
                  userRolesToUpsert.push(schoolUserRoleToUpdate);
                }

                else {
                  let schoolUserRoleToUpdate: any = {
                    id: userRole.id,
                    is_revoked: 0,
                    revoked_on: null,
                    revoked_by_uid: null
                  };
                  userRolesToUpsert.push(schoolUserRoleToUpdate);
                }
              }
            }
          }

          syncedDiplomaStudentSchoolEnrolmentIds.push(studentDiplomaEnrolment.id);
        }
      }

      diplomaExamMarksSyncedSuccessfully.push(diplomaExamMarks[i]);
    }

    console.log(`# of deleted diploma student school enrolment records being synced, marked as isDeleted` +
    ` in student_school_enrolments: ` + deletedDiplomaStudentSchoolEnrolmentRecords.length);
    console.log(`# of deleted diploma exam mark records being synced, marked as isDeleted` +
    ` in diploma_exam_marks: ` + deletedDiplomaExamMarkRecords.length);
    console.log(`# of deleted diploma exam sittings records found, which are marked as isDeleted` +
    ` in diploma_exam_sittings, and connected to a mark in diploma_exam_marks: ` + deletedDiplomaExamSittingRecords.length);

    if (SSETWUserMetasToUpsert.length > 0) {
      try {
        console.log("Inserting/updating tw_user_metas for SSE now.")
        const results =
        await knex("mpt_dev"+"."+twUserMetaTableName)
        .insert(SSETWUserMetasToUpsert)
        .onConflict(twUserMetasColumnsToMergeOnForSSE)
        .merge(twUserMetaColumnsToMerge)
        console.log("Inserting/updating tw_user_metas for SSE done.")
      }

      catch(e) {
        console.log(e);
        throw(e);
      }
    }

    if (gradeTWUserMetasToInsert.length > 0) {
      try {
        // no updates with this logic with this, just inserts
        console.log("Inserting grade tw_user_metas for DEM now.");
        const results =
        await knex("mpt_dev"+"."+twUserMetaTableName)
        .insert(gradeTWUserMetasToInsert);
        console.log("Inserting grade tw_user_metas for DEM done.");
      }

      catch(e) {
        console.log(e);
        throw(e);
      }
    }

    for (let query of dynamicGradeTWumUpdateQueryParams) {
      console.log(`Updating tw_user_metas for SSE linked to DEM, for Grade ${query.grade} now.`);
      await dbRawWrite(this.app, [query.grade, adminUid, nowTime, query.ids],
      gradeTWumUpdateQuery);
      console.log(`Updating tw_user_metas for SSE linked to DEM, for Grade ${query.grade} done.`);
    }

    if (DEMTWUSerMetasToUpsert.length > 0) {
      try {
        console.log("Inserting/updating tw_user_metas for DEM now.")
        const results =
        await knex("mpt_dev"+"."+twUserMetaTableName)
        .insert(DEMTWUSerMetasToUpsert)
        .onConflict(twUserMetasColumnsToMergeOnForDEM)
        .merge(twUserMetaColumnsToMerge)
        console.log("Inserting/updating tw_user_metas for DEM done.")
      }

      catch(e) {
        console.log(e);
        throw(e);
      }
    }

    if (userRolesToUpsert.length > 0) {
      try {
        console.log("Upserting new school and school_classes user_roles now, for diploma exam mark sync.")
        const resultsUserRoles =
        await knex("mpt_dev"+"."+"user_roles")
        .insert(userRolesToUpsert)
        .onConflict(userRolesColumnsToMergeOn)
        .merge(userRolesColumnsToMerge);
        console.log("Upserting new school and school_classes user_roles done, for diploma exam mark sync.");
      }

      catch(e) {
        console.log(e);
        throw(e);
      }
    }

    else {
      console.log("No user_roles required to be upserted.");
    }

    if (userMetasToUpsert.length > 0) {
      try {
        console.log("Creating/updating user_metas started, for diploma exam mark sync.");
        const resultsUserMetas =
        await knex(schema+"."+userMetaTableName)
        .insert(userMetasToUpsert)
        .onConflict(userMetasColumnsToMergeOn)
        .merge(userMetasColumnsToMerge);
        console.log("Creating/updating user_metas done, for diploma exam mark sync.")
      }

      catch(e) {
        console.log(e);
        throw(e);
      }
    }

    else {
      console.log("No user_metas required to be upserted.");
    }


    if (diplomaExamMarksFailedToSync.length !== 0) {
      console.log("Failed diploma exam mark IDs, because of unexpected errors: "
      + diplomaExamMarksFailedToSync);
    }

    console.log("Syncing diploma student school enrolments (G12).");
    // mark the G12 student enrolment rows successfully used for school user_roles school as synced

    if (syncedDiplomaStudentSchoolEnrolmentIds.length !== 0) {
      await this.turnOnSyncedFlag("pasi_data", "student_school_enrolments", syncedDiplomaStudentSchoolEnrolmentIds);
    }

    else {
      console.log("No diploma student school enrolment able to be synced.");
    }

    return [diplomaExamMarksSyncedSuccessfully, diplomaExamMarksFailedToSync];
  }

  async getAllRelevantDiplomaExamSittings(ASNs: string[]) {
    /*
    // get all DES associated not just with the immediate diploma exam marks being synced, but with 
    // all ASNs associated with those diploma exam marks, as we may need the other sittings linked with an ASN 
    */
    if (!ASNs || !ASNs.length) {
      return [];
    }

    const displmaExamSittings = [];

    for (let i = 0; i < ASNs.length; i+=diplomaExamSittingsChunkSize) {
      const ASNsChunkIds = ASNs.slice(i, i+diplomaExamSittingsChunkSize);
      const diplomaExamSittingsChunk: any[] = await dbRawRead(this.app, [ASNsChunkIds],
        `
        SELECT
          des.ExamRefId,
          des.IsDeleted AS IsDesDeleted,
          OrganizationCode,
          des.RefId AS SittingRefId,
          de.CourseCode,
          de.SchoolYear,
          de.ExamPeriod
        FROM pasi_data.diploma_exam_marks dem
          JOIN JSON_TABLE(
            dem.ComponentMarks ,
            "$.ComponentMarkInfo[*]"
            COLUMNS(
              desRefId VARCHAR(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci PATH '$.ExamSittingRefId'
            )
          ) AS cm
          JOIN pasi_data.diploma_exam_sittings des ON cm.desRefId = des.RefId
          JOIN pasi_data.diploma_exams de ON des.ExamRefId = de.RefId
        WHERE dem.StateProvinceId IN (?);`
      );
      displmaExamSittings.push(...diplomaExamSittingsChunk)
    }
    return displmaExamSittings;
  }

  async upsertForStudentEnrollments(studentEnrolments: any[], adminUid: number, nowTime: any): Promise<any[]> {
    if (studentEnrolments.length === 0) {
      return [[], []];
    }

    console.log("Getting required uids for student school enrolment.");
    const uidASNPairs = await this.getRequiredUids(studentEnrolments.map(enrolments => enrolments.StateProvinceId));
    return await this.upsertIntoUserRolesAndUserMetas(studentEnrolments, uidASNPairs, adminUid, nowTime);
  }

  async upsertIntoUserMetasAndUsers(students: any[], adminUid: number, nowTime: any): Promise<any[]> {
    if (students.length === 0) {
      return [[], []];
    }

    // take care of all users and user_metas insertions/updates
    // also updates pasi_data.students' records uid as needed

    // note, `abed_pasi` key_namespace is already used and is for importing students manually through the UI using CSVs
    const PASIKeyNamespace = userMetasPasiKeyNamespace;

    // list of PASI keys (linking to pasi_data.students table columns) to sync; comment out ones not needed
    const PASIKeys =
    [
      `AvailabilityStatus`,
      // `CitizenshipStatuses`, // not required to be saved at this point, so commented out
      `CreatedOnUtc`,
      // `DateOfDeath`, // not required to be saved at this point, so commented out
      `DisclosureRestrictions`,
      // `EmailAddresses`, // not required to be saved at this point, so commented out
      // `Gender`, // not required to be saved at this point, so commented out
      `IdentificationRecord`, // contains student's DOB, which is stored in `user_metas` with key `DateOfBirth`
      `IsDeactivated`,
      `IsDeceased`,
      `LastUpdateUtcTime`,
      `MaturityDate`,
      // `MaturityReason`, // not required to be saved at this point, so commented out
      // `MedicalAlerts`, // not required to be saved at this point, so commented out
      `OriginalNameRefId`,
      //`OtherPhoneNumbers`, // not required to be saved at this point, so commented out
      // `ParentGuardians`, // not required to be saved at this point, so commented out
      `PasiCoreVersion`,
      `PreferredNameRefId`,
      // `PreferredPhoneNumber`, // not required to be saved at this point, so commented out
      // `PrimaryLanguageSpokenAtHome`,// not required to be saved at this point, so commented out
      `PrimaryStateProvinceId`,
      `ScheduledRecordDisposalDate`,
      `SecondaryStateProvinceIds`,
      // `Section23Eligibility`, // not required to be saved at this point, so commented out
      // `StudentAddresses`, // not required to be saved at this point, so commented out
      `StudentNames`, // student's name would go in users table as well, rest only go in `user_metas`
    ];

    const DOBKey = "DateofBirth";
    const errMsgKey = "errMsg";
    const ASNKeyNamespace = "abed_course";
    const keyNamespaceName = "key_namespace";
    const schema = "mpt_dev";
    const pasiSchema = "pasi_data";
    const tableName = "user_metas";
    const knex: Knex = this.app.get('knexClientWrite');

    const userMetasDataToUpsert: any[] = [];
    const userMetasColumnsToMergeOn = ['uid', 'key'];
    const userMetasColumnsToMerge = ['value', 'updated_on', 'updated_by_uid', 'key_namespace'];

    const usersColumnsToMergeOn = ['id'];
    const usersColumnsToMerge = ['first_name', 'last_name', 'middle_name', 'is_PASI_student'];
    const usersTable = "users";

    console.log("Creating/updating users started.");

    // find any ASN existing for students missing an uid (pasi_data.students.uid)
    const newStudentUids: any[] = await this.findUidsFromASNs(students.
    filter(student => student.uid == null).
    map(student => student.StateProvinceId), ASNKey, ASNKeyNamespace);

    // console.log(newStudentUids);

    let pasiStudentsToUpdate: any[] = []; // for pasi_data.students
    for (let info of newStudentUids) {
      const idx = students.findIndex(student => student.StateProvinceId === info.ASN);

      // console.log(idx);

      // idx should never be -1 based on the logic above, but always good to have a fallback
      if (idx !== -1) {
        students[idx].uid = info.uid;

        // we also need to update pasi_data.students.uid, if we find existing cases in the `user_metas` table
        pasiStudentsToUpdate.push(
        {
          id: students[idx].id, // note, this id is `pasi_data.students.id`
          uid: students[idx].uid
        });
      }
    }

    if (pasiStudentsToUpdate.length > 0) {
      await knex(pasiSchema+"."+"students")
      .insert(pasiStudentsToUpdate)
      .onConflict("id")
      .merge(["uid"]);
    }

    // console.log(students);

    const newStudents = students.filter(student => student.uid == null);
    const oldStudents = students.filter(student => student.uid != null);

    // create users
    try {
      await this.createUsers(newStudents, nowTime, adminUid, schema, pasiSchema, usersTable);
    }
    catch(e) {
      console.log(e);
      throw(e);
    };

    // update users
    try {
      await this.updateUsers(oldStudents, usersColumnsToMergeOn, usersColumnsToMerge, schema, usersTable,
      nowTime, adminUid);
    }
    catch(e) {
      console.log(e);
      throw(e);
    }
    console.log("Creating/updating users done.");

    students = newStudents.concat(oldStudents);

    // at this point, we are guranteed to have uids for every student in students (students[i].uid)
    for (let i = 0; i < students.length; i++)
    {
      // set up data to upsert into user_metas here
      for (let key of PASIKeys) {
        let userMetaRowToUpsert: any = {
          uid: students[i].uid,
          key,
          value: this.convertStringOrObjToString(students[i][key]),
          updated_by_uid: adminUid,
          updated_on: nowTime,
          created_by_uid: adminUid,
          created_on: nowTime,
          key_namespace: PASIKeyNamespace
        };

        userMetasDataToUpsert.push(userMetaRowToUpsert);
      }

      // the following user_meta namespaces are inserted as the current ABED system uses and expects
      // these keys, so this makes PASI import of students more compatible with our system
      let ASNRowToInsert: any = {
        uid: students[i].uid,
        key: ASNKey,
        value: students[i].StateProvinceId,
        updated_by_uid: adminUid,
        updated_on: nowTime,
        created_by_uid: adminUid,
        created_on: nowTime,
        key_namespace: ASNKeyNamespace
      };

      let DOBRowToInsert: any = {
        uid: students[i].uid,
        key: DOBKey,
        value: this.parseDOB(students[i]?.IdentificationRecord?.BirthDate),
        updated_by_uid: adminUid,
        updated_on: nowTime,
        created_by_uid: adminUid,
        created_on: nowTime,
        key_namespace: ASNKeyNamespace
      };

      let errMsgEmptyRowToInsert: any = {
        uid: students[i].uid,
        key: errMsgKey,
        value: "[]",
        updated_by_uid: adminUid,
        updated_on: nowTime,
        created_by_uid: adminUid,
        created_on: nowTime,
        key_namespace: ASNKeyNamespace
      };

      let keyNameSpaceRowToInsert: any = {
        uid: students[i].uid,
        key: keyNamespaceName,
        value: "abed_sdc",
        updated_by_uid: adminUid,
        updated_on: nowTime,
        created_by_uid: adminUid,
        created_on: nowTime,
        key_namespace: ASNKeyNamespace
      };

      userMetasDataToUpsert.push(ASNRowToInsert);
      userMetasDataToUpsert.push(DOBRowToInsert);
      userMetasDataToUpsert.push(errMsgEmptyRowToInsert);
      userMetasDataToUpsert.push(keyNameSpaceRowToInsert);
    }

    console.log("Creating/updating user_metas started.");
    try {
      var result =
      await knex(schema+"."+tableName)
      .insert(userMetasDataToUpsert)
      .onConflict(userMetasColumnsToMergeOn)
      .merge(userMetasColumnsToMerge);
      console.log("Creating/updating user_metas done.")
    }

    catch(e) {
      console.log("Error in upserting", e);
      throw(e);
    }

    // 2nd array in the main array below is the failed students failed to sync, which will always be null
    return [students, []];
  }

  public parseDOB(DOB: string | undefined) {
    if (DOB === undefined) {
      return "";
    }

    // using previously made function (also made for the same date format, for importing PASI CSVs before)
    return this.app.service('public/school-admin/student').getDateStrFromDateTime(DOB);
  }

  parseGroupType(grade: string): string {
    if (grade == null) {
      return "";
    }

    return "ABED_GRADE_" + (+grade);
  }

  parseSchoolOrgCode(code: string): string {
    // only parses if the organization is a school code
    if (code == null) {
      return "";
    }

    if (!code.includes("S")) {
    /*
    // this is to prevent school authority or other organization's code being incorrectly parse as a school's
    // note schools, school authorities and other organizations all get codes from 1 to 9999 (they aren't unique between each other)
    // I ran a query to check if there's any such cases, and there's not, but just in case there are for the future,
    // this is a safety net which prevents syncing of that instance.
    // Query:
        SELECT ExamRefId, IsDeleted, OrganizationCode, RefId
        FROM pasi_data.diploma_exam_sittings
        WHERE OrganizationCode NOT LIKE "%S%"
        ;
    // Source: https://extranet.education.alberta.ca/pasidevnet/Docs/Technical%20API/html/d4aa8a92-8b6b-37c1-dd31-190e25e8526d.htm/
    */
      return "";
    }

    // remove "S." from school code
    return code.slice(2);

  }

  parseOrganizationCode(code: string): string {
    // parses both school and school authority code
    if (code == null) {
      return "";
    }

    // remove S. or A. from code
    return code.slice(2);
  }

  formIdentificationRecordFromDOB(dob: string | undefined) {
    const dobParsed = dob == null ? "" : dob;
    return {
      BirthDate: dobParsed
    };
  }

  formDisclosureRestrictionsFromHDR(hasDisclosureRestrictions: boolean | undefined) {
    return {
      StudentDisclosureRestrictionInfo: [
      {
        IsActive: hasDisclosureRestrictions == null ? false : hasDisclosureRestrictions
      }]
    };
  }

  formPASIStyleOrgCode(isSchool: boolean, orgCode: string) {
    // if iSchool== false, then it's a school authority

    if (orgCode == "") {
      return "";
    }

    return isSchool ? "S." + orgCode : "A." + orgCode;
  }

  public convertStringOrObjToString(input: string | Object): string {
    if (input == null) {
      return "";
    }

    if (Object.getPrototypeOf(input) === Object.prototype) {
      return this.objectToJSON(input);
    }

    return ""+input;
  }

  private getStudentOldTWID(gradeTWUserMetas: any[], uid: number): number {
    const studentGradeMetas = gradeTWUserMetas.filter(row => +row.uid === +uid);

    if (studentGradeMetas.length === 0) {
      return latestSchoolYearG12TWID;
    }

    else if (studentGradeMetas.length === 1) {
      return studentGradeMetas[0].test_window_id;
    }

    else {
      studentGradeMetas.sort((s1, s2) => {return s2.test_window_id - s1.test_window_id});
      return studentGradeMetas[0].test_window_id;
    }
  }

  async setDiplomaExamInfo(students: any[] | null | undefined, schoolGroupId: number) {
    if (students != null) {
      const response: any = await this.get(schoolGroupId, {
        query: {
          getStudentDiplomaExams: 1,
          uids: students.map(student => student.id),
          schoolGroupId: schoolGroupId
        }
      });

      for (let student of students) {
        const examData = response[student.id];
        student.diplomaExamInfo = examData == null ? [] : examData;
      }
    }
  }

  async getDataForStudentSchoolEnrolments(limitOnRecordSet: {limitStart: number, numRowsToFetch: number}, idsToIgnore: number[]) {
    /*
    // Used for getting student school enrolments record to sync, as the query is very specific, as opposed to other
    // services.
    // Note: we only sync students originally being in the allowedStudentSchoolEnrolmentYears and
    // allowedStudentSchoolEnrolmentGrades, which are identified by students.uid IS NOT NULL,
    // as students are synced first and based on the student enrolment records with the above conditions.

    // We specficially EXCLUDE allowedStudentSchoolEnrolmentGrades, keeping only allowedStudentSchoolEnrolmentYears,
    // to account for grade changes to be tracked and saved to our system.

    // We retrieve and account for deleted enrolments as well.
    */

    let params: any[] = [allowedStudentSchoolEnrolmentYears, allowedDiplomaGrades];
    let params2: any[] = [limitOnRecordSet.limitStart, limitOnRecordSet.numRowsToFetch];

    if (idsToIgnore.length !== 0) {
      params.push(idsToIgnore);
    }

    params = params.concat(params2);

    return await dbRawRead(this.app, params,
    `
      SELECT s.uid, sse.*
      FROM pasi_data.student_school_enrolments sse
      INNER JOIN pasi_data.students s on s.StateProvinceId = sse.stateProvinceId AND s.uid is NOT NULL
      WHERE sse.SchoolYear in (?)
      AND sse.Grade NOT IN (?)
      AND sse.synced_to_mpt_dev = 0
      ${idsToIgnore.length !== 0 ? 'AND sse.id not in (?)' : ''}
      ORDER BY sse.StateProvinceId, sse.Grade, sse.IsDeleted ASC, sse.RegistrationExitDate DESC, sse.RegistrationStartDate DESC
      LIMIT ?, ?
      ;
    `);
  }

  getExistingDemOrgCodes(ComponentMarks: any, diplomaExamSittings: any[]): string[] {
    // ComponentMarks comes from DEM.ComponentMarks
    let existingDemOrgCodes: string[] = [];
    for (let mark of ComponentMarks.ComponentMarkInfo) {
      // guranteed to find only 1 row because RefId is UNIQUE in the diploma exam sittings table
      const foundDES = diplomaExamSittings.find(sitting => sitting.SittingRefId === mark.ExamSittingRefId);

      // console.log(foundDES, "error would be here 1");
      if (foundDES != null) {
        existingDemOrgCodes.push(foundDES.OrganizationCode);
      }
      
    }
    
    return existingDemOrgCodes;
  }

  async isThereANonDeletedSameTypeDEM(demRow: any, diplomaExamSittings: any[], nonDeletedDems: any[]): Promise<boolean> {
    // note: "sameType" DEMs = 2 marks having the same OrganzationCode, SchoolYear and ExamPeriod

    const {ComponentMarks} = demRow;
    const existingDemOrgCodes = this.getExistingDemOrgCodes(ComponentMarks, diplomaExamSittings);

    for (let reg of nonDeletedDems) {
      for (let mark of reg.ComponentMarks.ComponentMarkInfo) {
        // guranteed to find only 1 row because RefId is UNIQUE in the diploma exam sittings table
        const foundDES = diplomaExamSittings.find(sitting => sitting.SittingRefId === mark.ExamSittingRefId);

        // console.log(foundDES, "error would be here", diplomaExamSittings, mark);
        if (foundDES != null) {
          if (existingDemOrgCodes.includes(foundDES.OrganizationCode)) {
            return true;
          }
        }
      }
    }

    return false;
  }

  async getNonDeletedDEMs(demRow: any): Promise<any[]> {
    const {StateProvinceId, SchoolYear, ExamPeriod} = demRow;
    
    return await dbRawRead(this.app, [StateProvinceId, SchoolYear, ExamPeriod], 
      `
      SELECT dem.ComponentMarks, dem.id
      FROM pasi_data.diploma_exam_marks dem
      INNER JOIN pasi_data.diploma_exams de on de.RefId = dem.ExamRefId
      WHERE dem.StateProvinceId = ? and dem.SchoolYear = ? and de.ExamPeriod = ? AND dem.IsDeleted = 0
      `);
    }
    
  /**
   * This function is used to get the PASI school year value from a test window linked to an access code.
   * @param semesterId: the school semester ID retrieved from the school class found from the access code entered
   * @returns the PASI school year of the corresponding test window associated with the school semester
  */
  public async getPASISchoolYearFromSemesterId(semesterId: number): Promise<{
    PASISchoolYear: string,
    testWindowId: number,
    typeSlug: string,
    pasiGradeCode: string
  } | null> {

    // should only return 1 row
    let TW = await dbRawRead(this.app, [semesterId],
    `
      SELECT tw.id, tw.PASI_school_year, tw.type_slug, tsm.pasi_grade_code
      FROM test_windows tw
      INNER JOIN school_semesters ss on ss.test_window_id = tw.id
      INNER JOIN type_slug_mappings tsm on tw.type_slug = tsm.type_slug
      WHERE ss.id = ?
      ;
    `);

    return TW.length === 0 ? null : {
      PASISchoolYear: TW[0].PASI_school_year,
      testWindowId: TW[0].id,
      typeSlug: TW[0].type_slug,
      pasiGradeCode: TW[0].pasi_grade_code
    };
  }

  /**
   * This function tries to sync the ASN from the PASI schema. This is done by attempts to sync
   * the student information from pasi_data.students and the student's school enrolment information
   * from pasi_data.student_school_enrolments. Any unexpected behaviour is thrown as an error.
   * @param ASN: the student ASN entered
   * @param nowTime: the current time to track in the DB
   * @param schoolYear: the PASI school year associated with the access code
  */

  public async trySyncASNFromPASISchema(ASN: string, nowTime: any,
  schoolYear: string, testWindowId: number, semesterId: number, twTypeSlug: string, pasiGradeCode: string): Promise<number> {
    /* Few Key Notes About The Below Record
    // 1. There should always only be 1 ASN in pasi_data.students
    // 2. Entry into this function only happens if an ASN is NOT found in user_metas.
    // 3. This HAS to mean if that ASN exists, the corresponding record in pasi_data.students has to have
    // uid = null and synced_to_mpt_dev = 0. Because as soon as a record is marked as synced,
    // there is guranteed to be an uid, whether it's a sync to update the user records or create them.
    */

    // do not sync ASN and do self-registration if the ASN is a secondary ASN
    const isASNSecondaryASN = await this.isASNASecondaryASN(ASN);
    // console.log(secondaryASNRecords);

    if (isASNSecondaryASN) {
      // TODO: confirm slug/error message
      throw new Errors.Forbidden("This ASN is a secondary ASN.", {secondaryASNError: true});
    }

    const studentRecord = await this.retrievePASIRecordForASN(ASN);

    if (studentRecord.length === 0) {
      // TODO: confirm if this message: " Incorrect ASN." can be thrown or if a new one is needed for this
      const isAllowStuSelfReg = await getSysConstNumeric(this.app, 'ALLOW_STU_SELF_REG'); // todo: memoize
      throw new Errors.NotFound('INVALID_USER_ID', {isAllowStuSelfReg});
    }

    const student = studentRecord[0];
    let sseRecords = await this.retrieveSSERecords(ASN, schoolYear);

    // console.log("pasi_data.student:", student);
    // console.log("sseRecords", sseRecords);

    if (sseRecords.length === 0) {
      const {dummySchoolGID, dummySClassGID} = await this.getDummySchoolAndSchoolClass();
      // console.log(dummySchoolGID, dummySClassGID);

      //insert the student into dummy school and class
      const syncedStudent = (await this.upsertIntoUserMetasAndUsers([student], 0, nowTime))[0][0];

      await this.app.service('db/write/user-roles').create({
        role_type: STUDENT_ROLE_TYPES.regular_student_role,
        uid: syncedStudent.uid,
        group_id: dummySchoolGID, // dummy school group_id
        created_by_uid: syncedStudent.uid,
      });

      await this.app.service('db/write/user-roles').create({
        role_type: STUDENT_ROLE_TYPES.regular_student_role,
        uid: syncedStudent.uid,
        group_id: dummySClassGID, // dummy class group_id
        created_by_uid: syncedStudent.uid,
      });


      // note for sync student grade: default language to English, as this student has no matching enrolment records anyways
      await this.turnOnSyncedFlag("pasi_data", "students", [student.id]);
      await this.ensureGroupTypeUserMeta(syncedStudent.uid, twTypeSlug, nowTime);
      await this.syncStudentGrade(syncedStudent.uid, testWindowId, pasiGradeCode, nowTime);
      await this.ensureLangUserMeta(syncedStudent.uid, "en", nowTime);

      return syncedStudent.uid;
    }

    /*
    // creates the new student, as they are guranteed to not exist in mpt_dev; only inserts, no updates.
    // then turns the pasi_data.student record to synced_to_mpt_dev = 1. The pasi_data.student.uid record
    // is updated in the upsertIntoUserMetasAndUsers function itself.
    */

    // syncedStudent is an array of arrays.
    // The length of the outer array will always be 2.
    // The 1st array is the important one, containing the students synced.
    // It will only have 1 student, with the new student just created / new pasi_data.student.uid just found
    const syncedStudent = (await this.upsertIntoUserMetasAndUsers([student], 0, nowTime))[0][0];
    await this.turnOnSyncedFlag("pasi_data", "students", [student.id]);
    await this.trySyncSSEFromPasiSchema(syncedStudent.uid, sseRecords, nowTime, testWindowId, semesterId, twTypeSlug);

    // note: we do not mark the SSE records as synced, as we only synced a school user_role from them.
    // We need other aspects not neccesarily possible to synced right now to fully mark the SSE records as synced.

    return syncedStudent.uid;
  }

  /**
   * This function returns any SSE records found for a particular ASN and SchoolYear, without considering Grade.
   * @param ASN: PASI student's StudentIdentificationNumber
   * @param schoolYear: PASI school year
   * @returns: SSE records tied to the ASN and SchoolYear
   */
  public async retrieveSSERecords(ASN: string, schoolYear: string): Promise<any[]> {
    return await dbRawRead(this.app, [ASN, schoolYear],
    `
      SELECT sse.*
      FROM pasi_data.student_school_enrolments sse
      WHERE sse.StateProvinceId = ? AND sse.SchoolYear = ? AND sse.IsDeleted = 0
      ;
    `);
  }

  /**
    This function retrieves any records from pasi_data.students, if any.
    * @param ASN: the student ASN entered
  */
  private async retrievePASIRecordForASN(ASN: string): Promise<any> {
    const records: any[] = await dbRawRead(this.app, [ASN],
      `
        SELECT *
        FROM pasi_data.students
        WHERE StateProvinceId = ?
        ;
      `);

    return records;
  }

  /**
    * This function attempts to sync the student school enrolment records of a particular student.
    * @param uid - the user id of the student attempting to login.
    * @param ASN: the student ASN entered
    * @param nowTime: the current time to track in the DB
    * @param schoolYear: the PASI school year associated with the access code
  */
  private async trySyncSSEFromPasiSchema(uid: number, sseRecords: any[], nowTime: any, testWindowId: number, semesterId: number, twTypeSlug: string): Promise<void> {
    // first, try find a SSE record that matches
    // the sse record should be non-deleted as we want non revoked school role for the login!
    // possible to have more than 1 sseRecord

    let formattedSchoolCodes: string[] = [];
    const uniqueSchoolCodes = Array.from(new Set(sseRecords.map(record => record.SchoolCode)));
    // console.log("SchoolCodes for user_roles: ", uniqueSchoolCodes);

    for (let schoolCode of uniqueSchoolCodes) {
      formattedSchoolCodes.push(this.formPASIStyleOrgCode(true, schoolCode));
    }

    const schoolRecords = await dbRawRead(this.app, [formattedSchoolCodes],
      `
        SELECT s.id, s.group_id, s.foreign_id, s.lang
        FROM schools s
        WHERE foreign_id in (?)
        ;
      `);

    const wereSchoolRolesCreated = await this.createSchoolUserRoles(schoolRecords, uid, nowTime);
    const wereSchoolClassRolesCreated = await this.createSchoolClassUserRoles(schoolRecords, semesterId, uid, nowTime);

    if (!wereSchoolRolesCreated) {
      // TODO: change error message to an according 1 for the scenario, to discuss
      throw new Errors.Forbidden("This student is not registered to any school.");
    }

    await this.ensureGroupTypeUserMeta(uid, twTypeSlug, nowTime);

    // there should only be 1 grade for a student in a SchoolYear under a school, no matter how many sse rows there are
    // it is possible for there to be multiple sse rows because of other fields (e.g. ExitDateType = WithdrawalDate, CompletionDate)
    // but the grade only remains 1 in all those rows. So we pick the 1st sse row's grade.
    await this.syncStudentGrade(uid, testWindowId, sseRecords[0].Grade, nowTime);

    // there should only be 1 school for a student in a SchoolYear in terms of their student school enrolment
    // it is possible for there to be multiple sse rows because of other fields (e.g. ExitDateType = WithdrawalDate, CompletionDate)
    // but the school only remains 1 in all those rows. So we pick the 1st school from the array (length should be 1) for the language.
    await this.ensureLangUserMeta(uid, schoolRecords[0].lang, nowTime);
  }

  /**
   * This student upserts a group type user meta for students, as one is expected in school admin view.
   * @param uid - the user id of the student attempting to login.
   * @param typeSlug - the test_window.type_slug needed for user_metas
   * @param nowTime - the current time to track in the DB
   */
  private async ensureGroupTypeUserMeta(uid: number, typeSlug: string, nowTime: any): Promise<void> {
    const groupTypeUserMetaKey = "GroupType";
    const groupTypeMetaKeyNamespace = "abed_course";
    const userMetasColumnsToMergeOn = ['uid', 'key'];
    const userMetasColumnsToMerge = ['value', 'updated_on', 'updated_by_uid', 'key_namespace'];
    const knex: Knex = this.app.get('knexClientWrite');
    const userMetasToUpsert = [];

    let userMetaToUpsert: any = {
      uid,
      key: groupTypeUserMetaKey,
      value: typeSlug,
      updated_by_uid: uid,
      updated_on: nowTime,
      created_by_uid: uid,
      created_on: nowTime,
      key_namespace: groupTypeMetaKeyNamespace
    };

    userMetasToUpsert.push(userMetaToUpsert);

    const results =
    await knex("mpt_dev.user_metas")
    .insert(userMetasToUpsert)
    .onConflict(userMetasColumnsToMergeOn)
    .merge(userMetasColumnsToMerge);
  }

  /**
   * This student upserts a language user meta for students, as one is expected in teacher view and when
   * logging in. It is based on the school linked to the school enrolment record of the student.
   * @param uid - the user id of the student attempting to login.
   * @param schoolLang - the language of the school linked to the enrolment record of the student
   * @param nowTime - the current time to track in the DB
   */
  private async ensureLangUserMeta(uid: number, schoolLang: "en" | "fr", nowTime: any): Promise<void> {
    const langUserMetaKey = "Lang";
    const langUserMetaKeyNamespace = "abed_dyn";
    const userMetasColumnsToMergeOn = ['uid', 'key'];
    const userMetasColumnsToMerge = ['value', 'updated_on', 'updated_by_uid', 'key_namespace'];
    const knex: Knex = this.app.get('knexClientWrite');
    const userMetasToUpsert = [];

    let langUserMetaToUpsert: any = {
      uid,
      key: langUserMetaKey,
      value: schoolLang,
      updated_by_uid: uid,
      updated_on: nowTime,
      created_by_uid: uid,
      created_on: nowTime,
      key_namespace: langUserMetaKeyNamespace
    };

    userMetasToUpsert.push(langUserMetaToUpsert);

    const results =
    await knex("mpt_dev.user_metas")
    .insert(userMetasToUpsert)
    .onConflict(userMetasColumnsToMergeOn)
    .merge(userMetasColumnsToMerge);
  }

  /**
   * This function upserts a grade user_meta into tw_user_metas for students, based on the grade of the student
   * tied to the student's school enrolment record.
   * @param uid - the user id of the student attempting to login.
   * @param testWindowId - the test window ID associated with the access code of the class the student is logging in with
   * @param grade - the student grade tied to the 1st enrollment record found (there should only be 1 grade)
   * @param nowTime: the current time to track in the DB
  */
  private async syncStudentGrade(uid: number, testWindowId: number, grade: string, nowTime: any): Promise<void> {
    const gradeRecordsToInsert: any[] = [];
    const gradeKeynamespace = "abed_pasi_sync";
    const knex: Knex = this.app.get('knexClientWrite');
    const records = await dbRawRead(this.app, [gradeKeynamespace, twumGradeKeyName, uid, testWindowId],
    `
      SELECT twum.id, twum.value, twum.test_window_id
      FROM tw_user_metas twum
      WHERE twum.key_namespace = ?
      AND twum.key = ?
      AND twum.uid = ?
      AND twum.test_window_id = ?
    `);

    if (records.length === 0) {
      gradeRecordsToInsert.push(
      {
        uid,
        key_namespace: gradeKeynamespace,
        key: twumGradeKeyName,
        value: grade,
        test_window_id: testWindowId,
        created_by_uid: uid,
        created_on: nowTime
      });

      await knex("mpt_dev.tw_user_metas")
      .insert(gradeRecordsToInsert);
    }

    else {
      const existingTwumRecordIds: number[] = records.map(record => record.id);
      await dbRawWrite(this.app, [grade, uid, nowTime, existingTwumRecordIds],
      `
        UPDATE tw_user_metas
        SET value = ?, updated_by_uid = ?, updated_on = ?
        WHERE id in (?)
      `);
    }
  }

  /**
   * This function creates a singular school class user_role for a given student, to a placeholder
   * class of a particular semester (test_window) and school.
   * @param schoolGroupId: school's group_id to insert the user_role for
   * @param semesterId: the semester_id to insert the user_role for
   * @param uid: the student's user id
   * @param nowTime: the current time as determined in the beginning of the script
   * @param teacherUid: uid of the admin causing this insertion
   */
  public async createSchoolClassUserRole(schoolGroupId: number, semesterId: number, uid: number, nowTime: any, teacherUid: number): Promise<void> {
    const placeholderSchoolClassRecord = (<Paginated<any>> await this.app
    .service('db/read/school-classes')
    .find({ query: {
      $select: ['id', 'is_placeholder', 'group_id', 'semester_id'],
      schl_group_id: schoolGroupId,
      is_placeholder: 1,
      semester_id: semesterId,
      $limit: 1,
    }}));

    if (placeholderSchoolClassRecord.data.length === 0) {
      // should never be the case, because of PASI sync logic, but just in case
      throw new Error("No placeholder class found for this writing session and school");
    }

    const classGroupId = placeholderSchoolClassRecord.data[0].group_id;
    const userRoleToUpsert: any = {
      role_type: "schl_student",
      uid,
      group_id: classGroupId,
      created_on: nowTime,
      created_by_uid: teacherUid,
      is_revoked: 0 // always 0 as the SSE record has to be non deleted (isDeleted = 0)
    };

    const existingUserRoles = <Paginated<any>> await this.app.service('db/write/user-roles').find({
      query: {
        uid,
        group_id: classGroupId,
        is_revoked: 0,
        role_type: "schl_student",
        $limit: 100,
      }
    });

    console.log(existingUserRoles, classGroupId, uid, semesterId, "exiting SCUR");

    // if no existing user_role for the same uid and group_id, create the user_role
    if (existingUserRoles.data.length === 0) {
      await this.app.service('db/write/user-roles').create(userRoleToUpsert);
    }
  }

  /**
    * This function creates school user roles for a given student and schools.
    * @param schoolCodes: the school codes as in `pasi_data.student_school_enrolment.SchoolCode` (e.g. 2304)
    * @param uid: the student's user id
    * @param nowTime: the current time as determined in the beginning of the script
    * @returns false if no school user role could be inserted because the school was not found in the database
    * @returns true if one or more school user role could be inserted.
  */
  private async createSchoolUserRoles(schoolRecords: any[], uid: number, nowTime: any): Promise<boolean> {
    let userRolesToInsert: any[] = [];
    if (schoolRecords.length === 0) {
      return false;
    }

    for (let school of schoolRecords) {
      const schoolUserRole: any = {
        role_type: "schl_student",
        uid,
        group_id: school.group_id,
        created_on: nowTime,
        created_by_uid: uid,
        is_revoked: 0 // always 0 as the SSE record has to be non deleted (isDeleted = 0)
      };

      userRolesToInsert.push(schoolUserRole);
    }

    const knexWrite: Knex = this.app.get('knexClientWrite');
    await knexWrite("mpt_dev.user_roles")
    .insert(userRolesToInsert);

    return true;
  }

    /**
    * This function creates school class user roles for a given student and schools.
    * @param schoolCodes: the school codes as in `pasi_data.student_school_enrolment.SchoolCode` (e.g. 2304)
    * @param uid: the student's user id
    * @param nowTime: the current time as determined in the beginning of the script
    * @returns false if no school user role could be inserted because the school was not found in the database
    * @returns true if one or more school user role could be inserted.
  */
  private async createSchoolClassUserRoles(schoolRecords: any[], semesterId: number, uid: number, nowTime: any): Promise<boolean> {
    let userRolesToInsert: any[] = [];
    if (schoolRecords.length === 0) {
      return false;
    }

    // there should only be 1 schoolRecord, and 99% of times, this will be true.
    for (let school of schoolRecords) {
      const schoolClasses = await this.findPlaceholderClass(semesterId, school.group_id);

      if (schoolClasses.length !== 0) {
        // if no placeholder classes found, just do not create one for now for this student but still
        // sync the student's school, which allows them to log in and write the assessment and be viewed
        // in the teacher UI

        const schoolClassUserRole: any = {
          role_type: "schl_student",
          uid,
          group_id: schoolClasses[0].group_id, // there should only be 1 school class found so pick 1st one
          created_on: nowTime,
          created_by_uid: uid,
          is_revoked: 0 // always 0 as the SSE record has to be non deleted (isDeleted = 0)
        };
  
        userRolesToInsert.push(schoolClassUserRole);
      }
    }

    if (userRolesToInsert.length > 0) {
      const knexWrite: Knex = this.app.get('knexClientWrite');
      await knexWrite("mpt_dev.user_roles")
      .insert(userRolesToInsert);

      return true;
    }

    return false;
  }

  async findPlaceholderClass(semesterId: number, schoolGroupId: number): Promise<any[]> {
    // there should only be 1 school class found from this query in ABED
    const schoolClass = await dbRawRead(this.app, [semesterId, schoolGroupId],
    `
      SELECT group_id, id, semester_id, schl_group_id
      FROM school_classes
      WHERE semester_id = ? AND schl_group_id = ?
      ;
    `);

    return schoolClass;
  }

  async isASNASecondaryASN(stateProvinceId: string): Promise<boolean> {
    const studentRecord = await dbRawRead(this.app, [""+stateProvinceId],
    `
      SELECT StateProvinceId, PrimaryStateProvinceId
      FROM pasi_data.students
      WHERE StateProvinceId = ?
      ;
    `);

    if (studentRecord.length === 0) {
      return false;
    }
    
    return (studentRecord[0].PrimaryStateProvinceId != null && studentRecord[0].PrimaryStateProvinceId != "");
  }

  async createDiplomaSchoolClassesForDEM(diplomaExamSittings: any[], diplomaTestWindows: any[],
  seenMissingTWErrors: Map<string, Set<string>>, seenSchoolCourseSchoolYearExamPeriodPairs: string[],
  schools: any[], nonPlaceholderSchoolClasses: any[], uGroupsToInsert: any[], nowTime: any,
  adminUid: number, schoolClassesToInsert: any[]): Promise<void>
  {
    const knex:Knex = this.app.get('knexClientRead');

    // console.log("School classes currently existing:\n", schoolClasses);
    console.log("Num unique diploma exam sittings: " + diplomaExamSittings.length);
    // For every unique school and course pairing, we need a school class
    // There can be multiple diploma exam sittings which have the same school and same course
    // Because of different ComponentCode, but we do not care about this for now
    for (let i = 0; i < diplomaExamSittings.length; i++) {

      // should only find 1 diploma (G12) test window for any 1 school year and exam period
      let diplomaTestWindowForSchoolYear = diplomaTestWindows.find(dtw => {
        return +dtw.PASI_school_year === +diplomaExamSittings[i].SchoolYear
        && dtw.pasi_exam_period === diplomaExamSittings[i].ExamPeriod;
      });

      if (diplomaTestWindowForSchoolYear == null) {
        /*
        // cannot even find a test window associated with G12 for the current school year and exam period
        // skip (need to create the test window and semester id and sync again to avoid this)
        */

        // to avoid spamming logs/console with the error (only show per SchoolYear and ExamPeriod)
        const seenExamPeriods = seenMissingTWErrors.get(diplomaExamSittings[i].SchoolYear);
        let displayError: boolean = false;
        
        if (seenExamPeriods != null) {
          if (!seenExamPeriods.has(diplomaExamSittings[i].ExamPeriod)) {
            displayError = true;
            seenExamPeriods.add(diplomaExamSittings[i].ExamPeriod);
          }
        }

        else {
          seenMissingTWErrors.set(diplomaExamSittings[i].SchoolYear, new Set([diplomaExamSittings[i].ExamPeriod]));
          displayError = true;
        }

        if (displayError) {
          console.log(`Cannot find a combination of school_semester, test_windows and type_slug_mappings for G12,`
          + ` for school year ${+diplomaExamSittings[i].SchoolYear}, for exam period ${diplomaExamSittings[i].ExamPeriod}` 
          + ` and for type_slug ${allowedDiplomaGroupType}.`
          + ` Ensure this is done (check type_slug_mappings, test_windows and school_semesters).`);  
        }

        continue;
      }

      const schoolCourseSchoolYearExamPeriodPair: string =
      diplomaExamSittings[i].OrganizationCode + "-" + diplomaExamSittings[i].CourseCode + "-" +
      diplomaExamSittings[i].SchoolYear + "-" + diplomaExamSittings[i].ExamPeriod;
      if (seenSchoolCourseSchoolYearExamPeriodPairs.includes(schoolCourseSchoolYearExamPeriodPair)) {
        // console.log("Skipping seen school course pair: ", i);
        continue;
      }

      else {
        seenSchoolCourseSchoolYearExamPeriodPairs.push(schoolCourseSchoolYearExamPeriodPair);
      }

      const school = schools.find(school => {
        return !(["", " "].includes(this.parseSchoolOrgCode(diplomaExamSittings[i].OrganizationCode)))
        && (+this.parseOrganizationCode(school.foreign_id) === +this.parseSchoolOrgCode(diplomaExamSittings[i].OrganizationCode));
      });

      // console.log(diplomaExamSittings[i]);
      // console.log(school);
      if (school == null) {
        // cannot even find such a school in our DB, skip (need to do a school sync again to avoid this)
        continue;
      }

      const schoolClass = nonPlaceholderSchoolClasses.find(schoolClass => {
        return schoolClass.course_code === diplomaExamSittings[i].CourseCode
        && !(["", " "].includes(this.parseSchoolOrgCode(diplomaExamSittings[i].OrganizationCode)))
        && +this.parseOrganizationCode(schoolClass.foreign_id) === +this.parseSchoolOrgCode(diplomaExamSittings[i].OrganizationCode)
        && +schoolClass.PASI_school_year === +diplomaExamSittings[i].SchoolYear
        && schoolClass.pasi_exam_period === diplomaExamSittings[i].ExamPeriod;
      });

      // console.log(schoolClass);
      if (schoolClass == null) {
        try {
          const schoolClassName = school.name + " - " + diplomaExamSittings[i].CourseCode;
          uGroupsToInsert.push({
          group_type: DBD_U_GROUP_TYPES.school_class,
          description: schoolClassName,
          created_on: nowTime,
          created_by_uid: adminUid
          });

          schoolClassesToInsert.push({
          group_id: 0, // is set right below in for loop
          schl_group_id: school.group_id,
          schl_dist_group_id: school.schl_dist_group_id,
          foreign_id: diplomaExamSittings[i].CourseCode,
          name: schoolClassName,
          semester_id: diplomaTestWindowForSchoolYear.semester_id,
          group_type: diplomaTestWindowForSchoolYear.type_slug,
          notes: "PASI Diploma (G12) School Class",
          is_placeholder: 0,
          key: ASNKey,
          dedicated_test_window_id: diplomaTestWindowForSchoolYear.id,
          is_active: 1,
          created_on: nowTime,
          created_by_uid: adminUid,
          access_code: generateAccessCode(8),
          });
        }

        catch(e) {
          console.log(e);
          throw(e);
        }
      }

      // console.log(i, ' going next');
    }

    // console.log(schoolClassesToInsert.length);
    if (schoolClassesToInsert.length > 0) {
      // console.log(uGroupsToInsert, schoolClassesToInsert);
      try {
        console.log("u_groups and school_classes insertions starting.");
        // length of this array is always 1
        // do u_groups insertions for school_classes
        const firstUGroupIdInserted: number[] =
        await knex("mpt_dev"+"."+"u_groups")
        .insert(uGroupsToInsert);
        let firstUGroupId: number = firstUGroupIdInserted[0];

        // note that the # of u_groups inserted above HAS to be equal to the length of schoolClassesToInsert
        // which HAS to equal to the length of uGroupsToInsert
        for (let i = 0; i < schoolClassesToInsert.length; i++) {
          schoolClassesToInsert[i].group_id = firstUGroupId;
          firstUGroupId++;
        }

        // do school_classes insertions
        await knex("mpt_dev"+"."+"school_classes")
        .insert(schoolClassesToInsert);
        console.log("Required school_classes and u_groups successfully created.");
      }

      catch(e) {
        console.log(e);
        throw(e);
      }
    }

    else {
      console.log("No school classes and u_groups required to be created.");
    }
  }

  async getDummySchoolAndSchoolClass(): Promise<{dummySchoolGID: number, dummySClassGID: number}> {
    let dummyData = await dbRawRead(this.app, [],
    `
      SELECT sc.schl_group_id, sc.group_id
      FROM school_classes sc
      INNER JOIN schools s on sc.schl_group_id = s.group_id
      WHERE sc.is_dummy = 1 AND s.is_dummy = 1
      ;
    `);

    if (dummyData.length === 0) {
      // no dummy data to use to self-sync a PASI student without student school enrolments, throw standard error
      // This should never happen, as our databases have dummy information now
      throw new Errors.GeneralError("This student has no matching enrolment record.");
    }

    return {
      dummySchoolGID: +dummyData[0].schl_group_id,
      dummySClassGID: +dummyData[0].group_id
    };
  }
}
