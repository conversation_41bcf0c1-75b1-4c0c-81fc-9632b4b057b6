// Initializes the `public/abed-pasi` service on path `/public/abed-pasi`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { AbedPasi } from './abed-pasi.class';
import hooks from './abed-pasi.hooks';

// Add this service to the service type index
declare module '../../../declarations' {
  interface ServiceTypes {
    'public/abed-pasi': AbedPasi & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/abed-pasi', new AbedPasi(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/abed-pasi');

  service.hooks(hooks);
}
