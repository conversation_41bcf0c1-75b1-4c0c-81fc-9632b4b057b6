import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { dbRawRead, dbRawWrite } from '../../../util/db-raw';
import { BadRequest } from '@feathersjs/errors';
import { currentUid } from '../../../util/uid';
import { Knex } from 'knex';
import asyncSleep from '../../../util/async-sleep';

interface Data {
  [x: string]: any;
}

interface ServiceOptions { }

const organizationsMergeCols = [
  'PrimarySite',
  'Code',
  'Status',
  'Name',
  'ShortName',
  'CodeType',
  'AuthorityOrgCodeEx',
  'OrganizationType',
  'IsEmpty',
  'CodeString',
  'CodeTypeString',
  'OrgCodeEx',
  'LegacyJurCode',
  'LegacySchCode',
  'LegalName',
  'LegalNameWithCode',
  'OperatingName',
  'OperatingNameWithCode',
  'Acronym',
  'AcronymWithCode',
  'ClassificationGroupCode',
  'ClassificationCodes',
  'Affiliation',
  'ProgramCodes',
  'IsActive',
  'is_synced'
];

export class AbedPed implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  knex: Knex;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.knex = this.app.get('knexClientWrite');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    if (!params?.query) throw new BadRequest();
    const getLatestVersion = params.query.getLatestVersion;
    if (+getLatestVersion === 1) {
      const latest = await dbRawRead(this.app, [],
        `
      SELECT plv.version
      FROM ped_data.ped_latest_version plv;
      `);
      //we only have one version record that we retrieve and update.
      if (latest.length) return latest[0];
    }
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: any, params?: Params): Promise<Data> {
    console.log("where")
    if (!params || !params.query) throw new BadRequest();
    const {
      getLatest,
      syncOrganizations,
      syncSchoolAdminStaffAccountIds,
      syncAccounts,
      syncStaff,
      schoolAdminsImport,
      updateLatest
    } = params.query;
    const cUid = await currentUid(this.app, params);
    if (+syncOrganizations === 1){
      await this.addOrUpdateOrganizations(data, cUid);
    }
    if (+syncSchoolAdminStaffAccountIds === 1){
      await this.setSchoolAdminStaffAccountIds(data);
    }
    if (+syncAccounts === 1) {
      await this.addOrUpdateAccounts(data);
    }
    if (+syncStaff === 1) {
      await this.addOrUpdateStaff(data);
    }
    if (+schoolAdminsImport === 1) {
      await this.schoolAdminsImport(cUid);
    }
    if (+updateLatest === 1) {
      const latestVersion = data.latestVersion;
      if (!latestVersion) throw new BadRequest();
      await dbRawWrite(this.app, [latestVersion],
        `
          UPDATE ped_data.ped_latest_version plv
          SET plv.version = ?, plv.updated_on = now()
          WHERE id = 1
          ;
      `);
    }
    return { success: true };
  }

  async addOrUpdateAccounts(accounts: any) {
    const accounts_ids = accounts.data.map((o: any) => o.AccountId);
    const existing_accs = <any>await dbRawRead(this.app, [accounts_ids],
      `
        SELECT * from ped_data.accounts
        where AccountId in (?);
        ;
    `);
    for (let oneAccount of accounts.data) {
      if (existing_accs.indexOf((existing_acc: any) => existing_acc.AccountId == oneAccount.AccountId) == -1) {
        <any>await dbRawWrite(this.app, [
        oneAccount.AccountId,
        oneAccount.ScreenName || null,
        oneAccount.SignInEmail,
        oneAccount.SmsNumber || null,
        oneAccount.LastLoginOn || null,
        oneAccount.GoverningOrgCodeEx || null,
        oneAccount.SalutationCode || null,
        oneAccount.FirstName || null,
        oneAccount.LastName || null,
        oneAccount.Department || null,
        oneAccount.JobTitle || null,
        oneAccount.PhoneNumber || null,
        oneAccount.PhoneExtension || null,
        oneAccount.PhoneAndExtension || null,
        oneAccount.TeachingCertificateNumber || null,
        oneAccount.ManagedByOrgCodeExSet || null],
          `
          INSERT INTO ped_data.accounts (
            AccountId, ScreenName, SignInEmail, SmsNumber, LastLoginOn,
            GoverningOrgCodeEx, SalutationCode, FirstName, LastName,
            Department, JobTitle, PhoneNumber, PhoneExtension, PhoneAndExtension,
            TeachingCertificateNumber, ManagedByOrgCodeExSet
          ) VALUES (
              ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
          );
        `);
      }
      else {
        <any>await dbRawWrite(this.app, [
          oneAccount.ScreenName || null,
          oneAccount.SignInEmail,
          oneAccount.SmsNumber || null,
          oneAccount.LastLoginOn || null,
          oneAccount.GoverningOrgCodeEx || null,
          oneAccount.SalutationCode || null,
          oneAccount.FirstName || null,
          oneAccount.LastName || null,
          oneAccount.Department || null,
          oneAccount.JobTitle || null,
          oneAccount.PhoneNumber || null,
          oneAccount.PhoneExtension || null,
          oneAccount.PhoneAndExtension || null,
          oneAccount.TeachingCertificateNumber || null,
          oneAccount.ManagedByOrgCodeExSet || null,
          oneAccount.AccountId],
          `
          UPDATE ped_data.accounts
          SET
              ScreenName = ?,
              SignInEmail = ?,
              SmsNumber = ?,
              LastLoginOn = ?,
              GoverningOrgCodeEx = ?,
              SalutationCode = ?,
              FirstName = ?,
              LastName = ?,
              Department = ?,
              JobTitle = ?,
              PhoneNumber = ?,
              PhoneExtension = ?,
              PhoneAndExtension = ?,
              TeachingCertificateNumber = ?,
              ManagedByOrgCodeExSet = ?
          WHERE AccountId = ?;
          `);
      }
    }
  }

  async addOrUpdateStaff(staff: any) {
    const staff_ids = staff.data.map((o: any) => o.StaffAccountId);
    const existing_staff = <any>await dbRawRead(this.app, [staff_ids],
      `
        SELECT * from ped_data.staff
        where StaffAccountId in (?);
        ;
    `);
    for (let oneStaff of staff.data) {
      if (existing_staff.indexOf((one_existing_staff: any) => one_existing_staff.StaffAccountId == oneStaff.StaffAccountId) == -1) {
        <any>await dbRawWrite(this.app, [
          oneStaff.AccountId,
          oneStaff.StaffAccountId,
          oneStaff.StaffAccountName || null,
          oneStaff.LegacyFirstName || null,
          oneStaff.LegacyLastName || null,
          oneStaff.OrgCodeEx || null,
          oneStaff.OrgCodeExGlobal || null,
          oneStaff.IsOrgCodeSelect || null,
          oneStaff.IsDeleted || null],
          `
          INSERT INTO ped_data.staff (
            AccountId, StaffAccountId, StaffAccountName, LegacyFirstName,
            LegacyLastName, OrgCodeEx, OrgCodeExGlobal, IsOrgCodeSelect, IsDeleted
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?
        );
        `);
      }
      else {
        <any>await dbRawWrite(this.app, [
          oneStaff.AccountId,
          oneStaff.StaffAccountName || null,
          oneStaff.LegacyFirstName || null,
          oneStaff.LegacyLastName || null,
          oneStaff.OrgCodeEx || null,
          oneStaff.OrgCodeExGlobal || null,
          oneStaff.IsOrgCodeSelect || null,
          oneStaff.IsDeleted || null,
          oneStaff.StaffAccountId],
          `
          UPDATE ped_data.staff
          SET
            AccountId = ?,
            StaffAccountName = ?,
            LegacyFirstName = ?,
            LegacyLastName = ?,
            OrgCodeEx = ?,
            OrgCodeExGlobal = ?,
            IsOrgCodeSelect = ?,
            IsDeleted = ?
          WHERE StaffAccountId = ?;
          `);
      }
    }
  }

  async schoolAdminsImport (uid:number) {
    const needsRole = await this.knex.select([
        'school_admins.StaffAccountId',
        'school_admins.status',
        'users.id as uid',
        'staff.OrgCodeEx',
        'accounts.FirstName',
        'accounts.LastName',
        'accounts.SignInEmail',
        'accounts.PhoneNumber'
      ])
      .from('ped_data.school_admins')
      .join('ped_data.staff', {'school_admins.StaffAccountId': 'staff.StaffAccountId'})
      .join('ped_data.accounts', {'staff.AccountId': 'accounts.AccountId'})
      .leftJoin('users', {'accounts.SignInEmail':'users.contact_email'})
      .whereIn('status', ['TO_REVOKE', 'TO_ASSIGN']);
    const toBeAssigned = needsRole.filter(nr => nr.status === 'TO_ASSIGN')
    await this.createUsers(toBeAssigned.filter(tba => !tba.uid), uid);
    await this.revokeSchoolAdminAccess(needsRole.filter(nr => nr.status === 'TO_REVOKE'), uid);
    await this.assignSchoolAdminAccess(toBeAssigned, uid);
  }

  async createUsers(toCreate:any[], cUid:number) {
    for (let tc of toCreate) {
      if(tc.SignInEmail == null || tc.SignInEmail == "") continue;
      //generate users records
      tc.uid = await this.addOrUpdateUser(tc, cUid);
      //generate auths records
      await this.addAuth(tc.uid, tc);
    }
  }

  async revokeSchoolAdminAccess (toRevoke:any[], uid:number) {
    if (toRevoke.length === 0) {
      return;
    }
    const schoolOrgCodes = new Set(toRevoke.map(tr => tr.OrgCodeEx.slice(2)));
    const schoolGroupIdData = await this.knex()
      .select('foreign_id', 'group_id')
      .from('schools')
      .whereIn('foreign_id', Array.from(schoolOrgCodes));
    const orgCodesToSchoolGroupIds = new Map(schoolGroupIdData.map(d => { return [d.foreign_id, d.group_id] }));
    for (const tr of toRevoke) {
      await this.knex('user_roles')
        .where({ uid })
        .andWhere({ group_id: orgCodesToSchoolGroupIds.get(parseInt(tr.OrgCodeEx.slice(2), 10)) })
        .update({
          is_revoked: 1,
          revoked_on: this.knex.raw('CURRENT_TIMESTAMP'),
          revoked_by_uid: tr.uid
        })
      await this.knex('ped_data.school_admins')
        .where({ StaffAccountId: tr.StaffAccountId })
        .update({
          status: 'SYNCED'
        })
    }
  }

  async assignSchoolAdminAccess (toAssign:any[], uid:number) {
    if (toAssign.length === 0) {
      return;
    }
    const schoolOrgCodes = new Set(toAssign.map(ta => ta.OrgCodeEx.slice(2)));
    const schoolGroupIdData = await this.knex()
      .select('foreign_id', 'group_id')
      .from('schools')
      .whereIn('foreign_id', Array.from(schoolOrgCodes));
    const orgCodesToSchoolGroupIds = new Map(schoolGroupIdData.map(d => { return [d.foreign_id, d.group_id] }));
    const insertData = toAssign.map(ta => {
      return {
        id: null,
        role_type: 'schl_admin',
        uid: ta.uid,
        group_id: orgCodesToSchoolGroupIds.get(parseInt(ta.OrgCodeEx.slice(2), 10)),
        created_on: this.knex.raw('CURRENT_TIMESTAMP'),
        created_by_uid: uid,
        expires_on: null,
        is_revoked: 0,
        revoked_on: null,
        revoked_by_uid: null,
        is_superfluous: 0
      }
    });
    await this.knex('user_roles')
      .insert(insertData)
    for (const ta of toAssign) {
      await this.knex('ped_data.school_admins')
        .where({ StaffAccountId: ta.StaffAccountId })
        .update({
          status: 'SYNCED'
        })
    }
  }

  async setSchoolAdminStaffAccountIds (staffAccountIds:number[]) {
    const result = await this.knex.select('StaffAccountId').from('ped_data.school_admins');
    const oldStaffAccountIds = new Set(result.map(sa => sa.StaffAccountId as number));
    const newIdsSet = new Set(staffAccountIds);
    const idsToRevoke = Array.from(oldStaffAccountIds).filter(id => !newIdsSet.has(id));
    if (idsToRevoke.length !== 0) {
      await this.knex('ped_data.school_admins')
        .insert(idsToRevoke.map(id => { return { StaffAccountId: id, status: 'TO_REVOKE' }}))
        .onConflict('StaffAccountId')
        .merge(['status']);
    }
    const idsToAssign = staffAccountIds.filter(id => !oldStaffAccountIds.has(id));
    if (idsToAssign.length !== 0) {
      await this.knex('ped_data.school_admins')
      .insert(idsToAssign.map(id => { return { StaffAccountId: id, status: 'TO_ASSIGN' }}))
      .onConflict('StaffAccountId')
      .merge(['status']);
    }
  }

  async addOrUpdateOrganizations(organizations: any[], uid:number) {
    organizations.forEach(org => {
      org.is_synced = 0;
      if (org.PrimarySite && typeof org.PrimarySite !== 'string') {
        org.PrimarySite = JSON.stringify(org.PrimarySite);
      }
      if (org.ProgramCodes && typeof org.ProgramCodes !== 'string') {
        org.ProgramCodes = JSON.stringify(org.ProgramCodes);
      }
      if (org.ClassificationCodes && typeof org.ClassificationCodes !== 'string') {
        org.ClassificationCodes = JSON.stringify(org.ClassificationCodes);
      }
    });
    if (organizations.length !== 0) {
      await this.knex('ped_data.organizations')
        .insert(organizations)
        .onConflict('OrgCodeEx')
        .merge(organizationsMergeCols);
      await asyncSleep(1000);
    }

    await this.updateSchoolDistricts(uid);
    await this.updateSchools(uid);
  }

  async updateSchoolDistricts (uid:number) {
    const districtsNeedSyncing = await this.knex.select('*')
      .from('ped_data.organizations')
      .leftJoin('school_districts', {'school_districts.foreign_id': 'organizations.Code'})
      .where('is_synced', 0)
      .andWhere('OrganizationType', 1);
    if (districtsNeedSyncing.length === 0) {
      return;
    }
    const newDistricts = districtsNeedSyncing.filter(d => d.id === null);
    const changedDistricts = districtsNeedSyncing.filter(d => d.id !== null);
    await this.createDistricts(newDistricts, uid);

    const districtUpdates = changedDistricts.map(d => {
      const primarySite = typeof d.PrimarySite === 'string' ? JSON.parse(d.PrimarySite) : d.PrimarySite;
      return {
        id: d.id,
        name: d.Name,
        address: primarySite?.StreetAddress || undefined,
        city: primarySite?.CityName || undefined,
        province: primarySite?.StateProvince || undefined,
        postal_code: primarySite?.PostalCode || undefined,
        phone_number: primarySite?.Phone || undefined,
        fax_number: primarySite?.Fax || undefined,
        foreign_id: d.Code,
        is_sample: 0,
      }
    })
    if (districtUpdates.length === 0) {
      return;
    }
    await this.knex('school_districts').insert(districtUpdates)
      .onConflict('id')
      .merge(Object.keys(districtUpdates[0]).filter(k => k !== 'id'));
  }

  async createDistricts (newDistricts:any[], uid:number) {
    if (newDistricts.length === 0) {
      return;
    }
    let firstNewGroupId = await this.createGroups(newDistricts, 'Name', 'school_district', uid);
    newDistricts.forEach(d => d.group_id = firstNewGroupId++);
    let firstNewId = (await this.knex('school_districts').insert(newDistricts.map(d => {
      const primarySite = typeof d.PrimarySite === 'string' ? JSON.parse(d.PrimarySite) : d.PrimarySite;
      return {
        group_id: d.group_id,
        name: d.Name,
        address: primarySite?.StreetAddress,
        city: primarySite?.CityName,
        province: primarySite?.StateProvince,
        postal_code: primarySite?.PostalCode,
        phone_number: primarySite?.Phone,
        fax_number: primarySite?.Fax,
        notes: 'created from PED data',
        is_active: 1,
        is_shown: 1,
        created_by_uid: uid,
        foreign_id: d.Code,
        is_sample: 0,
        group_type: 'school_district'
      }
    })))[0];
    newDistricts.forEach(d => d.id = firstNewId++);
    await this.knex('ped_data.organizations')
      .whereIn('OrgCodeEx', newDistricts.map(d => d.OrgCodeEx))
      .update({'is_synced': 0});
  }


  async createGroups (groupData:any[], descFieldName: string, groupType: string, uid: number) {
    const newGroups = groupData.map(g => {
      return {
        group_type: groupType,
        description: g[descFieldName],
        created_by_uid: uid
      }
    });
    return (await this.knex('u_groups').insert(newGroups))[0];
  }

  async updateSchools (uid: number) {
    const schoolsNeedSyncing = await this.knex.select('*')
      .from('ped_data.organizations')
      .leftJoin('schools', {'schools.foreign_id': 'organizations.Code'})
      .where('is_synced', 0)
      .andWhere('OrganizationType', 2);
    if (schoolsNeedSyncing.length === 0) {
      return;
    }

    const districtOrgCodes = new Set(schoolsNeedSyncing.map(
      s => parseInt(s.AuthorityOrgCodeEx.slice(2), 10)
    ));
    const districtGroupIdData:{foreign_id:number, group_id:number}[] = await this.knex.select('foreign_id', 'group_id')
      .from('school_districts')
      .whereIn('foreign_id', Array.from(districtOrgCodes));
    const orgCodesToGroupIds = new Map(districtGroupIdData.map(d => { return [d.foreign_id, d.group_id] }));
    const newSchools = schoolsNeedSyncing.filter(s => s.id === null);
    const changedSchools = schoolsNeedSyncing.filter(s => s.id !== null);
    await this.createSchools(newSchools, orgCodesToGroupIds, uid);
    const schoolUpdates = changedSchools.map(s => {
      const primarySite = typeof s.PrimarySite === 'string' ? JSON.parse(s.PrimarySite) : s.PrimarySite;
      return {
        schl_dist_group_id: orgCodesToGroupIds.get(parseInt(s.AuthorityOrgCodeEx.slice(2), 10)),
        foreign_id: s.Code,
        name: s.Name,
        address: primarySite?.StreetAddress || undefined,
        city: primarySite?.CityName || undefined,
        province: primarySite?.StateProvince || undefined,
        postal_code: primarySite?.PostalCode || undefined,
        phone_number: primarySite?.Phone || undefined,
        fax_number: primarySite?.Fax || undefined
      }
    });
    let schoolColsToMerge = Object.keys(schoolUpdates[0]).filter(k => k !== 'foreign_id');
    await this.knex('schools')
      .insert(schoolUpdates)
      .onConflict('foreign_id')
      .merge(schoolColsToMerge);
    await this.knex('ped_data.organizations')
    .whereIn('OrgCodeEx', changedSchools.map(d => d.OrgCodeEx))
    .update({'is_synced': 0});
  }

  async createSchools (newSchools:any[], orgCodesToGroupIds:Map<number, number>, uid:number) {
    if (newSchools.length === 0) {
      return;
    }
    let firstNewGroupId = await this.createGroups(newSchools, 'Name', 'school', uid);
    newSchools.forEach(d => d.group_id = firstNewGroupId++);
    let firstNewId = (await this.knex('schools').insert(newSchools.map(s => {
      const primarySite = typeof s.PrimarySite === 'string' ? JSON.parse(s.PrimarySite) : s.PrimarySite;
      return {
        group_id: s.group_id,
        schl_dist_group_id: orgCodesToGroupIds.get(parseInt(s.AuthorityOrgCodeEx.slice(2), 10)),
        name: s.Name,
        lang: 'en',
        notes: 'created from PED data',
        is_active: 1,
        is_shown: 1,
        created_by_uid: uid,
        foreign_id: s.Code,
        address: primarySite?.StreetAddress || undefined,
        city: primarySite?.CityName || undefined,
        province: primarySite?.StateProvince || undefined,
        postal_code: primarySite?.PostalCode || undefined,
        phone_number: primarySite?.Phone || undefined,
        fax_number: primarySite?.Fax || undefined
      };
    })))[0];
    newSchools.forEach(s => s.id = firstNewId++);
    await this.knex('ped_data.organizations')
      .whereIn('OrgCodeEx', newSchools.map(d => d.OrgCodeEx))
      .update({'is_synced': 0});
  }


  async addOrUpatePrimarySites(primary_sites: any) {
    const primary_sites_ids = primary_sites.data.map((p: any) => p.SiteId);
    const existingSites = <any>await dbRawRead(this.app, [primary_sites_ids],
      `
        SELECT * from ped_data.primary_sites
        where SiteId in (?);
        ;
    `);
    for (let site of primary_sites.data) {
      if (existingSites.indexOf((existingSite: any) => existingSite.SiteId == site.SiteId) == -1) {
        <any>await dbRawWrite(this.app, [site.SiteId || null, site.OrgCodeEx || null, site.SiteName || null, site.IsPrimaryAdminSite || null, site.Phone, site.Fax || null,
        site.WebsiteUrl || null, site.EmailAddress || null, site.StreetAddress || null, site.CityName || null, site.StateProvince || null,
        site.PostalCode || null, site.CountryName || null, site.AlbertaInfrastructureFacilityId || null,
        site.DeliveryStreetAddress || null, site.DeliveryCityName || null, site.DeliveryStateProvince || null,
        site.DeliveryPostalCode || null, site.DeliveryCountryName || null, site.OnsiteVisitOrDeliveryComments || null,
        site.GeoCodedLocationType || null, site.GeoCodedPlaceId || null, site.GeoCodedLocation || null,
        site.GeoCodedOn || null, site.GeoLocationLatitude || null, site.GeoLocationLongitude || null, site.GoogleMapsUrl || null],
          `
          INSERT INTO ped_data.primary_sites (
            SiteId, OrgCodeEx, SiteName, IsPrimaryAdminSite, Phone, Fax,
            WebsiteUrl, EmailAddress, StreetAddress, CityName, StateProvince,
            PostalCode, CountryName, AlbertaInfrastructureFacilityId,
            DeliveryStreetAddress, DeliveryCityName, DeliveryStateProvince,
            DeliveryPostalCode, DeliveryCountryName, OnsiteVisitOrDeliveryComments,
            GeoCodedLocationType, GeoCodedPlaceId, GeoCodedLocation,
            GeoCodedOn, GeoLocationLatitude, GeoLocationLongitude, GoogleMapsUrl
          ) VALUES (
              ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
          );
        `);
      } else {
        <any>await dbRawWrite(this.app, [site.OrgCodeEx,
        site.SiteName,
        site.IsPrimaryAdminSite,
        site.Phone,
        site.Fax,
        site.WebsiteUrl,
        site.EmailAddress,
        site.StreetAddress,
        site.CityName,
        site.StateProvince,
        site.PostalCode,
        site.CountryName,
        site.AlbertaInfrastructureFacilityId,
        site.DeliveryStreetAddress,
        site.DeliveryCityName,
        site.DeliveryStateProvince,
        site.DeliveryPostalCode,
        site.DeliveryCountryName,
        site.OnsiteVisitOrDeliveryComments,
        site.GeoCodedLocationType,
        site.GeoCodedPlaceId,
        site.GeoCodedLocation,
        site.GeoCodedOn,
        site.GeoLocationLatitude,
        site.GeoLocationLongitude,
        site.GoogleMapsUrl,
        site.SiteId],
          `
          UPDATE ped_data.primary_sites
          SET
              OrgCodeEx = ?,
              SiteName = ?,
              IsPrimaryAdminSite = ?,
              Phone = ?,
              Fax = ?,
              WebsiteUrl = ?,
              EmailAddress = ?,
              StreetAddress = ?,
              CityName = ?,
              StateProvince = ?,
              PostalCode = ?,
              CountryName = ?,
              AlbertaInfrastructureFacilityId = ?,
              DeliveryStreetAddress = ?,
              DeliveryCityName = ?,
              DeliveryStateProvince = ?,
              DeliveryPostalCode = ?,
              DeliveryCountryName = ?,
              OnsiteVisitOrDeliveryComments = ?,
              GeoCodedLocationType = ?,
              GeoCodedPlaceId = ?,
              GeoCodedLocation = ?,
              GeoCodedOn = ?,
              GeoLocationLatitude = ?,
              GeoLocationLongitude = ?,
              GoogleMapsUrl = ?
          WHERE SiteId = ?;
        `);
      }
    }
  }

  async addOrUpdateUser(admin: any, created_by_uid: number) {
    const user = <any>await this.app.service('db/read/users').find({
      query: {
        contact_email: admin.SignInEmail
      }
    });
    if (user.total) {
      await this.app.service('db/write/users').update(user.data[0].id, {
        first_name: admin.FirstName,
        last_name: admin.LastName,
        contact_phone: admin.PhoneNumber,

      })
      return user.data[0].id;
    } else {
      const newUser = await this.app.service('db/write/users').create({
        first_name: admin.FirstName,
        last_name: admin.LastName,
        contact_phone: admin.PhoneNumber,
        contact_email: admin.SignInEmail,
        account_type: 'schl-admin',
        created_by_uid
      })
      return newUser.id;
    }
  }

  async addAuth(uid: number, admin: any) {
    const auth = <any>await dbRawRead(this.app, [admin.SignInEmail],
      `
        SELECT id from mpt_dev.auths
        where email = ?;
        ;
    `);
    if (auth.length == 0) {
      <any>await dbRawWrite(this.app, [uid, admin.SignInEmail],
        `
          INSERT INTO mpt_dev.auths (uid, enabled, email, password, failed_login_attempts)
            VALUES (?, '1', ?, '$2a$10$itvSGvjFLEz0fravlx7ple1LnyE0B0/JbJL49xbVUeN5Bx/H1NBt2', '0');
      `);
    }
  }

  async addUserToSchool(uid: number, school_group_id: number, created_by_uid: number) {

    return await this.app
      .service('auth/user-role-actions').assignUserRoleToGroup(
        {
          role_type: 'schl_admin',
          uid: uid,
          group_id: school_group_id,
          created_by_uid,
        }
      )
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
