// Initializes the `public/abed-ped` service on path `/public/abed-ped`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { AbedPed } from './abed-ped.class';
import hooks from './abed-ped.hooks';

// Add this service to the service type index
declare module '../../../declarations' {
  interface ServiceTypes {
    'public/abed-ped': AbedPed & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/abed-ped', new AbedPed(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/abed-ped');

  service.hooks(hooks);
}
