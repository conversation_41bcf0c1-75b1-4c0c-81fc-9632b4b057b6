import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { dbRawRead } from '../../../util/db-raw';

interface Data {}

interface AssessmentDef 
{
  id: number,
  assessment_slug: string,
  order: number,
  is_disabled: boolean
}

enum Requests
{
  assessmentDef = "assessmentDef"
}

interface ServiceOptions {}

export class AssessmentsInfo implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<AssessmentDef[]>
  {
    if (params?.query?.request === Requests.assessmentDef)
    {
      return await dbRawRead(this.app, [], 
      `
      SELECT ad.id, ad.assessment_slug, ad.order, ad.is_disabled
      FROM assessment_def ad
      ORDER BY ad.order
      ;
      `);
    }
    
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
