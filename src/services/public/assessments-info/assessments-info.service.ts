// Initializes the `public/assessments-info` service on path `/public/assessments-info`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { AssessmentsInfo } from './assessments-info.class';
import hooks from './assessments-info.hooks';

// Add this service to the service type index
declare module '../../../declarations' {
  interface ServiceTypes {
    'public/assessments-info': AssessmentsInfo & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/assessments-info', new AssessmentsInfo(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/assessments-info');

  service.hooks(hooks);
}
