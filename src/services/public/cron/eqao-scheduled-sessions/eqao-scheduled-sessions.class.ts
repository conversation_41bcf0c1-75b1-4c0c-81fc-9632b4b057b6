import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import ExcelJS, { Workbook } from 'exceljs';
import { Stream } from 'stream';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { storeInS3 } from '../../../upload/upload.listener';
import { Readable } from 'stream';
import { currentUid } from '../../../../util/uid';
import { dbRawRead, dbRawWrite } from '../../../../util/db-raw';
import logger from '../../../../logger';
import { dbDateNow } from '../../../../util/db-dates';

interface ITestWindowInfo { 
  test_window_id: number, 
  type_slug: string,
  slug_prefix: string,
  filename: string,
  schedule: {id: number}, // more
}
interface Data {
  [key: string]: any,
}

interface ServiceOptions {}

export class EQAOScheduledSessions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: Data, params?: Params): Promise<Data> {
    const uid = params?.authentication?.accessToken ? await currentUid(this.app, params) : null;
    const activeWindows = await this.prepSessionSchedulesForAllWindows();
    this.computeSessionSchedulesForWindows(activeWindows, uid);
    return {activeWindows};
  }

  async prepSessionSchedulesForAllWindows():Promise<ITestWindowInfo[]>{
    const activeWindows:ITestWindowInfo[] = await dbRawRead(this.app, [], `
      select twtar.test_window_id
          , twtar.type_slug
      from test_windows tw 
      join test_window_td_alloc_rules twtar 
        on twtar.test_window_id = tw.id
        and twtar.is_custom = 0
      where is_allow_session_schedule = 1
        group by tw.id
    ;`);

    for (let window of activeWindows){
      const {type_slug, test_window_id} = window;
      window.filename = `Scheduled Sessions ${type_slug} ${test_window_id} ${(new Date()).toISOString().split(':').join('-')}.xlsx`;
      window.slug_prefix = type_slug.split('_')[0]; // todo: this is temporary, we should just be using the type_slug directly
      // Insert new row into session_schedules
      window.schedule = await this.app
        .service('db/write/session-schedules')
        .create({
          test_window_id,
          is_completed: 0,
          slug_prefix: window.slug_prefix
        });
    }
    return activeWindows
  }
  async computeSessionSchedulesForWindows(activeWindows:ITestWindowInfo[], created_by_uid:number | null){

    for (let window of activeWindows){
      const {
        test_window_id, 
        type_slug, 
        filename,
        slug_prefix,
        schedule,
      } = window;
      
      // Generate workbook
      const failedCases:string[] = [];
      const stream = await this.generateWorkbookStream(slug_prefix, test_window_id, failedCases);
      const filePath = await this.uploadToS3(filename, stream, test_window_id);
      await this.updateTestWindowFiles(filePath, test_window_id, created_by_uid || null);
  
      await this.app
        .service('db/write/session-schedules')
        .patch(schedule.id, {
          is_completed: 1,
          last_completed_on: dbDateNow(this.app),
          logs: JSON.stringify({
            failedCases,
          })
        });
    }
    return {};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  private async uploadToS3(filename:string, stream: Readable, testWindowId: number): Promise<string> {
    logger.silly('Start uploading to s3');
    const filePath = `data_release/eqao_data_extracts/scheduled_sessions/test_window_id/${testWindowId}/${filename}`;
    await storeInS3(filePath, stream);
    logger.silly('File uploaded to: %s',filePath);
    return filePath;
  }

  private async updateTestWindowFiles(filePath: string, testWindowId: number, uid: number | null): Promise<void> {
    const slug = 'SCHED_SESS';
    let sql: string;
    // Update old record
    sql = `
      UPDATE test_window_files
      SET is_revoked = 1, revoked_on = now() ${uid ? ` ,revoked_by_uid = ${uid}` : ''}
      WHERE test_window_id = ? AND slug = ? AND is_revoked = 0;
    `;
    await dbRawWrite(this.app, [testWindowId, slug], sql);

    // Insert new record
    sql = `
      INSERT INTO test_window_files
        (test_window_id, slug, file_path, created_on, is_revoked ${uid ? ' ,created_by_uid' : ''})
      VALUES
        (?, ?, ?, now(), 0 ${uid ? `, ${uid}` : ''});
    `;
    await dbRawWrite(this.app, [testWindowId, slug, filePath], sql);

    logger.silly(`Updated DB table test_window_files`);
  }

  private async generateWorkbookStream(slugPrefix: string, testWindowId: number, failedCases:string[]): Promise<Readable> {
    const stream = new Stream.PassThrough();

    // Construct the parameters to pass
    const params = { query: { ts_slugs: [slugPrefix + '_OPERATIONAL'], test_window_id: testWindowId } };
    const paramsWithSample = { query: {...params.query, ts_slugs: [...params.query.ts_slugs, slugPrefix + '_SAMPLE'] } };

    // Get Excel sheets definition
    const sheetDefs = this.getSheetDefs();

    // Create new Excel workbook
    const wb =  new ExcelJS.stream.xlsx.WorkbookWriter({
      stream: stream,
    });

    // Get data and add sheets to excel
    for (let sheet of sheetDefs){
      console.log('SCHED_SESS ', sheet.caption, new Date().toISOString());
      await this.addWorksheet(wb, failedCases, sheet.caption, sheet.route, sheet.withSample ? paramsWithSample : params);
    }

    wb.commit();

    logger.silly('Generated workbook');

    return stream;
  }

  private async addWorksheet(wb: Workbook, failedCases:string[], caption: string, route: string, params: { query: { ts_slugs: string[], test_window_id: number } }): Promise<void> {
    const ws = wb.addWorksheet(caption);
    try {
      const records = await (this.app.service(route) as any).find(params) as Data[];
      if (records.length > 0) {
        ws.columns = Object.keys(records[0]).map(c => Object({ header: c, key: c }));
        records.forEach((row, idx) => {
          if (idx === 0) {
            ws.addRow(row).font = { bold: true };
          } else {
            ws.addRow(row);
          }
        });
      }
    }
    catch (e){
      failedCases.push(caption)
    }
    ws.commit();
    logger.silly('Added Sheet: %s', caption);
  }

  private getSheetDefs() {
    return [
      {caption: 'Submissions Summary',                route: 'public/test-ctrl/schools/data-dl/responses/subm-summary',          withSample: false},
      {caption: 'Language-based Summary',             route: 'public/test-ctrl/schools/data-dl/responses/lang-subm-summ',        withSample: false},
      {caption: 'by Board',                           route: 'public/test-ctrl/schools/data-dl/responses/by-board',              withSample: true},
      {caption: 'by School',                          route: 'public/test-ctrl/schools/data-dl/responses/by-school',             withSample: true},
      {caption: 'by Date',                            route: 'public/test-ctrl/schools/data-dl/responses/by-date',               withSample: true},
      {caption: 'Schools by Date',                    route: 'public/test-ctrl/schools/data-dl/responses/sch-by-date',           withSample: true},
      {caption: 'Response Submissions Summary',       route: 'public/test-ctrl/schools/data-dl/responses/cr-subm',               withSample: false},
      {caption: 'Num of Students by Panel Number',    route: 'public/test-ctrl/schools/data-dl/responses/num-stu-by-pnum',       withSample: false},
      {caption: 'Submissions',                        route: 'public/test-ctrl/schools/data-dl/responses/subm',                  withSample: false},
      {caption: 'Student Information Validation',     route: 'public/test-ctrl/schools/data-dl/responses/stu-asmt-val'},
      {caption: 'Generated Reports',                  route: 'public/test-ctrl/schools/data-dl/responses/sch-isr-report-access', withSample: false},
    ];
  }
}
