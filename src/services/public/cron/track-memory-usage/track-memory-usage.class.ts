import {
  Id,
  NullableId,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  ServiceMethods,
} from "@feathersjs/feathers";
import { Application } from "../../../../declarations";
import { Errors } from "../../../../errors/general";
import * as https from "https";
import { writeHeapSnapshot } from "v8";
import logger from "../../../../logger";
import AWS from "aws-sdk";

AWS.config.update({ region: "ca-central-1" });
interface Data {
  memoryUsage: number;
  status: string;
}

interface ServiceOptions {}

export class TrackMemoryUsage implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  interval: NodeJS.Timeout | undefined;
  memoryThreshold = 1024 * 1024 * 90000; // 90000 MB in bytes (disabled)
  monitorEnabled: boolean = true;
  monitorFrequency: number = 1000 * 30; // 30 seconds
  processRunning: boolean = false;
  autoScaling = new AWS.AutoScaling();

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // Monitor memory usage every 30 seconds
  startMonitoring() {
    // Clear the existing interval if it's already running
    if (this.interval) {
      clearInterval(this.interval);
      logger.info("Previous monitoring interval cleared.");
    }

    // Start a new interval
    this.interval = setInterval(() => {
      if (this.monitorEnabled) {
        this.checkMemory();
      }
    }, this.monitorFrequency);

    logger.info(`Memory monitoring started with frequency: ${this.monitorFrequency / 1000} seconds.`);
  }


  // Method to check memory usage and take action if above threshold
  async checkMemory() {
    // Skip check if previous process is running
    if (this.processRunning) {
      return;
    }
    this.processRunning = true;
    try {
      const memoryUsage = process.memoryUsage();
      const readableMemoryUsage = (memoryUsage.heapUsed / 1024 / 1024).toFixed(2);

      if (memoryUsage.heapUsed < this.memoryThreshold) {
        // console.log(
        //   `Memory usage is normal: ${(memoryUsage.heapUsed / 1024 / 1024).toFixed(
        //     2
        //   )} MB`
        // );
        logger.info(
          `Memory usage is normal: ${readableMemoryUsage} MB`
        );
        this.processRunning = false; // Explicitly reset before returning
        return;
      }

      // High memory usage - proceed with handling
      logger.info(
        `High memory usage detected: ${readableMemoryUsage} MB`
      );
      const instanceId = await this.getInstanceId();
      logger.info(
        `Retrieved InstanceID: ${instanceId}`
      );
      const autoScalingGroupName = await this.getAutoScalingGroupName(
        instanceId
      );
      await this.enterStandby(instanceId, autoScalingGroupName);
      await this.takeHeapDump();
      await this.sendAlert(memoryUsage.heapUsed / 1024 / 1024 , instanceId, autoScalingGroupName);
    } catch (err) {
      logger.info(
        `Error when checking node process memory usage: ${err}`
      );
    }
    this.processRunning = false;
  }


  // Retrieve the current instance ID using the EC2 metadata service
  async getInstanceId(): Promise<string> {
    const instanceMetadataService = new AWS.MetadataService();
    return new Promise((resolve, reject) => {
      instanceMetadataService.request('/latest/meta-data/instance-id', (err, data) => {
        if (err) {
          logger.error(
            `Error retrieving instance ID from AWS SDK: ${err.message}`
          );
          reject(err);
        } else {
          resolve(data);
        }
      });
    });
  }

    // Retrieve the current instance ID using the EC2 metadata service
    // getInstanceId(): string {
    //   const instanceId = execSync(
    //     "curl -s http://169.254.169.254/latest/meta-data/instance-id"
    //   )
    //     .toString()
    //     .trim();
    //   logger.info("InstanceMetadata", `Retrieved instance ID: ${instanceId}`);
    //   return instanceId;
    // }


  // Retrieve Auto Scaling group name for the instance using AWS SDK
  async getAutoScalingGroupName(instanceId: string): Promise<string> {
    const ec2 = new AWS.EC2();
    const params = { InstanceIds: [instanceId] };
    const response = await ec2.describeInstances(params).promise();
    const tags = response.Reservations?.[0]?.Instances?.[0]?.Tags || [];
    const asgTag = tags.find((tag) => tag.Key === "aws:autoscaling:groupName");

    if (!asgTag) {
      throw new Error("Auto Scaling group name not found for this instance.");
    }

    logger.info(
      `Retrieved Auto Scaling group name: ${asgTag.Value}`
    );
    return asgTag.Value!;
  }

  // Enter standby mode for the current instance
  async enterStandby(instanceId: string, autoScalingGroupName: string) {
    const params = {
      AutoScalingGroupName: autoScalingGroupName,
      InstanceIds: [instanceId],
      ShouldDecrementDesiredCapacity: false, // needs to replace with another instance when sent to standby
    };

    this.autoScaling.enterStandby(params, (err, data) => {
      if (err) {
        logger.error(
          `Error entering standby for instance ${instanceId}: ${err}`
        );
      } else {
        logger.info(
          `Instance ${instanceId} entered standby successfully: ${JSON.stringify(
            data
          )}`
        );
      }
    });
  }

  // Take a heap dump and save it to a file
  async takeHeapDump() {
    const heapDumpFilename = writeHeapSnapshot();
    logger.info(`Heap dump taken successfully: ${heapDumpFilename}`);
    this.monitorEnabled = false;
  }

  // Send an alert to Mattermost using the https module
  sendAlert(memoryUsage: number, instanceId: string, autoScalingGroupName: string) {
    const mattermostWebhookUrl = "https://mattermost.vretta.com/hooks/tzcp5n7ao3ys5qo58ccm89tfih"; // Mattermost webhook URL - currently webhook-testing

    const alertMessage = {
      text: `:siren: *High Memory Usage Alert* :siren:\n- **Memory Usage**: ${memoryUsage.toFixed(
        2
      )} MB\n- **Instance ID**: ${instanceId}\n- **Auto Scaling Group**: ${autoScalingGroupName}\n\nThe instance has been taken out of service, and a heap dump was created for analysis.`,
    };

    const payload = JSON.stringify(alertMessage); // Stringify once here

    const url = new URL(mattermostWebhookUrl);
    const options = {
      hostname: url.hostname,
      path: url.pathname,
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Content-Length": Buffer.byteLength(payload), // Ensure proper length calculation
      },
    };

    const req = https.request(options, (res) => {
      let response = '';
      res.on('data', (chunk) => {
        response += chunk;
      });

      res.on('end', () => {
        logger.info(
          `Alert sent to Mattermost successfully. Response: ${response}`
        );
      });
    });

    req.on("error", (error) => {
      logger.error(`Error sending alert: ${error.message}`);
    });

    req.write(payload); // Send the correct payload
    req.end();
  }



  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Error("Method not implemented.");
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(id: Id, params?: Params): Promise<Data> {
    throw new Error("Method not implemented.");
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  /**
   * Start period background node memory usage monitoring. Accepts override for memory threshold.
   */
  async create(data: Data, params?: Params) {
    // Ensure params and params.query exist
    const query = params?.query || {};

    // Set memoryThreshold from params.query or default to the predefined value
    const memoryThreshold = query.memoryThreshold || this.memoryThreshold;

    this.monitorEnabled = true;

    // Start monitoring with the current or default memoryThreshold
    this.memoryThreshold = memoryThreshold;
    this.startMonitoring();

    return [];
  }


  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error("Method not implemented.");
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error("Method not implemented.");
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  /**
   * Stop period background node memory usage monitoring.
   */
  async remove(id: NullableId, params?: Params) {
    this.monitorEnabled = false;
    if (this.interval) {
      clearInterval(this.interval);
        logger.info("Memory monitoring interval has been cleared.");
    } else {
        logger.info("No active monitoring interval to clear.");
    }
    return [];
  }
}
