// Initializes the `public/cron/track-memory-usage` service on path `/public/cron/track-memory-usage`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { TrackMemoryUsage } from './track-memory-usage.class';
import hooks from './track-memory-usage.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/cron/track-memory-usage': TrackMemoryUsage & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/cron/track-memory-usage', new TrackMemoryUsage(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/cron/track-memory-usage');

  service.hooks(hooks);
}
