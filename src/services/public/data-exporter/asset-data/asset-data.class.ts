import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawReadReporting } from '../../../../util/db-raw';
import { pullFromS3 } from '../../../upload/upload.listener';


interface PartitionData {
  kind: "partition",
  partKey: string,
  field: string,
  parts: any[],
}

interface DFData {
  kind: "dataframe",
  schema: string[],
  records: any[],
}

interface DagState {
  isComplete: boolean
  filepath?: string
  partitions?: {
    [key: string]: {
      partKey: string,
      filepath: string,
      field: string,
      partitions: IPartInfo[]
    }
  }
}

interface IPartInfo {
  name: string,
  path: string,
  // partSlug: string,
  // length: number,
  // size: number,
}

type Data = DFData | PartitionData;

interface ServiceOptions {}

export class AssetData implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const {jobTag, assetSlug, partKey, partSlug} = params?.query || {};

    // Input Validation
    if (!jobTag) {
      throw new Errors.BadRequest('Must request by jobTag or exportId');
    }
    // Input validation
    if (!assetSlug) {
      // TODO: (jladan) should we support getting the list of asset slugs available in the run?
      throw new Errors.BadRequest('Missing asset slug');
    }

    // TODO: implement lookup of export by canonical job tag
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    // data-exporter/asset-data/<export-id>
    const exportId = id;
    const {assetSlug, partKey, partSlug} = params?.query || {};

    // Input validation
    if (!assetSlug) {
      // TODO: (jladan) should we support getting the list of asset slugs available in the run?
      throw new Errors.BadRequest('Missing parameters: must at least specify assetSlug');
    }

    // Get export information
    const state = await this.getDagState(<number> exportId);

    // Pull asset data
    let assetState = state[assetSlug];
    if (!assetState) {
      throw new Errors.NotFound()
    }

    // Handle the different types of data queries
    if (!partKey && !partSlug) {
      return await this.getAssetData(assetState, assetSlug);
    }
    else if (!partSlug) {
      return await this.getPartitionInfo(assetState, partKey);
    }
    else if (partSlug) {
      return await this.getPartData(assetState, assetSlug, partKey, partSlug)
    }

    throw new Errors.BadRequest()
  }

  async getDagState(exportId: number) {
    const eRecords = await dbRawReadReporting(this.app, {exportId},
      'select dej.dag_state from data_export_jobs dej where dej.id = :exportId');
    if (eRecords.length != 1) {
      throw new Errors.BadRequest('Unrecognized exportId');
    }
    return <DagState[]> JSON.parse(eRecords[0].dag_state).state;
  }

  async getAssetData(state: DagState, assetSlug: string) : Promise<DFData> {

    const assetUrl = state.filepath
    if (!assetUrl) {
      throw new Errors.NotFound(`Cached full asset for ${assetSlug} not found`)
    }
    const data = <Buffer> await pullFromS3(assetUrl);
    const records = JSON.parse(data.toString());
    return {
      kind: "dataframe",
      schema: records[0] ? Object.keys(records[0]): [],
      records,
    }
  }

  async getPartitionInfo(state: DagState, partKey: string) : Promise<PartitionData> {
    const partInfo = state.partitions?.[partKey];
    if (!partInfo) {
      throw new Errors.NotFound(`partition '${partKey}' does not exist for the asset`);
    }
    const parts = partInfo.partitions.map( (p: any) => {
      return {partSlug: p.name, length: p.length, size: p.size}
    })
    return {
      kind: "partition",
      partKey: partInfo.partKey,
      field: partInfo.field,
      parts,
    }
  }

  async getPartData(state: DagState, assetSlug: string, partKey: string, partName: string) : Promise<DFData> {
    let partInfo = state.partitions?.[partKey];
    if (!partInfo) {
      throw new Errors.NotFound(`partition '${partKey}' does not exist for the asset`);
    }
    const part = partInfo.partitions.find( (p: IPartInfo) => p.name === partName )
    const partPath = part?.path;
    if (!partPath) {
      throw new Errors.NotFound(`Partition not found for ${assetSlug}, ${partKey}, ${partName}.`);
    }

    let records: any;
    try {
      const data = <Buffer> await pullFromS3(partPath);
      records = JSON.parse(data.toString());
    }
    catch (err) {
      throw new Errors.NotFound(`Data missing for information missing for ${assetSlug}, ${partKey}.`);
    }

    return {
      kind: "dataframe",
      schema: records[0] ? Object.keys(records[0]): [],
      records,
    }

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
