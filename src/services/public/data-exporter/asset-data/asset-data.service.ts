// Initializes the `public/data-exporter/asset-data` service on path `/public/data-exporter/asset-data`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { AssetData } from './asset-data.class';
import hooks from './asset-data.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/data-exporter/asset-data': AssetData & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/data-exporter/asset-data', new AssetData(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/data-exporter/asset-data');

  service.hooks(hooks);
}
