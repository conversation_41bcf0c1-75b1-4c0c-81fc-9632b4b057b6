import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { DagAsset, DagAssetDependencyTypes, DependencySourcingType, TEMP_DAG_ASSETS } from '../data-export/model/assets';
import { objToArr } from '../../../../util/param-sanitization';
import { dbRawReadReporting } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class DataAssetDefs implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const assetDefs = TEMP_DAG_ASSETS;

    return TEMP_DAG_ASSETS;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async getCodebookFields(assets: any[]) {
    // TODO: determine the query
    const query = `
select ddtf.id
    , ddtf.table_slug
    , ddtf.slug
    , ddtf.caption
    , ddtf.type
    , ddtf.description
    , ddtf.is_nullable
    , ddtf.is_primary_key
    , ddtf.is_foreign_key
    , ddtf.is_foreign_derived
    , ddtf.fk_table_type
    , ddtf.fk_table_slug
    , ddtf.fk_field_slug
    , ddtf.fd_table_type
    , ddtf.fd_field_slug
from data_def_table_fields ddtf
join data_def_tables ddt
    on ddt.slug = ddtf.table_slug
where ddtf.is_revoked = 0
and ddt.id in (select id from data_def_tables where \`type\` = 'dag' and is_revoked = 0 and table_slug in (:assetSlugs));
    `;

    const assetSlugs = assets.map( asset => asset.slug );
    const fields = await dbRawReadReporting(this.app, {assetSlugs}, query)

    for (const asset of assets) {
      asset.fields = fields.filter( field => field.table_slug == asset.slug );
    }
    return fields;
  }
}
