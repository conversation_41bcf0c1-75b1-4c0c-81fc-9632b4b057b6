// Initializes the `public/data-exporter/data-asset-defs` service on path `/public/data-exporter/data-asset-defs`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { DataAssetDefs } from './data-asset-defs.class';
import hooks from './data-asset-defs.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/data-exporter/data-asset-defs': DataAssetDefs & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/data-exporter/data-asset-defs', new DataAssetDefs(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/data-exporter/data-asset-defs');

  service.hooks(hooks);
}
