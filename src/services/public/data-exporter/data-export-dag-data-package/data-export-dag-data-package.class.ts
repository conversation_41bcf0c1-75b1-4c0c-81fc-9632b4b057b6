import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { STORAGE_BUCKET_PREFIX, WHITELABEL_CONTEXT } from '../../../../constants/whitelabel';
import { dbRawReadReporting, dbRawWrite } from '../../../../util/db-raw';

interface Data {
  exportId?: number;
  dagJobName?: string;
  pathhash?: string;
  bucket?: string;
  packagePath?: string;
  url?: string;
}

interface ServiceOptions {}

export class DataExportDagDataPackage implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  /**
   * Returns the expected location for the package url to be stored
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    // Get the export id information
    const exportId = id;
    const eRecords = await dbRawReadReporting(this.app, {exportId},
      'select dej.id, dej.dag_job_name, dej.path_hash, dej.dag_package_url from data_export_jobs dej where dej.id = :exportId');
    if (eRecords.length != 1) {
      throw new Errors.BadRequest('Unrecognized exportId');
    }
    const exportJob = eRecords[0];

    // if (eRecords[0].dag_package_url) {
    //   return eRecords[0].dag_package_url;
    // }

    let packagePath = `data-jobs/${WHITELABEL_CONTEXT}/${exportJob.id}/packages/${exportJob.path_hash}/`;

    return {
      exportId: exportJob.id,
      dagJobName: exportJob.dag_job_name,
      pathhash: exportJob.path_hash,
      bucket: STORAGE_BUCKET_PREFIX,
      packagePath,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const exportId = typeof id === 'number' ? id : typeof id === 'string' ? parseInt(id) : undefined;
    if (!exportId) {
      throw new Errors.BadRequest('exportId is required');
    }
    const eRecords = await dbRawReadReporting(this.app, {exportId},
      'select dej.id from data_export_jobs dej where dej.id = :exportId');
    if (eRecords.length != 1) {
      throw new Errors.BadRequest('Unrecognized exportId');
    }
    try {
      this.app.service('public/data-exporter/data-export').launchPackager(exportId);
    } catch (err) {
      throw new Errors.Unavailable("Failed to launch the packager");
    }

    // Just return back the export id on success
    return {
      exportId,
    };
  }

  /**
   * Used to update dag_package_url for the export job
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const exportId = id;
    const { url } = data;
    if (!url || typeof url !== 'string') {
      throw new Errors.BadRequest('url is required');
    }

    // Get the current job
    const eRecords = await dbRawReadReporting(this.app, {exportId},
      'select dej.id, dej.dag_job_name, dej.path_hash, dej.dag_package_url from data_export_jobs dej where dej.id = :exportId');
    if (eRecords.length != 1) {
      throw new Errors.BadRequest('Unrecognized exportId');
    }
    const exportJob = eRecords[0];

    // Perform the patch
    await this.app.service('db/write/data-export-jobs').patch(exportJob.id, {
      dag_package_url: url,
    });

    return {
      exportId: exportJob.id,
      dagJobName: exportJob.dag_job_name,
      pathhash: exportJob.path_hash,
      url,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
