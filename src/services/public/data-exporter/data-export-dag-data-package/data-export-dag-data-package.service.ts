// Initializes the `public/data-exporter/data-export-dag-data-package` service on path `/public/data-exporter/data-export-dag-data-package`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { DataExportDagDataPackage } from './data-export-dag-data-package.class';
import hooks from './data-export-dag-data-package.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/data-exporter/data-export-dag-data-package': DataExportDagDataPackage & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/data-exporter/data-export-dag-data-package', new DataExportDagDataPackage(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/data-exporter/data-export-dag-data-package');

  service.hooks(hooks);
}
