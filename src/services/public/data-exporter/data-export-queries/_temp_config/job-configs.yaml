- slug: pipeline_config
  description: defines the variables that are needed to orchestrate the rest of the pipeline
  params: 
    - name: test_window_ids
      type: number[]
      description: which administration windows are in scope 
      isRequired: true
    - name: include_sample_schools
      type: boolean
      description: Should the pipeline include schools that are flagged as sandbox schools or contained within sample school districts
      default: false 
    - name: include_sample_assessments
      type: boolean
      description: Should the pipeline include data relating to assessments that are not tagged as practice/sample/classroom assessments
      default: false 
    - name: filter_to_assessments
      type: string[]
      description: allows a subset assessment type slugs to be provided for the pipeline run