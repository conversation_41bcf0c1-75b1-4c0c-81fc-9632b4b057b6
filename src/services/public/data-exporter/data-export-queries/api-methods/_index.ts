import { IExportApiCallDef } from "../types/type"

import { SQL_05B_EXCLUSION_THRESHOLDS } from "./q05b_responses/exclusion-thresholds"
import { SQL_05B_PRINCIPAL_KIT_META } from "./q05b_responses/pk-meta"
import { SQL_05B_PRINCIPAL_KIT_SCHOOL_ASMT } from "./q05b_responses/pk-school-asmt"
import { SQL_05B_PRINCIPAL_KIT_STUDENTS } from "./q05b_responses/pk-students"
import { SQL_05B_SPEC_EXCLUSION_THRESHOLD } from "./q05b_responses/spec-exclusion-thresholds"

interface IExporterApiCallRef {
    [key:string]: IExportApiCallDef
}

export const ExporterApiCallRef:IExporterApiCallRef = {
    'SQL_05B_EXCLUSION_THRESHOLDS': SQL_05B_EXCLUSION_THRESHOLDS,
    'SQL_05B_SPEC_EXCLUSION_THRESHOLD': SQL_05B_SPEC_EXCLUSION_THRESHOLD,
    'SQL_05B_PRINCIPAL_KIT_META': SQL_05B_PRINCIPAL_KIT_META,
    'SQL_05B_PRINCIPAL_KIT_SCHOOL_ASMT': SQL_05B_PRINCIPAL_KIT_SCHOOL_ASMT,
    'SQL_05B_PRINCIPAL_KIT_STUDENTS': SQL_05B_PRINCIPAL_KIT_STUDENTS,
}
