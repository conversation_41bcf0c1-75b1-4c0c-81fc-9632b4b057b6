import { Application } from "@feathersjs/express";

import { IExportApiCallDef } from "../../types/type";

import { group_by } from "../../transforms/group-by";
import { map_col } from "../../transforms/map-col";
import { inner_join, left_join } from "../../transforms/merge";
import { restrict_cols } from "../../transforms/restrict-cols";
import { drop_duplicates } from "../../transforms/drop-duplicates";
import { setWhereInplace } from "../../transforms/set-where";

// Type definitions
type value = number | string | boolean
type IRow = {[key: string]: value}

interface IConfig {
    item_responses_fixed_filled: IRow[],
    item_register: IRow[],
    is_field_test: any,
    is_field_test_override: Number,
    spec_exclusion_threshold: IRow[],
}

export const SQL_05B_EXCLUSION_THRESHOLDS:IExportApiCallDef = {
    requiredInputs: [],
    req: async (app: Application, config:IConfig) => {

        // Override is_field_test param if provided
        if (config.is_field_test_override != null) {  
            config.is_field_test = config.is_field_test_override
        } else {
            config.is_field_test = config.is_field_test[0] // Extracting boolean from array
        }

        // Restrict columns from inputs for efficiency
        config.item_responses_fixed_filled = restrict_cols(config.item_responses_fixed_filled,
            ["twtdar_id", "asmt_code", "test_attempt_id", "test_design_id", "is_nr",  "item_id"]
        );

        config.item_register = restrict_cols(config.item_register,
            ["twtar_id", "component_slug"]
        );

        // Calculate omit_rate for attempts in result
        let result = group_by(
            config.item_responses_fixed_filled,
            ["twtdar_id", "asmt_code", "test_design_id", "test_attempt_id"], // Attempt level aggregation
            [
                {
                    col_new: "nr_sum",
                    agg_type: "sum",
                    col_target: "is_nr",
                },
                {
                    col_new: "n_items",
                    agg_type: "count",
                    col_target: "item_id",
                }
            ]);
        result = map_col(result, "omit_rate", ["nr_sum", "n_items"], "divide", null, null);

        // Merge in component_slug from item_register
        config.item_register = drop_duplicates(config.item_register, ["twtar_id"])
        result = left_join(result, config.item_register, ["twtdar_id"], ["twtar_id"])
        
        // Set-Where transform to add is_stem flag
        const stemCourses = ["MATH", "SCI"]
        result = setWhereInplace(result,
            {
                col: "asmt_code",
                comparison: "matches-pattern",
                value: stemCourses.join("|") // Stem courses regex for pattern matching, like MATH|SCI
            },
            { is_stem: 1 },
            { is_stem: 0 }
        );

        // Create a map for lookup on omit-rate thresholds
        let threshold_map = new Map<string, number>();
        for (let row of config.spec_exclusion_threshold) {
            const key = `${row["is_stem"]};${row["test_response_type"]}`; // key: 0;MS
            threshold_map.set(key, Number(row["max_omit_rate"]));
        }

        // Add exclude flag based on omit-rate
        for (let row of result) {
            const is_stem = Number(row["is_stem"]);
            const component_slug = String(row["component_slug"]);
            const omit_rate = Number(row["omit_rate"]);

            if (config.is_field_test) {
                const key = `${is_stem};${component_slug}`;
                const threshold = threshold_map.get(key);

                row["exclude"] = threshold !== undefined && omit_rate >= threshold ? 1 : 0;
            } else {
                row["exclude"] = 0;
            }
        }

        return result
    }
}
