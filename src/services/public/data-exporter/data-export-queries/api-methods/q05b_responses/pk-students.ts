import { Application } from "@feathersjs/express";

import { IExportApiCallDef } from "../../types/type";

interface IConfig {
  tw_ids: number[],
  include_sample_assessments: boolean
}

export const SQL_05B_PRINCIPAL_KIT_STUDENTS:IExportApiCallDef = {
    requiredInputs: [],
    req: async (app: Application, config:IConfig) => {

      return await app
        .service("public/dist-admin/data-exporter/pk-extraction")
        .getPrincipalKitStudents(config.tw_ids, config.include_sample_assessments);
    }
}