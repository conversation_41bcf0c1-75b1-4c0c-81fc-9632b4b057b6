import { Application } from "@feathersjs/express";

import { IExportApiCallDef } from "../../types/type";

interface IConfig {}

export const SQL_05B_SPEC_EXCLUSION_THRESHOLD:IExportApiCallDef = {
    requiredInputs: [],
    req: async (app: Application, config:IConfig) => {

      // TODO: These should be put into a reporting profile instead of being hardcoded
      
      return [
        { is_stem: 0, test_response_type: "MS", max_omit_rate: 0.1 },
        { is_stem: 0, test_response_type: "NF", max_omit_rate: 0.1 },
        { is_stem: 1, test_response_type: "MS", max_omit_rate: 0.16 },
        { is_stem: 1, test_response_type: "NF", max_omit_rate: 0.67 }
      ];
    }
}