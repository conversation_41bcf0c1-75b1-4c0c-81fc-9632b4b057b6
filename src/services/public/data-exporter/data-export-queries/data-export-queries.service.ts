// Initializes the `public/data-exporter/data-export-queries` service on path `/public/data-exporter/data-export-queries`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { DataExportQueries } from './data-export-queries.class';
import hooks from './data-export-queries.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/data-exporter/data-export-queries': DataExportQueries & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/data-exporter/data-export-queries', new DataExportQueries(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/data-exporter/data-export-queries');

  service.hooks(hooks);
}
