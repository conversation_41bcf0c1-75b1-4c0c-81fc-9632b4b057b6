import { IExportQueryDef } from "../../types/type";

export const SQL_00_META_CODEBOOK_TABLE_FIELDS:IExportQueryDef = {
    requiredInputs: [
        'data_def_table_ids'
    ],
    queryGen: (config:{}) => `
           select ddtf.id
            , ddtf.table_slug
            , ddtf.slug
            , ddtf.caption
            , ddtf.type
            , ddtf.description
            , ddtf.is_nullable
            , ddtf.is_primary_key
            , ddtf.is_foreign_key
            , ddtf.is_foreign_derived
            , ddtf.fk_table_type
            , ddtf.fk_table_slug
            , ddtf.fk_field_slug
            , ddtf.fd_table_type
            , ddtf.fd_field_slug
        from data_def_table_fields ddtf
        join data_def_tables ddt
            on ddt.slug = ddtf.table_slug
        where ddtf.is_revoked = 0
        and ddt.id in (:data_def_table_ids);
    `
}
