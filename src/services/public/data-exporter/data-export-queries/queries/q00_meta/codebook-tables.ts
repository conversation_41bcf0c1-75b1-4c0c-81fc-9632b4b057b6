import { IExportQueryDef } from "../../types/type";

export interface ICodebookTableConfig {
  asset_slugs?: string[]
}

export const SQL_00_META_CODEBOOK_TABLES:IExportQueryDef = {
    requiredInputs: [
    ],
    optionalInputs: [
      'asset_slugs',
    ],
    queryGen: (config: ICodebookTableConfig) => `
        select ddt.id
             , ddt.slug
             , ddt.caption
             , ddt.life_cycle_group
             , ddt.description
        from data_def_tables ddt
        where ddt.type = 'dag'
          and ddt.is_revoked = 0
          ${ config.asset_slugs ? `and ddt.slug in (:asset_slugs)` : '' };
    `
}
