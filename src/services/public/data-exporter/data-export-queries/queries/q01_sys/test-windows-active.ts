import { IExportQueryDef } from "../../types/type";

export const SQL_01_SYS_TEST_WINDOWS_ACTIVE: IExportQueryDef = {
  requiredInputs: [],
  optionalInputs: [],
  // todo: can optionally take in a snapshot date parameter from the pipeline which it can use in place of now()
  queryGen: (config: any) => `
    SELECT
         tw.id tw_id
       , tw.type_slug AS tw_type_slug
       , tw.title
       , tw.date_start
       , tw.date_end
    FROM test_windows tw
    WHERE tw.date_end > NOW()
      AND tw.is_qa = 0
  `,
};
