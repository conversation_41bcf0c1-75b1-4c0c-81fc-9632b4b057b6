import { IExportQueryDef } from "../../types/type";

export const SQL_01_SYS_TEST_WINDOWS:IExportQueryDef = {
    requiredInputs: [
        'tw_ids'
    ],
    queryGen: (config:{}) => `
       select id test_window_id
           , tw.type_slug
           , tw.is_qa
           , tw.is_active
           , tw.is_field_test
           , tw.is_bg
           , tw.is_for_pasi
           , tw.date_start
           , tw.date_end
           , tw.window_code
           , tw.window_date_human
           , tw.PASI_school_year
           , tw.title
           , tw.next_tw_id
           , tw.is_duration_enforced
           , tw.hardstop_offset_h
           , tw.is_teacher_creation
           , tw.is_allow_tqer_live_override
       from test_windows tw 
       where tw.id in (:tw_ids)
   `
}
