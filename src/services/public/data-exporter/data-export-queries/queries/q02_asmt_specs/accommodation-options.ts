import { IExportQueryDef } from "../../types/type";

interface IAccommodationOptionsConfig {
}

export const SQL_02_ASMT_SPECS_ACCOMMODATION_OPTIONS:IExportQueryDef = {
    requiredInputs: [
        'tw_type_slugs',
    ],
    queryGen: (config:IAccommodationOptionsConfig) => `
        select acc.id acc_id
             , acc.foreign_id accommodation_number
             , acc.name accommodation_name
             , acc.accommodation_type value_data_type
             , acc.accommodation_slug acc_type_slug
             , acc.sortOrder sort_order
        from accommodations acc
        where acc.system_slug in (:tw_type_slugs)
            and acc.is_revoked  = 0
            and acc.foreign_id is not null
   `
}


