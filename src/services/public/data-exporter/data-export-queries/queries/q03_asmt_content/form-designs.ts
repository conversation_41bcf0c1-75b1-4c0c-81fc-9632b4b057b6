import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    focus_on_custom_assessments: boolean,
}

export const SQL_03_ASMT_CONTENT_FORM_DESIGNS:IExportQueryDef = {
    requiredInputs: [
        'twtar_ids'
    ],
    optionalInputs: [
        'focus_on_custom_assessments', // config only
    ],
    queryGen: (config:IQueryConfig) => `
        select twtar.id twtar_id
            , twtar.test_window_id
            , tw.window_code test_term
            , twtar.test_design_id
            , twtar.type_slug
            , ac.course_name_full
            , ac.course_code_foreign course_code
            , twtar.lang
            , twtar.form_code
            , twtt.foreign_component_code component_slug
            , twtar.is_secured
            , twtar.is_questionnaire
            , twtar.test_design_id
            , twtar.tqr_ovrd_td_id
        from test_window_td_alloc_rules twtar
        join test_windows tw
            on twtar.test_window_id = tw.id
        join test_window_td_types twtt
            on twtt.type_slug = twtar.type_slug
            and twtt.test_window_id is null
            and twtt.is_revoked = 0
        join assessment_courses ac
            on ac.course_code = twtt.course_code
        where twtar.id in (:twtar_ids)
            and twtar.is_custom = ${ config.focus_on_custom_assessments ? '1' : '0' }
    `
}
