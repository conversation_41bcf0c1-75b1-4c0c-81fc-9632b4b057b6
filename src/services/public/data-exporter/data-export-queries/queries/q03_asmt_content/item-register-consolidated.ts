import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_03_ASMT_CONTENT_ITEM_REGISTER_CONSOLIDATED:IExportQueryDef = {
    requiredInputs: [
        'twtar_ids'
    ],
    queryGen: (config:IQueryConfig) => `
        select twtar.id as twtar_id
             , tw.window_code test_term
             , twtar.type_slug
             , ac.course_name_full
             , ac.course_code
             , twtar.lang test_lang
             , twtar.form_code
             , twtt.foreign_component_code component_slug
             , tqr.id tqr_id
             , tqr.student_question item_nbr
             , tqr.question_id item_id
             , tqr.item_version_id
             , tqr.lang response_lang
             , tqr.concepts_code report_label_short -- will be updated later
             , tqr.question_label item_name
             , tqr.entry_order joined_with_item_nbr -- will be updated later
             , tqr.test_design_id
             , IFNULL(tqsi.is_human_scored, 0) is_human_scored
        from test_window_td_alloc_rules twtar
        join test_windows tw
            on twtar.test_window_id = tw.id
        join test_window_td_types twtt
            on twtt.type_slug = twtar.type_slug
            and twtt.test_window_id is null
            and twtt.is_revoked = 0
        left join assessment_courses ac
            on ac.course_code = twtt.course_code
        join test_question_register tqr
            on tqr.test_design_id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
        left join test_question_scoring_info tqsi
            on tqsi.id = tqr.tqsi_id
        where twtar.id in (:twtar_ids)
            and not (twtar.test_design_id = 0)
            and not (twtar.test_design_id is null)
    `
}
