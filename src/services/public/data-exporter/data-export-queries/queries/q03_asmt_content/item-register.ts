import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_03_ASMT_CONTENT_ITEM_REGISTER:IExportQueryDef = {
    requiredInputs: [
        'td_ids'
    ],
    queryGen: (config:IQueryConfig) => `
        select tqr.id tqr_id -- todo:pick columns, and also pull relevant mapping
             , tqr.question_id item_id
             , tqr.question_label item_name
             , tqr.lang
             , tqr.test_design_id
             , tqr.is_questionnaire
             , tqr.is_reading_passage
             , tqr.score_profile_id
             , tqr.score_points
             , IFNULL(tqsi.is_human_scored, 0) is_human_scored
             , tqr.expected_answer
             , tqr.student_question item_nbr
             , tqr.concepts_code report_label_short
        from test_question_register tqr
        left join test_question_scoring_info tqsi
            on tqsi.id = tqr.tqsi_id
        where tqr.test_design_id in (:td_ids)
        group by tqr.id
        order by is_questionnaire asc, is_reading_passage asc;
    `
}
