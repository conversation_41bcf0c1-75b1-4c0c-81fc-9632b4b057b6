import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_03_ASMT_CONTENT_ITEM_SCALE_REGISTER:IExportQueryDef = {
    requiredInputs: [
        'twtar_ids'
    ],
    queryGen: (config:IQueryConfig) => `
        select ac.course_name_full
             , tw.window_code test_term
             , tqr.question_id item_id
             , tqr.question_label item_name
             , tqr.student_question item_scale_nbr	
             , tqr.concepts_code report_label_short -- will be updated later
             , tqr.test_design_id
        from test_window_td_alloc_rules twtar
        join test_windows tw
            on twtar.test_window_id = tw.id
        join test_window_td_types twtt
            on twtt.type_slug = twtar.type_slug
            and twtt.test_window_id is null
            and twtt.is_revoked = 0
        join assessment_courses ac 
            on ac.course_code = twtt.course_code
        join test_question_register tqr
            on tqr.test_design_id = twtar.test_design_id
        where twtar.id in (:twtar_ids);
    `
}