import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_03_ASMT_CONTENT_TQR_GENERIC_PARAM_MAP:IExportQueryDef = {
    requiredInputs: [],
    queryGen: (config:IQueryConfig) => `
        SELECT 
          tqrgpm.id,
          tqrgpm.tqr_col,
          tqrgpm.param_type,
          tqrgpm.item_bank_code
        FROM
          test_question_register_generic_param_map tqrgpm
        WHERE 
          tqrgpm.is_revoked = 0
          and tqrgpm.item_bank_code is not null
    `
}
