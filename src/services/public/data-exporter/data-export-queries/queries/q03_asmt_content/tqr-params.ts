import { IExportQueryDef } from "../../types/type";

// Type definitions
type value = number | string | boolean | null | undefined
type IRow = {[key: string]: value}

interface IQueryConfig {
  tqr_generic_param_map: IRow[],
}

export const SQL_03_ASMT_CONTENT_TQR_PARAMS:IExportQueryDef = {
    requiredInputs: [],
    queryGen: (config:IQueryConfig) => {
      let cols = '';
      if (config.tqr_generic_param_map.length > 0) {
        cols = ',' + config.tqr_generic_param_map
          .map((row: IRow) => `${row.tqr_col} ${row.item_bank_code}`)
          .join(', ');
      }

      return `
        SELECT 
          tqr.id tqr_id
          ${ cols }
        FROM
          test_question_register tqr 
        WHERE
          tqr.id in (:tqr_ids)
    `
    }
}
