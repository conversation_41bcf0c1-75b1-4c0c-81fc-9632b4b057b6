import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
  test_window_ids: number[],
  days_back: number,
  focus_on_custom_assessments: boolean,
}


export const SQL_03B_AUTH_MONIT_FORM_ALLOCATIONS: IExportQueryDef = {
  requiredInputs: ['test_window_ids'],
  optionalInputs: [
    "days_back", 
    "auth_group_name",
    'focus_on_custom_assessments', // config only
  ],

  queryGen: (config: IQueryConfig) => `
    SELECT 
          twtarl.id AS log_id
        , twtarl.twtar_id
        , JSON_UNQUOTE(JSON_EXTRACT(twtarl.patches, '$.test_design_id')) AS patches__test_design_id
        , JSON_UNQUOTE(JSON_EXTRACT(twtarl.patches, '$.tqr_ovrd_td_id')) AS patches__tqr_ovrd_td_id
        , twtarl.patched_on
        , twtarl.patched_by_uid
        , twtarl.test_design_id AS new_test_design_id
        , twtar.test_design_id AS original_test_design_id
        , twtar.form_code
        , twtar.lang
        , twtar.type_slug
        , twtar.is_sample
        , twtar.is_questionnaire
        , twtar.is_marking_req
        , twtar.is_print
        , tw.id AS test_window_id
        , tw.type_slug AS test_window_type
        , tw.date_start
        , tw.date_end
        , tw.window_code
        , twtarl.patches
    FROM test_window_td_alloc_rules_log twtarl
    JOIN test_window_td_alloc_rules twtar 
      ON twtar.id = twtarl.twtar_id
    JOIN test_windows tw 
      ON tw.id = twtar.test_window_id
    WHERE (twtarl.patches LIKE '%test_design_id%'OR twtarl.patches LIKE '%tqr_ovrd_td_id%')
      AND twtar.test_window_id IN (:test_window_ids)
      AND twtar.is_custom = ${ config.focus_on_custom_assessments ? '1' : '0' }
      AND twtarl.patched_on > DATE_ADD(NOW(), INTERVAL -${config.days_back || 7} DAY)
      AND twtarl.patched_on < DATE_ADD(NOW(), INTERVAL 0 DAY)
    ORDER BY twtarl.id DESC
  `
};
