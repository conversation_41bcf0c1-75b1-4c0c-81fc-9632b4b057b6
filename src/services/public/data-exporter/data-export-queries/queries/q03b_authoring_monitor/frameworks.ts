import { IExportQueryDef } from "../../types/type";

export const SQL_03B_AUTH_MONIT_FRAMEWORKS: IExportQueryDef = {
  requiredInputs: [],
  optionalInputs: ["days_back"],
  queryGen: (config: any) => `
    SELECT 
       tqs.group_id AS auth_group_id,
       ag.name,
       ag.type_slug AS ag_type_slug,
       tqs.twtar_type_slug,
       tqsl.question_set_id,
       tqs.name,
       COUNT(tqsl.id) AS n_saves,
       MIN(tqsl.updated_on) AS min_tqsl_updated_on,
       MAX(tqsl.updated_on) AS max_tqsl_updated_on,
       GROUP_CONCAT(DISTINCT a.email) AS auth_emails
    FROM temp_question_set_log tqsl
    JOIN temp_question_set tqs
      ON tqs.id = tqsl.question_set_id
    LEFT JOIN authoring_groups ag
      ON ag.group_id = tqs.group_id
    JOIN auths a
      ON a.uid = tqsl.updated_by_uid
    WHERE tqsl.updated_on > DATE_ADD(NOW(), INTERVAL -${config.days_back || 7} DAY)
      AND tqsl.updated_on < DATE_ADD(NOW(), INTERVAL 0 DAY)
    GROUP BY tqsl.question_set_id
    ORDER BY ag.name, tqs.twtar_type_slug, tqs.name
  `
};
