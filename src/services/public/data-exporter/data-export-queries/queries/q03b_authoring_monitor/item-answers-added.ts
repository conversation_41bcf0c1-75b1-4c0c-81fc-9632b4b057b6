import { IExportQueryDef } from "../../types/type";

export const SQL_03B_AUTH_MONIT_ITEM_ANSWERS_ADDED: IExportQueryDef = {
  requiredInputs: [],
  optionalInputs: ["days_back"],
  queryGen: (config: any) => `
    SELECT *
    FROM test_question_expected_responses tqer
    WHERE tqer.created_on > DATE_ADD(NOW(), INTERVAL -${config.days_back || 7} DAY)
      AND tqer.created_on < DATE_ADD(NOW(), INTERVAL 0 DAY)
      AND tqer.is_from_admin = 0
  `
};
