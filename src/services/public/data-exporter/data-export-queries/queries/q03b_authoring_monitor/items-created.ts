import { IExportQueryDef } from "../../types/type";

export const SQL_03B_AUTH_MONIT_ITEMS_CREATED: IExportQueryDef = {
  requiredInputs: [],
  optionalInputs: ["days_back"], // default 7
  queryGen: (config: any) => `
    SELECT 
       tqs.group_id AS agid,
       ag.name AS ag_name,
       ag.type_slug AS ag_type_slug,
       tqs.twtar_type_slug,
       tqs.id AS item_set_id,
       tqs.name AS item_set_name,
       tq.id AS item_id,
       tq.question_label AS item_label,
       LENGTH(tq.config) AS item_config_len,
       tq.created_on
    FROM test_questions tq
    JOIN temp_question_set tqs
      ON tqs.id = tq.question_set_id
    LEFT JOIN authoring_groups ag
      ON ag.group_id = tqs.group_id
    LEFT JOIN auths a
      ON a.uid = tq.created_by_uid
    WHERE tq.created_on > DATE_ADD(NOW(), INTERVAL -${config.days_back || 7} DAY)
      AND tq.created_on < DATE_ADD(NOW(), INTERVAL 0 DAY)
  `
};
