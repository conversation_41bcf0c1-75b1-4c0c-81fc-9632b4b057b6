import { IExportQueryDef } from "../../types/type";

export const SQL_03B_AUTH_MONIT_ITEMS_MODIFIED: IExportQueryDef = {
  requiredInputs: [],
  optionalInputs: ["days_back"],
  queryGen: (config: any) => `
    SELECT 
       tqv.test_question_id,
       COUNT(tqv.id) AS n_saves,
       COUNT(DISTINCT tqv.created_by_uid) AS n_save_users
    FROM test_question_versions tqv
    WHERE tqv.created_on > DATE_ADD(NOW(), INTERVAL -${config.days_back || 7} DAY)
      AND tqv.created_on < DATE_ADD(NOW(), INTERVAL 0 DAY)
    GROUP BY tqv.test_question_id
  `
};
