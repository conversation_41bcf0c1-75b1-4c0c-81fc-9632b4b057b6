import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    test_window_ids: number[],
    days_back: number,
  }
  

export const SQL_03B_AUTH_MONIT_RECENTLY_READY_FOR_STRUCT_REVIEWS: IExportQueryDef = {
    requiredInputs: ['test_window_ids'],
    optionalInputs: [
      "days_back", 
      "auth_group_name",
    ],  
    queryGen: (config: any) => `
        SELECT
              twtar.test_window_id
            , JSON_EXTRACT(tw.title, '$.en') AS title_en
            , twtar.type_slug
            , twtar.is_active
            , ts.comment AS signoff_ministry
            , ts_vretta.comment AS signoff_vretta
        FROM test_windows tw
        JOIN test_window_td_alloc_rules twtar
            ON twtar.test_window_id = tw.id
             and twtar.is_custom = 0
            AND twtar.type_slug NOT LIKE '%DO_NOT_USE%'
            AND twtar.type_slug NOT LIKE '%ABED_DEMO%'
        JOIN auths a
            ON a.uid = twtar.td_assigned_by_uid
        JOIN twtdar_signoffs ts
            ON ts.twtar_id = twtar.id
            AND ts.signoff_slug = 'form_content_rev_4'
            AND ts.is_revoked = 0
        LEFT JOIN twtdar_signoffs ts_vretta
            ON ts_vretta.twtar_id = twtar.id
            AND ts_vretta.signoff_slug = 'form_design_keys_sys'
            AND ts_vretta.is_revoked = 0
        WHERE DATE(ts.created_on) >= DATE_ADD(NOW(), INTERVAL -${config.days_back || 14} DAY)
            AND twtar.test_window_id IN (:test_window_ids)
            AND ts_vretta.id IS NULL
        GROUP BY twtar.id
        ORDER BY ts.created_on DESC
  `
};
