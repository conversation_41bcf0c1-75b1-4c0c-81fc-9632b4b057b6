import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    include_sample_assessments: boolean,
}

export const SQL_04A_GROUPS_MARKER_USER_ROLES:IExportQueryDef = {
    requiredInputs: [
        'marking_window_id',
    ],
    queryGen: (config:IQueryConfig) => `
        select
          ur.uid,
          ur.role_type
        from
          marking_windows mw
        join user_roles ur on
          ur.group_id = mw.group_id
        where
          mw.id = :marking_window_id
    `
}