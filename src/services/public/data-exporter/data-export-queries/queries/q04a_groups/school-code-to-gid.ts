import { IExportQueryDef } from "../../types/type";

export const SQL_SCHL_CODE_TO_GID:IExportQueryDef = {
    requiredInputs: [
        's_codes',
    ],
    queryGen: (config:{}) => `
        SELECT s.id s_id 
            , s.group_id s_gid
            , s.foreign_id s_code
            , sd.foreign_id sd_code
        FROM schools s 
        JOIN school_districts sd 
            on sd.group_id  = s.schl_dist_group_id 
        where s.foreign_id IN (:s_codes)
    `
}