import { IExportQueryDef } from "../../types/type";

export const SQL_SCHOOL_SC_PLACHOLDER:IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
        's_gids',
    ],
    queryGen: (config:{}) => `
        select sc.schl_group_id s_gid 
            , tw.type_slug
            , sc.group_id sc_gid 
        from school_semesters ss 
        join test_windows tw 
            on tw.id = ss.test_window_id 
        join school_classes sc 
            on sc.semester_id = ss.id 
            and sc.is_placeholder = 1
        join u_groups ug 
            on sc.group_id = ug.id 
        where ss.test_window_id in (:tw_ids)
            and sc.schl_group_id in (:s_gids)
        group by sc.schl_group_id, tw.type_slug
    `
}