import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    include_sample_schools: boolean,
}

export const SQL_04A_GROUPS_SCHOOLS:IExportQueryDef = {
    requiredInputs: [
        's_ids',
        'tw_ids',
    ],
    queryGen: (config:IQueryConfig) => `
        select tsa.test_window_id
             , tsa.school_id
             , max(tsa.is_all_type_slugs) is_all_type_slugs
             , GROUP_CONCAT(tsa.type_slug) asmt_codes
        from twtdar_schools_allowed tsa 
        where tsa.school_id in (:s_ids)
          and tsa.test_window_id in (:tw_ids)
          and tsa.is_revoked = 0
          and tsa.is_date_override = 0 
        group by tsa.school_id
    `
}