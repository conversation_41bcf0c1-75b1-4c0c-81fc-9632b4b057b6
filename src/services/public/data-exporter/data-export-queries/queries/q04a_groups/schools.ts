import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    include_sample_schools: boolean,
}

export const SQL_04A_GROUPS_SCHOOLS:IExportQueryDef = {
    requiredInputs: [
        'include_sample_schools'
    ],
    queryGen: (config:IQueryConfig) => `
        select  s.id            s_id
              , s.group_id        s_gid
              , s.foreign_id      s_code
              , s.name            s_name
              , s.lang            s_lang
              , s.city            s_city
              , s.country         s_country
              , sd.id             sd_id
              , sd.group_id       sd_gid
              , sd.foreign_id     sd_code
              , sd.name           sd_name
              , sd.brd_lang       sd_lang
              , sd.is_sample      is_sample
              , s.is_sandbox      is_sandbox
        from schools s
        join school_districts sd 
            on sd.group_id = s.schl_dist_group_id
        ${ !config.include_sample_schools ? 'where sd.is_sample = 0' : ''}
    `
}