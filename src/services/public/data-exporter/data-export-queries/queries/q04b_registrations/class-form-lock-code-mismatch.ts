// PATCH: define SQL_05A_TA_FORMS_SLUG_MISMATCH

import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
  test_window_ids: number[];
  focus_on_custom_assessments: boolean,
}

export const SQL_04B_SC_CF_SLUG_MISMATCH: IExportQueryDef = {
  requiredInputs: [
    "test_window_ids"
  ],
  optionalInputs: [
      'focus_on_custom_assessments', // config only
  ],
  queryGen: (config: IQueryConfig) => `
    select sccf.id sccf_id
        , sccf.school_class_id
        , sc.name class_name
        , twtt.caption_short asmt_profile_short_name
        , sccf.created_on locked_on
        , sccf.twtdar_id 
        , sccf.type_slug asmt_code_locked
        , twtar.type_slug asmt_code_linked 
        , group_concat(DISTINCT a.email) teacher_emails
        , group_concat(DISTINCT CASE WHEN scts_old.slug = sccf.type_slug THEN scts_old.test_session_id END) old_slug_session_ids
        , group_concat(DISTINCT CASE WHEN scts_new.slug = twtar.type_slug THEN scts_new.test_session_id END) new_slug_session_ids
    FROM school_class_common_forms sccf 
    JOIN test_window_td_alloc_rules twtar 
        ON twtar.id = sccf.twtdar_id 
        and twtar.is_custom = ${ config.focus_on_custom_assessments ? '1' : '0' }
    JOIN school_classes sc 
        ON sc.id = sccf.school_class_id
    LEFT JOIN test_window_td_types twtt
        ON twtt.type_slug = twtar.type_slug
        AND twtt.test_window_id IS NULL
        AND twtt.is_revoked = 0
    LEFT JOIN user_roles ur 
        on ur.role_type not in ('schl_student')
        and ur.group_id = sc.group_id
        and ur.is_revoked = 0
    LEFT JOIN auths a 
        on a.uid = ur.uid 
    LEFT JOIN school_class_test_sessions scts_old
        ON scts_old.school_class_id = sc.id
        AND scts_old.slug = sccf.type_slug
    LEFT JOIN test_sessions ts_old
        ON ts_old.id = scts_old.test_session_id
        AND ts_old.is_closed = 0
        AND ts_old.is_cancelled = 0
    LEFT JOIN school_class_test_sessions scts_new
        ON scts_new.school_class_id = sc.id
        AND scts_new.slug = twtar.type_slug
    LEFT JOIN test_sessions ts_new
        ON ts_new.id = scts_new.test_session_id
        AND ts_new.is_closed = 0
        AND ts_new.is_cancelled = 0
    WHERE twtar.test_window_id IN (:test_window_ids)
      AND sccf.is_revoked = 0 
      AND sccf.type_slug != twtar.type_slug
    GROUP BY sccf.id
  `,
};

