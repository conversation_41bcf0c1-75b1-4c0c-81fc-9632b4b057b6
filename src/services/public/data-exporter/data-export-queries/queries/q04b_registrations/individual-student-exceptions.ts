import { IExportQueryDef } from "../../types/type";

export const SQL_04B_INDIVIDUAL_STUDENT_EXCEPTIONS: IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
    ],
    queryGen: (config:{}) => `
      select test_window_id
          , twtdar_id as twtar_id
          , uid
          , test_attempt_id
          , category
          , JSON_VALUE(action_config, "$.pct_score" RETURNING FLOAT) pct_score
          , action_config->>"$.outcome" outcome
      from tw_exceptions_students
      where category = "INDIVIDUAL"
        and test_window_id in (:tw_ids);
      ;
    `
}
