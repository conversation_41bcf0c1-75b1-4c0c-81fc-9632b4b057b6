import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    include_sample_schools: boolean,
}

export const SQL_04B_REGISTRATIONS_SCHOOL_GROUPINGS:IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
        's_ids',
    ],
    queryGen: (config:IQueryConfig) => `
        select sc.id                sc_id
             , sc.group_id          sc_gid
             , sc.schl_group_id     s_gid
             , sc.name              sc_name
             , sc.access_code       access_code
             , sc.is_active         is_active
             , sc.is_placeholder    is_placeholder
             , sc.semester_id       semester_id
             , sc.created_on        created_on
             , sc.deactivated_on    deactivated_on
        from school_semesters ss
        join school_classes sc
            on sc.semester_id = ss.id
        join schools s
            on sc.schl_group_id = s.group_id
        where ss.test_window_id in (:tw_ids)
            and s.id in (:s_ids)
    `
}