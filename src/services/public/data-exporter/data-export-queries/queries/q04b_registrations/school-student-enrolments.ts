import { IExportQueryDef } from "../../types/type";

export const SQL_SCHOOL_STUDENT_ENROLMENTS:IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
        's_gids',
    ],
    queryGen: (config:{}) => `
        select sc.schl_group_id s_gid 
            , ur.uid
            , tw.type_slug
            , sc.group_id sc_gid 
        from school_semesters ss 
        join test_windows tw 
            on tw.id = ss.test_window_id 
        join school_classes sc 
            on sc.semester_id = ss.id 
        join user_roles ur 
            on ur.group_id = sc.group_id 
        where ss.test_window_id in (:tw_ids)
            and sc.schl_group_id in (:s_gids)
            and ur.role_type  = 'schl_student'
        group by sc.schl_group_id, ur.uid
    `
}