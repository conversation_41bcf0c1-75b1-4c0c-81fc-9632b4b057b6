import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_04B_REGISTRATIONS_SCHOOL_CLASSES_GUEST:IExportQueryDef = {
    requiredInputs: [
        'sc_gids',
    ],
    queryGen: (config:IQueryConfig) => {
        return `
          select
            scg.id,
            scg.invig_sc_group_id,
            scg.guest_sc_group_id,
            scg.created_on
          from
            school_classes_guest scg
          where
            scg.invig_sc_group_id in (:sc_gids)
            and scg.is_revoked = 0;
        `
    }
}
