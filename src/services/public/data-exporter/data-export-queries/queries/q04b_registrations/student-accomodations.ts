import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_04B_REGISTRATIONS_STUDENT_ACCOMMODATIONS:IExportQueryDef = {
    requiredInputs: [
        'uids',
        'acc_ids',
    ],
    queryGen: (config:IQueryConfig) => {
        const um_gov_id_keys = ['StudentIdentificationNumber'] // should be defined in the environment
        return `
        select ua.id ua_id
             , ua.uid
             , ua.accommodation_id acc_id
             , ua.accommodation_value
        from users_accommodations ua
        where ua.accommodation_id in (:acc_ids)
            and ua.uid in (:uids);
        `
    }
}
