import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_04B_REGISTRATIONS_STUDENT_ADMIN_META_BOOKLETS:IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
        'uids',
    ],
    queryGen: (config:IQueryConfig) => `
        select tw.id test_window_id /* STUDENT_ADMIN_META_BOOKLETS */
             , tum.uid
             , tum.value booklet_index
             , tum.asmt_type_slug booklet_pasi_course_code
             , count(0) n_intersect
        from test_windows tw
        left join tw_user_metas tum
            on tum.test_window_id = tw.id
            and tum.key = 'BookletIndex'
        where tw.id in (:tw_ids)
        and tum.uid in (:uids)
        group by tum.uid
               , tum.asmt_type_slug
               , tw.id
    `
}
