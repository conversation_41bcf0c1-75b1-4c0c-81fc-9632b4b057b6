import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_04B_REGISTRATIONS_STUDENT_ADMIN_META:IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
        'uids',
    ],
    queryGen: (config:IQueryConfig) => `
        select tw.id test_window_id
            , tum.uid
            , tum.key_namespace
            , tum.key
            , tum.value
            , tum.asmt_type_slug
            , tum.meta
        from test_windows tw
        join school_semesters ss
            on ss.test_window_id = tw.id
        join school_classes sc
            on sc.semester_id = ss.id
        join user_roles ur
            on ur.group_id = sc.group_id
        join tw_user_metas tum
            on tum.uid = ur.uid
            and tum.test_window_id = ss.test_window_id
            and tum.\`key\` = 'BookletIndex'
        join schools s
            on sc.schl_group_id = s.group_id
        join school_districts sd
            on sd.group_id = s.schl_dist_group_id
        where tw.id in (:tw_ids)
          and tum.uid in (:uids)
    `
}
