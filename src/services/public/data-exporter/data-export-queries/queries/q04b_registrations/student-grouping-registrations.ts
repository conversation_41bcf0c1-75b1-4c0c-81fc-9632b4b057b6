import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_04B_REGISTRATIONS_STU_G_REGSTRATIONS:IExportQueryDef = {
    requiredInputs: [
        'sc_gids',
    ],
    queryGen: (config:IQueryConfig) => {
        return `
        select ur.id ur_id
             , ur.uid
             , ur.group_id sc_gid
             , ur.created_on
             , IF(ur.role_type = 'abed_nperson_student', 1, 0) is_walkin
             , ur.is_revoked
             , ur.revoked_on
        from user_roles ur
        where ur.group_id in (:sc_gids)
            and (ur.role_type = 'schl_student' or ur.role_type = 'abed_nperson_student')
        ;
        `
    }
}
