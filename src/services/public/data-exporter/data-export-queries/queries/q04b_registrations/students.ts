import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_04B_REGISTRATIONS_STUDENTS:IExportQueryDef = {
    requiredInputs: [
        'uids',
    ],
    queryGen: (config:IQueryConfig) => {
        const um_gov_id_keys = ['StudentIdentificationNumber'] // should be defined in the environment
        return `
            select u.id               uid
                , um_gov_id.value     stu_gov_id
                , u.first_name        first_name
                , u.last_name         last_name
                , u.is_PASI_student   is_sis_sync
                , um_dob.value        stu_dob
            from users u
            left join user_metas um_gov_id
                on um_gov_id.uid = u.id
                and um_gov_id.key in ('StudentIdentificationNumber')
            left join user_metas um_dob
                on um_dob.uid = u.id
                and um_dob.key = 'DateofBirth'
            where u.id in (:uids)
        `
    }
}
