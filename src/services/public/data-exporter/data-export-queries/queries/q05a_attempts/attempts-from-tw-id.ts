import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    tw_ids: number,
    include_sample_assessments: boolean,
    include_sample_schools: boolean,
    sc_id: number,
    s_id: number,
    sd_id: number,
    eys_only: boolean,
    twtar_type_slugs: string[],
    focus_on_custom_assessments: boolean,
}

export const SQL_05A_ATTEMPTS_FROM_TW_ID:IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
    ],
    optionalInputs: [
        'include_sample_assessments', // config only
        'include_sample_schools', // config only
        'sc_id', // config + param
        's_id', // config + param
        'sd_id', // config + param
        'eys_only', // config only
        'focus_on_custom_assessments', // config only
    ],
    queryGen: (config:IQueryConfig) => `
        select /*+ MAX_EXECUTION_TIME(300000) */
                ta.id attempt_id
              , ta.is_closed
              , ta.uid
              , sc.id sc_id
              , s.id s_id
              , sd.id sd_id
              , ts.id ts_id
              , twtar.id twtar_id
        from school_class_test_sessions scts
        join school_classes sc
            on sc.id = scts.school_class_id
        join test_sessions ts
            on ts.id = scts.test_session_id
            and ts.is_cancelled = 0
        join test_attempts ta
            on ta.test_session_id = ts.id
            and ta.is_invalid = 0
        join test_window_td_alloc_rules twtar
            on twtar.id = ta.twtdar_id
        join test_windows tw 
            on twtar.test_window_id = tw.id 
        join test_attempt_question_responses taqr
            on taqr.test_attempt_id = ta.id
        join schools s
            on s.group_id  = sc.schl_group_id
            and s.is_sandbox = 0
            -- and s.is_not_reported = 0
        join school_districts sd
            on sd.group_id = s.schl_dist_group_id
        where twtar.test_window_id in (:tw_ids)
            ${ config.sc_id ? 'and sc.id = :sc_id' : ''}
            ${ config.s_id ? 'and s.id = :s_id' : ''}
            ${ config.sd_id ? 'and sd.id = :sd_id' : ''}
            and twtar.is_custom = ${ config.focus_on_custom_assessments ? '1' : '0' }
            ${ config.include_sample_schools ? '' : 'and sd.is_sample = 0 ' }
            ${ config.include_sample_assessments ? '' : 'and (tw.is_classroom_assessment=1 or twtar.is_sample = 0)'}
            ${ config.eys_only ? `and twtar.type_slug LIKE '%EYS%'` : ''}
            ${ config.twtar_type_slugs && config.twtar_type_slugs.length > 0 ? 'and twtar.type_slug in (:twtar_type_slugs)' : '' }
        group by ta.id
        limit 500000
    `
}

export const SQL_05A_ATTEMPTS_FROM_TW_ID_TESTCENTRE:IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
    ],
    optionalInputs: [
    ],
    queryGen: (config:IQueryConfig) => `
        select /*+ MAX_EXECUTION_TIME(300000) */
              tw.id admin_window_id
            , ta.id attempt_id
            , JSON_EXTRACT(tw.title, '$.en') admin_window_name
            , cb.foreign_id TestingJID
            , cb.id jurisdiction_id
            , JSON_EXTRACT(cb.title, '$.en') jurisdiction_name
            , i.id test_center_id
            , i.name test_center_name
            , ts.id test_session_id
            , ta.uid CAECUID
            , u.first_name
            , twtar.id twtar_id
            , twtar.lang
            , twtar.type_slug asmt_code
            , twtar.form_code
            , twtar.is_print
            , ta.is_invalid is_attempt_invalidated
            , ts.is_cancelled is_session_cancelled
            , ts.is_closed is_session_closed
            , ta.started_on is_started
            , (CASE WHEN ta.started_on IS NULL THEN 0 ELSE 1 END) AS is_started
            , ta.started_on
            , tr.created_on results_computed_on
            , tr.results_released_on
        from certification_bodies cb
        join institutions i
            on i.jurisdiction_id = cb.id
        join test_sessions ts
            on ts.instit_group_id = i.group_id
        join test_attempts ta
            on ta.test_session_id = ts.id
        join users u
            on u.id = ta.uid
        join test_window_td_alloc_rules twtar
            on twtar.id = ta.twtdar_id
            and twtar.is_custom = 0
        join test_windows tw
            on tw.id = twtar.test_window_id
        left join test_reports tr
            on tr.test_attempt_id = ta.id
            and tr.is_invalid = 0
        where cb.is_sample = 0
            and ta.started_on is not null
            and twtar.test_window_id in (:tw_ids)
    `
}

