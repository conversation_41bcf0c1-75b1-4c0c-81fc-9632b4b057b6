import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    tw_ids: number,
    uids: number[],
    twtar_ids: number[],
}

export const SQL_05A_ATTEMPTS_FROM_UID:IExportQueryDef = {
    requiredInputs: [
        "uids", // from load_students
        "twtar_ids",    // from load_twtar
    ],
    optionalInputs: [
    ],
    queryGen: (config:IQueryConfig) => `
        select ta.id ta_id
            , ta.uid
            , ta.test_session_id
            , ta.lang
            , ta.test_form_id
            , ta.created_on
            , ta.started_on
            , ta.is_closed
            , ta.closed_on
            , ta.time_ext_m
            , ta.is_invalid
            , ta.last_updated_by_uid
            , ta.twtdar_id
        from test_attempts ta
        where ta.uid in (:uids)
          and ta.twtdar_id in (:twtar_ids)
    `
}
