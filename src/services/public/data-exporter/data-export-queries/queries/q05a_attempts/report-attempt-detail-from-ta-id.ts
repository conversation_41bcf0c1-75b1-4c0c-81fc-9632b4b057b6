
import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    include_sample_assessments: boolean,
}

export const SQL_05A_REPORT_ATTEMPTS_DETAIL_FROM_TA_ID:IExportQueryDef = {
    requiredInputs: [
        'ta_ids',
    ],
    queryGen: (config:IQueryConfig) => `
        select /*+ MAX_EXECUTION_TIME(300000) */ s.name s_name
            , s.foreign_id s_code
            , sd.foreign_id sd_code
            , sd.name sd_name
            , sc.name sc_name
            , sc.id sc_id
            , scts.slug assessment_code
            , twtar.form_code
            , ta.uid student_uid
            , ta.id attempt_id
            , um_sin.value student_gov_id
            , um_gender.value gender
            , u.first_name student_fname
            , u.last_name student_lname
            , DATE_FORMAT(ta.started_on, '%Y-%m-%d') started_on_date
        from school_class_test_sessions scts
        join school_classes sc
            on sc.id = scts.school_class_id
        join test_sessions ts
            on ts.id = scts.test_session_id
            and ts.is_cancelled = 0
        join test_attempts ta
            on ta.test_session_id = ts.id
            and ta.is_invalid = 0
        left join user_metas um_sin
            on um_sin.uid = ta.uid
            and um_sin.key = 'StudentIdentificationNumber' -- todo:WHITELABEL
        left join user_metas um_gender
            on um_gender.uid = ta.uid
            and um_gender.key = 'Gender' -- todo:WHITELABEL
        join test_window_td_alloc_rules twtar
            on twtar.id = ta.twtdar_id
        join schools s
            on s.group_id  = sc.schl_group_id
        join users u on u.id = ta.uid
        join school_districts sd
            on sd.group_id = s.schl_dist_group_id
        where ta.id IN (:ta_ids)
        group by ta.id
        limit 500000
    `
}
