import { IExportQueryDef } from "../../types/type";

interface IQueryConfig { }

export const SQL_05A_SCHOOL_CLASS_TEST_SESSION:IExportQueryDef = {
    requiredInputs: [
        'sc_ids',
        'twtar_type_slugs',
    ],
    queryGen: (config: IQueryConfig) => `
      select scts.id               scts_id
          , scts.school_class_id  sc_id
          , scts.test_session_id  ts_id
          , scts.slug             twtar_type_slug
          , s.name                school_name
          , s.foreign_id          school_code
          , sd.foreign_id         school_authority_code
          , sd.is_sample          is_sample_school
      from school_class_test_sessions scts
      join school_classes sc
        on sc.id = scts.school_class_id
      join schools s
        on s.group_id  = sc.schl_group_id
        and s.is_not_reported  = 0
      join school_districts sd
        on sd.group_id = s.schl_dist_group_id
      where scts.school_class_id in (:sc_ids)
          and scts.slug in (:twtar_type_slugs)
    `
}
