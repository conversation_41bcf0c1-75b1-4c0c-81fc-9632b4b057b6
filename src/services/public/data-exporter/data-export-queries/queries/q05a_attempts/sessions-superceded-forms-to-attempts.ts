import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
  ts_ids: number[]; 
  focus_on_custom_assessments: boolean,
}

export const SQL_05A_SESSIONS_SUPERCEDED_FORMS_TO_ATTEMPTS: IExportQueryDef = {
  requiredInputs: [
    "ts_ids", 
  ],
  optionalInputs: [
    'focus_on_custom_assessments', // config only
  ],
  queryGen: (config: IQueryConfig) => `
    SELECT 
          twtar.test_window_id
        , scts.test_session_id session_id
        , sccf.id sccf_id
        , sccf.school_class_id
        , twtar.type_slug asmt_code
        , tf_ta.test_design_id test_design_id_ta
        , tf_sccf.test_design_id test_design_id_sccf
        , twtar.test_design_id AS test_design_id_new
        , ta.test_form_id ta_tf_id 
        , count(distinct ta.id) n_attempts
        , CASE 
            WHEN ta.id is null THEN 'NO_INIT'
            WHEN ta.test_form_id = sccf.test_form_id THEN 'MATCH'
            ELSE 'MISMATCH'
          END AS form_match_status
    FROM test_window_td_alloc_rules twtar
    JOIN school_class_common_forms sccf 
      ON sccf.twtdar_id = twtar.id 
     AND sccf.is_revoked = 0
    JOIN school_class_test_sessions scts
      ON scts.school_class_id = sccf.school_class_id
     and scts.slug = twtar.type_slug
    JOIN test_sessions ts
      ON ts.id = scts.test_session_id
    JOIN test_forms tf_sccf
      ON tf_sccf.id = sccf.test_form_id
    left JOIN test_attempts ta
      ON ta.test_session_id = ts.id
    left JOIN test_forms tf_ta
      ON tf_ta.id = ta.test_form_id
    WHERE twtar.is_custom = ${ config.focus_on_custom_assessments ? '1' : '0' }
    ${ config.ts_ids.length ? `AND scts.test_session_id IN (:ts_ids)` : `AND 1=0` }
    group by sccf.id, scts.test_session_id, ta.test_form_id
    order by scts.test_session_id
  `
}; 