import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
  test_window_ids: number[]; 
  exclude_ts_ids: number[]; 
  include_ts_ids: number[]; 
  focus_on_custom_assessments: boolean,
}

export const SQL_05A_SESSIONS_SUPERCEDED_FORMS: IExportQueryDef = {
  requiredInputs: [
    "test_window_ids", 
    "exclude_ts_ids", 
    "include_ts_ids"
  ],
  optionalInputs: [
    'focus_on_custom_assessments', // config only
  ],

  queryGen: (config: IQueryConfig) => `
    SELECT 
          twtar.test_window_id
        , scts.test_session_id session_id
        , twtar.type_slug asmt_code
        , tf.test_design_id
        , twtar.test_design_id AS test_design_id_new
        , sc.name class_name
        , ts.date_time_start as ts_date_time_start
        , sccf.school_class_id
        , sccf.created_on AS sccf_created_on
        , sd.name sa_name 
        , sd.foreign_id sa_code 
        , s.name s_name 
        , s.foreign_id s_code 
    FROM test_window_td_alloc_rules twtar
    JOIN school_class_common_forms sccf 
      ON sccf.twtdar_id = twtar.id 
     AND sccf.is_revoked = 0
    JOIN school_classes sc
        on sc.id = sccf.school_class_id
    JOIN schools s
        ON s.group_id = sc.schl_group_id
    JOIN school_districts sd
        ON sd.group_id = s.schl_dist_group_id
        AND sd.is_sample = 0
    LEFT JOIN school_class_test_sessions scts
        ON sc.id = scts.school_class_id
        and scts.slug = twtar.type_slug
    LEFT JOIN test_sessions ts
        ON ts.id = scts.test_session_id
    JOIN test_forms tf 
      ON tf.id = sccf.test_form_id
    WHERE twtar.test_window_id IN (:test_window_ids)
      AND twtar.is_custom = ${ config.focus_on_custom_assessments ? '1' : '0' }
      AND (
        twtar.test_design_id != tf.test_design_id
        ${ config.include_ts_ids.length ? `OR scts.test_session_id IN (:include_ts_ids)` : '' }
      )
      ${ config.exclude_ts_ids.length ? `AND NOT EXISTS (
        SELECT 1
        FROM school_class_test_sessions scts_exclude
        WHERE scts_exclude.test_session_id IN (:exclude_ts_ids)
            AND scts_exclude.school_class_id = sccf.school_class_id
      )`: ''}
  `
};
