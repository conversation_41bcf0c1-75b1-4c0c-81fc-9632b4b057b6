import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
  test_window_ids: number[]; // or number, depending on usage
  focus_on_custom_assessments: boolean,
}

export const SQL_05A_TA_FORMS_SUPERCEDED: IExportQueryDef = {
  requiredInputs: [
    "test_window_ids"
  ],
  optionalInputs: [
    'focus_on_custom_assessments', // config only
  ],

  queryGen: (config: IQueryConfig) => `
    SELECT ta.id AS ta_id
         , ts.is_closed
         , ts.is_cancelled
         , ta.started_on IS NOT NULL AS is_started
         , sc_ta.started_on IS NOT NULL AS is_any_in_class_started
         , (scts.slug = twtar.type_slug) AS is_ts_twtar_slug_match
         , ta.test_session_id AS ts_id
         , twtar.id twtar_id
         , twtar.type_slug
         , sd.is_sample AS is_sample_school
         , s.name AS school_name
         , ts.date_time_start
         , tf.test_design_id
         , twtar.test_design_id
         , twtar.test_window_id
    FROM test_window_td_alloc_rules twtar
    JOIN test_attempts ta 
        ON twtar.id = ta.twtdar_id
       AND ta.is_invalid = 0
       AND ta.uid > 0
    JOIN test_sessions ts
        ON ts.id = ta.test_session_id
    LEFT JOIN test_attempts sc_ta
        ON sc_ta.test_session_id = ts.id 
        AND sc_ta.started_on is not null 
    JOIN test_forms tf
        ON tf.id = ta.test_form_id
    JOIN test_designs td
        ON td.id = twtar.test_design_id
    JOIN school_class_test_sessions scts
        ON scts.test_session_id = ta.test_session_id
    JOIN school_classes sc
        ON sc.id = scts.school_class_id
    JOIN schools s
        ON s.group_id = sc.schl_group_id
    JOIN school_districts sd
        ON sd.group_id = s.schl_dist_group_id
        AND sd.is_sample = 0
    WHERE twtar.test_window_id IN (:test_window_ids)
      AND twtar.is_sample = 0
      AND tf.test_design_id != twtar.test_design_id
      AND twtar.is_custom = ${ config.focus_on_custom_assessments ? '1' : '0' }
    GROUP BY ta.id 
    ORDER BY twtar.type_slug, s.name
  `,
};
