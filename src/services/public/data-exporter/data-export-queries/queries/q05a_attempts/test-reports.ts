import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_05A_ATTEMPTS_TEST_REPORTS:IExportQueryDef = {
    requiredInputs: [
        'ta_ids',
    ],
    queryGen: (config:IQueryConfig) => `
        select /*+ MAX_EXECUTION_TIME(300000) */
              tr.uid
              , tr.test_attempt_id
              , tr.is_successful
              , tr.is_no_show
              , tr.institution_name
              , tr.num_questions_answered
              , tr.num_questions_total
              , tr.created_on
              , tr.test_session_date_time_start
              , tr.is_invalid
              , tr.slug
              , tr.result_code
              , tr.results_released_on
              , tr.raw_score
        from test_reports tr
        where tr.test_attempt_id in (:ta_ids);
    `
}