import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_05A_TS_DETAIL_FROM_TA_ID:IExportQueryDef = {
    requiredInputs: [
        'ta_ids',
    ],
    queryGen: (config:IQueryConfig) => `
        select  /*+ MAX_EXECUTION_TIME(300000) */ ts.id ts_id
        , ts.name_custom ts_name
        , ts.date_time_start ts_start
        , count(distinct ta.uid) as num_students
        , count(case when ta.started_on is not null then 1 else null end) num_students_started
        , count(case when (ta.started_on is not null and ta.is_closed = 1) then 1 else null end) num_students_submitted
        , ts.is_closed ts_is_closed
        , ur.uid teacher_uid
        , u.contact_email teacher_email
        , concat(u.first_name, " ", u.last_name) as teacher_name
        from 
        test_attempts ta 
        join test_sessions ts 
          on ts.id = ta.test_session_id
        join school_class_test_sessions scts  
          on scts.test_session_id = ts.id
        join school_classes sc 
          on sc.id = scts.school_class_id 
        left join user_roles ur 
          on ur.group_id = sc.group_id 
          and ur.role_type = 'schl_teacher'
          and ur.is_revoked = 0
        left join users u
          on u.id = ur.uid
        where ta.id in (:ta_ids)
        group by ts.id     
    `
}