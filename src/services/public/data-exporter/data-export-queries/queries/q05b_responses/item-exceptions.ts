import { IExportQueryDef } from "../../types/type";

export const SQL_05B_ITEM_EXCEPTIONS: IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
    ],
    queryGen: (config:{}) => `
      select tei.is_score_override
          , tei.item_id
          , tei.item_label
          , tei.lang
          , tei.match_response_value
          , tei.score_override
          , tei.test_window_id
          , tei.is_nr_override
          , tei.nr_override
      from tw_exceptions_items tei
      where tei.test_window_id in (:tw_ids)
          and tei.revoked_by_uid is null
      ;
    `
}
