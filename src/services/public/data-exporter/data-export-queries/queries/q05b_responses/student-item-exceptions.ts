import { IExportQueryDef } from "../../types/type";

export const SQL_05B_STUDENT_ITEM_EXCEPTIONS: IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
    ],
    queryGen: (config:{}) => `
        select tesi.item_id
            , tesi.uid
            , tesi.match_response_value
            , tesi.new_response_value
            , tesi.new_score
            , tesi.is_score_override
            , tesi.is_response_value_override
        from tw_exceptions_student_items tesi
        where tesi.test_window_id in (:tw_ids)
            and tesi.is_revoked = 0
        ;
    `
}
