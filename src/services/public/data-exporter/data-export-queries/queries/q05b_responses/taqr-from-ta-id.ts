import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_05B_TAQR_FROM_TA_ID:IExportQueryDef = {
    requiredInputs: [
        'ta_ids',
    ],
    queryGen: (config:IQueryConfig) => `
        select /*+ MAX_EXECUTION_TIME(300000) */
              taqr.id as taqr_id
            , taqr.test_attempt_id ta_id
            , taqr.test_question_id item_id
            , twtar.lang
            , taqr.score
            , taqr.weight
            , taqr.is_nr
            , taqr.is_invalid
        from test_attempt_question_responses taqr
        join test_attempts ta
            on ta.id = taqr.test_attempt_id
        join test_window_td_alloc_rules twtar
            on twtar.id = ta.twtdar_id
        where taqr.test_attempt_id IN (:ta_ids)
    `
}
