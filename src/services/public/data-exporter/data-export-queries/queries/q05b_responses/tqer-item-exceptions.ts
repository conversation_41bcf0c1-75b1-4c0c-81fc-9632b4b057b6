import { IExportQueryDef } from "../../types/type";

export const SQL_05B_TQER_ITEM_EXCEPTIONS: IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
        'item_ids',
    ],
    queryGen: (config:{}) => `
    select tqer.item_id
        , tqer.lang
        , tqer.formatted_response as match_response_value
        , tqer.score_override
    from test_question_expected_responses tqer
    where tqer.item_id in (:item_ids)
        and tqer.is_item_score_exceptions = 1
        and tqer.is_revoked = 0
      ;
    `
}

