

import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
  focus_on_custom_assessments: boolean,
}

export const SQL_06_MARKING_WINDOW_ASMT_CODE: IExportQueryDef = {
    requiredInputs: [
        'mw_ids',
    ],
    optionalInputs: [
      'focus_on_custom_assessments', // config only
    ],
    queryGen: (config:IQueryConfig) => `
        select mwi.marking_window_id
            , twtar.type_slug twtar_type_slug
            , count(distinct mwi.item_id) twtar_n_marked_items
            , count(distinct mwi.id) twtar_n_marked_scales
        from marking_windows mw 
        join marking_window_items mwi
            on mwi.marking_window_id = mw.id
        join marking_window_test_window mwtw
            on mwtw.marking_window_id = mwi.marking_window_id 
        join test_window_td_alloc_rules twtar 
            on twtar.test_window_id = mwtw.test_window_id 
            and twtar.lang = mw.lang -- todo this should be a more explicittwtar type slug mapping
            and twtar.is_custom = ${ config.focus_on_custom_assessments ? '1' : '0' }
        join test_question_register tqr 
            on tqr.question_id  = mwi.item_id 
            and tqr.test_design_id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
        where mw.id in (:mw_ids)
        group by mw.id, mwi.marking_window_id, twtar.type_slug
        order by mw.id, mwi.marking_window_id, twtar.type_slug
    `
}
