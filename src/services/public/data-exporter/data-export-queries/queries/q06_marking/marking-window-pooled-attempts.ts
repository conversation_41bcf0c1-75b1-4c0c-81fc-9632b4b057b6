

import { IExportQueryDef } from "../../types/type";

// deliberately not scoping to uid or attempt id... this will help us identify "extra" student attempts that have been pooled and to see which ones they are 

export const SQL_06_MARKING_WINDOW_POOLED_ATTEMPTS: IExportQueryDef = {
    requiredInputs: [
        'mw_ids',
    ],
    queryGen: (config:{}) => `
        select mwi.marking_window_id
            , mtc.ta_id
            , count(distinct mtc.taqr_id) n_items
            , count(mwi.id) n_scales
        from marking_windows mw 
        join marking_window_items mwi
            on mwi.marking_window_id = mw.id
        join marking_taqr_cache mtc 
            on mtc.mwi_id = mwi.id 
        where mw.id in (:mw_ids)
        group by mwi.marking_window_id
            , mtc.ta_id
    `
}
