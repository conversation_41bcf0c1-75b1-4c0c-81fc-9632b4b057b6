import { IExportQueryDef } from "../../types/type";

export const SQL_06_MARKING_WINDOWS: IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
    ],
    queryGen: (config:{}) => `
        select mwtw.test_window_id
            , mwtw.marking_window_id 
            , mwtw.created_on mwtw_created_on
            , mwtw.is_material mwtw_is_material
            , mw.name mw_name 
            , mw.start_on mw_start_on
            , mw.end_on mw_end_on 
            , mw.is_scoring_disabled  mw_is_scoring_disabled
            , mw.is_active mw_is_active 
            , mw.is_archived mw_is_archived
            , mw.is_hidden_for_scorers mw_is_hidden_for_scorers
            , mw.is_rescore mw_is_rescore
            , mw.is_scan_reassign_allowed 
            , mw.is_scorer_annotation_allowed 
            , mw.is_test_centre mw_is_test_centre
        --      , mw.meta_config 
        --      , mw.twtdar_ids 
        from marking_window_test_window mwtw 
        join marking_windows mw 
            on mw.id = mwtw.marking_window_id
        where mwtw.test_window_id in (:tw_ids)
            and mwtw.is_removed = 0 
            and mwtw.is_sample = 0 
    `
}
