import { IExportQueryDef } from "../../types/type";

// Caveat: This checks which questions are expected based on the latest changes in test design, not which questions were in the student's form (which is not stored in db...)
interface IQueryConfig {
    tw_ids: number,
}

export const SQL_07_SCAN_COUNT_BY_DATE_FROM_TA_ID:IExportQueryDef = {
    requiredInputs: [
        'ta_ids',
    ],
    optionalInputs: [
    ],
    queryGen: (config:IQueryConfig) => `
      SELECT /*+ MAX_EXECUTION_TIME(300000) */
          DATE_FORMAT(DATE(tasr.uploaded_on), '%Y-%m-%d') AS upload_date,
          COUNT(*) AS n_scans
      FROM test_attempts ta
      JOIN test_attempt_question_responses taqr
          ON taqr.test_attempt_id = ta.id
      JOIN test_attempt_scan_responses tasr
          ON tasr.taqr_id = taqr.id
      WHERE ta.id in (:ta_ids)
      GROUP BY DATE(tasr.uploaded_on)
      ORDER BY upload_date;
    `
}

