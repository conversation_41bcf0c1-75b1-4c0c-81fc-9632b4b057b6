import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    tw_ids: number,
}

export const SQL_07_SCAN_INFO_FROM_TA_ID:IExportQueryDef = {
    requiredInputs: [
        'ta_ids',
    ],
    optionalInputs: [
    ],
    queryGen: (config:IQueryConfig) => `
      select /*+ MAX_EXECUTION_TIME(300000) */
              scan_detail.*
            , (num_scans_required - num_scans_uploaded) as num_scans_missing
            , CASE WHEN num_scans_uploaded > 0 THEN 1 ELSE 0 END as is_any_uploaded
            , CASE WHEN (num_scans_required - num_scans_uploaded) > 0 THEN 1 ELSE 0 END as is_any_scan_missing
      from (
        select ta.id as attempt_id
             , CASE WHEN ta.is_submitted = 1 THEN count(distinct tqsi.item_id) ELSE  count(distinct tasr.id) END as num_scans_required
             , count(distinct tasr.id) as num_scans_uploaded
             , min(uploaded_on) as first_scan_uploaded_on
             , max(uploaded_on) as last_scan_uploaded_on
        from test_attempts ta
        join test_window_td_alloc_rules twtdar
          on ta.twtdar_id = twtdar.id
        join test_question_register tqr
          on tqr.test_design_id = twtdar.test_design_id
        join test_question_scoring_info tqsi
          on tqsi.id = tqr.tqsi_id
          and tqsi.lang = twtdar.lang
          and tqsi.is_paper_response = 1
        left join test_attempt_question_responses taqr
          on taqr.test_attempt_id = ta.id
          and taqr.test_question_id = tqsi.item_id
          and taqr.is_invalid = 0
        left join test_attempt_scan_responses tasr
          on tasr.taqr_id = taqr.id
          and tasr.is_discarded = 0
        where ta.id in (:ta_ids)
        group by ta.id
      ) as scan_detail;
    `
}

