import { Application } from "@feathersjs/express";
import { serviceQueryChunked } from "./query-chunked";
import { serviceQuerySimple } from "./query-simple";
import { serviceApiRequestChunked } from "./api-request-chunked";
import { serviceApiRequestSimple } from "./api-request-simple";
import { serviceApiMethodSimple } from "./api-method-simple";
import { serviceTransformJoin, IServiceTransformJoin as ISTJ } from "./transforms";
import {
  serviceTransformFilterCol,
  serviceTransformDropDuplicates,
  serviceTransformGroupBy,
  serviceTransformMapCol,
  serviceTransformRestrictCols,
  serviceTransformSortBy,
  serviceTransformAggregate,
  serviceTransformNormedHistogram,
  serviceTransformReplaceWhere,
  serviceTransformRenameCols,
  serviceTransformConcat,
  serviceTransformFillNa,
  serviceTransformCutoffs,
  serviceTransformApplyCutscores,
  serviceTransformSetWhere,
  serviceTransformPointBiserial,
  serviceTransformPivot,
  serviceTransformWrStats,
  serviceTransformCastCol,
} from "./transforms";

export type IServiceTransformJoin = ISTJ;

export const dataExportQueryServices:{[key:string]: (app:Application, config:any) => Promise<any[]>} = {
    'query-chunked': serviceQueryChunked,
    'query': serviceQuerySimple,
}

export const dataExportApiServices: {[key:string]: (app:Application, config:any) => Promise<any[]>} = {
    'api-request': serviceApiRequestSimple,
    'api-request-chunked': serviceApiRequestChunked,
    'api-method': serviceApiMethodSimple,
}

export const dataExportApiMethodServices: {[key:string]: (app:Application, config:any) => Promise<any[]>} = {
  'api-method': serviceApiMethodSimple,
}

export const dataExportTransformServices: {[key:string]: (app:Application, config:any, sequenceData: any) => Promise<any[]>} = {
  'join': serviceTransformJoin,
  'filter-col': serviceTransformFilterCol,
  'drop-duplicates': serviceTransformDropDuplicates,
  'group-by': serviceTransformGroupBy,
  'map-col': serviceTransformMapCol,
  'restrict-cols': serviceTransformRestrictCols,
  'sort-by': serviceTransformSortBy,
  'aggregate': serviceTransformAggregate,
  'normed-histogram': serviceTransformNormedHistogram,
  'replace-where': serviceTransformReplaceWhere,
  'rename-cols': serviceTransformRenameCols,
  'concat': serviceTransformConcat,
  'fill-na': serviceTransformFillNa,
  'cutoffs': serviceTransformCutoffs,
  'apply-cutscores': serviceTransformApplyCutscores,
  'set-where': serviceTransformSetWhere,
  'point-biserial': serviceTransformPointBiserial,
  'pivot': serviceTransformPivot,
  'wr-stats': serviceTransformWrStats,
  'cast-col': serviceTransformCastCol,
}
