import { Application } from "@feathersjs/express";
import { Errors } from '../../../../../errors/general';
import { DataJobLogger } from "./logger";
import { ExporterApiCallRef } from "../api-methods/_index";

interface IServiceApiRequestSimple {
    slug?:string,
    endpoint?: string,
    method?: string,
    props: {
        [name:string]: string | number | string[] | number[]
    },
    data: {
        [name:string]: string | number | string[] | number[]
    },
    logger: DataJobLogger,
}

export const serviceApiMethodSimple = async (app:Application, config:IServiceApiRequestSimple) => {

    // TODO: will need some way to separate the props and the data (the records to send)
    const {
        slug,
        endpoint,
        method,
        props,
        data,
    } = config;

    const timeStart = +(new Date())

    if (endpoint == 'public/dist-admin/data-exporter/analysis' && method == 'computeCutscores') {
        // TODO: hardcoding the parameter name is not preferred
        const { scores_by_domain } = data;
        let records = await app.service('public/dist-admin/data-exporter/analysis').computeCutscores(scores_by_domain, props);
        const timeDiff =  ((+(new Date()) - timeStart)/1000).toFixed(3)
        console.log('api-method-simple ::', method, endpoint, 'end', timeDiff)
        return records
    }
    
    if (slug && ExporterApiCallRef[slug]){
        return ExporterApiCallRef[slug].req(app, data)
    }

    const timeDiff =  ((+(new Date()) - timeStart)/1000).toFixed(3)
    console.log('api-method-simple ::', method, endpoint, 'end', timeDiff)

    throw new Errors.Forbidden(`exporter api method ${method} not recognized`)
}
