import { Application } from "@feathersjs/express";
import { Errors } from '../../../../../errors/general';
import { DataJobLogger } from "./logger";

const supported_methods = ['POST', 'create'];

interface IServiceApiRequestChunked {
    endpoint: string,
    method: string,
    chunkedParam:string,
    chunkSize: number,
    props: {
        [name:string]: string | number | string[] | number[]
    },
    data: {
        [name:string]: string | number | string[] | number[]
    },
    logger: DataJobLogger,
}

export const serviceApiRequestChunked = async (app:Application, config:IServiceApiRequestChunked) => {

    const {
        endpoint,
        method,
        chunkedParam,
        chunkSize,
        props,
        data,
    } = config;

    // TODO: input validation (ensure chunkedParam is property of data, etc.)
    const keys = <string[] | number[]> data[chunkedParam];

    const records = []; // response
    const timeStart = +(new Date());

    if (!supported_methods.includes(method)) {
        console.log(`Method "${method}" not supported use one of ${supported_methods}`);
        throw new Errors.Unprocessable(`exporter api method ${method} not recognized`);
    }

    let n = Math.ceil(keys.length / chunkSize);
    console.log('api-request-chunked :: started', method, endpoint, 'with', n, 'chunks');
    for (let i=0; i < n; i++){
        const timeDiff =  ((+(new Date()) - timeStart)/1000).toFixed(3);
        console.log('api-request-chunked ::', method, endpoint, i, '/', n, timeDiff);
        const chunkKeys = keys.slice(i*chunkSize, (i+1)*chunkSize);
        const chunkData = {
            ... data,
            [chunkedParam]: chunkKeys,
        }
        if (chunkKeys.length){
            const recordsChunk = await app.service(endpoint).create(chunkData, props);
            records.push(... recordsChunk)
        }
    }

    return records

}
