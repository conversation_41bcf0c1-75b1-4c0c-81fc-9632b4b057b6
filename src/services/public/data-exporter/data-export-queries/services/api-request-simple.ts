import { Application } from "@feathersjs/express";
import { Errors } from '../../../../../errors/general';
import { DataJobLogger } from "./logger";

interface IServiceApiRequestSimple {
    endpoint: string,
    method: string,
    props: {
        [name:string]: string | number | string[] | number[] | object
    },
    data: {
        [name:string]: string | number | string[] | number[]
    },
    logger: DataJobLogger,
}

const SCORING_EXTRACT_ENDPOINT = 'public/scor-lead/window-stats';

export const serviceApiRequestSimple = async (app:Application, config:IServiceApiRequestSimple) => {

    const {
        endpoint,
        method,
        props,
        data,
    } = config;

    // TODO
    if (endpoint == SCORING_EXTRACT_ENDPOINT) {
        props.query = {marking_window_id: data.marking_window_id};
    }

    const timeStart = +(new Date())

    let records;
    switch(method) {
        case "GET":
        case "find":
            records = await app.service(endpoint).find(props);
            break;
        case "POST":
        case "create":
            records = await app.service(endpoint).create(data, props);
            break;
        default:
            throw new Errors.Unprocessable(`exporter api method ${method} not recognized`)
    }

    const timeDiff =  ((+(new Date()) - timeStart)/1000).toFixed(3)
    console.log('api-request-simple ::', method, endpoint, 'end', timeDiff)

    return records
}
