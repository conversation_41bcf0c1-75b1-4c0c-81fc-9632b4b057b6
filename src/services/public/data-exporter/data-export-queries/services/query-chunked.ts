import { Application } from "@feathersjs/express";
import { dbRawReadReporting } from "../../../../../util/db-raw";
import { DataJobLogger } from "./logger";

interface IServiceQueryChunked { 
    queryName:string, 
    query:string, 
    chunkedParam:string, 
    makeDistinct?:boolean,
    props:{
        [name:string]: string | number | string[] | number[]
    }, 
    logger: DataJobLogger,
    chunkSize:number
}

export const serviceQueryChunked = async (app:Application, config:IServiceQueryChunked) => {

    const {
        queryName,
        query,
        chunkedParam,
        props,
        chunkSize,
        makeDistinct,
    } = config;
    
    let keys: string[] | number[] = <string[] | number[]> props[chunkedParam];
    if (makeDistinct) {
        // @ts-expect-error : added because Set does not like the union type
        keys = [... new Set(keys)];
    }

    const records = []; // response
    const timeStart = +(new Date());

    let n = Math.ceil(keys.length / chunkSize);
    for (let i=0; i < n; i++){
        const timeDiff =  ((+(new Date()) - timeStart)/1000).toFixed(3)
        console.log('query-chunked ::', queryName, i, '/', n, timeDiff)
        const chunkKeys = keys.slice(i*chunkSize, (i+1)*chunkSize);
        const chunkProps = {
            ... props, 
            [chunkedParam]: chunkKeys,
        }
        if (chunkKeys.length){
            const recordsChunk = await dbRawReadReporting(app, chunkProps, query);
            records.push(... recordsChunk)
        }
    }

    return records

}