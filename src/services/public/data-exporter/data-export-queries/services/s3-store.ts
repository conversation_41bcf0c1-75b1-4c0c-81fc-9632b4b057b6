import { Application } from "@feathersjs/express";
import { IActiveJob } from "../types/type";

export class DataJobS3Store {
    constructor(
        private app: Application,
        private job: IActiveJob,
    )
    {

    }

    // data frames
    getDatrame(slug:string){
        
    }
    getDatrameChunked(slug:string, data:any[]){

    }
    postDatrame(slug:string){
        
    }
    postDatrameChunked(slug:string, data:any[]){

    }

    // packages
    getPackage(slug:string){
        
    }
    postPackage(slug:string, data:any ){
        
    }
}