import { IExportTransformDef } from "../types/type";

/* q01_sys */
// import { SQL_01_SYS_TEST_WINDOWS } from "./q01_sys/test-windows";


interface IExporterTransformerRef {
    [key:string]: IExportTransformDef | any
}

export const ExporterTrasnformRef:IExporterTransformerRef = {
    'filter-col': (datafame:any[]) => { return datafame },
    'group-by': (datafame:any[]) => { return datafame },
    'merge': (datafame:any[]) => { return datafame },
    'map-col': (datafame:any[]) => { return datafame },
    'restrict-cols': (datafame:any[]) => { return datafame },
    'asset-path-choice': (datafame:any[]) => { return datafame }, // use boolean value to select an asset path
}