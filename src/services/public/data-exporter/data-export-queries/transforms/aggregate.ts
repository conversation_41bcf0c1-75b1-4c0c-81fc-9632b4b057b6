/** Aggregation algorithm for the API-DAG
 */
import { aggregators } from './aggregators';

// Type definitions
type value = number | string | any
type IRow = {[key: string]: value}

/** 
 * aggregate transform definition 
 */
export function aggregate(df_input: IRow[], agg: IRow[]) {

  if (df_input.length === 0) {
    return [];
  }

  const result: IRow = {};

  // Calculating aggregations
  agg.forEach(agg_entry => {
    const { col_new, agg_type, col_target } = agg_entry;

    // Extract target column values
    const values = df_input.map(row => row[col_target] as number | string | boolean);

    // Perform the aggregation
    switch (agg_type) {
      case "sum":
        result[col_new] = aggregators.sum(values as number[]);
        break;
      case "mean":
        result[col_new] = aggregators.mean(values as number[]);
        break;
      case "nunique":
        result[col_new] = aggregators.nunique(values);
        break;
      case "list_unique":
        result[col_new] = aggregators.list_unique(values);
        break;
      case "count":
        result[col_new] = values.length;
        break;
      case "min":
        result[col_new] = aggregators.min(values as number[]);
        break;
      case "max":
        result[col_new] = aggregators.max(values as number[]);
        break;
      case "variance":
        result[col_new] = aggregators.variance(values as number[]);
        break;
      case "stdev":
        result[col_new] = aggregators.stdDev(values as number[]);
        break;
      case "minDate":
        result[col_new] = aggregators.minDate(values as string[]);
        break;
      case "maxDate":
        result[col_new] = aggregators.maxDate(values as string[]);
        break;
      default:
        throw new Error(`Unsupported aggregation type: ${agg_type}`);
    }
  });

  return [result];
}