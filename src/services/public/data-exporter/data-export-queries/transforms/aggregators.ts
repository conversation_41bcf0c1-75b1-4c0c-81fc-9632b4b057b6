/** Aggregate functions for the API-DAG
 */

// Export aggregator functions
// TODO:
// - [ ] need a way to validate types of input/output
// - [ ] number transforms should filter out nulls/nans
export const aggregators = {
  sum,
  mean,
  nunique,
  list_unique,
  min,
  max,
  variance,
  stdDev,
  quantile,
  concat,
  minDate,
  maxDate
}

/**
 * Aggregate function for computing sum of all values in an array
 */
function sum(values: number[]): number {
  return values.reduce((acc, num) => acc + num, 0)
}

/**
 * Aggregate function for computing mean of all values in an array
 */
function mean(values: number[]): number {
  const total = values.reduce((acc, num) => acc + num, 0)
  return total / values.length
}

/**
 * Aggregate function for computing number of unique elements
 */
function nunique(values: any[]): number {
  return new Set(values).size
}

/**
 * Aggregate function for getting all unique elements
 */
function list_unique(values: any[]): any[] {
  return Array.from(new Set(values));
}

/**
 * Aggregate function for computing minimum
 */
function min(values: number[]): number {
  return Math.min(...values)
}

/**
 * Aggregate function for computing maximum
 */
function max(values: number[]): number {
  return Math.max(...values)
}

/**
 * Aggregate function for computing variance (dof = 1)
 */
function variance(values: number[]): number | null {
  // Input Validation
  if (values.length < 2) {
    return null
  }

  const meanValue = mean(values);
  const squaredDiffs = values.map(num => Math.pow(num - meanValue, 2));
  return sum(squaredDiffs) / (values.length - 1);
}

/**
 * Aggregate function for computing reduced bias standard deviation of all elements in an array
 *
 *  Note: This is the common "unbiased" estimator, but still technically has bias (https://en.wikipedia.org/wiki/Unbiased_estimation_of_standard_deviation)
 */
function stdDev(values: number[]): number | null {
  let v = variance(values);
  if (v === null) {
    return null
  }
  return Math.sqrt(v);
}

/**
 * Aggregate function for computing quantiles
 */
function quantile(values: number[], q: number) {
  const sorted = [...values].sort((a, b) => a - b);
  const pos = (sorted.length - 1) * q;
  const base = Math.floor(pos);
  const rest = pos - base;
  if (sorted[base + 1] !== undefined) {
    return sorted[base] + rest * (sorted[base + 1] - sorted[base]);
  } else {
    return sorted[base];
  }
}

function concat(values: any[]): string {
  return values.map(v => String(v)).join(';');
}


/**
 * Aggregate function to find the earliest date
 */
function minDate(values: string[]): string {
  const minDate = values
  .filter(d => (new Date(d).getTime()))
  .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())?.[0];
  return minDate ?? null;
}

/**
 * Aggregate function to find the latest date
 */
function maxDate(values: string[]): string {
  const maxDate = values
  .filter(d => (new Date(d).getTime()))
  .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())?.[0];
  return maxDate ?? null;
}
