/**
 * @module ApplyCutscores
 *
 * Apply cutscores of the form
 *
 * { "column": "column_name", "cuts": [ { "label": "label", "cutoff": 0.5, "inclusive": true }  ] }
 *
 */

// Type definitions
type Value = number | string | boolean | null
type IRow = {[key: string]: Value}

/*
 * Apply cutscores (as received from the trfm_cutoffs, or other source)
 *
 */
export function applyCutscores( df_input: IRow[], groupKey: string[], scoreColumn: string, cuts: any[], labelColumn: string): IRow[] {

  let result: IRow[] = [];

  for (let row of df_input) {
    let record = {...row};
    let score = record[scoreColumn];

    // NOTE: null scores do not get a label
    if (score === null || score === undefined) {
      record[labelColumn] = null;
      continue
    }

    let cutSubset = cuts.filter(cut => isKeyMatch(cut, groupKey, record));
    // Ensure the cuts are in order from lowest to highest
    cutSubset.sort((a, b) => a.cutoff - b.cutoff);

    for (let cutDef of cutSubset) {
      let { label, cutoff, inclusive } = cutDef;
      if (!inclusive && score < cutoff) {
        record[labelColumn] = label;
        break;
      }
      else if (inclusive && score <= cutoff) {
        record[labelColumn] = label;
        break;
      }
    }
    // If no cutScore matches, then set to null
    if (record[labelColumn] === undefined) {
      record[labelColumn] = null;
    }
    result.push(record);
  }

  return result;
}

function isKeyMatch(recordA: any, groupKeys: string[], recordB: IRow): boolean {
  for (let key of groupKeys) {
    if (recordB[key] !== recordA[key]) {
      return false;
    }
  }
  return true;
}
