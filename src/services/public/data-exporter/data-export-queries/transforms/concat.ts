/**
 * @module Concat
 *
 * Concatenates a list of dataframes
 */

// Type definitions
type Value = number | string | boolean | null
type IRow = {[key: string]: Value}

/*
 * Concatenate a list of dataframes (ignoring schema inconsistency)
 *
 * TODO: possible confirm same schema or fill nulls
 *
 */
export function concatDataFrames( df_inputs: IRow[][] ): IRow[] {
  return Array.prototype.concat(...df_inputs)
}
