/**
 * @module Correlations
 *
 * "Transform" to compute the rpb and crpb correlations for items
 *
 */

import { aggregators } from './aggregators';

// Type definitions
type Value = number | string | boolean | null
type IRow = {[key: string]: Value}


/** Computes RPB and CRPB of items within a test
 *
 * Reference: https://www.notion.so/vretta/Psychometric-Calculation-80b51d48ae98489f8615be1d9552dfbe?pvs=4
 *
 * NOTE: Does not filter out non-responses. Those can be filtered in the input responses if required.
 * **WARNING:** the input dataframe must only be for a single test (asmt_code, form_code)
 *
 * Input schema
 * - asmt_code
 * - form_code
 * - test_design_id
 * - item_id
 * - is_correct (1 for correct, 0 for incorrect)
 * - total_score
 * - score_max
 *
 * Output schema:
 * - asmt_code
 * - form_code
 * - test_design_id
 * - item_id
 * - rpb
 * - crpb
 */
export function pointBiserial(item_responses: IRow[], keep_cols: string[] | undefined): IRow[] {
  // TODO: group by keep_cols (probably rename) so that the asset will work if not already partitioned
  if (!keep_cols) {
    keep_cols = ['asmt_code', 'form_code', 'test_design_id'];
  }
  let result: IRow[] = [];
  // Get unique item_ids
  const item_ids = [...new Set(item_responses.map(r => r.item_id))];
  // loop over item_ids
  for (const item_id of item_ids) {
    const item_responses_filtered = item_responses.filter(r => r.item_id == item_id);
    // Base row
    let row: IRow = {item_id: item_id};
    for (const col of keep_cols) {
      row[col] = item_responses_filtered[0][col];
    }
    const score_max = <number> item_responses_filtered[0].score_max;
    const scores = item_responses_filtered.map(r => <number> r.total_score);
    const adj_scores = item_responses_filtered.map(r => !r.is_correct ? <number> r.total_score : <number> r.total_score - score_max );
    const stdev_score = aggregators.stdDev(scores);
    const adj_stdev_score = aggregators.stdDev(adj_scores);
    const n_total = item_responses_filtered.length;
    const n_correct = item_responses_filtered.filter(r => r.is_correct).length;
    const n_incorrect = n_total - n_correct;
    const mean_correct = aggregators.mean(item_responses_filtered.filter(r => r.is_correct).map( (r: IRow) => <number> r.total_score));
    const mean_incorrect = aggregators.mean(item_responses_filtered.filter(r => !r.is_correct).map( (r: IRow) => <number> r.total_score));

    const p = n_correct / n_total;
    let rpb: number | null  = null;
    let crpb: number | null  = null;
    let reliabilityIndex: number | null  = null;
    if (n_total != 0 && stdev_score !== null && adj_stdev_score !== null && stdev_score != 0 && score_max !== null) {
      // NOTE: this denominator is n*(n-1) because stdev is derived from unbiased sample variance
      const diff_factor = Math.sqrt( n_correct * n_incorrect / (n_total * (n_total - 1)) );
      rpb = (mean_correct - mean_incorrect) / stdev_score * diff_factor;
      crpb = (mean_correct - mean_incorrect - score_max) / adj_stdev_score * diff_factor;
      reliabilityIndex = rpb * Math.sqrt( p * (1-p ));
    }
    row.rpb = rpb;
    row.crpb = crpb;
    row.p = n_correct / n_total;
    row.iri = reliabilityIndex;
    result.push(row);
  }
  return result;
}
