/**
 * @module Cutoffs
 *
 * Calculates cutoff values
 */

// Type definitions
type value = number | string | boolean | null
type IRow = {[key: string]: value}

type CutoffDef = {
  label: string;
  percentile: number;
  inclusive: boolean;
}

type CutScore = {
  cutoff: number;
  label: string;
  inclusive: boolean;
}
interface CutScoreRow extends CutScore {
  [key: string]: value
}

/*
 * Calculate the cutoff points for a dataframe
 *
 */
export function cutoffs(df_input: IRow[], group_by: string[], col_target: string, cutoffDefs: CutoffDef[]) {

  const output: CutScoreRow[] = [];

  // Group the data
  const groupedData = groupBy(df_input, group_by);

  for (const groupKey in groupedData) {
    const group = groupedData[groupKey];
    const targetValues = group.map(row => row[col_target] as number).filter(v => v !== undefined);

    // Calculate cutoff points
    for (let { label, percentile, inclusive } of cutoffDefs) {
      const cutoff = calculatePercentile(targetValues, percentile);

      // Create record
      const groupValues: { [key: string]: value } = {};
      group_by.forEach(key => {
        groupValues[key] = group[0][key];
      });

    output.push({
      ...groupValues,
      cutoff: cutoff,
      label,
      inclusive,
    });
    }
  }

  return output;
}

function groupBy(data: IRow[], group_by: string[]): { [key: string]: IRow[] } {
  const groups: { [key: string]: IRow[] } = {};
  for (const row of data) {
    const key = group_by.map(col => row[col]).join('|');
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(row);
  }
  return groups;
}

function calculatePercentile(data: number[], percentile: number): number {
  const index = Math.ceil((percentile / 100) * data.length) - 1;
  return data.sort((a, b) => a - b)[Math.max(0, index)];
}

