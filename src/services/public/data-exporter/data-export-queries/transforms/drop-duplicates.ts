/** Drop duplicates algorithm for the API-DAG
 */

// Type definitions
type value = number | string | boolean
type IRow = {[key: string]: value}

/*
 * drop-duplicates transform definition
 */
export function drop_duplicates(df_input: IRow[], subset: string[] | undefined) {
  let result = [];

  if (df_input.length === 0) {
    return []
  }

  // TODO: I don't like having the default option of using all columns
  if (!subset || subset.length === 0) {
    subset = Object.keys(df_input[0]);
  }

  const seen = new Set();
  for (const row of df_input) {
    const key = hash_fields(row, subset);
    if (!seen.has(key)) {
      seen.add(key);
      result.push(row);
    }
  }
  return result;
}

function hash_fields(row: IRow, subset: string[]) {
  return subset.map(k => String(row[k])).join('|');
}
