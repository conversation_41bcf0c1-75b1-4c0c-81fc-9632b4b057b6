/**
 * @module FillNa
 *
 * fill null or undefined values
 */

// Type definitions
type Value = number | string | boolean | null
type IRow = {[key: string]: Value}

/*
 * Fill null or undefined values
 *
 */
export function fillNa( df_input: IRow[], column: string, value: Value): IRow[] {
  let result: IRow[] = [];
  for (let row of df_input) {
    let tmp = {...row};
    if (tmp[column] === null || tmp[column] === undefined) {
      tmp[column] = value;
    }
    result.push(tmp);
  }
  return result;
}

export function fillNaInplace( df_input: IRow[], column: string, value: Value): IRow[] {
  let result: IRow[] = [];
  for (let row of df_input) {
    let tmp = row;
    if (tmp[column] === null || tmp[column] === undefined) {
      tmp[column] = value;
    }
    result.push(tmp);
  }
  return result;
}

export function fillNaMulti( df_input: IRow[], values: {[key: string]: Value}): IRow[] {
  let result: IRow[] = [];
  for (let row of df_input) {
    let tmp = {...row};
    for (let [column, value] of Object.entries(values)) {
      if (tmp[column] === null || tmp[column] === undefined) {
        tmp[column] = value;
      }
    }
    result.push(tmp);
  }
  return result;
}

export function fillNaMultiInplace( df_input: IRow[], values: {[key: string]: Value}): IRow[] {
  let result: IRow[] = [];
  for (let row of df_input) {
    for (let [column, value] of Object.entries(values)) {
      if (row[column] === null || row[column] === undefined) {
        row[column] = value;
      }
    }
    result.push(row);
  }
  return result;
}
