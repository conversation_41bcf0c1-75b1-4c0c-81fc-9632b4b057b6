/** Column filters algorithm for the API-DAG
 */

// Type definitions
type value = number | string | boolean | null
type IRow = {[key: string]: value}

/*
 * Filter column transform definition
 *
 * All filters **keep** the record if the comparison is TRUE
 *
 */
export function filter_col(df_input: IRow[], column: string, comparison: string, value?: any): IRow[] {

  // Input validation
  if (df_input.length > 0 && !df_input[0].hasOwnProperty(column)) {
    // throw exception if column is not present in the dataframe
    // TODO: Confirm implementation: Do we skip (and return the dataframe without filtering) or throw an exception here
    throw new Error(`Column "${column}" not found in the DataFrame`);
  }

  switch (comparison) {

    case "in":
      return df_input.filter(row => value.includes(''+row[column]));
    case "not-in":
      return df_input.filter(row => !value.includes(''+row[column]));

    case "equals":
      return df_input.filter(row => row[column] === value);

    case "not-equal":
      return df_input.filter(row => row[column] !== value);

    case "greater-than":
      return df_input.filter(row => {
        // handling possible null values while comparison
        const cell_val = row[column];
        return cell_val != null && cell_val > value;
    });

    case "greater-than-equal":
      return df_input.filter(row => {
        // handling possible null values while comparison
        const cell_val = row[column];
        return cell_val != null && cell_val >= value;
    });

    case "less-than":
      return df_input.filter(row => {
        // handling possible null values while comparison
        const cell_val = row[column];
        return cell_val != null && cell_val < value;
    });

    case "less-than-equal":
      return df_input.filter(row => {
        // handling possible null values while comparison
        const cell_val = row[column];
        return cell_val != null && cell_val <= value;
    });

    case "non-null":
      return df_input.filter(row => row[column] !== null && row[column] !== undefined);

    case "null":
      return df_input.filter(row => row[column] === null || row[column] === undefined);

    case "is-empty-string":
      // TODO: this needs unit tests
      return df_input.filter(row => row[column] === undefined || row[column] === null || String(row[column]).trim() === '');

    case "not-empty-string":
      // TODO: this needs unit tests
      return df_input.filter(row => !!row[column] && String(row[column]).trim() !== '');

    default:
      // Default to throwing an exception for unsupported comparison
      // TODO: Confirm implementation for default case
      throw new Error(`Unsupported comparison: ${comparison}`);
  }
}
