/** Group by algorithm for the API-DAG
 */

import { aggregators } from './aggregators';

// Type definitions
type Value = number | string | any
type IRow = {[key: string]: Value}

/**
 * group-by transform definition
 */
export function group_by(df_input: IRow[], columns: string[], agg: IRow[]) {
  // Performing grouping
  const groups: { [key: string]: { key: Value[], rows: IRow[] } } = {};

  // Creating Groups {{{
  df_input.forEach(row => {
    // Create a key from the values in columns {{{
    // Using array here to preserve type
    const group_key_arr = columns.map(col => row[col]);

    // Creating a string to use as a key for the map
    const group_key = group_key_arr.join('|')
    // }}}

    // Add the row to the corresponding group
    if (!groups[group_key]) {
        groups[group_key] = { key: group_key_arr, rows: []};
    }
    groups[group_key].rows.push(row);
  });
  // }}}

  const result: IRow[] = [];

  // Iterating over each group and computing aggregations
  for (const group_key in groups) {
    const group_rows = groups[group_key].rows;

    const result_row: IRow = {};

    // Adding col-value pairs from the group in the result
    columns.forEach((col, index) => {
      result_row[col] = groups[group_key].key[index];
    })

    // Calculating aggregations
    agg.forEach(agg_entry => {
      const {col_new, agg_type, col_target} = agg_entry;

      const values = group_rows.map(row => row[col_target] as number | string | boolean);

      /* TODO: this is incompatible with the quantile definition Somya added for marking reports
      const aggregator = aggregators[agg_type];
      if (!aggregator) {
        throw new Error(`Unsupported aggregation type: ${agg_type}`);
      }
      result_row[col_new] = aggregator(values as number[]);
      */

      switch(agg_type) {
        case "sum":
          result_row[col_new] = aggregators.sum(values as number[]);
          break;
        case "mean":
          result_row[col_new] = aggregators.mean(values as number[]);
          break;
        case "nunique":
          result_row[col_new] = aggregators.nunique(values);
          break;
        case "list_unique":
          result_row[col_new] = aggregators.list_unique(values);
          break;
        case "count":
          result_row[col_new] = values.length;
          break;
        case "min":
          result_row[col_new] = aggregators.min(values as number[]);
          break;
        case "max":
          result_row[col_new] = aggregators.max(values as number[]);
          break;
        case "variance":
          result_row[col_new] = aggregators.variance(values as number[]);
          break;
        case "stdev":
          result_row[col_new] = aggregators.stdDev(values as number[]);
          break;
        case "quantile-17_5":
          result_row[col_new] = aggregators.quantile(values as number[], 0.175);
          break;
        case "quantile-82_5":
          result_row[col_new] = aggregators.quantile(values as number[], 0.825);
          break;
        case "concat":
          result_row[col_new] = aggregators.concat(values);
          break;
        case "minDate":
          result_row[col_new] = aggregators.minDate(values as string[]);
          break;
        case "maxDate":
          result_row[col_new] = aggregators.maxDate(values as string[]);
          break;
        case "first":
          result_row[col_new] = values[0];
          break;
        default:
          throw new Error(`Unsupported aggregation type: ${agg_type}`);
      }
    });

    result.push(result_row)
  }
  return result;
}

