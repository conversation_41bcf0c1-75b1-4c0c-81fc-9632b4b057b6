/** Map columns algorithm for the API-DAG
 */

// Type definitions
type value = number | string | boolean | null
type IRow = {[key: string]: value}

/*
 * Map column transform definition
 */
export function map_col(df_input: IRow[], col_output: string, source_cols: string[], operation: string, value_true: value, value_false: value) {

  // Input validation
  const op = operations[operation];
  if (!op) {
    throw new Error(`Unsupported operation: ${operation}`);
  }
  if (op.arity > 0 && op.arity !== source_cols.length) {
    throw new Error(`Operation ${operation} requires ${op.arity} source columns, but ${source_cols.length} were provided.`);
  }


  // TODO: extend it to extract from list for dynamic list size
  const col_source_1 = source_cols[0];
  const col_source_2 = source_cols[1];

  return df_input.map((row) => {
    const vals = source_cols.map(col => row[col]);
    const val1 = row[col_source_1];
    const val2 = row[col_source_2];

    let result: value;
    switch (operation) {
      case "convert-bool":
        result = convertBool(val1, value_true, value_false);
        break;
      case "to-number":
        result = convertNum(val1);
        break;
      case "equal":
        result = equal(val1, val2, value_true, value_false);
        break;
      case "not-equal":
        result = notEqual(val1, val2, value_true, value_false);
        break;
      case "greater-than":
        result = greaterThan(val1, val2, value_true, value_false);
        break;
      case "less-than":
        result = lessThan(val1, val2, value_true, value_false);
        break;
      case "greater-than-equal":
        result = greaterThanEqual(val1, val2, value_true, value_false);
        break;
      case "less-than-equal":
        result = lessThanEqual(val1, val2, value_true, value_false);
        break;
      case "divide":
        result = divide(val1, val2);
        break;
      case "percentage":
        result = percentage(val1, val2);
        break;
      case "concat":
        result = concatenate(vals, {delimiter: ";"});
        break;
      case "add":
        result = add(val1, val2);
        break;
      case "subtract":
        result = subtract(val1, val2);
        break;
      case "dp-std-err":
        result = stdErrDP(vals);
        break;
      default:
        throw new Error(`Unsupported operation: ${operation}`);
    }

    // Add the result to the new column and return the updated row
    return {
      ...row,
      [col_output]: result,
    };
  });
}

type OpFuncBool  = (val1: value, value_true: value, value_false: value) => value
type OpFuncConvert  = (val1: value) => value
type OpFuncCond  = (val1: value, val2: value, value_true: value, value_false: value) => value
type OpFuncConcat = (vals: value[], delimiter?: string) => string
type OpFuncGeneral = (values: value[], opts?: any) => value

type OpFunc = OpFuncBool | OpFuncConvert | OpFuncCond | OpFuncConcat | OpFuncGeneral;

export const operations : {[key: string]: {op: OpFunc, arity: number}} = {
  "convert-bool": { op: convertBool, arity: 1 },
  "to-number": { op: convertNum, arity: 1 },
  "equal": { op: equal, arity: 2 },
  "not-equal": { op: notEqual, arity: 2},
  "greater-than": { op: greaterThan, arity: 2},
  "less-than": { op: lessThan, arity: 2},
  "greater-than-equal": { op: greaterThanEqual, arity: 2},
  "less-than-equal": { op: lessThanEqual, arity: 2},
  "percentage": { op: percentage, arity: 2},
  "divide": { op: divide, arity: 2},
  "concat": { op: concatenate, arity: -1},
  "add": { op: add, arity: 2},
  "subtract": { op: subtract, arity: 2},
  "dp-std-err": { op: stdErrDP, arity: 4},
}

function convertBool(val: value, value_true: value, value_false: value) {
  if (typeof val === "boolean") {
    return val ? value_true : value_false;
  }
  return null;
}

function convertNum(val: value) {
  return Number(val);
}

function equal(val1: value, val2: value, value_true: value, value_false: value) {
  return val1 === val2 ? value_true: value_false;
}

function notEqual(val1: value, val2: value, value_true: value, value_false: value) {
  return val1 !== val2 ? value_true: value_false;
}

function greaterThan(val1: value, val2: value, value_true: value, value_false: value) {
  return typeof val1 === "number" && typeof val2 === "number" && val1 > val2 ? value_true : value_false;
}

function lessThan(val1: value, val2: value, value_true: value, value_false: value) {
  return typeof val1 === "number" && typeof val2 === "number" && val1 < val2 ? value_true : value_false;
}

function greaterThanEqual(val1: value, val2: value, value_true: value, value_false: value) {
  return typeof val1 === "number" && typeof val2 === "number" && val1 >= val2 ? value_true : value_false;
}

function lessThanEqual(val1: value, val2: value, value_true: value, value_false: value) {
  return typeof val1 === "number" && typeof val2 === "number" && val1 <= val2 ? value_true : value_false;
}

function divide(val1: value, val2: value) {
  return typeof val1 === "number" && typeof val2 === "number" ? (val1 / val2) : null;
}

function percentage(val1: value, val2: value) {
  return typeof val1 === "number" && typeof val2 === "number" ? (val1 / val2) * 100 : null;
}

function concatenate(vals: value[], opts: any) {
  let delimiter = opts.delimiter ?? ";";
  return vals.join(delimiter);
}

function add(val1: value, val2: value) {
  return typeof val1 === "number" && typeof val2 === "number" ? val1 + val2 : null;
}

function subtract(val1: value, val2: value) {
  return typeof val1 === "number" && typeof val2 === "number" ? val1 - val2 : null;
}

/** Standard error of the Discriminating Power (difference of proportions)
*
* @see https://www.notion.so/vretta/Psychometric-Calculation-80b51d48ae98489f8615be1d9552dfbe?pvs=4#252b3e96bbdf49fa8f928d999839f199
*
* Values are p_high, N_high, p_low, N_low.
* All 4 values must be present and numeric, otherwise returns null.
*
*/
function stdErrDP(values: value[]): number | null {
  if (values.length < 4) {
    return null
  }
  for (let v of values) {
    if (typeof v !== "number") {
      return null
    }
  }
  const [p_high, n_high, p_low, n_low] = <number[]> values;
  return Math.sqrt((p_high * (1 - p_high)) / n_high + (p_low * (1 - p_low)) / n_low);
}
