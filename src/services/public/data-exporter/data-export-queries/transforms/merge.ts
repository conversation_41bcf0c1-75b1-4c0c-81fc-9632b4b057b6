/** Merge algorithms for the API-DAG
 */

type value = number | string | boolean
type IRow = {[key: string]: value}
type IRowNullable = {[key: string]: value | null}

// TODO: perform benchmarks for appropriate thresholds
const MAX_HASHED_LENGTH = 100; // if one or the other is below this, then a hash-join is good
const MAX_NESTED_LENGTH = 1000; // if both are below this, a nested loop is acceptible

/*
 * Inner Join method
 *
 * Will select an appropriate algorithm, but currently only sort-merge join is implemented.
 *
 * TODO: support multiple fields in the "on" property
 *
 */
export function inner_join(leftIn: IRow[], rightIn: IRow[], left_on: string[], right_on?: string[]) {
  /* TODO: use the hashed joins if one of the tables is small
  if (leftIn.length < MAX_HASHED_LENGTH) {
    return _hashed_inner_join(leftIn, rightIn, left_on, right_on, true);
  } else if (rightIn.length < MAX_HASHED_LENGTH) {
    return _hashed_inner_join(leftIn, rightIn, left_on, right_on, false);
  } else if (leftIn.length < MAX_NESTED_LENGTH && rightIn.length < MAX_NESTED_LENGTH) {
    return _nested_inner_join(leftIn, rightIn, left_on, right_on);
  }
  */
  if (!right_on) {
    right_on = left_on;
  }
  return _sortmerge_inner_join(leftIn, rightIn, left_on, right_on);
}

export function left_join(leftIn: IRowNullable[], rightIn: IRowNullable[], left_on: string[], right_on?: string[]) {
  // TODO: use the hashed joins if one of the tables is small
  if (!right_on) {
    right_on = left_on;
  }
  return _sortmerge_left_join(leftIn, rightIn, left_on, right_on);
}

/**
  * Hash-based inner join algorithm
  */
function _hashed_inner_join(left: IRow[], right: IRow[], left_on: string, right_on: string, hashLeft: boolean) {
    // TODO:
}

/**
  * Nested loop inner join algorithm
  */
function _nested_inner_join(left: IRow[], right: IRow[], left_on: string, right_on: string) {
    // TODO:
}

/**
  * MergeJoin algorithm
  *
  * Must be used on sorted data
  *
  * Reference: https://en.wikipedia.org/wiki/Sort-merge_join
  *
  *
  */
function _sortmerge_inner_join(left: IRow[], right: IRow[], left_on: string[], right_on: string[]) {
  // NOTE: make an index sorted on the key, rather than worry about mutating the input
  // we should probably drop all null values from the index
  const lindex: [number, string][] = left.map((r: IRow, i: number) => [i, extractKey(r, left_on)]);
  const rindex: [number, string][] = right.map((r: IRow, i: number) => [i, extractKey(r, right_on)]);

  lindex.sort(compKey);
  rindex.sort(compKey);

  // The merge operation
  const results: IRowNullable[] = [];
  let cursorA = 0;
  let cursorB = 0;

  // skip if table is empty
  if (lindex.length == 0 || rindex.length == 0) {
    return results;
  }

  // TODO: configurable schema for output
  const schema = mergeSchemas(left[0], right[0], right_on);

  // Keep going until we reach the end of either index
  outerLoop:
  while (true) {
    let idxA = lindex[cursorA];
    let idxB = rindex[cursorB];
    // find next matching key
    while (idxA[1] !== idxB[1]) { // TODO: need to ensure this handles null values properly
      if (idxA[1] < idxB[1]) {
        idxA = lindex[++cursorA];
        if (!idxA) {
          break outerLoop;
        }
      } else {
        idxB = rindex[++cursorB]
        if (!idxB) {
          break outerLoop;
        }
      }
    }

    // assert( idxA[1] === idxB[1] ) // found the next match at this point

    // Perform the actual merge (prioritizing rows from left table)
    let marked_cursor = cursorA;
    let marked_idx = idxA;

    innerLoop:
    while (true) {
      // merge all left rows that match current right row
      while (idxA[1] === idxB[1]) {
        results.push( joinRowWithSchema(left[idxA[0]], right[idxB[0]], schema) );
        idxA = lindex[++cursorA];
        if (!idxA) {
          break; // continue to next row from `right`
        }
      }

      // select the next row from `right`
      idxB = rindex[++cursorB];
      if (!idxB) {
        break outerLoop; // no more rows in `right`
      }
      if (marked_idx[1] === idxB[1]) {
        // next right key matches left key (continue with the joining)
        cursorA = marked_cursor;
        idxA = marked_idx;
      } else {
        if (!idxA) {
          // no more rows in left table, end whole join
          break outerLoop;
        } else {
          // still rows in left, so find the next match
          break innerLoop;
        }
      }
    }
  }

  return results;
}

/** simplest join method **deprecated**
 */
function joinRow(leftRow: IRow, rightRow: IRow, left_on: string, right_on: string) {
  // remove old key
  let tmp = {...rightRow};
  delete tmp[right_on];

  // TODO: rename shared columns on right row
  return {...leftRow, ...tmp}
}

function compKey(a: [number, string], b: [number, string]) {
  if (a[1] < b[1]) {
    return -1;
  } else if (a[1] > b[1]) {
    return 1;
  } else {
    return 0;
  }
}
function extractKey(row: IRowNullable, key: string[]) {
  return key.map(k => String(row[k])).join('|');
}

/** Default left join method
 *
 * Uses the sort merge join algorithm, but only takes keys from the left
 * @param left
 * @param right
 * @param left_on
 * @param right_on
 */
function _sortmerge_left_join(left: IRowNullable[], right: IRowNullable[], left_on: string[], right_on: string[]) {
  // NOTE: make an index sorted on the key, rather than worry about mutating the input
  // TODO: we should probably drop all null values from the index
  const lindex: [number, string][] = left.map((r: IRowNullable, i: number) => [i, extractKey(r, left_on)]);
  const rindex: [number, string][] = right.map((r: IRowNullable, i: number) => [i, extractKey(r, right_on)]);
  lindex.sort(compKey);
  rindex.sort(compKey);

  if (lindex.length == 0 ) {
    return [];
  }
  if (rindex.length == 0) {
    return left;
  }

  // TODO: Determine the schema of the output table
  const schema = mergeSchemas(left[0], right[0], right_on);

  // The merge operation
  const results: IRowNullable[] = [];
  let cursorA = 0;
  let cursorB = 0;


  outerLoop:
  while (cursorA < lindex.length) {
    let idxA = lindex[cursorA];
    let idxB = rindex[cursorB];

    // Find next right row that matches current left row
    while (idxB[1] < idxA[1]) {
      idxB = rindex[++cursorB]
      if (!idxB) {
        // No more rows in the right table to match
        break outerLoop;
      }
    }
    // Note: Current row will be handled by this iteration no matter what at this point
    // If there are no more rows in the right table, then the outer look will break before this
    cursorA++;

    // Match found
    if (idxB[1] === idxA[1]) {
      // Mark current row for next iteration
      let marked_cursor = cursorB;
      let marked_idx = idxB;

      // Join all matching rows from the right table
      while (idxB[1] === idxA[1]) {
        results.push( joinRowWithSchema(left[idxA[0]], right[idxB[0]], schema) );
        idxB = rindex[++cursorB];
        if (!idxB) {
          break;
        }
      }

      // Restore right cursor
      cursorB = marked_cursor;
      idxB = marked_idx;
    } // No match found: add left row and march forward
    else {
      results.push( joinRowWithSchema(left[idxA[0]], {}, schema) );
    }
  }

  // At this point, the right table is empty, so just add the rest of the left table
  while (cursorA < lindex.length) {
    let idxA = lindex[cursorA];
    results.push(  joinRowWithSchema(left[idxA[0]], {}, schema) );
    cursorA++;
  }

  return results;
}


function joinRowWithSchema(leftRow: IRowNullable, rightRow: IRowNullable, schema: SchemaMap) : IRowNullable {
  // TODO: actually use the schema
  // remove old key
  let tmp = {...leftRow};
  for (let key of schema.fromRight) {
    tmp[key] = rightRow[key] === undefined ? null : rightRow[key];
  }
  for (let [key, renamed] of schema.rightRenames) {
    tmp[renamed] = rightRow[key] === undefined ? null : rightRow[key];
  }

  return tmp;
}

type SchemaMap = {
  fromLeft: string[],
  fromRight: string[],
  leftRenames: [string, string][],
  rightRenames: [string, string][],
}

function mergeSchemas(left: IRowNullable, right: IRowNullable, right_on: string[]) {
  const schema: SchemaMap = {
    'fromLeft': [],
    'fromRight': [],
    'leftRenames': [],
    'rightRenames': [],
  }
  schema.fromLeft = Object.keys(left);
  // TODO: should we add `_left` to the keys in both?
  const rschema = Object.keys(right).filter(k => !right_on.includes(k));
  for (let key of rschema) {
    if (schema.fromLeft.includes(key)) {
      schema.rightRenames.push([key, `${key}_right`]);
    }
    else {
      schema.fromRight.push(key);
    }
  }

  return schema
}
