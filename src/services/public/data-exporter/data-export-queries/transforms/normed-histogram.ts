/** Algorithm for Normed Histogram in the API-DAG
 */

// Type definitions
type value = number | string | any
type IRow = {[key: string]: value}

/** 
 * normed-histogram transform definition 
 */
export function normed_histogram(df_input: IRow[], n_bins: number) {

  const { count, mean, stdev } = df_input[0];

  // Range
  const x0 = mean - 4 * stdev;
  const x1 = mean + 4 * stdev;

  const bin_width = (x1 - x0) / n_bins;

  // Centers of bins
  const x_values = Array.from({ length: n_bins }, (_, i) => x0 + i * bin_width + bin_width / 2);

  // Gaussian dist function
  const gaussian = (x: number) => (1 / (stdev * Math.sqrt(2 * Math.PI))) * Math.exp(-0.5 * Math.pow((x - mean) / stdev, 2));

  const y_values = x_values.map(x => gaussian(x));

  // Normalization based on the count
  const normalization_factor = count * bin_width; // total area of the histogram
  const y_normalized = y_values.map(y => (y * bin_width) / normalization_factor);

  return x_values.map((x, i) => ({ x0: x - bin_width / 2, x1: x + bin_width / 2, y: y_normalized[i] }));
}
