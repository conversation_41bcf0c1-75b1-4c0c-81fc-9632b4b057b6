/** Pivot table transforms
 */


type Value = number | string | boolean | null | undefined;
type IRow = {[key: string]: Value};
type AggFunc = (vals: Value[]) => Value | undefined;

/**
 * Run a pivot transform
 *
 * Creates a pivot table where each row starts with `rowHeader` and each column is a value from `colHeader`.
 * The initial or default value for a cell is `init`.
 * If `init !== undefined`, then all missing values will be filled with its value.
 */
export function pivot(data: IRow[], rowHeader: string[], colHeader: string, colValue: string, aggregator: AggFunc, colPrefix?: string, defaultValue?: Value) {
  if (data.length === 0) {
    return []
  }
  if (!colPrefix) {
    colPrefix = "";
  }
  // Group records
  const groups = groupIndex(data, rowHeader);

  const headers: Set<string> = new Set();
  const result: IRow[] = [];

  for (let g of groups) {
    // Collector of values for each column
    const rowCache: {[key: string]: Value[]} = {};
    for (let record of g.records) {
      let col = record[colHeader];
      const val = record[colValue];
      if (col === undefined || col === null) {
        throw new Error("Pivot cannot support Nulls for the columns index");
      }
      col = colPrefix + col; // coerce to string
      let values = rowCache[col];
      if (!values) {
        values = [];
        rowCache[col] = values;
      }
      rowCache[col].push(val);
    }
    const row: IRow = {};
    for (let h of rowHeader) {
      row[h] = g.records[0][h];
    }
    for (let [col, vals] of Object.entries(rowCache)) {
      row[col] = aggregator(vals);
      headers.add(col);
    }
    result.push(row);
  }
  if (defaultValue !== undefined) {
    // Fill in missing values
    for (let record of result) {
      for (let col of headers) {
        record[col] = record[col] === undefined ? defaultValue : record[col];
      }
    }
  }
  return result;
}

export const aggregators : {[key:string]: AggFunc} = {
  'first': aggFirst,
  'last': aggLast,
  'max': aggMax,
  'min': aggMin,
  'mean': aggMean,
  'count': aggCount,
}

function groupIndex(data: IRow[], groupKey: string[]) {
  const index: [number, string][] = [];
  for (let [idx, record] of data.entries()) {
    index.push([idx, makeKey(record, groupKey)])
  }
  index.sort((a: [number, string], b: [number, string]) => a[1].localeCompare(b[1]))
  const groups: {key: string, records: IRow[]}[] = [];
  let current = index[0][1];
  let g: IRow[] = [];
  for (let [idx, key] of index) {
    if (current !== key) {
      groups.push({key: current, records: g});
      g = [];
      current = key;
    }
    g.push(data[idx]);
  }
  groups.push({key: current, records: g});
  return groups;
}

function makeKey(record: IRow, cols: string[]) {
  let key = "";
  for (let col of cols) {
    key += "||" + record[col];
  }
  return key;
}

function aggFirst(values: Value[]) {
  if (!values || values.length == 0) {
    return undefined
  }
  return values[0]
}

function aggLast(values: Value[]) {
  if (!values || values.length == 0) {
    return undefined
  }
  return values[values.length - 1]
}

function aggMax(values: Value[]) {
  let fvals : (string | number)[] = < (string | number)[] > values.filter( (v) => v !== null && v !== undefined);
  if (!fvals || fvals.length == 0) {
    return undefined
  }
  let maxval = fvals[0];
  for (let i = 1; i < fvals.length; i++) {
    if (fvals[i] > maxval) {
      maxval = fvals[i];
    }
  }
  return maxval
}

function aggMin(values: Value[]) {
  let fvals : (string | number)[] = < (string | number)[] > values.filter( (v) => v !== null && v !== undefined);
  if (!fvals || fvals.length == 0) {
    return undefined
  }
  let minval = fvals[0];
  for (let i = 1; i < fvals.length; i++) {
    if (fvals[i] < minval) {
      minval = fvals[i];
    }
  }
  return minval
}

function aggMean(values: Value[]) {
  let fvals = < number[] > values.map(toNumber).filter( (v) => v !== null && v !== undefined);
  if (!fvals || fvals.length == 0) {
    return undefined
  }
  let total = fvals.reduce( (a, b) => a+b);
  return total / fvals.length;
}

function toNumber(v: Value) {
  let res: number;
  if (v === undefined || v === null) {
    return undefined
  } else if (typeof v == 'string') {
    let trimmed = v.trim();
    if (trimmed === '') {
      return undefined
    }
    res = Number(trimmed)
  } else {
    res = +v;
  }
  if (isNaN(res)) {
    return undefined
  }
  return res;
}

function aggCount(values: Value[]) {
  return values.length;
}
