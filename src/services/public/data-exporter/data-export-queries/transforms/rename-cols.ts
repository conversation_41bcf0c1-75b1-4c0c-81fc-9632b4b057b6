/**
 * @module RenameCols
 *
 * Rename columns in a record-based dataframe
 */

// Type definitions
type Value = number | string | boolean | null
type IRow = {[key: string]: Value}

/*
 * Filter column transform definition
 *
 * Notes:
 * - This will silently add missing columns with null values.
 * - Does not mutate input
 */
export function renameCols( df_input: IRow[], columns: {[key:string]: string}): IRow[] {
  let result: IRow[] = [];

  for (let row of df_input) {
    let tmp = {...row};
    for (let [key, renamed] of Object.entries(columns)) {
      // TODO: produce a warning for missing columns?
      tmp[renamed] = tmp[key] === undefined ? null : tmp[key];
      delete tmp[key];
    }
    result.push(tmp);
  }
  return result;
}

export function renameColsInplace( df_input: IRow[], columns: {[key:string]: string}): IRow[] {
  let result: IRow[] = [];

  for (let row of df_input) {
    for (let [key, renamed] of Object.entries(columns)) {
      // TODO: produce a warning for missing columns?
      row[renamed] = row[key] === undefined ? null : row[key];
      delete row[key];
    }
    result.push(row);
  }
  return result;
}
