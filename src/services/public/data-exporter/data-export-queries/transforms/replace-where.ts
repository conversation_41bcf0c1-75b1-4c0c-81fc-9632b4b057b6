/**
 * @module ReplaceWhere
 *
 * Replace values in target dataframe from another where a condition is met
 */

// Type definitions
type value = number | string | boolean | null
type IRow = {[key: string]: value}

/*
 * Filter column transform definition
 */
export default function replaceWhere(
      df_target: IRow[], df_source: IRow[],
      targetKey: string[], sourceKey: string[],
      targetCol: string, sourceCol: string,
      imputedCol?: string
      ): IRow[] {

  // TODO: Input validation -- may not be required
  if (df_target.length > 0 && !df_target[0].hasOwnProperty(targetCol)) {
    throw new Error(`Column "${targetCol}" not found in the Target DataFrame`);
  }
  if (df_target.length > 0 && !df_target[0].hasOwnProperty(targetCol)) {
    throw new Error(`Column "${sourceCol}" not found in the Source DataFrame`);
  }

  let results: IRow[] = [];

  // TODO: May be faster to use a Map if df_target is large
  for (const targetRow of df_target) {
    const resultRow: IRow = {...targetRow}
    const sourceRow = df_source.find(row => {
      for (let i = 0; i < targetKey.length; i++) {
        if (String(row[sourceKey[i]]) !== String(resultRow[targetKey[i]])) {
          return false
        }
      }
      return true
    })

    if (sourceRow) {
      resultRow[targetCol] = sourceRow[sourceCol]
    }
    if (imputedCol) {
      resultRow[imputedCol] = +!!sourceRow;
    }
    results.push(resultRow)
  }

  return results
}
