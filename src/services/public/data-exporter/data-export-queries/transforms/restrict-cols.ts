/** Restrict columns algorithm for the API-DAG
 */

// Type definitions
type value = number | string | boolean
type IRow = {[key: string]: value}

/*
 * Restrict column transform definition
 */
export function restrict_cols(df_input: IRow[], columns: string[]) {
  if (df_input.length == 0 || columns.length == 0) {
    return []
  }

  // Input Validation: Checking if requested columns are present in the dataframe
  // TODO: Confirm implementation: Do we only return valid columns or throw an exception if the requested columns are not present in the input df
  const missing_columns = columns.filter(col => !(col in df_input[0]));
  if (missing_columns.length > 0) {
      throw new Error(`Requested columns not available: ${missing_columns.join(', ')}`);
  } 

  /*
  * Implementation if we only return columns present in the input dataframe and ignore missing columns
  */
  // const valid_columns = columns.filter(col => col in df_input[0]);

  // Map row to new object with restricted columns
  return df_input.map(row => {
    const restricted_row: IRow = {};
    for (const column of columns) {
      restricted_row[column] = row[column];
    }
    return restricted_row;
  });
} 