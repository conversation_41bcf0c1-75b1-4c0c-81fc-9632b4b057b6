/**
 * @module SetWhere
 *
 * Overwrite column values where a condition is met
 *
 */

// Type definitions
type Value = number | string | boolean | null
type NonNullValue = number | string | boolean
type IRow = {[key: string]: Value}

type Condition = {
  comparison: string,
  col: string,
  value: NonNullValue,
}

export function setWhere( df_input: IRow[], condition: Condition, values: {[key: string]: Value}, values_false?: {[key: string]: Value}): IRow[] {

  let predicate = makePredicate(condition);
  if (!predicate) {
    throw new Error(`Configuration error: unrecognized comparison: ${condition.comparison}`);
  }
  let result: IRow[] = [];
  for (let row of df_input) {
    let tmp = {...row};
    if (predicate(row)) {
      for (let [column, value] of Object.entries(values)) {
        tmp[column] = value;
      }
    } else if (values_false) { // if values_false are provided, set column value to that if predicate does not match 
      for (let [column, value] of Object.entries(values_false)) {
        tmp[column] = value;
      }
    }
    result.push(tmp);
  }
  return result;
}

export function setWhereInplace( df_input: IRow[], condition: Condition, values: {[key: string]: Value}, values_false?: {[key: string]: Value}): IRow[] {

  let predicate = makePredicate(condition);
  if (!predicate) {
    throw new Error(`Configuration error: unrecognized comparison: ${condition.comparison}`);
  }
  let result: IRow[] = [];
  for (let row of df_input) {
    if (predicate(row)) {
      for (let [column, value] of Object.entries(values)) {
        row[column] = value;
      }
    } else if (values_false) { // if values_false are provided, set column value to that if predicate does not match 
      for (let [column, value] of Object.entries(values_false)) {
        row[column] = value;
      }
    }
    result.push(row);
  }
  return result;
}

// Predicates

// TODO: These predicates should be available as a module for all other transforms
// TODO: How would we support using a second column instead of a constant value?
// TODO: support for a `is-in` and `between`
function makePredicate(comparisonDef: Condition) {
  let {comparison, col: column, value} = comparisonDef

  switch (comparison) {
    case "equals":
      return (row: IRow) => row[column] == value;
    case "not-equal":
      return (row: IRow) => row[column] != value;
    case "greater-than":
      return (row: IRow) => {
        let rval = row[column];
        if (rval === null || rval === undefined) return false;
        return rval > value;
      } ;
    case "less-than":
      return (row: IRow) => {
        let rval = row[column];
        if (rval === null || rval === undefined) return false;
        return rval < value;
      } ;
    case "greater-than-equal":
      return (row: IRow) => {
        let rval = row[column];
        if (rval === null || rval === undefined) return false;
        return rval >= value;
      } ;
    case "less-than-equal":
      return (row: IRow) => {
        let rval = row[column];
        if (rval === null || rval === undefined) return false;
        return rval <= value;
      } ;
    case "matches-pattern": 
      return (row: IRow) => {
        let rval = row[column];
        return new RegExp(String(value)).test(String(rval))
      }
  }
}
