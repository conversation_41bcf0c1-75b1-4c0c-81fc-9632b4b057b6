/** Sort algorithm for the API-DAG
 */

// Type definitions
type value = number | string | boolean | null
type IRow = {[key: string]: value}

/*
 * sort-by transform definition
 */
export function sort_by(df_input: IRow[], sort_by_columns: string[], ascending: boolean[]): IRow[] {

  // Input validation: empty input dataframe
  if (df_input.length == 0) {
    return []
  }

  // Input Validation: Checking if sort columns are present in the dataframe
  // TODO: Confirm implementation: Do we throw an exception if the sort columns are not present in the input df
  const missing_columns = sort_by_columns.filter(col => !(col in df_input[0]));
  if (missing_columns.length > 0) {
      throw new Error(`Sort columns specified not present: ${missing_columns.join(', ')}`);
  }

  // Comparator function for sorting based on multiple columns
  const comp_key = (row_a: IRow, row_b: IRow): number => {
    for (let i = 0; i < sort_by_columns.length; i++) {
      const col = sort_by_columns[i];
      const isAscending = ascending[i];

      const val_a = row_a[col];
      const val_b = row_b[col];

      // Treating Null or undefined values "smallest"
      // This is the same behaviour as MySQL and sqlite
      if (val_a === null || val_a === undefined) {
        if (val_b !== null && val_b !== undefined) {
          return isAscending ? -1 : 1;
        } else {
          continue;
        }
      }
      else if (val_b === null || val_b === undefined) {
        return isAscending ? 1 : -1;
      }

      // Value comparison
      if (val_a < val_b) return isAscending ? -1 : 1;
      if (val_a > val_b) return isAscending ? 1 : -1;
    }
    return 0;
  };

  // Return the sorted dataframe
  return df_input.slice().sort(comp_key);
}
