/** WR stats calculation algorithm for the API-DAG
 */
import { decodeHTML } from 'entities';

// Type definitions
type Value = number | string | any
type IRow = {[key: string]: Value}

/**
 * wr-stats transform definition
 */
export function wr_stats(df_input: IRow[], column: string) {
  return df_input.map((row) => {
    if (typeof row[column] === 'string') {
      const { character_count, word_count, sentence_count } = analyzeHtmlString(row[column] as string);

      row[`character_count`] = character_count;
      row[`word_count`] = word_count;
      row[`sentence_count`] = sentence_count;
    }
    return row;
  });
}

function analyzeHtmlString(htmlString: string): { character_count: number, word_count: number, sentence_count: number } {
  // Decode HTML entities
  htmlString = decodeHtmlEntities(htmlString);

  // Remove HTML tags using regex, then punctuation, then spaces
  let cleanedString = htmlString.replace(/<.*?>/g, '');
  cleanedString = cleanedString.replace(/&nbsp;/g, ' ').replace(/\xa0/g, ' ');

  let strNoPunct = cleanedString.replace(/[^\w\s]/g, '');

  const charCount = strNoPunct.replace(/\s+/g, '').length;

  // Tokenize words and count
  const words = strNoPunct.split(/\s+/);
  const wordCount = words.length;

  // Count sentences based on common sentence-ending punctuation
  const sentenceBoundaries = ['.', '?', '!'];
  let sentences: string[] = [];
  let currentSentence = '';

  const sentenceWords = cleanedString.split(/\s+/);
  for (let word of sentenceWords) {
    if (sentenceBoundaries.some(boundary => word.endsWith(boundary))) {
      sentences.push(currentSentence.trim());
      currentSentence = '';
    } else {
      currentSentence += ' ' + word;
    }
  }

  // Add the last sentence
  if (currentSentence.trim()) {
    sentences.push(currentSentence.trim());
  }

  return {
    character_count: charCount,
    word_count: wordCount,
    sentence_count: sentences.length,
  };
}

function decodeHtmlEntities(htmlString: string): string {
  return decodeHTML(htmlString);
}
