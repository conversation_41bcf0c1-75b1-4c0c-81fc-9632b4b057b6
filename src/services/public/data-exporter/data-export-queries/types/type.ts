import { Application } from "@feathersjs/express";

export interface IActiveJob {
    exportId: number,
    status: string, // todo
    storagePath: string,
    jobDagState: IDagJobState, // recursive structure or list
}

export type AssetSlug = string;

export interface IDagJobState {
    assetsSlugs: AssetSlug[],
    assetDependencies: {
        [assetSlug:AssetSlug]: AssetSlug[]
    }
    assetCompletions: {
        [assetSlug:AssetSlug]: {
            isReady: boolean,
            isStarted: boolean,
            isError: boolean,
            errorDetails: any[],
            startedOn: number,
            completedOn: number,
            delayBeforeStartMs: number,
            durationMs: number,
            numberOfParts: number,
            benchmarkTime: number, // should come from the asset, should be
        }
    }
}

export interface IDagJobConfig {
    jobType: JobType,
    jobName?: string,
    notes: string,
    isBg?:boolean,
    testWindowId?:number, // so common, might as well keep it here for now, might move it later
    canonicalSlug?:string, // so common, might as well keep it here for now, might move it later
    config: {[key: string]: any} // pipeline_config options
}

export enum JobType {
    JOB = 'job',
    ASSET = 'asset',
}

export interface IExportAssetMaterializaerDef {
    requiredInputs: string[],
    optionalInputs?: string[],
}

export interface IExportQueryDef extends IExportAssetMaterializaerDef {
    queryGen: (config:any) => string,
}

export interface IExportTransformDef  extends IExportAssetMaterializaerDef {
    transform: (config:any) => string,
}

export interface IExportApiCallDef extends IExportAssetMaterializaerDef {
    req: (app: Application, config:any) => PromiseLike<any[]>,
}

export interface IExportAsset {
    slug: string,
    description: string,
    structure: IExportAssetStructure | string,
    structureConfig: IExportAssetStructureConfig,
    method: IExportAssetMethod | string,
    methodConfig: IExportAssetMethodConfig,
    dependencySourcings: ISourcing[],
}

// todo: rule: only one streamed sourcing per asset?

interface ISourcing {
    param: string,
    type: SourcingType,
    config: ISourcingConfig,
}

enum SourcingType {
    JOB_CONFIG = 'job-config',
    ASSET_COL = 'asset-col',
    DATAFRAME = 'dataframe',
}
interface ISourcingConfig {}
interface ISourcingConfigJobConfig extends ISourcingConfig {
    configSlug: string,
    param: string,
}
interface ISourcingConfigAssetCol extends ISourcingConfig {
    assetSlug: string,
    col: (string | number)[],
}


enum IExportAssetStructure {
    DATAFRAME = 'dataframe' ,
    DATAFRAME_CHUNKED = 'dataframe-chunked' ,
    ZIP = 'zip' ,
    EXCEL = 'excel' ,
}

interface IExportAssetStructureConfig {} // base class

interface IExportAssetStructureConfigDataframe extends IExportAssetStructureConfig {
    columns: IDataframeColumn[]
}

interface IDataframeColumn {
    name: string,
    type: DataframeColType,
    isNullable: boolean,
    // foreign keys don't really make sense in this context, as it would have to bury to the original source of truth, so we distinguish between the database table that contains the source of truth and just the immediate source in the dag. The immediate source in the dag will often be derivable automatically if the asset is well-structured
}
enum DataframeColType {
    NUM_BOOLEAN = 'NUM_BOOLEAN',
    NUM_INT = 'NUM_INT',
    NUM_INT_UNSIGNED = 'NUM_INT_UNSIGNED',
    NUM_FLOAT = 'NUM_FLOAT',
    NUM_TIMESTAMP_UNIX = 'NUM_TIMESTAMP_UNIX',
    NUM_TIME_MS = 'NUM_TIME_MS',
    TXT_SLUG = 'TXT_SLUG',
    TXT_SMALL = 'TXT_SMALL',
    TXT_LONG = 'TXT_LONG',
    TXT_JSON = 'TXT_JSON',
    TXT_TIMESTAMP_ISO = 'TXT_JSON',
}

enum IExportAssetMethod {
    QUERY = 'query',
    QUERY_CHUNKED = 'query-chunked',
    TRANSFORM = 'transform',
    API = 'api',
    API_CHUNKED = 'api-chunked',
    PACKAGE = 'package', // create zip files and
    UNPACK = 'unpack',
    UPLOAD_AND_WAIT = 'upload-and-wait', // drop a file into a worker and poll for completion
}

interface IExportAssetMethodConfig {} // base class

interface IExportAssetMethodConfigQuery extends IExportAssetMethodConfig {
    querySlug: string,
}
interface IExportAssetMethodConfigQueryChunked extends IExportAssetMethodConfigQuery {
    chunkSize: number,
}

interface IExportAssetMethodConfigTransform extends IExportAssetMethodConfig {
    transformSlug: string,
}
interface IExportAssetMethodConfigApi extends IExportAssetMethodConfig {
    method: string,
    path: string,
    params: any,
    data: any,
}
interface IExportAssetMethodConfigApiChunked extends IExportAssetMethodConfigApi {
    chunkSize: number,
}


/* DataFrame types */
export type Value = number | string | boolean
export type IRecord = {[key: string]: Value}

interface IRecordList {
    schema: string[],
    data: IRecord[],
}

interface IColumnarFrame {
    schema: string[]
    data: IDataframeColumn[]
}

interface ITaggedRecordList extends IRecordList {
    kind: "records",
}

interface ITaggedColumnarFrame {
    kind: "columnar",
}

type IDataFrame = ITaggedRecordList | ITaggedColumnarFrame;
