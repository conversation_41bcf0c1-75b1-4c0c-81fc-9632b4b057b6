import { Errors } from '../../../../../errors/general';
import { DagAsset, DependencySourcingType } from '../model/assets';
import {
  dataExportApiServices,
  dataExportQueryServices,
  dataExportTransformServices,
} from '../../data-export-queries/services/_index';
import { ExporterQueryRef } from '../../data-export-queries/queries/_index';
import { checkConfigs } from '../../data-export-queries/services/transforms';

export async function validateConfig(sequence: DagAsset[], pipeline_config: any) {
  const errors: string[] = [];
  const warnings: string[] = [];

  for (let asset of sequence) {
    const { slug: assetSlug, method, methodConfig } = asset;
    if (!checkMethodExists(method)) {
      if (method == 'placeholder') {
        errors.push(`Asset ${assetSlug} :: is a placeholder and will prevent execution`);
      } else {
        errors.push(`Method ${method} does not exist in ${assetSlug} definition`);
      }
    }
    // check configuration of transform methods
    if (method == 'transforms') {
      const { errors: transformErrors,
              warnings: transformWarnings
      } = listTransformErrors(methodConfig, pipeline_config, asset.dependencySourcings);
      errors.push(... transformErrors.map( s => `Asset ${assetSlug} :: ${s}`));
      warnings.push(...transformWarnings.map( s => `Asset ${assetSlug} :: ${s}`));
    }
    // check configuration of query methods
    if (['query', 'query-chunked'].includes(method)) {
      const { errors: queryErrors,
              warnings: queryWarnings,
      } = listQueryErrors(asset);
      errors.push(... queryErrors.map( s => `Asset ${assetSlug} :: ${s}`));
      warnings.push(...queryWarnings.map( s => `Asset ${assetSlug} :: ${s}`));
    }
    // TODO: check configuration of api methods
    // TODO: check dependency sourcing configuration - assetSlug existence already checked by creating the sequence
  }

  return {errors, warnings};
}

function checkMethodExists(method: string) {
  return Object.keys(dataExportApiServices).includes(method) ||
    Object.keys(dataExportQueryServices).includes(method) ||
    method == 'transforms';
}

function listQueryErrors(assetDef: DagAsset) {
  const { methodConfig } = assetDef;
  const errors: string[] = [];
  const warnings: string[] = [];

  const paramNames: string[] = [];
  for (let dep of assetDef.dependencySourcings) {
    paramNames.push(dep.param);
  }


  // Critical errors that block execution
  if (!methodConfig) {
    errors.push(`No methodConfig defined for query`);
    return {errors, warnings};
  }
  if (!methodConfig.querySlug) {
    errors.push(`No query defined for query`);
    return {errors, warnings};
  }
  const queryDef = ExporterQueryRef[methodConfig.querySlug];
  if (!queryDef) {
    errors.push(`Query ${methodConfig.querySlug} is not recognized`);
    return {errors, warnings};
  }

  // check for required parameters
  if (queryDef.requiredInputs) {
    for (let req of queryDef.requiredInputs) {
      if (!paramNames.includes(req)) {
        errors.push(`Required input ${req} is not defined for query ${methodConfig.querySlug}`);
      }
    }
  }
  // warn if optional parameters are not explicit: default to falsey
  if (queryDef.optionalInputs) {
    for (let opt of queryDef.optionalInputs) {
      if (!paramNames.includes(opt)) {
        warnings.push(`Optional input ${opt} is not defined for query, will use default value`);
      }
    }
  }

  // warn if parameters are unused
  for (let dep of paramNames) {
    if ((!queryDef.requiredInputs || !queryDef.requiredInputs.includes(dep)) && (!queryDef.optionalInputs || !queryDef.optionalInputs.includes(dep) )) {
      warnings.push(`Parameter ${dep} defined in dependencySourcings is not used in the query`);
    }
  }

  // TODO: check parameters for chunked queries
  return {errors, warnings};

}

function listTransformErrors(methodConfig: any, pipeline_config: any, dependencies: any[]) {
  const { sequence, output } = methodConfig;
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!output) {
    errors.push(`No output defined for transform sequence`);
  }

  // Track parameter names to make sure they are all defined
  const paramNames: string[] = [];
  for (let dep of dependencies) {
    paramNames.push(dep.param);
  }

  let lastDfOutput: string = '';
  for (let [i, transformStep] of sequence.entries()) {
    const { kind, df_output, config } = transformStep;
    if (!checkTransformExists(kind)) {
      errors.push(`Transform ${kind} does not exist in step ${i+1}`);
    }
    if (!df_output) {
      errors.push(`No df_output defined for transform step ${i+1}`);
    }
    if (!config) {
      errors.push(`No config defined for transform step ${i+1}`);
    }
    paramNames.push(df_output);
    const { errors: configErrors,
            warnings: configWarnings
    } = checkTransformConfig(kind, config, paramNames);
    errors.push(... configErrors.map( s => `transforms step ${i+1} config :: ${s}`));
    warnings.push(...configWarnings.map( s => `transforms step ${i+1} config :: ${s}`));
    lastDfOutput = df_output;
  }
  // TODO: warn if df_output does not re-use names: this can affect efficiency and memory usage

  // check if 'output' is not a df_output of previous step
  if (output !== lastDfOutput) {
    warnings.push(`transforms 'output' "${output}" does not match last step's df_output "${lastDfOutput}"`);
  }
  if (!paramNames.includes(output)) {
    errors.push(`transforms 'output' "${output}" does not match any transform step. Sugggested: "${lastDfOutput}"`);
  }
  // TODO: warn if 'output' is not the last step's df_output
  return {errors, warnings};
}

function checkTransformExists(kind: string) {
  return Object.keys(dataExportTransformServices).includes(kind);
}

function checkTransformConfig(kind: string, config: any, paramNames: string[]) {
  const errors: string[] = [];
  const warnings: string[] = [];

  const checkFn = checkConfigs[kind];
  if (!checkFn) {
    warnings.push(`No checker defined for transform kind ${kind}`);
    return {errors, warnings};
  }
  const { errors: configErrors,
          warnings: configWarnings
  } = checkFn(config, paramNames);
  errors.push(... configErrors.map( (s: string) => s));
  warnings.push(...configWarnings.map( (s: string) => s));
  return {errors, warnings};
}
