export enum DependencySourcingType {
  JOB_CONFIG = 'job-config',
  ASSET_COL = 'asset-col',
  DATAFRAME = 'dataframe',
}

/* TODO: Types of shools
 *
 * - defined schools
 * - active schools // load_db_schools, and load_db_school_groupings (for classes)
 * - schools with attempts in them
 * */

export enum DagScope {
  SCHOOLS = 'schools',
  TEST_CENTRES = 'test-centres',
}

export enum CheckAssetSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
}

export const DagAssetDependencyTypes = [
  DependencySourcingType.ASSET_COL,
  DependencySourcingType.DATAFRAME
]

export const DagAssetScopes = [
  DagScope.SCHOOLS,
  DagScope.TEST_CENTRES,
]

export interface FieldDef {
  slug: string,
  type: string,
  caption?: string,
  description?: string,
  is_primary_key?: boolean,
  is_nullable?: boolean
  fk_table_slug?: string,
  fk_field_slug?: string,
}


export interface DagAsset {
  slug: string;
  scopes?: string[];
  caption?: string;
  description: string;
  isCheckAsset?: boolean;
  severity?: string;
  structure: string;
  method: string;
  methodConfig: any;
  partitionOut?: any;   // TODO: clearly define partition definition
  partitionBy?: {key: string, concatOut?: boolean};
  dependencySourcings: DependencySourcing[];
  _dependencies?: string[]; // List of asset slugs this asset depends on
  storeCache?: boolean;
  schema?: FieldDef[];   // list of columns in output (for documentation and validations)
}

export interface DependencySourcing {
  param: string;
  type: DependencySourcingType | string;
  config: {
    configSlug?: string;
    param?: string;
    asset?: string;
    col?: string;
    cols?: string[];  // For restricting columns pulled from an asset
  };
}


export const TEMP_DAG_ASSETS:DagAsset[] = [
  {
    "slug": "load_ta_from_tw",
    "scopes": ["schools"],
    "caption": "test_attempts_from_tw",
    "description": "Loads all test attempts for a test window.",
    "structure": "dataframe",
    "schema": [
      {"slug": "attempt_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": true},
      {"slug": "is_closed", "type": "BOOL_INT", "caption": "Is Closed", "description": "Flag indicating if the test attempt is closed.", "is_primary_key": false},
      {"slug": "uid", "type": "INTEGER_UNSIGNED", "caption": "User Id", "description": "Foreign key referencing the users table.", "is_primary_key": false},
      {"slug": "sc_id", "type": "INTEGER_UNSIGNED", "caption": "School Class Id", "description": "Identifier for the school class.", "is_primary_key": false},
      {"slug": "s_id", "type": "INTEGER_UNSIGNED", "caption": "School Id", "description": "Identifier for the school.", "is_primary_key": false},
      {"slug": "sd_id", "type": "INTEGER_UNSIGNED", "caption": "School District Id", "description": "Identifier for the school district.", "is_primary_key": false},
      {"slug": "ts_id", "type": "INTEGER_UNSIGNED", "caption": "Test Session Id", "description": "Identifier for the test session.", "is_primary_key": false},
      {"slug": "twtar_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window TD Alloc Rule ID", "description": "Identifier for the Test Window TD Alloc Rule.", "is_primary_key": false},
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_05A_ATTEMPTS_FROM_TW_ID"
    },
    "dependencySourcings": [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      },
      {
        "param": "include_sample_assessments",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "include_sample_assessments"
        }
      },
      {
        "param": "include_sample_schools",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "include_sample_schools"
        }
      },
      { param: "twtar_type_slugs",
        type: "job-config",
        config: {
          configSlug: "pipeline_config",
          param: "twtar_type_slugs"
        }
      }
    ]
  },
  {
    "slug": "load_ta_from_tw",
    "scopes": ["test-centres"],
    "caption": "test_attempts_from_tw",
    "description": "Loads all test attempts for a test window.",
    "structure": "dataframe",
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_05A_ATTEMPTS_FROM_TW_ID_TESTCENTRE"
    },
    "dependencySourcings": [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      },
    ]
  },
  {
    "slug": "load_ta_from_tw__testcentres",
    "scopes": ["test-centres"],
    "caption": "test_attempts_from_tw",
    "description": "Loads test windows",
    "structure": "dataframe",
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_05A_ATTEMPTS_TESTCENTRES"
    },
    "dependencySourcings": [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      },
    ]
  },
  {
    "slug": "load_ta_detail",
    "caption": "test_attempt_detail",
    "description": "Test attempts table for the test-controller view",
    "structure": "dataframe",
    "schema": [
      {"slug": "s_name", "type": "STRING", "caption": "School Name", "description": "Name of the school.", "is_primary_key": false},
      {"slug": "s_code", "type": "STRING", "caption": "School Code", "description": "Code of the school.", "is_primary_key": false},
      {"slug": "sd_code", "type": "STRING", "caption": "School District Code", "description": "Code of the school district.", "is_primary_key": false},
      {"slug": "sd_name", "type": "STRING", "caption": "School District Name", "description": "Name of the school district.", "is_primary_key": false},
      {"slug": "sc_name", "type": "STRING", "caption": "School Class Name", "description": "Name of the school class.", "is_primary_key": false},
      {"slug": "sc_id", "type": "INTEGER_UNSIGNED", "caption": "School Class Id", "description": "Identifier for the school class.", "is_primary_key": false},
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": false},
      {"slug": "ts_id", "type": "INTEGER_UNSIGNED", "caption": "Test Session Id", "description": "Identifier for the test session.", "is_primary_key": false},
      {"slug": "student_uid", "type": "INTEGER_UNSIGNED", "caption": "Student User Id", "description": "Foreign key referencing the users table.", "is_primary_key": false},
      {"slug": "attempt_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": true},
      {"slug": "form_code", "type": "STRING", "caption": "Form Code", "description": "The code associated with the test form.", "is_primary_key": false},
      {"slug": "student_gov_id", "type": "INTEGER_UNSIGNED", "caption": "Student Government ID", "description": "Alberta Student Government ID.", "is_primary_key": false},
      {"slug": "student_fname", "type": "STRING", "caption": "Student First Name", "description": "First name of the student.", "is_primary_key": false},
      {"slug": "student_lname", "type": "STRING", "caption": "Student Last Name", "description": "Last name of the student.", "is_primary_key": false},
      {"slug": "time_spent_min", "type": "FLOAT", "caption": "Time Spent Minimum", "description": "Minimum time spent on the test attempt.", "is_primary_key": false},
      {"slug": "num_screens_accessed", "type": "INTEGER_UNSIGNED", "caption": "Number Of Screens Accessed", "description": "Number of screens that were accessed for the test attempt.", "is_primary_key": false},
      {"slug": "machine_score", "type": "FLOAT", "caption": "Machine Score", "description": "Machine-calculated score of the test_attempt.", "is_primary_key": false},
      {"slug": "machine_weight", "type": "FLOAT", "caption": "Machine Weight", "description": "Machine-calculated weight of the test_attempt.", "is_primary_key": false},
      {"slug": "last_touch_on", "type": "DATETIME", "caption": "Last Touched on", "description": "Date and time of the last touch on the attempt.", "is_primary_key": false},
      {"slug": "started_on_date", "type": "DATETIME", "caption": "Started On Date", "description": "Date and time when the test attempt was started.", "is_primary_key": false},
      {"slug": "is_started", "caption": "Is Started", "type": "BOOL_INT", "description": "Flag indicating if the test attempt was started", },
      {"slug": "is_submitted", "caption": "Is Submitted", "type": "BOOL_INT", "description": "Flag indicating if the test attempt was submitted", },
    ],
    "method": 'query-chunked',
    "methodConfig":  {
      "querySlug": 'SQL_05A_ATTEMPTS_DETAIL_FROM_TA_ID',
      "chunkedParam": 'ta_ids',
      "chunkSize": 2500,
    },
    "dependencySourcings": [
      {
        "param": "ta_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_ta_from_tw",
          "col": "attempt_id"
        }
      },
      {
        "param": "include_not_reported_schools",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "include_not_reported_schools"
        }
      }
    ]
  },
  {
    slug: 'trfm_attempts_by_sc',
    "caption": "attempts_by_sc",
    description: "Test attempts aggregated by class.",
    structure: "dataframe",
    schema: [
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": false},
      {"slug": "s_code", "type": "STRING", "caption": "School Code", "description": "Code of the school.", "is_primary_key": false},
      {"slug": "sc_id", "type": "INTEGER_UNSIGNED", "caption": "School Class Id", "description": "Identifier for the school class.", "is_primary_key": false},
      {"slug": "sd_code", "type": "STRING", "caption": "School District Code", "description": "Code of the school district.", "is_primary_key": false},
      {"slug": "sd_name", "type": "STRING", "caption": "School District Name", "description": "Name of the school district.", "is_primary_key": false},
      {"slug": "n_students", "type": "INTEGER_UNSIGNED", "caption": "Number of Students", "description": "Number of students.", "is_primary_key": false},
      {"slug": "min_started_on", "type": "STRING", "caption": "Minimum Started on", "description": "Minimum started on date of attempts.", "is_primary_key": false},
      {"slug": "max_started_on", "type": "STRING", "caption": "Maximum Started on", "description": "Maximum of the school.", "is_primary_key": false},
      {"slug": "min_last_touch_on", "type": "STRING", "caption": "Minimum Last Touch On", "description": "Minimum Last Touch On date for attempts.", "is_primary_key": false},
      {"slug": "max_last_touch_on", "type": "STRING", "caption": "Maximum Last Touch On", "description": "Maximum Last Touch On date for attempts.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "group-by",
          df_output: "ta_detail",
          config: {
            df_input: "ta_detail",
            group_by: ["assessment_code", "s_code", "sc_id", "sd_code", "sd_name", "s_name"],
            agg: [
              {
                col_new: "n_students",
                agg_type: "nunique",
                col_target: "student_uid",
              },
              {
                col_new: "min_started_on",
                agg_type: "min",
                col_target: "started_on",
              },
              {
                col_new: "max_started_on",
                agg_type: "max",
                col_target: "started_on",
              },
              {
                col_new: "min_last_touch_on",
                agg_type: "min",
                col_target: "last_touch_on",
              },
              {
                col_new: "max_last_touch_on",
                agg_type: "max",
                col_target: "last_touch_on",
              },
            ]
          },
        },
        {
          kind: "cast-col",
          df_output: "ta_detail",
          config: {
            df_input: "ta_detail",
            casts: [
              {
                col_target: "min_started_on",
                target_type: "date"
              },
              {
                col_target: "max_started_on",
                target_type: "date"
              },
              {
                col_target: "min_last_touch_on",
                target_type: "date"
              },
              {
                col_target: "max_last_touch_on",
                target_type: "date"
              }
            ]
          }
        }
      ],
      output: "ta_detail"
    },
    dependencySourcings: [
      { param: "ta_detail",
        type: "dataframe",
        config: {
          asset: "load_ta_detail",
          cols: undefined,
        }
      }
    ],
  },
  {
    slug: 'trfm_attempts_by_s',
    "caption": "attempts_by_s",
    description: "Test attempts aggregated by school.",
    structure: "dataframe",
    schema: [
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": false},
      {"slug": "s_code", "type": "STRING", "caption": "School Code", "description": "Code of the school.", "is_primary_key": false},
      {"slug": "sd_code", "type": "STRING", "caption": "School District Code", "description": "Code of the school district.", "is_primary_key": false},
      {"slug": "sd_name", "type": "STRING", "caption": "School District Name", "description": "Name of the school district.", "is_primary_key": false},
      {"slug": "s_name", "type": "STRING", "caption": "School Name", "description": "Name of the school.", "is_primary_key": false},
      {"slug": "n_classes", "type": "INTEGER_UNSIGNED", "caption": "Number of Classes", "description": "Number of classes.", "is_primary_key": false},
      {"slug": "n_students", "type": "INTEGER_UNSIGNED", "caption": "Number of Students", "description": "Number of students.", "is_primary_key": false},
      {"slug": "min_started_on", "type": "STRING", "caption": "Minimum Started on", "description": "Minimum started on date of attempts.", "is_primary_key": false},
      {"slug": "max_started_on", "type": "STRING", "caption": "Maximum Started on", "description": "Maximum of the school.", "is_primary_key": false},
      {"slug": "min_last_touch_on", "type": "STRING", "caption": "Minimum Last Touch On", "description": "Minimum Last Touch On date for attempts.", "is_primary_key": false},
      {"slug": "max_last_touch_on", "type": "STRING", "caption": "Maximum Last Touch On", "description": "Maximum Last Touch On date for attempts.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "group-by",
          df_output: "ta_detail",
          config: {
            df_input: "ta_detail",
            group_by: ["assessment_code", "s_code", "sd_code", "sd_name", "s_name"],
            agg: [
              {
                col_new: "n_classes",
                agg_type: "nunique",
                col_target: "sc_id",
              },
              {
                col_new: "n_students",
                agg_type: "nunique",
                col_target: "student_uid",
              },
              {
                col_new: "min_started_on",
                agg_type: "min",
                col_target: "started_on",
              },
              {
                col_new: "max_started_on",
                agg_type: "max",
                col_target: "started_on",
              },
              {
                col_new: "min_last_touch_on",
                agg_type: "min",
                col_target: "last_touch_on",
              },
              {
                col_new: "max_last_touch_on",
                agg_type: "max",
                col_target: "last_touch_on",
              },
            ]
          },
        },
        {
          kind: "cast-col",
          df_output: "ta_detail",
          config: {
            df_input: "ta_detail",
            casts: [
              {
                col_target: "min_started_on",
                target_type: "date"
              },
              {
                col_target: "max_started_on",
                target_type: "date"
              },
              {
                col_target: "min_last_touch_on",
                target_type: "date"
              },
              {
                col_target: "max_last_touch_on",
                target_type: "date"
              }
            ]
          }
        }
      ],
      output: "ta_detail"
    },
    dependencySourcings: [
      { param: "ta_detail",
        type: "dataframe",
        config: {
          asset: "load_ta_detail",
          cols: undefined,
        }
      }
    ],
  },
  {
    slug: 'trfm_attempts_by_sd',
    "caption": "attempts_by_sd",
    description: "Test attempts aggregated by school authority.",
    structure: "dataframe",
    schema: [
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": false},
      {"slug": "s_code", "type": "STRING", "caption": "School Code", "description": "Code of the school.", "is_primary_key": false},
      {"slug": "sd_code", "type": "STRING", "caption": "School District Code", "description": "Code of the school district.", "is_primary_key": false},
      {"slug": "sd_name", "type": "STRING", "caption": "School District Name", "description": "Name of the school district.", "is_primary_key": false},
      {"slug": "s_name", "type": "STRING", "caption": "School Name", "description": "Name of the school.", "is_primary_key": false},
      {"slug": "n_schools", "type": "INTEGER_UNSIGNED", "caption": "Number of Schools", "description": "Number of schools.", "is_primary_key": false},
      {"slug": "n_classes", "type": "INTEGER_UNSIGNED", "caption": "Number of Classes", "description": "Number of classes.", "is_primary_key": false},
      {"slug": "n_students", "type": "INTEGER_UNSIGNED", "caption": "Number of Students", "description": "Number of students.", "is_primary_key": false},
      {"slug": "min_started_on", "type": "STRING", "caption": "Minimum Started on", "description": "Minimum started on date of attempts.", "is_primary_key": false},
      {"slug": "max_started_on", "type": "STRING", "caption": "Maximum Started on", "description": "Maximum of the school.", "is_primary_key": false},
      {"slug": "min_last_touch_on", "type": "STRING", "caption": "Minimum Last Touch On", "description": "Minimum Last Touch On date for attempts.", "is_primary_key": false},
      {"slug": "max_last_touch_on", "type": "STRING", "caption": "Maximum Last Touch On", "description": "Maximum Last Touch On date for attempts.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "group-by",
          df_output: "ta_detail",
          config: {
            df_input: "ta_detail",
            group_by: ["assessment_code", "sd_code", "sd_name"],
            agg: [
              {
                col_new: "n_schools",
                agg_type: "nunique",
                col_target: "s_code",
              },
              {
                col_new: "n_classes",
                agg_type: "nunique",
                col_target: "sc_id",
              },
              {
                col_new: "n_students",
                agg_type: "nunique",
                col_target: "student_uid",
              },
              {
                col_new: "min_started_on",
                agg_type: "min",
                col_target: "started_on",
              },
              {
                col_new: "max_started_on",
                agg_type: "max",
                col_target: "started_on",
              },
              {
                col_new: "min_last_touch_on",
                agg_type: "min",
                col_target: "last_touch_on",
              },
              {
                col_new: "max_last_touch_on",
                agg_type: "max",
                col_target: "last_touch_on",
              },
            ]
          },
        },
        {
          kind: "cast-col",
          df_output: "ta_detail",
          config: {
            df_input: "ta_detail",
            casts: [
              {
                col_target: "min_started_on",
                target_type: "date"
              },
              {
                col_target: "max_started_on",
                target_type: "date"
              },
              {
                col_target: "min_last_touch_on",
                target_type: "date"
              },
              {
                col_target: "max_last_touch_on",
                target_type: "date"
              }
            ]
          }
        }
      ],
      output: "ta_detail"
    },
    dependencySourcings: [
      { param: "ta_detail",
        type: "dataframe",
        config: {
          asset: "load_ta_detail",
          cols: undefined,
        }
      }
    ],
  },
  {
    slug: 'trfm_attempts_by_subject',
    "caption": "attempts_by_subject",
    description: "Test attempts aggregated by subject.",
    structure: "dataframe",
    schema: [
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": false},
      {"slug": "n_school_districts", "type": "INTEGER_UNSIGNED", "caption": "Number of School Districts", "description": "Number of school districts.", "is_primary_key": false},
      {"slug": "n_schools", "type": "INTEGER_UNSIGNED", "caption": "Number of Schools", "description": "Number of schools.", "is_primary_key": false},
      {"slug": "n_classes", "type": "INTEGER_UNSIGNED", "caption": "Number of Classes", "description": "Number of classes.", "is_primary_key": false},
      {"slug": "n_students", "type": "INTEGER_UNSIGNED", "caption": "Number of Students", "description": "Number of students.", "is_primary_key": false},
      {"slug": "min_started_on", "type": "STRING", "caption": "Minimum Started on", "description": "Minimum started on date of attempts.", "is_primary_key": false},
      {"slug": "max_started_on", "type": "STRING", "caption": "Maximum Started on", "description": "Maximum of the school.", "is_primary_key": false},
      {"slug": "min_last_touch_on", "type": "STRING", "caption": "Minimum Last Touch On", "description": "Minimum Last Touch On date for attempts.", "is_primary_key": false},
      {"slug": "max_last_touch_on", "type": "STRING", "caption": "Maximum Last Touch On", "description": "Maximum Last Touch On date for attempts.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "group-by",
          df_output: "ta_detail",
          config: {
            df_input: "ta_detail",
            group_by: ["assessment_code"],
            agg: [
              {
                col_new: "n_school_districts",
                agg_type: "nunique",
                col_target: "sd_code",
              },
              {
                col_new: "n_schools",
                agg_type: "nunique",
                col_target: "s_code",
              },
              {
                col_new: "n_classes",
                agg_type: "nunique",
                col_target: "sc_id",
              },
              {
                col_new: "n_students",
                agg_type: "nunique",
                col_target: "student_uid",
              },
              {
                col_new: "min_started_on",
                agg_type: "min",
                col_target: "started_on",
              },
              {
                col_new: "max_started_on",
                agg_type: "max",
                col_target: "started_on",
              },
              {
                col_new: "min_last_touch_on",
                agg_type: "min",
                col_target: "last_touch_on",
              },
              {
                col_new: "max_last_touch_on",
                agg_type: "max",
                col_target: "last_touch_on",
              },
            ]
          },
        },
        {
          kind: "cast-col",
          df_output: "ta_detail",
          config: {
            df_input: "ta_detail",
            casts: [
              {
                col_target: "min_started_on",
                target_type: "date"
              },
              {
                col_target: "max_started_on",
                target_type: "date"
              },
              {
                col_target: "min_last_touch_on",
                target_type: "date"
              },
              {
                col_target: "max_last_touch_on",
                target_type: "date"
              }
            ]
          }
        }
      ],
      output: "ta_detail"
    },
    dependencySourcings: [
      { param: "ta_detail",
        type: "dataframe",
        config: {
          asset: "load_ta_detail",
          cols: undefined,
        }
      }
    ],
  },
  {
    slug: 'trfm_attempts_by_date_subj',
    "caption": "attempts_by_date_subj",
    description: "Test attempts aggregated by date and subject.",
    structure: "dataframe",
    schema: [
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": false},
      {"slug": "started_on_date", "type": "STRING", "caption": "Started On Date", "description": "Started on date of attempts.", "is_primary_key": false},
      {"slug": "n_students", "type": "INTEGER_UNSIGNED", "caption": "Number of Students", "description": "Number of students.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "group-by",
          df_output: "ta_detail",
          config: {
            df_input: "ta_detail",
            group_by: ["assessment_code", "started_on_date"],
            agg: [
              {
                col_new: "n_students",
                agg_type: "nunique",
                col_target: "student_uid",
              }
            ]
          },
        },
      ],
      output: "ta_detail"
    },
    dependencySourcings: [
      { param: "ta_detail",
        type: "dataframe",
        config: {
          asset: "load_ta_detail",
          cols: undefined,
        }
      }
    ],
  },
  {
    slug: 'trfm_attempts_by_date',
    "caption": "attempts_by_date",
    description: "Test attempts aggregated by date.",
    structure: "dataframe",
    schema: [
      {"slug": "started_on_date", "type": "STRING", "caption": "Started On Date", "description": "Started on date of attempts.", "is_primary_key": false},
      {"slug": "n_students", "type": "INTEGER_UNSIGNED", "caption": "Number of Students", "description": "Number of students.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "group-by",
          df_output: "ta_detail",
          config: {
            df_input: "ta_detail",
            group_by: ["started_on_date"],
            agg: [
              {
                col_new: "n_students",
                agg_type: "nunique",
                col_target: "student_uid",
              }
            ]
          },
        },
      ],
      output: "ta_detail"
    },
    dependencySourcings: [
      { param: "ta_detail",
        type: "dataframe",
        config: {
          asset: "load_ta_detail",
          cols: undefined,
        }
      }
    ],
  },
  {
    "slug": "load_school_admins",
    "caption": "school_admins",
    "description": "Loads school administrators to be displayed on the TC view",
    "structure": "dataframe",
    schema: [
      { "slug": "s_name", "type": "STRING", "caption": "School Name", "description": "Name of the school.", "is_primary_key": false },
      { "slug": "s_code", "type": "STRING", "caption": "School Code", "description": "Foreign ID of the school.", "is_primary_key": false },
      { "slug": "first_name", "type": "STRING", "caption": "First Name", "description": "First name of the school admin.", "is_primary_key": false },
      { "slug": "last_name", "type": "STRING", "caption": "Last Name", "description": "Last name of the school admin.", "is_primary_key": false },
      { "slug": "contact_email", "type": "STRING", "caption": "Contact Email", "description": "Email address of the school admin.", "is_primary_key": false },
      { "slug": "is_claimed", "type": "BOOLEAN", "caption": "Is Claimed", "description": "Whether the school admin account is claimed.", "is_primary_key": false },
      { "slug": "created_on", "type": "DATE", "caption": "Role Created On", "description": "Timestamp when the school admin role was created.", "is_primary_key": false }
    ], 
    "method": "query",   
    "methodConfig": {
      "querySlug": "SQL_04A_GROUPS_SCHOOL_ADMINS"
    },
    "dependencySourcings": [
      {
        "param": "include_sample_school_districts",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "include_sample_school_districts"
        }
      }
    ]
  },
  {
    "slug": "load_school_teachers",
    "caption": "school_teachers",
    "description": "Loads teachers to be displayed on the TC view",
    "structure": "dataframe",
    schema: [
      { "slug": "s_name", "type": "STRING", "caption": "School Name", "description": "Name of the school.", "is_primary_key": false },
      { "slug": "s_code", "type": "STRING", "caption": "School Code", "description": "Foreign ID of the school.", "is_primary_key": false },
      { "slug": "first_name", "type": "STRING", "caption": "First Name", "description": "First name of the teacher.", "is_primary_key": false },
      { "slug": "last_name", "type": "STRING", "caption": "Last Name", "description": "Last name of the teacher.", "is_primary_key": false },
      { "slug": "contact_email", "type": "STRING", "caption": "Contact Email", "description": "Email address of the teacher.", "is_primary_key": false },
      { "slug": "is_claimed", "type": "BOOLEAN", "caption": "Is Claimed", "description": "Whether the teacher account is claimed.", "is_primary_key": false },
      { "slug": "created_on", "type": "DATE", "caption": "Role Created On", "description": "Timestamp when the teacher role was created.", "is_primary_key": false }
    ],    
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_04A_GROUPS_TEACHERS"
    },
    "dependencySourcings": [
      {
        "param": "include_sample_school_districts",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "include_sample_school_districts"
        }
      }
    ]
  },
  {
    "slug": "load_ts_detail",
    "caption": "test_session_detail",
    "description": "Test sessions table for the test-controller view",
    "structure": "dataframe",
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_05A_ATTEMPTS_TS_DETAIL"
    },
    "dependencySourcings": [
      // {
      //   "param": "tw_ids",
      //   "type": "asset-col",
      //   "config": {
      //     "asset": "load_ta_from_tw",
      //     "col": "attempt_id"
      //   }
      // }
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      },
    ]
  },
  // 05 Responses
  {
    slug: 'load_taqr',
    "scopes": ["schools"],
    "caption": "test_attempt_question_responses",
    description: "Reduced fields of all question resonses for all test attempts.",
    structure: "dataframe",
    schema: [
      {"slug": "taqr_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Question Response ID", "description": "Identifier for the test attempt question response.", "is_primary_key": true},
      {"slug": "ta_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Id", "description": "Foreign key referencing the test_attempt table.", "is_primary_key": false},
      {"slug": "item_id", "type": "STRING", "caption": "Item ID", "description": "Identifier for the item.", "is_primary_key": false},
      {"slug": "score", "type": "INTEGER_UNSIGNED", "caption": "Score", "description": "Score for the test attempt question response.", "is_primary_key": false},
      {"slug": "weight", "type": "INTEGER_UNSIGNED", "caption": "Weight", "description": "Weight for the test attempt question response.", "is_primary_key": false},
      {"slug": "is_nr", "type": "INTEGER_UNSIGNED", "caption": "Is No Response", "description": "Flag indicating if the response is NR (Non Response).", "is_primary_key": false},
      {"slug": "is_invalid", "type": "INTEGER_UNSIGNED", "caption": "Is Invalid", "description": "Flag indicating if the test attempt is invalid.", "is_primary_key": false},
    ],
    method: "query-chunked",
    methodConfig:  {
      querySlug: 'SQL_05B_TAQR_FROM_TA_ID',
      chunkedParam: 'ta_ids',
      chunkSize: 500,
    },
    partitionOut: {
      key: 'partby_ta_ids',
      field: 'ta_id',
      partSize: 500
    },
    dependencySourcings: [
      {
        param: "ta_ids",
        type: "asset-col",
        config: {
          asset: "load_test_attempts_from_uid",
          col: "ta_id"
        }
      }
    ]
  },
  {
    slug: 'load_taqr',
    "scopes": ["test-centres"],
    "caption": "test_attempt_question_responses",
    description: "Reduced fields of all question resonses for all test attempts.",
    structure: "dataframe",
    method: "query-chunked",
    methodConfig:  {
      querySlug: 'SQL_05B_TAQR_FROM_TA_ID',
      chunkedParam: 'ta_ids',
      chunkSize: 500,
    },
    partitionOut: {
      key: 'partby_ta_ids',
      field: 'ta_id',
      partSize: 500
    },
    dependencySourcings: [
      {
        param: "ta_ids",
        type: "asset-col",
        config: {
          asset: "load_ta_from_tw",
          col: "attempt_id"
        }
      }
    ]
  },
  {
    slug: 'load_twtar',
    caption: "twtar",
    description: "Test window test design allocation rules",
    structure: "dataframe",
    schema: [
      {"slug": "twtar_id", "type": "INTEGER_UNSIGNED", "caption": "Twtar Id", "description": "Identifier for the Test Window TD Alloc Rule.", "is_primary_key": true},
      {"slug": "type_slug", "type": "STRING", "caption": "Twtar Type Slug", "description": "Type Slug for the test window TD alloc rule.", "is_primary_key": false},
      {"slug": "foreign_component_code", "type": "STRING", "caption": "Foreign Component Code", "description": "Foreign component code for the Twtar.", "is_primary_key": false},
      {"slug": "is_secured", "type": "INTEGER_UNSIGNED", "caption": "Is Secured", "description": "Flag indicating if the twtar is secured.", "is_primary_key": false},
      {"slug": "course_name_short", "type": "STRING", "caption": "Course Name Short", "description": "Short name for the course.", "is_primary_key": false},
      {"slug": "is_classroom_common_form", "type": "INTEGER_UNSIGNED", "caption": "Is Classroom Common Form", "description": "Flag indicating if it is common classroom form.", "is_primary_key": false},
      {"slug": "override_td_id", "type": "INTEGER_UNSIGNED", "caption": "Override Test Design Id", "description": "Override identifier for a test design related to test quality review (TQR), indicating a specific override to the standard test design.", "is_primary_key": false},
      {"slug": "is_simple_cover", "type": "BOOL_INT", "caption": "Is Simple Cover", "description": "Flag indicating if the Twtar has a simple cover design.", "is_primary_key": false},
      {"slug": "is_download_results", "type": "INTEGER_UNSIGNED", "caption": "Is Download Results", "description": "Flag indicating if the results were downloaded.", "is_primary_key": false},
      {"slug": "is_active", "type": "INTEGER_UNSIGNED", "caption": "Is Active", "description": "Flag indicating if the Twtar is active.", "is_primary_key": false},
      {"slug": "is_school_allowed_strict", "type": "INTEGER_UNSIGNED", "caption": "Is School Allowed Strict", "description": "Flag indicating if schools are (strictly) allowed.", "is_primary_key": false},
      {"slug": "course_code", "type": "STRING", "caption": "Course Code", "description": "Code for the course.", "is_primary_key": false},
      {"slug": "course_name_full", "type": "STRING", "caption": "Course Name Full", "description": "Full name of the course.", "is_primary_key": false},
      {"slug": "test_window_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window Id", "description": "Identifier for the test window .", "is_primary_key": false},
      {"slug": "component_slug", "type": "STRING", "caption": "Component Slug", "description": "Slug representing the test component.", "is_primary_key": false},
      {"slug": "is_sample", "type": "INTEGER_UNSIGNED", "caption": "Is Sample ", "description": "Flag indicating if it is a sample Twtar.", "is_primary_key": false},
      {"slug": "is_active_for_qa", "type": "INTEGER_UNSIGNED", "caption": "Is Active For QA", "description": "Flag indicating if the Twtar is active for QA.", "is_primary_key": false},
      {"slug": "test_duration", "type": "INTEGER_UNSIGNED", "caption": "Test Duration", "description": "Duration of the test.", "is_primary_key": false},
      {"slug": "caption_short", "type": "STRING", "caption": "Caption Short", "description": "Short version of the caption.", "is_primary_key": false},
      {"slug": "long_name", "type": "STRING", "caption": "Long Name", "description": "Long name for the Twtar.", "is_primary_key": false},
      {"slug": "resource_td_id", "type": "INTEGER_UNSIGNED", "caption": "Resource Test Design Id", "description": "Identifier of the resource test design.", "is_primary_key": false},
      {"slug": "course_code_foreign", "type": "STRING", "caption": "Course Code Foreign", "description": "Foreign code of the course.", "is_primary_key": false},
      {"slug": "is_perusal_allow", "type": "INTEGER_UNSIGNED", "caption": "Is Perusal Allow", "description": "Flag indicating whether perusal is allowed for the Twtar.", "is_primary_key": false},
      {"slug": "cover_security_msg", "type": "INTEGER_UNSIGNED", "caption": "Cover Security Message", "description": "Security message for the Twtar cover.", "is_primary_key": false},
      {"slug": "selection_order", "type": "INTEGER_UNSIGNED", "caption": "Selection Order", "description": "Selection order for the twtar.", "is_primary_key": false},
      {"slug": "is_questionnaire", "type": "INTEGER_UNSIGNED", "caption": "Is Questionnaire", "description": "Flag indicating if the Twtar is part of a questionnaire.", "is_primary_key": false},
      {"slug": "is_local_score", "type": "INTEGER_UNSIGNED", "caption": "Is Local Score", "description": "Flag indicating if twtar is given a local score.", "is_primary_key": false},
      {"slug": "form_code", "type": "STRING", "caption": "Form Code", "description": "The code associated with the test form.", "is_primary_key": false},
      {"slug": "alloc_td_id", "type": "INTEGER_UNSIGNED", "caption": "Published Test Design (Allocation)", "description": "Identifier linking the allocation rule to a specific test design configuration.", "is_primary_key": false},
      {"slug": "is_swap_risk", "type": "INTEGER_UNSIGNED", "caption": "Is Swap Risk", "description": "Flag indicating swap risk in the Twtar.", "is_primary_key": false},
      {"slug": "is_field_test", "type": "INTEGER_UNSIGNED", "caption": "Is Field Test", "description": "Flag indicating whether the Twtar is part of a field test.", "is_primary_key": false},
      {"slug": "resource_caption", "type": "STRING", "caption": "Resource Caption", "description": "Caption for the resource associated with the Twtar.", "is_primary_key": false},
      {"slug": "lang", "type": "STRING", "caption": "Language", "description": "Language of the Twtar.", "is_primary_key": false},
      {"slug": "td_id", "type": "INTEGER_UNSIGNED", "caption": "Test Design Id", "description": "Identifier for the test design associated with the Twtar.", "is_primary_key": false},
    ],
    method: 'query',
    methodConfig: {
      querySlug: 'SQL_02_ASMT_SPECS_TEST_WINDOW_ALLOC_RULES',
    },
    dependencySourcings: [
      {
        param: "tw_ids",
        type: "job-config",
        config: {
          configSlug: "pipeline_config",
          param: "test_window_ids"
        }
      },
      {
        param: "include_sample_assessments",
        type: "job-config",
        config: {
          configSlug: "pipeline_config",
          param: "include_sample_assessments"
        }
      },
      {
        param: "include_questionnaire",
        type: "job-config",
        config: {
          configSlug: "pipeline_config",
          param: "include_questionnaire"
        }
      },
      {
        param: "only_secured_twtdar",
        type: "job-config",
        config: {
          configSlug: "pipeline_config",
          param: "only_secured_twtdar"
        }
      },
      { param: "twtar_type_slugs",
        type: "job-config",
        config: {
          configSlug: "pipeline_config",
          param: "twtar_type_slugs"
        }
      }
    ]
  },
  {
    "slug": "load_db_tw",
    "caption": "tw",
    "description": "Test window information for selected test windows",
    "structure": "dataframe",
    "schema": [
      {"slug": "test_window_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window Id", "description": "Identifier for the test window.", "is_primary_key": true},
      {"slug": "type_slug", "type": "STRING", "caption": "Type Slug", "description": "Slug representing the type of test window.", "is_primary_key": false},
      {"slug": "is_qa", "type": "INTEGER_UNSIGNED", "caption": "Is QA", "description": "Flag indicating whether the test window is for Quality Assurance (QA).", "is_primary_key": false},
      {"slug": "is_active", "type": "INTEGER_UNSIGNED", "caption": "Is Active", "description": "Flag indicating whether the test window is active.", "is_primary_key": false},
      {"slug": "is_field_test", "type": "INTEGER_UNSIGNED", "caption": "Is Field Test", "description": "Flag indicating whether the test window is for Field Tests.", "is_primary_key": false},
      {"slug": "is_bg", "type": "INTEGER_UNSIGNED", "caption": "Is Background", "description": "Flag indicating whether the test window is a background test window.", "is_primary_key": false},
      {"slug": "is_for_pasi", "type": "INTEGER_UNSIGNED", "caption": "Is For PASI", "description": "Flag indicating whether the test window is for PASI", "is_primary_key": false},
      {"slug": "date_start", "type": "STRING", "caption": "Date Start", "description": "Start date of the test window.", "is_primary_key": false},
      {"slug": "date_end", "type": "STRING", "caption": "Date End", "description": "End date of the test window.", "is_primary_key": false},
      {"slug": "window_code", "type": "STRING", "caption": "Window Code", "description": "Code associated with the test window for identification or reporting purposes.", "is_primary_key": false},
      {"slug": "window_date_human", "type": "STRING", "caption": "Window Date Human", "description": "Human-readable representation of the test window dates.", "is_primary_key": false},
      {"slug": "PASI_school_year", "type": "STRING", "caption": "PASI School Year", "description": "School year associated with the test window in PASI.", "is_primary_key": false},
      {"slug": "title", "type": "STRING", "caption": "Title", "description": "Title or name of the test window.", "is_primary_key": false},
      {"slug": "next_tw_id", "type": "INTEGER_UNSIGNED", "caption": "Next Test Window Idea", "description": "Identifier for the next test window.", "is_primary_key": false},
      {"slug": "is_duration_enforced", "type": "INTEGER_UNSIGNED", "caption": "Is Duration Enforced", "description": "Flag indicating whether the duration of the test window is enforced.", "is_primary_key": false},
      {"slug": "hardstop_offset_h", "type": "INTEGER_UNSIGNED", "caption": "Hardstop Offset (In Hours)", "description": "Offset in hours for a hard stop associated with the test window.", "is_primary_key": false},
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_01_SYS_TEST_WINDOWS",
    },
    "dependencySourcings": [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids",
        }
      }
    ]
  },
  {
    slug: 'load_item_register_consolidated',
    "caption": "item_register_consolidated",
    description: "The full item register for the allocated test designs.",
    structure: "dataframe",
    schema: [
      {"slug": "twtar_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window TD Alloc Rule ID", "description": "Identifier for the Test Window TD Allocation Rule associated with the item.", "is_primary_key": false},
      {"slug": "test_term", "type": "STRING", "caption": "Test Term", "description": "Term in which the test is conducted.", "is_primary_key": false},
      {"slug": "type_slug", "type": "STRING", "caption": "Type Slug", "description": "Type slug representing the Twtar.", "is_primary_key": false},
      {"slug": "course_name_full", "type": "STRING", "caption": "Course Name Full", "description": "Full name of the course associated with the item.", "is_primary_key": false},
      {"slug": "course_code", "type": "STRING", "caption": "Course Code", "description": "Code representing the course associated with the item.", "is_primary_key": false},
      {"slug": "test_lang", "type": "STRING", "caption": "Test Language", "description": "Language in which the test is administered.", "is_primary_key": false},
      {"slug": "form_code", "type": "STRING", "caption": "Form Code", "description": "The code associated with the test form.", "is_primary_key": false},
      {"slug": "component_slug", "type": "STRING", "caption": "Component Slug", "description": "Slug representing the test component.", "is_primary_key": false},
      {"slug": "tqr_id", "type": "INTEGER_UNSIGNED", "caption": "Test Question Register Id", "description": "Identifier for the test question register record.", "is_primary_key": false},
      {"slug": "item_nbr", "type": "INTEGER_UNSIGNED", "caption": "Item Number", "description": "Number assigned to the item within the test.", "is_primary_key": false},
      {"slug": "item_id", "type": "INTEGER_UNSIGNED", "caption": "Item Id", "description": "Identifier for the specific item in the test.", "is_primary_key": true},
      {"slug": "response_lang", "type": "STRING", "caption": "Response Language", "description": "Language in which the response to the item is expected.", "is_primary_key": false},
      {"slug": "report_label_short", "type": "STRING", "caption": "Report Label Short", "description": "Short label used for reporting the item.", "is_primary_key": false},
      {"slug": "item_name", "type": "STRING", "caption": "Item Name", "description": "Name or description of the item.", "is_primary_key": false},
      {"slug": "joined_with_item_nbr", "type": "INTEGER_UNSIGNED", "caption": "Joined With Item Number", "description": "Entry Order from TQR", "is_primary_key": false},
      {"slug": "test_design_id", "type": "INTEGER_UNSIGNED", "caption": "Test Design Id", "description": "Identifier for the test design associated with the item.", "is_primary_key": false},
      {"slug": "is_human_scored", "type": "INTEGER_UNSIGNED", "caption": "Is Human Scored", "description": "Flag indicating if the item is human scored.", "is_primary_key": false},
    ],
    method: 'query',
    methodConfig: {
      querySlug: 'SQL_03_ASMT_CONTENT_ITEM_REGISTER_CONSOLIDATED',
    },
    dependencySourcings: [
      {
        param: "twtar_ids",
        type: "asset-col",
        config: {
          asset: "load_twtar",
          col: "twtar_id",
        }
      },
    ]
  },
  {
    slug: "load_tqr_generic_param_map",
    "caption": "tqr_generic_param_map",
    description: "The generic parameter mapping for item register (TQR)",
    structure: "dataframe",
    schema: [
      {"slug": "tqr_gen_param_id", "type": "INTEGER_UNSIGNED", "caption": "Test Question Register Generic Param Id", "description": "Identifier for the generic parameter of the item register.", "is_primary_key": true},
      {"slug": "tqr_col", "type": "STRING", "caption": "Test Question Register", "description": "Column mapped to the Item Register column name.", "is_primary_key": false},
      {"slug": "param_type", "type": "STRING", "caption": "Parameter Type", "description": "Type of the generic parameter.", "is_primary_key": false},
      {"slug": "item_bank_code", "type": "STRING", "caption": "Item Bank Code", "description": "Code that should be used for the column in the item register.", "is_primary_key": false},
    ],
    method: 'query',
    methodConfig: {
      querySlug: 'SQL_03_ASMT_CONTENT_TQR_GENERIC_PARAM_MAP',
    },
    dependencySourcings: [] // TODO: Should this take item_bank_codes as input?
  },
  {
    slug: "load_tqr_params",
    "caption": "tqr_params",
    description: "Generic parameter values from the test question register",
    structure: "dataframe",
    schema: [
      {"slug": "tqr_id", "type": "INTEGER_UNSIGNED", "caption": "Test Question Register Id", "description": "Identifier for the test question register record.", "is_primary_key": false},
    ],
    method: 'query',
    methodConfig: {
      querySlug: 'SQL_03_ASMT_CONTENT_TQR_PARAMS',
    },
    dependencySourcings: [
      { param: "tqr_ids",
        type: "asset-col",
        config: {
          asset: "load_item_register_consolidated",
          col: "tqr_id",
        },
      },
      { param: "tqr_generic_param_map",
        type: "dataframe",
        config: {
          asset: "load_tqr_generic_param_map",
        }
      }
    ]
  },
  {
    slug: 'load_formatted_responses',
    "caption": "formatted_responses",
    description: "Formatted responses for all taqr entries.",
    structure: "dataframe",
    schema: [
      {"slug": "response_type", "type": "STRING", "caption": "Response Type", "description": "Type of response provided.", "is_primary_key": false},
      {"slug": "is_nr", "type": "INTEGER_UNSIGNED", "caption": "Is No Response", "description": "Flag indicating if the response is NR (Non Response).", "is_primary_key": false},
      {"slug": "formatted_response_strict", "type": "STRING", "caption": "Formatted Response Strict", "description": "Strictly formatted version of the response.", "is_primary_key": false},
      {"slug": "response_id", "type": "INTEGER_UNSIGNED", "caption": "Response Id", "description": "Unique identifier for the specific response.", "is_primary_key": true},
      {"slug": "formatted_response", "type": "STRING", "caption": "Formatted Response", "description": "Processed or cleaned version of the response.", "is_primary_key": false},
      {"slug": "score", "type": "INTEGER_UNSIGNED", "caption": "Score", "description": "Score awarded for the given response.", "is_primary_key": false},
    ],
    method: 'api-request-chunked',
    methodConfig: {
      endpoint: "public/test-ctrl/schools/student-responses",
      method: "POST",
      props: {},
      data: {
        isRawIncluded: false,
        taqr_ids: [],   // TODO: need to be able to config the base data, and the data fields sourced from dependencies.
      },
      chunkedParam: 'taqr_ids',
      chunkSize: 1000,
    },
    partitionBy: {
      key: 'partby_ta_ids',
    },
    dependencySourcings: [
      { param: "taqr_ids",
        type: "asset-col",
        config: {
          asset: "load_taqr",
          col: "taqr_id",
        },
      },
    ],
  },
  {
    slug: 'trfm_item_responses',
    "caption": "item_responses",
    description: "Unfiltered item responses including score, weight, and formatted responses.",
    structure: "dataframe",
    schema: [
      {"slug": "ta_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": true},
      {"slug": "item_id", "type": "STRING", "caption": "Item Id", "description": "Identifier for the item.", "is_primary_key": true},
      {"slug": "taqr_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Question Response ID", "description": "Identifier for the test attempt question response.", "is_primary_key": false, "is_nullable": true},
      {"slug": "weight", "type": "INTEGER_UNSIGNED", "caption": "Weight", "description": "Weight for the test attempt question response.", "is_primary_key": false},
      {"slug": "is_nr", "type": "INTEGER_UNSIGNED", "caption": "Is No Response", "description": "Flag indicating if the response is NR (Non Response).", "is_primary_key": false},
      {"slug": "formatted_response", "type": "STRING", "caption": "Formatted Response", "description": "Processed or cleaned version of the response.", "is_primary_key": false},
      {"slug": "formatted_response_strict", "type": "STRING", "caption": "Formatted Response Strict", "description": "Strictly formatted version of the response.", "is_primary_key": false},
      {"slug": "score", "type": "INTEGER_UNSIGNED", "caption": "Score", "description": "Score awarded for the given response.", "is_primary_key": false},
      {"slug": "is_invalid", "type": "INTEGER_UNSIGNED", "caption": "Is Invalid", "description": "Flag indicating whether the test attempt is invalid.", "is_primary_key": false},
      {"slug": "response_type", "type": "STRING", "caption": "Response Type", "description": "Type of response provided.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "join",
          df_output: "df",
          config: {
            how: "inner",
            left: "load_taqr",
            right: "load_formatted_responses",
            left_on: "taqr_id",
            right_on: "response_id",
          },
        },
        {
          kind: "rename-cols",
          df_output: "df",
          config: {
            df_input: "df",
            new_names: {
              "score_right": "score",
              "is_nr_right": "is_nr"
            },
            inplace: true
          }
        },
        // Merge with Item Expected Answers to get coded_response
        {
          kind: "restrict-cols",
          df_output: "coded_responses",
          config: {
            df_input: "coded_responses",
            cols: ["item_id", "formatted_response", "lang", "coded_response"]
          },
        },
        {
          kind: "join",
          df_output: "df",
          config: {
            how: "left",
            left: "df",
            right: "coded_responses",
            left_on: ["item_id", "formatted_response", "lang"],
            right_on: ["item_id", "formatted_response", "lang"],
          }
        },
      ],
      output: "df"
    },
    partitionBy: {
      key: 'partby_ta_ids',
      concatOut: false,
    },
    dependencySourcings: [
      { param: "load_taqr",
        type: "dataframe",
        config: {
          asset: "load_taqr",
          cols: undefined,
        }
      },
      { param: "load_formatted_responses",
        type: "dataframe",
        config: {
          asset: "load_formatted_responses",
        }
      },
      { param: "coded_responses",
        type: "dataframe",
        config: {
          asset: "trfm_coded_responses",
        }
      }
    ],
  },
  {
    slug: 'trfm_item_responses_td',
    scopes: ["test-centres"],
    "caption": "item_responses_td",
    description: "Item responses with test design info",
    structure: "dataframe",
    partitionBy: {
      key: 'partby_ta_ids',
      concatOut: true,
    },
    method: 'transforms',
    methodConfig: {
      sequence: [{
        kind: "join",
        df_output: "resp_ta",
        config: {
          how: "inner",
          left: "item_resp",
          right: "ta",
          left_on: "ta_id",
          right_on: "attempt_id",
        },
      },
        {
          kind: "join",
          df_output: "result",
          config: {
            how: "inner",
            left: "resp_ta",
            right: "twtar",
            left_on: "twtar_id",
            right_on: "twtar_id",
          },
        },
      ],
      output: "result"
    },
    dependencySourcings: [
      { param: "item_resp",
        type: "dataframe",
        config: {
          asset: "trfm_item_responses",
        },
      },
      { param: "ta",
        type: "dataframe",
        config: {
          asset: "load_ta_from_tw",
          cols: ["attempt_id", "twtar_id", "uid"],
        },
      },
      { param: "twtar",
        type: "dataframe",
        config: {
          asset: "load_twtar",
          cols: ["twtar_id", "td_id", "type_slug", "form_code"],
        },
      },
    ],
  },
  {
    slug: 'load_test_reports',
    scopes: ["test-centres"],
    "caption": "test_reports",
    description: "Load test reports based on the test attempt ids",
    structure: "dataframe",
    method: 'query',
    methodConfig: {
      querySlug: 'SQL_05A_ATTEMPTS_TEST_REPORTS',
    },
    dependencySourcings: [
      {
        param: "ta_ids",
        type: "asset-col",
        config: {
          asset: "load_ta_from_tw",
          col: "attempt_id",
        }
      },
    ]
  },
  {
    slug: 'trfm_result_distribution',
    scopes: ["test-centres"],
    caption: "result_distribution",
    description: "Result distribution for test reports",
    structure: "dataframe",
    method: 'transforms',
    methodConfig: {
      sequence: [{
        kind: "join",
        df_output: "reports_ta",
        config: {
          how: "inner",
          left: "test_reports",
          right: "ta",
          left_on: "test_attempt_id",
          right_on: "attempt_id",
        },
      },
        {
          kind: "join",
          df_output: "merged_df",
          config: {
            how: "inner",
            left: "reports_ta",
            right: "twtar",
            left_on: "twtar_id",
            right_on: "twtar_id",
          },
        },
        {
          kind: "group-by",
          df_output: "result",
          config: {
            df_input: "merged_df",
            group_by: ["type_slug", "form_code", "result_code"],
            agg: [{
              col_new: "count",
              agg_type: "count",
              col_target: "attempt_id",
            }]
          },
        },
      ],
      output: "result"
    },
    dependencySourcings: [
      { param: "test_reports",
        type: "dataframe",
        config: {
          asset: "load_test_reports",
        },
      },
      { param: "ta",
        type: "dataframe",
        config: {
          asset: "load_ta_from_tw",
          cols: ["attempt_id", "twtar_id"],
        },
      },
      { param: "twtar",
        type: "dataframe",
        config: {
          asset: "load_twtar",
          cols: ["twtar_id", "form_code"],
        },
      },
    ],
  },
  {
    slug: 'trfm_item_response_value_stats',
    scopes: ["test-centres"],
    "caption": "item_response_value_stats",
    description: "Response value stats for test centres",
    structure: "dataframe",
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "filter-col",
          df_output: "item_responses_clean", // todo: this should be filtered in a prior asset...
          config: {
            df_input: "item_resp",
            col: 'item_id',
            comparison: 'in',
            value_src: 'item_ids'
          }
        },
        {
          kind: "group-by",
          df_output: "items_n",
          config: {
            df_input: "item_responses_clean",
            group_by: ["item_id", "formatted_response", "score"],
            agg:[{
              col_new: "n",
              agg_type: "count",
            }],
          },
        },
        {
          kind: "group-by",
          df_output: "items_count",
          config: {
            df_input: "item_responses_clean",
            group_by: ["type_slug", "item_id", "form_code"],
            agg:[{
              col_new: "count",
              agg_type: "count",
            }],
          },
        },
        {
          kind: "restrict-cols",
          df_output: "item_info",
          config: {
            df_input: "items_count",
            cols: ["type_slug", "item_id", "form_code"],
          },
        },
        {
          kind: "join",
          df_output: "responses_n",
          config: {
            how: "inner",
            left: "item_info",
            right: "items_n",
            left_on: "item_id",
            right_on: "item_id",
          },
        },
      ],
      output: "responses_n"
    },
    dependencySourcings: [
      { param: "item_resp",
        type: "dataframe",
        config: {
          asset: "trfm_item_responses_td",
        },
      },
      {
        param: "item_ids",
        type: "asset-col",
        config: {
          "asset": "load_item_register_consolidated",
          "col": "item_id"
        }
      },
      { param: "item_register",
        type: "dataframe",
        config: {
          asset: "load_item_register_consolidated",
          cols: ["twtar_id", "item_nbr"]
        },
      },
    ],
  },
  {
    slug: "trfm_item_scale_stats",
    scopes: ["test-centres"],
    caption: "item_scale_stats",
    description: "Item scale stats for test centres",
    structure: "dataframe",
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "filter-col",
          df_output: "item_responses_clean", // todo: this should be filtered in a prior asset...
          config: {
            df_input: "item_responses",
            col: 'item_id',
            comparison: 'in',
            value_src: 'item_ids'
          }
        },
        {
          kind: "group-by",
          df_output: "item_mean_scores",
          config: {
            df_input: "item_responses_clean",
            group_by: ["form_code", "item_id"],
            agg: [{
              col_new: "p",
              agg_type: "mean",
              col_target: "score",
            },
              {
                col_new: "n",
                agg_type: "count",
              },
              {
                col_new: "n_nr",
                agg_type: "sum",
                col_target: "is_nr"
              }
            ],
          },
        },
        {
          kind: "group-by",
          df_output: "item_responses_count",
          config: {
            df_input: "item_responses_clean",
            group_by: ["td_id", "item_id", "type_slug"],
            agg: [{
              col_new: "count",
              agg_type: "count",
            }],
          },
        },
        {
          kind: "restrict-cols",
          df_output: "item_info",
          config: {
            df_input: "item_responses_count",
            cols: ["td_id", "item_id", "type_slug"],
          },
        },
        {
          kind: "join",
          df_output: "responses_mean_score",
          config: {
            how: "inner",
            left: "item_info",
            right: "item_mean_scores",
            left_on: "item_id",
            right_on: "item_id",
          },
        },
      ],
      output: "responses_mean_score"
    },
    dependencySourcings: [
      { param: "item_responses",
        type: "dataframe",
        config: {
          asset: "trfm_item_responses_td",
          cols: ["td_id", "item_id", "type_slug", "is_nr"],
        },
      },
      {
        param: "item_ids",
        type: "asset-col",
        config: {
          "asset": "load_item_register_consolidated",
          "col": "item_id"
        }
      },
      { param: "item_register",
        type: "dataframe",
        config: {
          asset: "load_item_register_consolidated",
          cols: ["item_id", "item_nbr"]
        },
      },
    ],
  },
  {
    slug: 'trfm_attempts_by_form_code',
    scopes: ["schools"],
    caption: "attempts_by_form_code",
    description: "Test attempts by form code",
    structure: "dataframe",
    method: 'transforms',
    methodConfig: {
      sequence: [{
        kind: "group-by",
        df_output: "result",
        config: {
          df_input: "attempts",
          group_by: ["assessment_code", "form_code", "started_on_date"],
          agg: [{
            col_new: "n_students",
            agg_type: "nunique",
            col_target: "student_uid",
          }]
        },
      },
      ],
      output: "result"
    },
    dependencySourcings: [
      { param: "attempts",
        type: "dataframe",
        config: {
          asset: "load_ta_detail",
        },
      },
    ],
  },
  // Student registrations
  {
    "slug": "load_db_schools",
    "caption": "Schools",
    "description": "All schools with registered classes in the assessment",
    "structure": "dataframe",
    "method": "query",
    "schema": [
      {"slug": "s_id", "type": "INTEGER_UNSIGNED", "caption": "School Id", "description": "Unique identifier for the school.", "is_primary_key": true},
      {"slug": "s_gid", "type": "INTEGER_UNSIGNED", "caption": "School Group Id", "description": "Identifier for the group or cluster to which the school belongs.", "is_primary_key": false},
      {"slug": "s_code", "type": "STRING", "caption": "School Code", "description": "Code representing the school.", "is_primary_key": false},
      {"slug": "s_name", "type": "STRING", "caption": "School Name", "description": "Full name of the school.", "is_primary_key": false},
      {"slug": "s_lang", "type": "STRING", "caption": "School Language", "description": "Language of instruction used at the school.", "is_primary_key": false},
      {"slug": "s_city", "type": "STRING", "caption": "School City", "description": "City where the school is located.", "is_primary_key": false},
      {"slug": "s_country", "type": "STRING", "caption": "School Country", "description": "Country where the school is located.", "is_primary_key": false},
      {"slug": "sd_id", "type": "INTEGER_UNSIGNED", "caption": "School District Id", "description": "Identifier for the school district.", "is_primary_key": false},
      {"slug": "sd_gid", "type": "INTEGER_UNSIGNED", "caption": "School District Group Id", "description": "Identifier for the school district group.", "is_primary_key": false},
      {"slug": "sd_code", "type": "STRING", "caption": "School District Code", "description": "Code representing the school district.", "is_primary_key": false},
      {"slug": "sd_name", "type": "STRING", "caption": "School District Name", "description": "Full name of the school district.", "is_primary_key": false},
      {"slug": "sd_lang", "type": "STRING", "caption": "School District Language", "description": "Language used in the school district.", "is_primary_key": false},
      {"slug": "is_sample", "type": "INTEGER_UNSIGNED", "caption": "Is Sample", "description": "Flag indicating whether the school is a sample school for testing or pilot purposes.", "is_primary_key": false},
      {"slug": "is_sandbox", "type": "INTEGER_UNSIGNED", "caption": "Is Sandbox", "description": "Flag indicating whether the school is a sandbox instance.", "is_primary_key": false},
    ],
    "methodConfig": {
      "querySlug": "SQL_04A_GROUPS_SCHOOLS"
    },
    "dependencySourcings": [
      {
        "param": "include_sample_schools",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "include_sample_schools"
        }
      }
    ]
  },
  {
    "slug": "load_db_school_groupings",
    "caption": "school_groupings",
    "description": "Loads groupings associated with an administration window (including those that have since been deleted)",
    "structure": "dataframe",
    "schema": [
      {"slug": "sc_id", "caption": "School Class Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the school class.", },
      {"slug": "sc_gid", "caption": "School Class Group Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the school class group.", },
      {"slug": "s_gid", "caption": "School Group Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the school group.", },
      {"slug": "sc_name", "caption": "School Class Name", "type": "STRING", "description": "Name of the school class.", },
      {"slug": "access_code", "caption": "Access Code", "type": "STRING", "description": "Access code associated with the school class.", },
      {"slug": "is_active", "caption": "Is Active", "type": "INTEGER_UNSIGNED", "description": "Flag indicating if the grouping is active.", },
      {"slug": "is_placeholder", "caption": "Is Placeholder", "type": "INTEGER_UNSIGNED", "description": "Indicates if the school class is a placeholder.", },
      {"slug": "semester_id", "caption": "Semester Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the semester associated with the school class", },
      {"slug": "created_on", "caption": "Created On", "type": "STRING", "description": "Date when the school class was created.", },
      {"slug": "deactivated_on", "caption": "Deactivated On", "type": "STRING", "description": "Date when the school class was deactivated.", },
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_04B_REGISTRATIONS_SCHOOL_GROUPINGS"
    },
    "dependencySourcings": [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      },
      {
        "param": "s_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_schools",
          "col": "s_id"
        }
      },
    ]
  },
  {
    "slug": "load_db_school_classes_guest",
    "caption": "school_classes_guest",
    "description": "Loads groupings for guest stuent classes associated with an administration window",
    "structure": "dataframe",
    "schema": [
      {"slug": "id", "type": "INTEGER_UNSIGNED", "caption": "Id", "description": "Identifier for the school classes guest record.", "is_primary_key": true},
      {"slug": "invig_sc_group_id", "type": "INTEGER_UNSIGNED", "caption": "Invigilator School Group Id", "description": "Identifier for the invigilator school grouping.", "is_primary_key": false, is_nullable: false, fk_table_slug: "load_db_students", fk_field_slug: "uid"},
      {"slug": "guest_sc_group_id", "type": "INTEGER_UNSIGNED", "caption": "Guest School Group Id", "description": "Identifier for the guest school grouping.", "is_primary_key": false, is_nullable: false, fk_table_slug: "load_db_school_groupings", fk_field_slug: "sc_gid"},
      {"slug": "created_on", "type": "STRING", "caption": "Created On", "description": "Timestamp indicating when the registration was created.", "is_primary_key": false},
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_04B_REGISTRATIONS_SCHOOL_CLASSES_GUEST"
    },
    "dependencySourcings": [
      {
        "param": "sc_gids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_school_groupings",
          "col": "sc_gid"
        }
      }
    ]
  },
  {
    "slug": "load_db_guest_student_grouping_registrations",
    "caption": "guest_student_grouping_registrations",
    "description": "Loads registration linkage ids for guest students",
    "structure": "dataframe",
    "schema": [
      {"slug": "ur_id", "type": "INTEGER_UNSIGNED", "caption": "User Role Id", "description": "Identifier for the user role.", "is_primary_key": true},
      {"slug": "uid", "type": "INTEGER_UNSIGNED", "caption": "User Id", "description": "Identifier for the user associated with the registration.", "is_primary_key": false, is_nullable: false, fk_table_slug: "load_db_students", fk_field_slug: "uid"},
      {"slug": "sc_gid", "type": "INTEGER_UNSIGNED", "caption": "School Class Group Id", "description": "Identifier for the school class group to which the user is registered.", "is_primary_key": false, is_nullable: false, fk_table_slug: "load_db_school_groupings", fk_field_slug: "sc_gid"},
      {"slug": "created_on", "type": "STRING", "caption": "Created On", "description": "Timestamp indicating when the registration was created.", "is_primary_key": false},
      {"slug": "is_walkin", "type": "BOOL_INT", "caption": "Is Walk-in", "description": "Flag indicating if the test taker is a walk-in student", "is_primary_key": false},
      {"slug": "is_revoked", "type": "BOOL_INT", "caption": "Is Revoked", "description": "Flag indicating whether the registration has been revoked.", "is_primary_key": false},
      {"slug": "revoked_on", "type": "STRING", "caption": "Revoked On", "description": "Timestamp indicating when the registration was revoked.", "is_primary_key": false},
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_04B_REGISTRATIONS_STU_G_REGSTRATIONS"
    },
    "dependencySourcings": [
      {
        "param": "sc_gids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_school_classes_guest",
          "col": "guest_sc_group_id"
        }
      }
    ]
  },
  {
    "slug": "load_db_student_grouping_registrations",
    "caption": "student_grouping_registrations",
    "description": "Loads enrolled (or formerly enrolled) students linkage ids",
    "structure": "dataframe",
    "schema": [
      {"slug": "ur_id", "type": "INTEGER_UNSIGNED", "caption": "User Role Id", "description": "Identifier for the user role.", "is_primary_key": true},
      {"slug": "uid", "type": "INTEGER_UNSIGNED", "caption": "User Id", "description": "Identifier for the user associated with the registration.", "is_primary_key": false, is_nullable: false, fk_table_slug: "load_db_students", fk_field_slug: "uid"},
      {"slug": "sc_gid", "type": "INTEGER_UNSIGNED", "caption": "School Class Group Id", "description": "Identifier for the school class group to which the user is registered.", "is_primary_key": false, is_nullable: false, fk_table_slug: "load_db_school_groupings", fk_field_slug: "sc_gid"},
      {"slug": "created_on", "type": "STRING", "caption": "Created On", "description": "Timestamp indicating when the registration was created.", "is_primary_key": false},
      {"slug": "is_walkin", "type": "BOOL_INT", "caption": "Is Walk-in", "description": "Flag indicating if the test taker is a walk-in student", "is_primary_key": false},
      {"slug": "is_revoked", "type": "BOOL_INT", "caption": "Is Revoked", "description": "Flag indicating whether the registration has been revoked.", "is_primary_key": false},
      {"slug": "revoked_on", "type": "STRING", "caption": "Revoked On", "description": "Timestamp indicating when the registration was revoked.", "is_primary_key": false},
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_04B_REGISTRATIONS_STU_G_REGSTRATIONS"
    },
    "dependencySourcings": [
      {
        "param": "sc_gids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_school_groupings",
          "col": "sc_gid"
        }
      }
    ]
  },
  {
    slug: "trfm_student_grouping_registrations",
    caption: "student_grouping_registrations",
    description: "",
    structure: "dataframe",
    "schema": [
      {"slug": "ur_id", "type": "INTEGER_UNSIGNED", "caption": "User Role Id", "description": "Identifier for the user role.", "is_primary_key": true},
      {"slug": "uid", "type": "INTEGER_UNSIGNED", "caption": "User Id", "description": "Identifier for the user associated with the registration.", "is_primary_key": false, is_nullable: false, fk_table_slug: "load_db_students", fk_field_slug: "uid"},
      {"slug": "sc_gid", "type": "INTEGER_UNSIGNED", "caption": "School Class Group Id", "description": "Identifier for the school class group to which the user is registered.", "is_primary_key": false, is_nullable: false, fk_table_slug: "load_db_school_groupings", fk_field_slug: "sc_gid"},
      {"slug": "created_on", "type": "STRING", "caption": "Created On", "description": "Timestamp indicating when the registration was created.", "is_primary_key": false},
      {"slug": "is_walkin", "type": "BOOL_INT", "caption": "Is Walk-in", "description": "Flag indicating if the test taker is a walk-in student", "is_primary_key": false},
      {"slug": "is_revoked", "type": "BOOL_INT", "caption": "Is Revoked", "description": "Flag indicating whether the registration has been revoked.", "is_primary_key": false},
      {"slug": "revoked_on", "type": "STRING", "caption": "Revoked On", "description": "Timestamp indicating when the registration was revoked.", "is_primary_key": false},
      {"slug": "is_guest_stu_grouping", "type": "BOOL_INT", "caption": "Is Guest Student Grouping", "description": "Flag indicating whether the grouping is for a guest student.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          "kind": "fill-na",
          "df_output": "stu_g_registrations",
          "config": {
            "df_input": "stu_g_registrations",
            "values": {
              "is_guest_stu_grouping": 0,
            },
            "inplace": true,
          }
        },
        {
          "kind": "fill-na",
          "df_output": "guest_stu_g_registrations",
          "config": {
            "df_input": "guest_stu_g_registrations",
            "values": {
              "is_guest_stu_grouping": 1,
            },
            "inplace": true,
          }
        },
        {
          "kind": "concat",
          "df_output": "stu_g_registrations",
          "config": {
            "df_inputs": ["stu_g_registrations", "guest_stu_g_registrations"]
          }
        }
      ],
      output: "stu_g_registrations"
    },
    dependencySourcings: [
      { param: "stu_g_registrations",
        type: "dataframe",
        config: {
          asset: "load_db_student_grouping_registrations",
        },
      },
      { param: "guest_stu_g_registrations",
        type: "dataframe",
        config: {
          asset: "load_db_guest_student_grouping_registrations",
        },
      }
    ],
  },
  {
    "slug": "load_db_students",
    "caption": "students",
    "description": "Loads enrolled (or formerly enrolled) students who could have accessed the assessment",
    "structure": "dataframe",
    "schema": [
      {"slug": "uid", "caption": "User Id", "type": "INTEGER_UNSIGNED", "description": "Unique identifier for the student.", },
      {"slug": "stu_gov_id", "caption": "Student Government Id", "type": "STRING", "description": "Alberta Student Government ID.", },
      {"slug": "first_name", "caption": "First Name", "type": "STRING", "description": "First name of the student.", },
      {"slug": "last_name", "caption": "Last Name", "type": "STRING", "description": "Last name of the student.", },
      {"slug": "is_sis_sync", "caption": "Is System Sync", "type": "INTEGER_UNSIGNED", "description": "Indicates if the student is synced", },
      {"slug": "stu_dob", "caption": "Student Date Of Birth", "type": "STRING", "description": "Date of birth of the student.", },
    ],
    "method": "query-chunked",
    "methodConfig": {
      "querySlug": "SQL_04B_REGISTRATIONS_STUDENTS",
      "chunkSize": 1000,
      "chunkedParam": "uids",
      "makeDistinct": true,
    },
    "dependencySourcings": [
      {
        "param": "uids",
        "type": "asset-col",
        "config": {
          "asset": "trfm_student_grouping_registrations",
          "col": "uid"
        }
      }
    ]
  },
  // New for exports
  {
    "slug": "load_test_attempts_from_uid",
    "caption": "test_attempts",
    "description": "Loads all test attempts for enrolled students",
    "structure": "dataframe",
    "schema": [
      {"slug": "ta_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": true},
      {"slug": "uid", "type": "INTEGER_UNSIGNED", "caption": "User Id", "description": "Identifier for the user who attempted the test.", "is_primary_key": false},
      {"slug": "test_session_id", "type": "INTEGER_UNSIGNED", "caption": "Test Session Id", "description": "Identifier for the test session associated with the attempt.", "is_primary_key": false},
      {"slug": "lang", "type": "STRING", "caption": "Language", "description": "Language in which the test was conducted.", "is_primary_key": false},
      {"slug": "test_form_id", "type": "INTEGER_UNSIGNED", "caption": "Test Form Id", "description": "Identifier for the test form.", "is_primary_key": false},
      {"slug": "created_on", "type": "STRING", "caption": "Created On", "description": "Timestamp indicating when the test attempt record was created.", "is_primary_key": false},
      {"slug": "started_on", "type": "STRING", "caption": "Started On", "description": "Timestamp indicating when the test attempt was started.", "is_primary_key": false},
      {"slug": "is_closed", "type": "INTEGER_UNSIGNED", "caption": "Is Closed", "description": "Flag indicating whether the test attempt is closed.", "is_primary_key": false},
      {"slug": "closed_on", "type": "STRING", "caption": "Closed On", "description": "Timestamp indicating when the test attempt was closed.", "is_primary_key": false},
      {"slug": "time_ext_m", "type": "INTEGER_UNSIGNED", "caption": "Time Extension (Minutes)", "description": "Additional time granted for the test attempt, measured in minutes.", "is_primary_key": false},
      {"slug": "is_invalid", "type": "INTEGER_UNSIGNED", "caption": "Is Invalid", "description": "Flag indicating whether the test attempt is invalid.", "is_primary_key": false},
      {"slug": "last_updated_by_uid", "type": "INTEGER_UNSIGNED", "caption": "Last Updated By Uid", "description": "Identifier for the user who last updated the test attempt record.", "is_primary_key": false},
      {"slug": "twtdar_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window TD Alloc Rule ID", "description": "Identifier for the Test Window TD Allocation Rule associated with the test attempt.", "is_primary_key": false},
    ],
    "method": "query-chunked",
    "methodConfig": {
      "querySlug": "SQL_05A_ATTEMPTS_FROM_UID",
      "chunkSize": 1000,
      "chunkedParam": "uids",
      "makeDistinct": true,
    },
    "dependencySourcings": [
      {
        "param": "uids",
        "type": "asset-col",
        "config": {
          "asset": "trfm_student_grouping_registrations",
          "col": "uid"
        }
      },
      {
        "param": "twtar_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_twtar",
          "col": "twtar_id"
        }
      }
    ]
  },
  {
    slug: "trfm_check_attempts_from_uid",
    caption: "check_attempts_from_uid",
    description: "Checks attempt_ids that are in the test window (load_ta_detail), but not linked to uids in the registrations assets (load_test_attempts_from_uid)",
    structure: "dataframe",
    isCheckAsset: true,
    severity: CheckAssetSeverity.WARNING,
    schema: [
      {"slug": "attempt_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": true},
      {"slug": "student_uid", "type": "INTEGER_UNSIGNED", "caption": "Student User Id", "description": "Foreign key referencing the users table.", "is_primary_key": false},
      {"slug": "student_gov_id", "type": "INTEGER_UNSIGNED", "caption": "Student Government ID", "description": "Alberta Student Government ID.", "is_primary_key": false},
      {"slug": "s_code", "type": "STRING", "caption": "School Code", "description": "Code of the school.", "is_primary_key": false},
      {"slug": "sc_id", "type": "INTEGER_UNSIGNED", "caption": "School Class Id", "description": "Identifier for the school class.", "is_primary_key": false},
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": false},
      {"slug": "started_on_date", "type": "DATETIME", "caption": "Started On Date", "description": "Date and time when the test attempt was started.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "restrict-cols",
          df_output: "ta_detail",
          config: {
            df_input: "ta_detail",
            cols: ["attempt_id", "student_uid", "student_gov_id", "s_code", "sc_id", "assessment_code", "started_on_date"]
          },
        },
        {
          kind: "filter-col",
          df_output: "ta_detail",
          config: {
            df_input: "ta_detail",
            col: "attempt_id",
            comparison: "not-in",
            value_src: "ta_ids",
          },
        }
      ],
      output: "ta_detail"
    },
    dependencySourcings: [
      { param: "ta_detail",
        type: "dataframe",
        config: {
          asset: "load_ta_detail",
        },
      },
      {
        "param": "ta_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_test_attempts_from_uid",
          "col": "ta_id",
        }
      },
    ],
  },
  {
    slug: "trfm_check_pk_uids_in_students",
    caption: "check_pk_uids_in_students",
    description: "Checks if all students in principal_kit_students are present in load_students",
    structure: "dataframe",
    isCheckAsset: true,
    severity: CheckAssetSeverity.WARNING,
    schema: [
      {"slug": "asmt_code", "caption": "Assessment Code", "type": "STRING", "description": "Code for the assessment", },
      {"slug": "student_uid", "caption": "Student User Id", "type": "INTEGER_UNSIGNED", "description": "User id of the student", },
      {"slug": "stu_gov_id", "caption": "Student Government Id", "type": "STRING", "description": "Government ID of the student", },
      {"slug": "s_code", "caption": "School Code", "type": "STRING", "description": "School Code", },
      {"slug": "s_group_id", "caption": "School Group Id", "type": "INTEGER_UNSIGNED", "description": "School Group Id", },
      {"slug": "s_id", "caption": "School Id", "type": "INTEGER_UNSIGNED", "description": "School Id", },
      {"slug": "test_window_id", "caption": "Test Window Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window", },
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "restrict-cols",
          df_output: "principal_kit_students",
          config: {
            df_input: "principal_kit_students",
            cols: ["asmt_code", "student_uid", "stu_gov_id", "s_code", "s_group_id", "s_id", "test_window_id"]
          },
        },
        {
          kind: "filter-col",
          df_output: "principal_kit_students",
          config: {
            df_input: "principal_kit_students",
            col: "student_uid",
            comparison: "not-in",
            value_src: "uids",
          },
        }
      ],
      output: "principal_kit_students"
    },
    dependencySourcings: [
      { param: "principal_kit_students",
        type: "dataframe",
        config: {
          asset: "trfm_principal_kit_students",
        },
      },
      {
        "param": "uids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_students",
          "col": "uid",
        }
      },
    ],
  },
  {
    "slug": "load_db_marking_not_pooled_attempts",
    "caption": "Attempts that are not pooled",
    "description": "...",
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          kind: "restrict-cols",
          df_output: "df_twtar_type_slug",
          config: {
            df_input: "df_twtar",
            cols: ["twtar_id", "type_slug", "long_name"],
          },
        },
        {
          kind: "join",
          df_output: "df_attempts_w_twtar",
          config: {
            how: "inner",
            left: "df_attempts",
            right: "df_twtar_type_slug",
            left_on: "twtar_id",
            right_on: "twtar_id",
          },
        },
        {
          "kind": "filter-col",
          "df_output": "df_notpooled_attempts",
          "config": {
            "df_input": "df_attempts_w_twtar",
            "col": "attempt_id",
            "comparison": "not-in",
            "value_src": {"df": "df_pooled_attempts", "col": "ta_id"}
          }
        },
        {
          kind: "restrict-cols",
          df_output: "result",
          config: {
            df_input: "df_notpooled_attempts",
            cols: ["type_slug", "uid", "attempt_id", "ts_id", "sc_id", "s_id", "sd_id"],
          },
        },
      ],
      "output": "result"
    },
    "dependencySourcings": [
      {
        "param": "df_pooled_attempts",
        "type": "dataframe",
        "config": {
          "asset": "load_db_marking_pooled_attempts",
        }
      },
      {
        "param": "df_attempts",
        "type": "dataframe",
        "config": {
          "asset": "load_ta_from_tw",
        }
      },
      {
        "param": "df_twtar",
        "type": "dataframe",
        "config": {
          "asset": "load_twtar",
        }
      },
    ]
  },
  {
    "slug": "load_db_marking_not_pooled_attempts_with_no_withhold",
    "caption": "Attempts that are not pooled (and not withheld)",
    "description": "...",
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "filter-col",
          "df_output": "df_withholds",
          "config": {
            "df_input": "df_tw_student_exceptions",
            "col": "category",
            "comparison": "equals",
            "value": "WITHHOLD",
          }
        },
        {
          "kind": "filter-col",
          "df_output": "df_not_pooled_attempts_no_withhold",
          "config": {
            "df_input": "df_not_pooled_attempts",
            "col": "attempt_id",
            "comparison": "not-in",
            "value_src": {"df": "df_withholds", "col": "test_attempt_id"}
          }
        },
      ],
      "output": "df_not_pooled_attempts_no_withhold"
    },
    "dependencySourcings": [
      {
        "param": "df_not_pooled_attempts",
        "type": "dataframe",
        "config": {
          "asset": "load_db_marking_not_pooled_attempts",
        }
      },
      {
        "param": "df_tw_student_exceptions",
        "type": "dataframe",
        "config": {
          "asset": "load_tw_student_exceptions_withhold",
        }
      },
    ]
  },
  {
    "slug": "load_db_marking_not_pooled_attempts_with_no_withhold_by_asmt_code",
    "caption": "Summary of attempts that are not pooled (and not withheld) by Assessment Code",
    "description": "...",
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          kind: "group-by",
          df_output: "df_attempts_summary",
          config: {
            df_input: "df_attempts",
            group_by: ["type_slug"],
            agg: [
              {
                col_new: "n_missing_attempts",
                agg_type: "count",
              },
            ]
          },
        },
      ],
      "output": "df_attempts_summary"
    },
    "dependencySourcings": [
      {
        "param": "df_attempts",
        "type": "dataframe",
        "config": {
          "asset": "load_db_marking_not_pooled_attempts_with_no_withhold",
        }
      }
    ]
  },
  {
    "slug": "load_db_marking_pooled_attempts_by_asmt_code",
    "caption": "Count of Pooled Test Attempts by Assessment Code",
    "description": "Loads all test attempts that are at least partially pooled into non-sample marking windows (todo: should pull relevant)",
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          kind: "restrict-cols",
          df_output: "df_marking_window_asmt_code__r",
          config: {
            df_input: "df_marking_window_asmt_code",
            cols: ["marking_window_id", "twtar_type_slug"],
          },
        },
        {
          kind: "join",
          df_output: "df_pooled_attempts_with_asmt_code",
          config: {
            how: "inner",
            left: "df_pooled_attempts",
            right: "df_marking_window_asmt_code__r",
            left_on: "marking_window_id",
            right_on: "marking_window_id",
          },
        },
        {
          kind: "group-by",
          df_output: "df_pooled_attempts_by_asmt_code",
          config: {
            df_input: "df_pooled_attempts_with_asmt_code",
            group_by: ["twtar_type_slug"],
            agg: [
              {
                col_new: "n_attempts",
                agg_type: "count",
                col_target: "ta_id",
              },
              {
                col_new: "n_scales_avg",
                agg_type: "mean",
                col_target: "n_scales",
              },
              {
                col_new: "n_items_avg",
                agg_type: "mean",
                col_target: "n_items",
              }
            ]
          },
        },
      ],
      "output": "df_pooled_attempts_by_asmt_code"
    },
    "dependencySourcings": [
      {
        "param": "df_pooled_attempts",
        "type": "dataframe",
        "config": {
          "asset": "load_db_marking_pooled_attempts",
        }
      },
      {
        "param": "df_marking_window_asmt_code",
        "type": "dataframe",
        "config": {
          "asset": "load_db_marking_window_asmt_code",
        }
      },
    ]
  },
  {
    "slug": "load_db_marking_pooled_attempts",
    "caption": "Pooled Test Attempts",
    "description": "Loads all test attempts that are at least partially pooled into non-sample marking windows (todo: should pull relevant)",
    "structure": "dataframe",
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_06_MARKING_WINDOW_POOLED_ATTEMPTS",
      // "chunkedParam": "mw_ids",
      // "makeDistinct": true,
    },
    "dependencySourcings": [
      {
        "param": "mw_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_marking_windows",
          "col": "marking_window_id"
        }
      },
      // {
      //   "param": "uids",
      //   "type": "asset-col",
      //   "config": {
      //     "asset": "load_db_student_grouping_registrations",
      //     "col": "uid"
      //   }
      // },
      // {
      //   "param": "twtar_ids",
      //   "type": "asset-col",
      //   "config": {
      //     "asset": "load_twtar",
      //     "col": "twtar_id"
      //   }
      // }
    ]
  },
  {
    "slug": "load_db_marking_windows",
    "caption": "Marking Windows",
    "description": "...",
    "structure": "dataframe",
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_06_MARKING_WINDOWS"
    },
    "dependencySourcings": [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      }
    ]
  },
  {
    "slug": "load_db_marking_window_local_marking_missing_score",
    "caption": "Local Marking Missing Scores",
    "description": "...",
    "structure": "dataframe",
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_06_MARKING_WINDOW_LOCAL_MARKING_MISSING_SCORE"
    },
    "dependencySourcings": [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      }
    ]
  },
  {
    "slug": "load_db_marking_window_asmt_code",
    "caption": "Marking Window Assessment Codes",
    "description": "...",
    "structure": "dataframe",
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_06_MARKING_WINDOW_ASMT_CODE"
    },
    "dependencySourcings": [
      {
        "param": "mw_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_marking_windows",
          "col": "marking_window_id"
        }
      }
    ]
  },
  {
    "slug": "load_tw_student_exceptions_withhold",
    "caption": "student_exceptions_withhold",
    "description": "Loads WITHHOLD Student Exceptions for the test window",
    "structure": "dataframe",
    "schema": [
      {"slug": "id", "caption": "Id", "type": "INTEGER_UNSIGNED", "description": "Unique identifier for the student exception record.", },
      {"slug": "test_window_id", "caption": "Test Window Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window.", },
      {"slug": "uid", "caption": "User Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the student.", },
      {"slug": "twtar_type_slug", "caption": "Twtar Type Slug", "type": "STRING", "description": "Slug for the test window test allocation rule type.", },
      {"slug": "school_group_id", "caption": "School Group Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the school group.", },
      {"slug": "twtdar_id", "caption": "Test Window TD Alloc Rule ID", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window TD allocation rule.", },
      {"slug": "school_class_id", "caption": "School Class Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the school class", },
      {"slug": "category", "caption": "Category", "type": "STRING", "description": "Category of the exception", },
      {"slug": "action_config", "caption": "Action Config", "type": "STRING", "description": "Configuration for actions related to the exception.", },
      {"slug": "is_pended", "caption": "Is Pended", "type": "INTEGER_UNSIGNED", "description": "Flag indicating if the exception is pended.", },
      {"slug": "test_attempt_id", "caption": "Test Attempt Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test attempt.", },
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_04B_STUDENT_EXCEPTIONS",
      "categories": ["WITHHOLD"]
    },
    "dependencySourcings": [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      }
    ]
  },
  {
    "slug": "load_tw_student_exceptions_los_confirm",
    "caption": "student_exceptions_los_confirm",
    "description": "Loads LOS_CONFIRM Student Exceptions for the test window",
    "structure": "dataframe",
    "schema": [
      {"slug": "id", "caption": "Id", "type": "INTEGER_UNSIGNED", "description": "Unique identifier for the student exception record.", },
      {"slug": "test_window_id", "caption": "Test Window Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window.", },
      {"slug": "uid", "caption": "User Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the student.", },
      {"slug": "twtar_type_slug", "caption": "Twtar Type Slug", "type": "STRING", "description": "Slug for the test window test allocation rule type.", },
      {"slug": "school_group_id", "caption": "School Group Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the school group.", },
      {"slug": "twtdar_id", "caption": "Test Window TD Alloc Rule ID", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window TD allocation rule.", },
      {"slug": "school_class_id", "caption": "School Class Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the school class", },
      {"slug": "category", "caption": "Category", "type": "STRING", "description": "Category of the exception", },
      {"slug": "action_config", "caption": "Action Config", "type": "STRING", "description": "Configuration for actions related to the exception.", },
      {"slug": "is_pended", "caption": "Is Pended", "type": "INTEGER_UNSIGNED", "description": "Flag indicating if the exception is pended.", },
      {"slug": "test_attempt_id", "caption": "Test Attempt Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test attempt.", },
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_04B_STUDENT_EXCEPTIONS",
      "categories": ["LOS_CONFIRM"]
    },
    "dependencySourcings": [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      }
    ]
  },
  {
    "slug": "load_tw_student_exceptions_los_confirm_course",
    "caption": "student_exceptions_los_confirm_course",
    "description": "Loads LOS_CONFIRM_COURSE Student Exceptions for the test window",
    "structure": "dataframe",
    "schema": [
      {"slug": "id", "caption": "Id", "type": "INTEGER_UNSIGNED", "description": "Unique identifier for the student exception record.", },
      {"slug": "test_window_id", "caption": "Test Window Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window.", },
      {"slug": "uid", "caption": "User Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the student.", },
      {"slug": "twtar_type_slug", "caption": "Twtar Type Slug", "type": "STRING", "description": "Slug for the test window test allocation rule type.", },
      {"slug": "school_group_id", "caption": "School Group Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the school group.", },
      {"slug": "twtdar_id", "caption": "Test Window TD Alloc Rule ID", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window TD allocation rule.", },
      {"slug": "school_class_id", "caption": "School Class Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the school class", },
      {"slug": "category", "caption": "Category", "type": "STRING", "description": "Category of the exception", },
      {"slug": "action_config", "caption": "Action Config", "type": "STRING", "description": "Configuration for actions related to the exception.", },
      {"slug": "is_pended", "caption": "Is Pended", "type": "INTEGER_UNSIGNED", "description": "Flag indicating if the exception is pended.", },
      {"slug": "test_attempt_id", "caption": "Test Attempt Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test attempt.", },
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_04B_STUDENT_EXCEPTIONS",
      "categories": ["LOS_CONFIRM_COURSE"]
    },
    "dependencySourcings": [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      }
    ]
  },
  {
    "slug": "load_tw_item_exceptions",
    "caption": "exceptions_items_applied",
    "description": "Loads Item Exceptions for the test window",
    "structure": "dataframe",
    "schema": [
      {"slug": "is_score_override", "type": "INTEGER_UNSIGNED", "caption": "Is Score Override", "description": "Flag indicating whether the score for the item is overridden.", "is_primary_key": false},
      {"slug": "item_id", "type": "INTEGER_UNSIGNED", "caption": "Item Id", "description": "Identifier for the specific item associated with the exception.", "is_primary_key": false},
      {"slug": "item_label", "type": "STRING", "caption": "Item Label", "description": "Label or description for the item.", "is_primary_key": false},
      {"slug": "lang", "type": "STRING", "caption": "Language", "description": "Language for the item.", "is_primary_key": false},
      {"slug": "match_response_value", "type": "STRING", "caption": "Match Response Value", "description": "Specific response value that needs to match for the exception to apply.", "is_primary_key": false},
      {"slug": "score_override", "type": "INTEGER_UNSIGNED", "caption": "Score Override Value", "description": "Value to which the score is overridden for the item.", "is_primary_key": false},
      {"slug": "test_window_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window Id", "description": "Identifier for the test window associated with the exception.", "is_primary_key": false},
      {"slug": "is_nr_override", "type": "INTEGER_UNSIGNED", "caption": "Is No Response (NR) Override", "description": "Flag indicating whether a \"No Response\" override is applied to the item.", "is_primary_key": false},
      {"slug": "nr_override", "type": "INTEGER_UNSIGNED", "caption": "No Response (NR) Override Value", "description": "Value assigned to override a \"No Response\" for the item.", "is_primary_key": false},
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_05B_ITEM_EXCEPTIONS"
    },
    "dependencySourcings": [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      }
    ]
  },
  {
    "slug": "load_tw_student_item_exceptions",
    "caption": "exceptions_students_items_applied",
    "description": "Loads Student Item Exceptions for the test windows.",
    "structure": "dataframe",
    "schema": [
      {"slug": "id", "type": "INTEGER_UNSIGNED", "caption": "Id", "description": "identifier for the student exception.", "is_primary_key": true},
      {"slug": "test_window_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window Id", "description": "Identifier for the test window the exception is applied in.", "is_primary_key": false},
      {"slug": "uid", "type": "INTEGER_UNSIGNED", "caption": "User Id", "description": "Identifier for the student associated with the exception.", "is_primary_key": false},
      {"slug": "twtar_type_slug", "type": "STRING", "caption": "Twtar Type Slug", "description": "Type slug for the Test Window TD Allocation Rule.", "is_primary_key": false},
      {"slug": "school_group_id", "type": "INTEGER_UNSIGNED", "caption": "School Group Id", "description": "Identifier for the school group.", "is_primary_key": false},
      {"slug": "twtdar_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window TD Alloc Rule ID", "description": "Identifier for the Test Window TD Allocation Rule", "is_primary_key": false},
      {"slug": "school_class_id", "type": "INTEGER_UNSIGNED", "caption": "School Class Id", "description": "Identifier for the school class.", "is_primary_key": false},
      {"slug": "category", "type": "STRING", "caption": "Category", "description": "Category of the exception.", "is_primary_key": false},
      {"slug": "action_config", "type": "Null", "caption": "Action Config", "description": "Action Config", "is_primary_key": false},
      {"slug": "is_pended", "type": "INTEGER_UNSIGNED", "caption": "Is Pended", "description": "Flag to indicate if student attempt is pended", "is_primary_key": false},
      {"slug": "test_attempt_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Id", "description": "Identifier for the specific test attempt associated with the exception.", "is_primary_key": false},
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_05B_STUDENT_ITEM_EXCEPTIONS"
    },
    "dependencySourcings": [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      }
    ]
  },
  {
    "slug": "trfm_test_attempts_exceptions_applied",
    "caption": "test_attempts_exceptions_applied",
    "description": "Test attempts with student attempt exceptions removed.",
    "structure": "dataframe",
    "schema": [
      {"slug": "ta_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Id", "description": "Identifier for the test attempt where exceptions were applied.", "is_primary_key": true},
      {"slug": "uid", "type": "INTEGER_UNSIGNED", "caption": "User Id", "description": "Identifier for the user associated with the test attempt.", "is_primary_key": false},
      {"slug": "test_session_id", "type": "INTEGER_UNSIGNED", "caption": "Test Session Id", "description": "Identifier for the test session linked to the test attempt.", "is_primary_key": false},
      {"slug": "lang", "type": "STRING", "caption": "Language", "description": "Language for the test attempt.", "is_primary_key": false},
      {"slug": "test_form_id", "type": "INTEGER_UNSIGNED", "caption": "Test Form Id", "description": "Identifier for the test form.", "is_primary_key": false},
      {"slug": "created_on", "type": "STRING", "caption": "Created On", "description": "Timestamp indicating when the record was created.", "is_primary_key": false},
      {"slug": "started_on", "type": "STRING", "caption": "Started On", "description": "Timestamp indicating when the test attempt started.", "is_primary_key": false},
      {"slug": "is_closed", "type": "INTEGER_UNSIGNED", "caption": "Is Closed", "description": "Flag indicating whether the test attempt is closed.", "is_primary_key": false},
      {"slug": "closed_on", "type": "STRING", "caption": "Closed On", "description": "Timestamp indicating when the test attempt was closed.", "is_primary_key": false},
      {"slug": "time_ext_m", "type": "INTEGER_UNSIGNED", "caption": "Time Extension (Minutes)", "description": "Additional time granted for the test attempt, measured in minutes.", "is_primary_key": false},
      {"slug": "is_invalid", "type": "INTEGER_UNSIGNED", "caption": "Is Invalid", "description": "Flag indicating whether the test attempt is invalid.", "is_primary_key": false},
      {"slug": "last_updated_by_uid", "type": "INTEGER_UNSIGNED", "caption": "Last Updated By User Id", "description": "Identifier for the user who last updated the test attempt.", "is_primary_key": false},
      {"slug": "twtdar_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window TD Alloc Rule ID", "description": "Identifier for the Test Window TD Allocation Rule.", "is_primary_key": false},
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "filter-col",
          "df_output": "exceptions",
          "config": {
            "df_input": "exceptions",
            "col": "test_attempt_id",
            "comparison": "non-null",
          }
        },
        {
          "kind": "filter-col",
          "df_output": "test_attempts",
          "config": {
            "df_input": "test_attempts",
            "col": "ta_id",
            "comparison": "not-in",
            "value_src": {"df": "exceptions", "col": "test_attempt_id"}
          }
        },
      ],
      "output": "test_attempts"
    },
    "dependencySourcings": [
      {
        "param": "test_attempts",
        "type": "dataframe",
        "config": {
          "asset": "load_test_attempts_from_uid",
        },
      },
      {
        "param": "exceptions",
        "type": "dataframe",
        "config": {
          "asset": "load_tw_student_exceptions_withhold",
        },
      },
    ]
  },
  {
    "slug": "trfm_students_withhold_applied",
    "caption": "students_withhold_applied",
    "description": "Registered students after removing students with 'WITHHOLD' exceptions.",
    "structure": "dataframe",
    "schema": [
      {"slug": "uid", "caption": "User Id", "type": "INTEGER_UNSIGNED", "description": "Unique identifier for the student.", },
      {"slug": "stu_gov_id", "caption": "Student Government Id", "type": "STRING", "description": "Alberta Student Government ID.", },
      {"slug": "first_name", "caption": "First Name", "type": "STRING", "description": "First name of the student.", },
      {"slug": "last_name", "caption": "Last Name", "type": "STRING", "description": "Last name of the student.", },
      {"slug": "is_sis_sync", "caption": "Is System Sync", "type": "INTEGER_UNSIGNED", "description": "Indicates if the student is synced", },
      {"slug": "stu_dob", "caption": "Student Date Of Birth", "type": "STRING", "description": "Date of birth of the student.", },
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "filter-col",
          "df_output": "exceptions",
          "config": {
            "df_input": "exceptions",
            "col": "test_attempt_id",
            "comparison": "null",
          }
        },
        {
          "kind": "filter-col",
          "df_output": "exceptions",
          "config": {
            "df_input": "exceptions",
            "col": "category",
            "comparison": "equals",
            "value": "WITHHOLD",
          }
        },
        {
          "kind": "filter-col",
          "df_output": "students",
          "config": {
            "df_input": "students",
            "col": "uid",
            "comparison": "not-in",
            "value_src": {"df": "exceptions", "col": "uid"}
          }
        },
      ],
      "output": "students"
    },
    "dependencySourcings": [
      {
        "param": "students",
        "type": "dataframe",
        "config": {
          "asset": "load_db_students",
        },
      },
      {
        "param": "exceptions",
        "type": "dataframe",
        "config": {
          "asset": "load_tw_student_exceptions_withhold",
        },
      },
    ]
  },
  {
    "slug": "trfm_check_items_discrep_response_type_summary",
    "caption": "check_items_discrep_response_type_summary",
    "description": "Groups trfm_check_items_discrep_response_type by item to provide a summary of discrepant response types",
    "isCheckAsset": true,
    "severity": CheckAssetSeverity.WARNING,
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      sequence: [
        {
          kind: "restrict-cols",
          df_output: "responses",
          config: {
            df_input: "responses",
            cols: ["item_id", "response_type", "response_type_count", "most_freq_response_type", "most_freq_type_count"]
          },
        },
        {
          kind: "drop-duplicates",
          df_output: "item_responses",
          config: {
            df_input: "responses"
          }
        },
      ],
      output: "item_responses"
    },
    "dependencySourcings": [
      {
        "param": "responses",
        "type": "dataframe",
        "config": {
          "asset": "trfm_check_items_discrep_response_type"
        }
      }
    ]
  },
  {
    "slug": "trfm_pre_check_nr_response_discrepancies",
    "caption": "pre_check_nr_response_discrepancies",
    "description": "Returns responses where formatted_response is empty but is_nr=0",
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          kind: "filter-col",
          df_output: "item_responses",
          config: {
            df_input: "item_responses",
            col: 'is_nr',
            comparison: 'equals',
            value: 0
          }
        },
        {
          kind: "filter-col",
          df_output: "item_responses",
          config: {
            df_input: "item_responses",
            col: 'formatted_response',
            comparison: 'is-empty-string',
          }
        },
      ],
      "output": "item_responses"
    },
    "dependencySourcings": [
      {
        "param": "item_responses",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_responses"
        }
      }
    ]
  },
  { // TODO: Likely deprecated
    "slug": "trfm_student_attempts_consolidated",
    "caption": "student_attempts_consolidated",
    "description": "Attach relevant metadata to the attempt records to produce a consolidated attempts table",
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          kind: "join",
          df_output: "df",
          config: {
            how: "left",
            left: "ta_valid_twtar",
            right: "load_db_students",
            left_on: "uid",
          }
        },
        {
          kind: "restrict-cols",
          df_output: "load_db_scts",
          config: {
            df_input: "load_db_scts",
            cols: ["ts_id", "sc_id"],
          },
        },
        {
          kind: "join",
          df_output: "df",
          config: {
            how: "left",
            left: "df",
            right: "load_db_scts",
            left_on: "ts_id",
          }
        },
        {
          kind: "restrict-cols",
          df_output: "load_db_school_groupings",
          config: {
            df_input: "load_db_school_groupings",
            cols: ["ts_id", "sc_id"],
          },
        },
        {
          kind: "join",
          df_output: "df",
          config: {
            how: "left",
            left: "df",
            right: "load_db_school_groupings",
            left_on: "sc_id",
          }
        },
        {
          kind: "map-col",
          df_output: "df",
          config: {
            df_input: "df",
            col_output: "is_in_school_list",
            source_cols: ["s_gid"],
            operation: "is_null",
            value_true: 0,
            value_false: 1
          }
        },
        {
          kind: "restrict-cols",
          df_output: "load_db_student_admin_meta_booklets",
          config: {
            df_input: "load_db_student_admin_meta_booklets",
            cols: ["uid", "booklet_index", "course_code_foreign"],
          },
        },
        {
          kind: "join",
          df_output: "df",
          config: {
            how: "left",
            left: "df",
            right: "load_db_student_admin_meta_booklets",
            left_on: ['uid', 'course_code_foreign'],
          }
        },
        {
          kind: "restrict-cols",
          df_output: "trfm_scts_school_meta",
          config: {
            df_input: "trfm_scts_school_meta",
            cols: ['ts_id', "is_sample_school", "school_code", "school_authority_code"],
          },
        },
        {
          kind: "join",
          df_output: "df",
          config: {
            how: "left",
            left: "df",
            right: "trfm_scts_school_meta",
            left_on: "ts_id",
          }
        },
      ],
      "output": "df"
    },
    "dependencySourcings": [
      {
        "param": "ta_valid_twtar",
        "type": "dataframe",
        "config": {
          "asset": "trfm_ta_valid_twtar"
        }
      },
      {
        "param": "load_db_students",
        "type": "dataframe",
        "config": {
          "asset": "load_db_students"
        }
      },
      {
        "param": "load_db_student_admin_meta_booklets",
        "type": "dataframe",
        "config": {
          "asset": "load_db_student_admin_meta_booklets"
        }
      },
      {
        "param": "trfm_scts_school_meta",
        "type": "dataframe",
        "config": {
          "asset": "trfm_scts_school_meta"
        }
      },
      {
        "param": "load_db_scts",
        "type": "dataframe",
        "config": {
          "asset": "load_db_scts"
        }
      },
      {
        "param": "load_db_school_groupings",
        "type": "dataframe",
        "config": {
          "asset": "load_db_school_groupings"
        }
      },
    ]
  },
  {
    "slug": "trfm_item_responses_nf_omit",
    "caption": "item_responses_nf_omit",
    "description": "Item responses with NF (not finished) and omit flags added",
    "structure": "dataframe",
    "schema": [
      {"slug": "response_type", "caption": "Response Type", "type": "STRING", "description": "Type of response recorded", },
      {"slug": "formatted_response_strict", "caption": "Formatted Response Strict", "type": "STRING", "description": "Strictly formatted version of the response", },
      {"slug": "test_design_id", "caption": "Test Design Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test design.", },
      {"slug": "twtdar_id", "caption": "Test Window TD Alloc Rule ID", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window test design allocation rule.", },
      {"slug": "taqr_id", "caption": "Test Attempt Question Response ID", "type": "INTEGER_UNSIGNED", "description": "Identifier for the question response in a test attempt.", },
      {"slug": "test_session_id", "caption": "Test Session Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test session.", },
      {"slug": "test_attempt_id", "caption": "Test Attempt Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test attempt.", },
      {"slug": "lang", "caption": "Language", "type": "STRING", "description": "Language in which the response was provided", },
      {"slug": "asmt_code", "caption": "Assessment Code", "type": "STRING", "description": "Code identifying the assessment.", },
      {"slug": "is_nr", "caption": "Is No Response (NR)", "type": "INTEGER_UNSIGNED", "description": "Indicates if the response was not provided.", },
      {"slug": "score_max", "caption": "Score Max", "type": "INTEGER_UNSIGNED", "description": "Maximum score for the item.", },
      {"slug": "form_code", "caption": "Form Code", "type": "STRING", "description": "Identifier for the test form.", },
      {"slug": "score_max", "caption": "Score Max", "type": "INTEGER_UNSIGNED", "description": "Maximum score for the item.", },
      {"slug": "score", "caption": "Score", "type": "INTEGER_UNSIGNED", "description": "Score achieved for the response.", },
      {"slug": "item_id", "caption": "Item Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the item", },
      {"slug": "is_human_scored", "caption": "Is Human Scored", "type": "INTEGER_UNSIGNED", "description": "Indicates if the response was scored manually", },
      {"slug": "omit", "caption": "Omit", "type": "INTEGER_UNSIGNED", "description": "Indicates if the item is omitted", },
      {"slug": "last_response_nbr", "caption": "Last Response Number", "type": "INTEGER_UNSIGNED", "description": "Sequence number of the last response for the item.", },
      {"slug": "is_score_override", "caption": "Is Score Override", "type": "INTEGER_UNSIGNED", "description": "Indicates if the score is overridden", },
      {"slug": "NF", "caption": "Not Finished", "type": "INTEGER_UNSIGNED", "description": "Indicates if the item was not finished", },
      {"slug": "is_invalid", "caption": "Is Invalid", "type": "INTEGER_UNSIGNED", "description": "Indicates if the response is invalid", },
      {"slug": "weight", "caption": "Weight", "type": "INTEGER_UNSIGNED", "description": "Weight assigned to the item in the scoring calculation.", },
      {"slug": "formatted_response", "caption": "Formatted Response", "type": "STRING", "description": "Formatted version of the response", },
      {"slug": "test_form_id", "caption": "Test Form Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test form.", },
      {"slug": "item_nbr", "caption": "Item Number", "type": "INTEGER_UNSIGNED", "description": "Number of the item within the test.", },
      {"slug": "test_window_id", "caption": "Test Window Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window.", },
    ],
    "partitionBy": {
      "key": "partby_ta_ids",
      "concatOut": false
    },
    partitionOut: { // Repartition by test form
      key: "test_form",
      field: "test_design_id",
      categorical: true,
    },
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "join",
          "df_output": "item_responses",
          "config": {
            "how": "left",
            "left": "item_responses",
            "right": "last_response",
            "left_on": ["test_attempt_id"],
            "right_on": ["test_attempt_id",],
          }
        },
        {
          "kind": "fill-na",
          "df_output": "item_responses",
          "config": {
            "df_input": "item_responses",
            "values": {
              "last_response_nbr": 0,
            },
            "inplace": true,
          }
        },
        {
          kind: "map-col",
          df_output: "item_responses",
          config: {
            df_input: "item_responses",
            col_output: "NF",
            source_cols: ["item_nbr", "last_response_nbr"],
            operation: "greater-than",
            value_true: 1,
            value_false: 0
          }
        },
        {
          kind: "map-col",
          df_output: "item_responses",
          config: {
            df_input: "item_responses",
            col_output: "omit",
            source_cols: ["is_nr", "NF"],
            operation: "not-equal",
            value_true: 1,
            value_false: 0
          }
        },
        {
          kind: "filter-col",
          df_output: "exclusion_threshold",
          config: {
            df_input: "exclusion_threshold",
            col: "exclude",
            comparison: "equals",
            value: 1
          }
        },
        {
          kind: "filter-col",
          df_output: "item_responses",
          config: {
            df_input: "item_responses",
            col: "test_attempt_id",
            comparison: "not-in",
            value_src: {"df": "exclusion_threshold", "col": "test_attempt_id"}
          }
        },
      ],
      "output": "item_responses"
    },
    "dependencySourcings": [
      {
        "param": "item_responses",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_responses_fixed_filled"
        }
      },
      {
        "param": "last_response",
        "type": "dataframe",
        "config": {
          "asset": "trfm_test_attempt_last_response"
        }
      },
      {
        "param": "exclusion_threshold",
        "type": "dataframe",
        "config": {
          "asset": "trfm_exclusion_threshold"
        }
      },
      // TODO: applying exclusions can be another asset
      // {
      //   "param": "trfm_exclusion_threshold",
      //   "type": "dataframe",
      //   "config": {
      //     "asset": "trfm_exclusion_threshold"
      //   }
      // }
    ]
  },
  {
    "slug": "trfm_check_students_mult_attempts",
    "caption": "check_students_mult_attempts",
    "description": "Flags students who have multiple test attempts for the same test form",
    "isCheckAsset": true,
    "severity": CheckAssetSeverity.WARNING,
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          kind: "group-by",
          df_output: "multiple",
          config: {
            df_input: "load_db_ta_valid",
            group_by: ["uid', 'test_form_id"],
            agg: [{
              col_new: "times",
              agg_type: "count",
              col_target: "test_form_id",
            }]
          },
        },
        {
          kind: "filter-col",
          df_output: "forms",
          config: {
            df_input: "multiple",
            col: 'times',
            comparison: 'greater-than',
            value: 1
          }
        },
        {
          kind: "aggregate",
          df_output: "forms",
          config: {
            df_input: "forms",
            agg: [{
              col_new: "unique_attempts",
              agg_type: "list_unique",
              col_target: "ta_id",
            },
            ]
          },
        },
        {
          kind: "filter-col",
          df_output: "load_db_ta_valid",
          config: {
            df_input: "load_db_ta_valid",
            col: "ta_id",
            comparison: "in",
            value_src: {"df": "forms", "col": "unique_attempts"}
          }
        },
      ],
      output: "load_db_ta_valid"
    },
    "dependencySourcings": [
      {
        "param": "load_db_ta_valid",
        "type": "dataframe",
        "config": {
          "asset": "load_db_ta_valid"
        }
      },
      {
        "param": "load_db_ta_valid",
        "type": "dataframe",
        "config": {
          "asset": "load_db_ta_valid"
        }
      }
    ]
  },
  {
    "slug": "trfm_invalid_attempts",
    "caption": "invalid_attempts",
    "description": "Invalid test attempts",
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      sequence: [
        {
          kind: "filter-col",
          df_output: "ta_valid",
          config: {
            df_input: "ta_valid",
            col: 'is_invalid',
            comparison: 'equals',
            value: 1
          },
        },
        {
          kind: "join",
          df_output: "df",
          config: {
            how: "inner",
            left: "ta_valid",
            right: "stus",
            left_on: "uid",
          },
        },
        {
          kind: "join",
          df_output: "regs",
          config: {
            how: "inner",
            left: "regs",
            right: "groupings",
            left_on: "sc_gid",
          },
        },
        {
          kind: "join",
          df_output: "regs",
          config: {
            how: "inner",
            left: "load_db_schools",
            right: "regs",
            left_on: "s_gid",
          },
        }, // TODO: drop columns sc_gid, sc_id after this step
        {
          kind: "drop-duplicates",
          df_output: "regs",
          config: {
            df_input: "regs"
          }
        },
        {
          kind: "join",
          df_output: "df",
          config: {
            how: "inner",
            left: "df",
            right: "regs",
            left_on: ["uid", "test_session_id"],
          },
        },
      ],
      output: "df"
    },
    "dependencySourcings": [
      {
        "param": "ta_valid",
        "type": "dataframe",
        "config": {
          "asset": "load_db_ta_valid"
        }
      },
      {
        "param": "regs",
        "type": "dataframe",
        "config": {
          "asset": "trfm_student_grouping_registrations"
        }
      },
      {
        "param": "groupings",
        "type": "dataframe",
        "config": {
          "asset": "load_db_school_groupings"
        }
      },
      {
        "param": "load_db_scts",
        "type": "dataframe",
        "config": {
          "asset": "load_db_scts"
        }
      },
      {
        "param": "stus",
        "type": "dataframe",
        "config": {
          "asset": "load_db_students"
        }
      },
      {
        "param": "load_db_schools",
        "type": "dataframe",
        "config": {
          "asset": "load_db_schools"
        }
      },
    ]
  },
  {
    "slug": "trfm_item_register_consolidated",
    "caption": "item_register_consolidated",
    "description": "Item Register with correct TQER information (ids and score_max)",
    "structure": "dataframe",
    "schema": [
      {"slug": "twtar_id", "caption": "Test Window TD Alloc Rule ID", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window TD allocation rule.", },
      {"slug": "test_term", "caption": "Test Term", "type": "STRING", "description": "Term or period for the test.", },
      {"slug": "type_slug", "caption": "Type Slug", "type": "STRING", "description": "Slug representing the type of test or assessment.", },
      {"slug": "course_name_full", "caption": "Course Name Full", "type": "STRING", "description": "Full name of the course associated with the test.", },
      {"slug": "course_code", "caption": "Course Code", "type": "STRING", "description": "Code representing the course.", },
      {"slug": "test_lang", "caption": "Test Lang", "type": "STRING", "description": "Language in which the test is conducted.", },
      {"slug": "form_code", "caption": "Form Code", "type": "STRING", "description": "Code representing the form.", },
      {"slug": "component_slug", "caption": "Component Slug", "type": "STRING", "description": "Slug representing a component of the test.", },
      {"slug": "item_nbr", "caption": "Item Number", "type": "INTEGER_UNSIGNED", "description": "Sequential number for the item in the test design.", },
      {"slug": "item_id", "caption": "Item Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test item.", },
      {"slug": "response_lang", "caption": "Response Lang", "type": "STRING", "description": "Language in which the response is provided.", },
      {"slug": "report_label_short", "caption": "Report Label Short", "type": "STRING", "description": "Short label used in reports for this item.", },
      {"slug": "item_name", "caption": "Item Name", "type": "STRING", "description": "Name of the test item.", },
      {"slug": "joined_with_item_nbr", "caption": "Joined With Item Number", "type": "INTEGER_UNSIGNED", "description": "Number of the item this item is joined with, if applicable.", },
      {"slug": "test_design_id", "caption": "Test Design Id", "type": "INTEGER_UNSIGNED", "description": "Unique identifier for the test design.", },
      {"slug": "is_human_scored", "caption": "Is Human Scored", "type": "INTEGER_UNSIGNED", "description": "Flag indicating if the item is scored by humans.", },
      {"slug": "score_max", "caption": "Score Max", "type": "INTEGER_UNSIGNED", "description": "Maximum score for the item.", },
      {"slug": "correct_tqer_ids", "caption": "Correct Tqer Ids", "type": "STRING", "description": "IDs of the correct test question expected answers.", },
    ],
    "method": "transforms",
    "methodConfig": {
      sequence: [
        {
          kind: "join",
          df_output: "item_register",
          config: {
            how: "left",
            left: "item_register",
            right: "correct_tqer",
            left_on: ["item_id", "response_lang"],
            right_on: ["item_id", "lang"],
          }
        },
        { // Written responses don't have correct TQERs
          kind: "fill-na",
          df_output: "item_register",
          "config": {
            "df_input": "item_register",
            "inplace": true,
            "values": {
              "score_max": null,
              "correct_tqer_ids": null,
            }
          }
        },
        {
          kind: "join",
          df_output: "item_register",
          config: {
            how: "left",
            left: "item_register",
            right: "tqr_params",
            left_on: "tqr_id",
            right_on: "tqr_id",
          }
        },
      ],
      output: "item_register"
    },
    "dependencySourcings": [
      {
        "param": "item_register",
        "type": "dataframe",
        "config": {
          "asset": "load_item_register_consolidated"
        }
      },
      {
        "param": "correct_tqer",
        "type": "dataframe",
        "config": {
          "asset": "trfm_correct_tqer"
        }
      },
      {
        "param": "tqr_params",
        "type": "dataframe",
        "config": {
          "asset": "load_tqr_params"
        }
      }
    ]
  },
  {
    "slug": "trfm_automated_messages",
    "caption": "automated_messages",
    "description": "Automated messages for statistics.",
    "structure": "dataframe",
    "method": "placeholder",
    "methodConfig": {},
    "dependencySourcings": [
      {
        "param": "trfm_item_response_value_stats",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_response_value_stats"
        }
      },
      {
        "param": "trfm_item_scale_stats",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_scale_stats"
        }
      },
      {
        "param": "trfm_all_expected_answers",
        "type": "dataframe",
        "config": {
          "asset": "trfm_all_expected_answers"
        }
      },
      {
        "param": "spec_threshold_values",
        "type": "dataframe",
        "config": {
          "asset": "spec_threshold_values"
        }
      }
    ]
  },
  {
    "slug": "load_db_student_admin_meta",
    "caption": "student_admin_meta",
    "description": "Data for the students sheet, down to a booklet level",
    "structure": "dataframe",
    "method": "query-chunked",
    "methodConfig": {
      "querySlug": "SQL_04B_REGISTRATIONS_STUDENT_ADMIN_META",
      "chunkedParam": 'uids',
      "chunkSize": 5000,
      "makeDistinct": true
    },
    "dependencySourcings": [
      {
        "param": "uids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_students",
          "col": "uid"
        }
      },
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      },
    ]
  },
  {
    "slug": "load_db_student_admin_meta_booklets",
    "caption": "student_admin_meta_booklets",
    "description": "Booklet index information for students in the test window.",
    "structure": "dataframe",
    "schema": [
      {"slug": "test_window_id", "caption": "Test Window Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window.", },
      {"slug": "uid", "caption": "User Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the user.", },
      {"slug": "booklet_index", "caption": "Booklet Index", "type": "STRING", "description": "Index of the booklet assigned to the student.", },
      {"slug": "booklet_pasi_course_code", "caption": "Booklet Pasi Course Code", "type": "STRING", "description": "PASI course code associated with the booklet.", },
      {"slug": "n_intersect", "caption": "N Intersect", "type": "INTEGER_UNSIGNED", "description": "Number of intersections related to the booklet or test.", },
    ],
    "method": "query-chunked",
    "methodConfig": {
      "querySlug": "SQL_04B_REGISTRATIONS_STUDENT_ADMIN_META_BOOKLETS",
      "chunkedParam": 'uids',
      "chunkSize": 5000,
      "makeDistinct": true
    },
    "dependencySourcings": [
      {
        "param": "uids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_students",
          "col": "uid"
        }
      },
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      },
    ]
  },
  {
    "slug": "trfm_check_items_discrep_response_type",
    "caption": "check_items_discrep_response_type",
    "description": "Check asset of discrepant response types for an item",
    "isCheckAsset": true,
    "severity": CheckAssetSeverity.WARNING,
    "structure": "dataframe",
    "partitionBy": {
      "key": "partby_ta_ids",
    },
    "method": "placeholder",
    "methodConfig": {},
    "dependencySourcings": [
      {
        "param": "trfm_item_responses_nf_omit",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_responses_nf_omit"
        }
      }
    ]
  },
  { // TODO: Part of the main export alignment
    "slug": "trfm_item_expected_answers",
    "caption": "item_expected_answers",
    "description": "Expected answers for items with test form information.",
    "structure": "dataframe",
    "schema": [
      {slug: "item_id", type: "INTEGER_UNSIGNED", caption: "Item ID", description: "Item ID", is_primary_key: true},
      {slug: "type_slug", type: "STRING", caption: "Assessment Code", description: "Code representing the assessment.", is_primary_key: true},
      {slug: "form_code", type: "INTEGER_UNSIGNED", caption: "Form Code", description: "Code representing the assessment form.", is_primary_key: true},
      {slug: "lang", type: "STRING", caption: "Language", description: "Language for the item's response", is_primary_key: true},
      {slug: "formatted_response", type: "STRING", caption: "Formratted Response", description: "Formatted response", is_primary_key: true},
      {slug: "coded_response", type: "STRING", caption: "Coded Response", description: "Coded response", is_nullable: true},
      {slug: "score", type: "INTEGER_UNSIGNED", caption: "Score", description: "Score associated  with the response"},
      {slug: "score_max", type: "INTEGER_UNSIGNED", caption: "Max Score", description: "Maximum possible score for the item"},
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        { // Remove extra columns
          "kind": "restrict-cols",
          "df_output": "twtar",
          "config": {
            "df_input": "twtar",
            "cols": ["lang", "td_id", "type_slug", "form_code"]
          }
        },
        {
          "kind": "restrict-cols",
          "df_output": "tqer",
          "config": {
            "df_input": "tqer",
            "cols": [
              "item_id", "formatted_response", "coded_response", "score", "score_max", "lang",
              'is_item_score_exceptions',
              'score_pre_override',
              'tqer_is_from_admin',
            ]
          }
        },
        {
          "kind": "restrict-cols",
          "df_output": "tqr",
          "config": {
            "df_input": "tqr",
            "cols": ['test_design_id', 'item_id']
          }
        },
        { // Begin joining the various assets
          "kind": "join",
          "df_output": "tqr",
          "config": {
            "how": "inner",
            "left": "tqr",
            "right": "twtar",
            "left_on": ["test_design_id"],
            "right_on": ["td_id"],
          }
        },
        {
          "kind": "drop-duplicates",
          "df_output": "tqr",
          "config": {
            "df_input": "tqr",
            "subset": ["type_slug","form_code","item_id"],
          }
        },
        {
          "kind": "join",
          "df_output": "tqer",
          "config": {
            "how": "inner",
            "left": "tqr",
            "right": "tqer",
            "left_on": ["item_id", "lang"],
            "right_on": ["item_id", "lang"],
          }
        },
        {
          "kind": "restrict-cols",
          "df_output": "tqer",
          "config": {
            "df_input": "tqer",
            "cols": ["item_id", "type_slug", "form_code", "lang", "formatted_response", "coded_response", "score", "score_max",
              'is_item_score_exceptions',
              'score_pre_override',
              'tqer_is_from_admin',
            ]
          }
        },
      ],
      "output": "tqer"
    },
    "dependencySourcings": [
      {
        "param": "twtar",
        "type": "dataframe",
        "config": {
          "asset": "load_twtar"
        }
      },
      {
        "param": "tqer",
        "type": "dataframe",
        "config": {
          "asset": "load_db_tqer"
        }
      },
      {
        "param": "tqr",
        "type": "dataframe",
        "config": {
          "asset": "load_db_tqr"
        }
      },
    ]
  },
  {
    "slug": "trfm_check_multiple_item_expected_answers",
    "caption": "check_multiple_item_expected_answers",
    "description": "Flags multiple rows with the same item_id, lang and formatted_response combination",
    "isCheckAsset": true,
    "severity": CheckAssetSeverity.WARNING,
    "schema": [
      {slug: "item_id", type: "INTEGER_UNSIGNED", caption: "Item ID", description: "Item ID", is_primary_key: true},
      {slug: "type_slug", type: "STRING", caption: "Assessment Code", description: "Code representing the assessment.", is_primary_key: true},
      {slug: "form_code", type: "INTEGER_UNSIGNED", caption: "Form Code", description: "Code representing the assessment form.", is_primary_key: true},
      {slug: "lang", type: "STRING", caption: "Language", description: "Language for the item's response", is_primary_key: true},
      {slug: "formatted_response", type: "STRING", caption: "Formratted Response", description: "Formatted response", is_primary_key: true},
      {slug: "score", type: "INTEGER_UNSIGNED", caption: "Score", description: "Score associated  with the response"},
      {slug: "score_max", type: "INTEGER_UNSIGNED", caption: "Max Score", description: "Maximum possible score for the item"},
      {slug: "is_item_score_exceptions", type: "BOOL_INT", caption: "Is Item Score Exception", description: "Flag indicating if an item score exception was applied"},
      {slug: "tqer_is_from_admin", type: "BOOL_INT", caption: "TQER Is From Admin", description: "Flag indicating if the expected answer record came from admin"},
      {slug: "count", type: "INTEGER_UNSIGNED", caption: "Count", description: "Count of records for this group"},
    ],
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      sequence: [
        {
          kind: "group-by",
          df_output: "item_expected_answers",
          config: {
            df_input: "item_expected_answers",
            group_by: ["item_id", "type_slug", "form_code", "lang", "formatted_response", "score", "score_max", "is_item_score_exceptions", "tqer_is_from_admin"],
            agg: [{
              col_new: "count",
              agg_type: "count",
              col_target: "tqer_is_from_admin",
            }]
          },
        },
        {
          kind: "filter-col",
          df_output: "item_expected_answers",
          config: {
            df_input: "item_expected_answers",
            col: "count",
            comparison: "greater-than",
            value: 1
          }
        }
        ],
        output: "item_expected_answers"
      },
      "dependencySourcings": [
          {
              "param": "item_expected_answers",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_item_expected_answers"
              }
          },
      ]
  },
  {
    "slug": "trfm_check_expected_answers_empty_columns",
    "caption": "check_expected_answers_empty_columns",
    "description": "Flags item expected answers that have an empty score_max or lang",
    "isCheckAsset": true,
    "severity": CheckAssetSeverity.ERROR,
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      sequence: [
        {
          kind: "filter-col",
          df_output: "empty_score_max",
          config: {
            df_input: "item_expected_answers",
            col: "score_max",
            comparison: "null"
          }
        },
        {
          kind: "filter-col",
          df_output: "empty_lang",
          config: {
            df_input: "item_expected_answers",
            col: "lang",
            comparison: "null"
          }
        },
        {
          "kind": "concat",
          "df_output": "empty_score_max",
          "config": {
            "df_inputs": ["empty_score_max", "empty_lang"]
          }
        }
        ],
        output: "empty_score_max"
      },
      "dependencySourcings": [
          {
              "param": "item_expected_answers",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_item_expected_answers"
              }
          },
      ]
  },
  {
    "slug": "trfm_coded_responses",
    "caption": "coded_responses",
    "description": "Coded responses for an item_id, formatted_response, lang combination.",
    "structure": "dataframe",
    "schema": [
      {slug: "item_id", type: "INTEGER_UNSIGNED", caption: "Item ID", description: "Item ID", is_primary_key: true},
      {slug: "formatted_response", type: "STRING", caption: "Formratted Response", description: "Formatted response", is_primary_key: true},
      {slug: "lang", type: "STRING", caption: "Language", description: "Language for the item's response", is_primary_key: true},
      {slug: "coded_response", type: "STRING", caption: "Coded Response", description: "Coded response", is_nullable: true},
      {slug: "unique_coded_response", type: "STRING", caption: "All unqiue coded responses present for the item and formatted_response", description: "Coded response", is_nullable: true},
      {slug: "nunique", type: "INTEGER_UNSIGNED", caption: "Unique coded responses", description: "Count of unique coded responses", is_nullable: true},
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "filter-col",
          "df_output": "item_expected_answers",
          "config": {
            "df_input": "item_expected_answers",
            "col": "coded_response",
            "comparison": "non-null"
          }
        },
        {
          "kind": "group-by",
          "df_output": "item_expected_answers",
          "config": {
            "df_input": "item_expected_answers",
            "group_by": ["item_id", "formatted_response", "lang"],
            "agg": [
              {
                "col_new": "coded_response",
                "agg_type": "first",
                "col_target": "coded_response"
              },
              {
                "col_new": "unique_coded_responses",
                "agg_type": "list_unique",
                "col_target": "coded_response"
              },
              {
                "col_new": "nunique",
                "agg_type": "nunique",
                "col_target": "coded_response"
              }
            ]
          }
        }
      ],
      "output": "item_expected_answers"
    },
    "dependencySourcings": [
      {
        "param": "item_expected_answers",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_expected_answers"
        }
      },
    ]
  },
  {
    "slug": "trfm_check_multiple_coded_responses",
    "caption": "check_multiple_coded_responses",
    "description": "Flags if there are multiple non-null coded responses for an item_id, formatted_response, lang combination.",
    "isCheckAsset": true,
    "severity": CheckAssetSeverity.ERROR,
    "structure": "dataframe",
    "schema": [
      {slug: "item_id", type: "INTEGER_UNSIGNED", caption: "Item ID", description: "Item ID", is_primary_key: true},
      {slug: "formatted_response", type: "STRING", caption: "Formratted Response", description: "Formatted response", is_primary_key: true},
      {slug: "lang", type: "STRING", caption: "Language", description: "Language for the item's response", is_primary_key: true},
      {slug: "coded_response", type: "STRING[]", caption: "Coded Response", description: "Coded response", is_nullable: true},
      {slug: "unique_coded_response", type: "STRING", caption: "All unqiue coded responses present for the item and formatted_response", description: "Coded response", is_nullable: true},
      {slug: "nunique", type: "INTEGER_UNSIGNED", caption: "Unique coded responses", description: "Count of unique coded responses", is_nullable: true},
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "filter-col",
          "df_output": "coded_responses",
          "config": {
            "df_input": "coded_responses",
            "col": "nunique",
            "comparison": "greater-than",
            "value": 1
          }
        }
      ],
      "output": "coded_responses"
    },
    "dependencySourcings": [
      {
        "param": "coded_responses",
        "type": "dataframe",
        "config": {
          "asset": "trfm_coded_responses"
        }
      },
    ]
  },
  {
    "slug": "trfm_check_score_max_discrepancies",
    "caption": "check_score_max_discrepancies",
    "description": "Check asset for TQER records with multiple score_max values",
    "isCheckAsset": true,
    "severity": "warning",
    "structure": "dataframe",
    "method": "placeholder", // TODO: requires loading entire dataframe into memory
    "methodConfig": {
      sequence: [
        {
          kind: "filter-col",
          df_output: "df_item_responses",
          config: {
            df_input: "df_item_responses",
            col: "item_id",
              comparison: "in",
              value_src: "item_ids"
            }
          },
          {
            kind: "group-by",
            df_output: "df_item_responses",
            config: {
              df_input: "df_item_responses",
              group_by: ["item_id", "score", "score_max", "formatted_response"],
              agg: [{
                col_new: "count",
                agg_type: "count",
                col_target: "test_attempt_question_response_id",
              }]
            },
          },
          {
            kind: "group-by",
            df_output: "majority_score_max",
            config: {
              df_input: "df_item_responses",
              group_by: ["item_id"],
              agg: [{
                col_new: "majority_score_max",
                agg_type: "mode",
                col_target: "score_max",
              }]
            },
          },
          {
            kind: "join",
            df_output: "merged_df",
            config: {
              how: "inner",
              left: "df_item_responses",
              right: "majority_score_max",
              left_on: "item_id",
            }
          },
          {
            kind: "filter-col",
            df_output: "merged_df",
            config: {
              df_input: "merged_df",
              col: "score_max",
              comparison: "not-equal",
              value_src: {"df": "merged_df", "col": "majority_score_max"}
            }
          },
        ],
        output: "merged_df"
      },
      "dependencySourcings": [
          {
              "param": "df_item_responses",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_item_responses_nf_omit"
              }
          },
          {
            "param": "tqr",
            "type": "dataframe",
            "config": {
                "asset": "load_db_tqr"
            }
          },
          {
            param: "item_ids",
            type: "asset-col",
            config: {
              "asset": "load_db_tqr",
              "col": "item_id"
            }
          },
      ]
  },
  {
      "slug": "trfm_check_numeric_responses",
      "caption": "check_numeric_responses",
      "description": "Check asset for numeric responses", // TODO: description
      "isCheckAsset": true,
      "severity": CheckAssetSeverity.WARNING,
      "structure": "dataframe",
      "method": "placeholder",
      "methodConfig": {},
      "dependencySourcings": [
          {
              "param": "trfm_item_responses_fixed",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_item_responses_fixed"
              }
          },
          {
            "param": "load_db_item_expected_answers",
            "type": "dataframe",
            "config": {
                "asset": "load_db_item_expected_answers"
            }
          },
          {
            "param": "trfm_item_register_consolidated",
            "type": "dataframe",
            "config": {
                "asset": "trfm_item_register_consolidated"
            }
          }
      ]
  },
  {
      "slug": "trfm_check_tqr_unassigned_items",
      "caption": "check_tqr_unassigned_items",
      "description": "Returns items that are not tied to a test design", // TODO
      "isCheckAsset": true,
      "severity": CheckAssetSeverity.WARNING,
      "structure": "dataframe",
      "method": "transforms",
      "methodConfig": {
        sequence: [
          {
            kind: "filter-col",
            df_output: "load_db_tqr",
            config: {
              df_input: "load_db_tqr",
              col: "test_design_id",
              comparison: "non-null",
            }
          },
        ],
        output: "load_db_tqr"
      },
      "dependencySourcings": [
          {
              "param": "load_db_tqr",
              "type": "dataframe",
              "config": {
                  "asset": "load_db_tqr"
              }
          }
      ]
  },
  {
    "slug": "trfm_student_accommodations_aggregated",
    "caption": "student_accommodations_aggregated",
    "description": "Aggregated student accommodations",
    "structure": "dataframe",
    "method": "placeholder",
    "methodConfig": {
      sequence: [
        {
          kind: "filter-col",
          df_output: "given_accomms",
          config: {
            df_input: "given_accomms",
            col: "accommodation_value",
            comparison: "equals",
            value: "1",
          },
        },
        // TODO: rename accoms.acc_type_slug to accommodation_slug
        {
          "kind": "join",
          "df_output": "given_accomms",
          "config": {
            "how": "left",
            "left": "given_accomms",
            "right": "accomms",
            "left_on": "acc_id",
            "right_on": "acc_id",
          }
        },
        // TODO: fill null accommodation_slug with "missing_accommodation_slug"
        // TODO: get list of slugs and number for each uid
        // TODO: "explode" the slug and number columns together and drop duplicates
        // TODO: convert type of accommodation_number
      ],
      output: "merge"
    },
    "dependencySourcings": [
      {
        "param": "given_accomms",
        "type": "dataframe",
        "config": {
          "asset": "load_db_student_accommodations"
        }
      },
      {
        "param": "accomms",
        "type": "dataframe",
        "config": {
          "asset": "load_db_aw_accommodation_options"
        }
      }
    ]
  },
  {
    "slug": "trfm_check_tqr_fields",
    "caption": "check_tqr_fields",
    "description": "Identify any TQR records with missing `item_nbr` or `score_points`",
    "isCheckAsset": true,
    "severity": "warning",
    "structure": "dataframe",
    schema: [
      {"slug": "tqr_id", "type": "INTEGER_UNSIGNED", "caption": "Test Question Register Id", "description": "Identifier for the Test Question Register (TQR) record.", "is_primary_key": true},
      {"slug": "item_id", "type": "INTEGER_UNSIGNED", "caption": "Item Id", "description": "Identifier for the specific item."},
      {"slug": "item_name", "type": "STRING", "caption": "Item Name", "description": "Name or description of the item.", },
      {"slug": "lang", "type": "STRING", "caption": "Language", "description": "Language associated with the item.", },
      {"slug": "test_design_id", "type": "INTEGER_UNSIGNED", "caption": "Test Design Id", "description": "Identifier for the test design associated with the item.", },
      {"slug": "is_questionnaire", "type": "BOOL_INT", "caption": "Is Questionnaire", "description": "Flag indicating whether the item is part of a questionnaire.", },
      {"slug": "is_reading_passage", "type": "INTEGER_UNSIGNED", "caption": "Is Reading Passage", "description": "Flag indicating whether the item is a reading passage.", },
      {"slug": "score_profile_id", "type": "INTEGER_UNSIGNED", "caption": "Score Profile Id", "description": "Identifier for the score profile associated with the item.", },
      {"slug": "score_points", "type": "INTEGER_UNSIGNED", "caption": "Score Points", "description": "Maximum score points assigned to the item.", },
      {"slug": "is_human_scored", "type": "INTEGER_UNSIGNED", "caption": "Is Human Scored", "description": "Flag indicating whether the item is scored manually by a human.", },
      {"slug": "expected_answer", "type": "STRING", "caption": "Expected Answer", "description": "The correct or expected answer for the item.", },
      {"slug": "item_nbr", "type": "INTEGER_UNSIGNED", "caption": "Item Number", "description": "The number of the item in the test question register.", },
      {"slug": "report_label_short", "type": "STRING", "caption": "Report Label Short", "description": "Short label for the item.", },
    ],
    "method": "transforms",
    "methodConfig": {
      sequence: [
        {
          kind: "filter-col",
          df_output: "missing_item_nbr",
          config: {
            df_input: "load_db_tqr",
            col: "item_nbr",
            comparison: "null",
          }
        },
        {
          kind: "filter-col",
          df_output: "missing_score_points",
          config: {
            df_input: "load_db_tqr",
            col: "score_points",
            comparison: "null",
          }
        },
        { // Human-scored items don't need to have score_points (score of correct answer)
          kind: "filter-col",
          df_output: "missing_score_points",
          config: {
            df_input: "missing_score_points",
            col: "is_human_scored",
            comparison: "not-equal",
            value: 1,
          }
        },
        {
          "kind": "concat",
          "df_output": "load_db_tqr",
          "config": {
            "df_inputs": ["missing_item_nbr", "missing_score_points"]
          }
        }
      ],
      output: "load_db_tqr"
    },
    "dependencySourcings": [
        {
            "param": "load_db_tqr",
            "type": "dataframe",
            "config": {
                "asset": "load_db_tqr"
            }
        }
    ]
  },
  {
    "slug": "trfm_item_responses_fixed_filled",
    "caption": "trfm_item_responses_fixed_filled",
    "description": "Item responses with imputed values for blank responses",
    "structure": "dataframe",
    "partitionBy": {
      "key": "partby_ta_ids",
    },
    schema: [
      {"slug": "test_attempt_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": false},
      {"slug": "response_type", "type": "STRING", "caption": "Response Type", "description": "Type of response provided.", "is_primary_key": false},
      {"slug": "formatted_response", "type": "STRING", "caption": "Formatted Response", "description": "Processed or cleaned version of the response.", "is_primary_key": false},
      {"slug": "test_design_id", "type": "INTEGER_UNSIGNED", "caption": "Test Design Id", "description": "Identifier for the test design associated with the question.", "is_primary_key": false},
      {"slug": "score_max", "type": "Null", "caption": "Score Max", "description": "Maximum possible score for the item.", "is_primary_key": false},
      {"slug": "item_nbr", "type": "INTEGER_UNSIGNED", "caption": "Item Number", "description": "Sequential number of the test item in the test design.", "is_primary_key": false},
      {"slug": "test_session_id", "type": "INTEGER_UNSIGNED", "caption": "Test Session Id", "description": "Identifier for the test session linked to the test attempt.", "is_primary_key": false},
      {"slug": "is_score_override", "type": "INTEGER_UNSIGNED", "caption": "Is Score Override", "description": "Flag indicating whether the score for the item is overridden.", "is_primary_key": false},
      {"slug": "test_window_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window Id", "description": "Identifier for the test window.", "is_primary_key": false},
      {"slug": "form_code", "type": "STRING", "caption": "Form Code", "description": "Code representing the test form.", "is_primary_key": false},
      {"slug": "formatted_response_strict", "type": "STRING", "caption": "Formatted Response Strict", "description": "Strictly formatted version of the response.", "is_primary_key": false},
      {"slug": "twtdar_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window TD Alloc Rule ID", "description": "Identifier for the Test Window TD Allocation Rule.", "is_primary_key": false},
      {"slug": "lang", "type": "STRING", "caption": "Language", "description": "Language of the item.", "is_primary_key": false},
      {"slug": "weight", "type": "INTEGER_UNSIGNED", "caption": "Weight", "description": "Weight for the test attempt question response.", "is_primary_key": false},
      {"slug": "score", "type": "INTEGER_UNSIGNED", "caption": "Score", "description": "Score awarded for the given response.", "is_primary_key": false},
      {"slug": "is_nr", "type": "INTEGER_UNSIGNED", "caption": "Is No Response", "description": "Flag indicating if the response is NR (Non Response).", "is_primary_key": false},
      {"slug": "item_id", "type": "INTEGER_UNSIGNED", "caption": "Item Id", "description": "Identifier for the item.", "is_primary_key": false},
      {"slug": "asmt_code", "type": "STRING", "caption": "Assessment Code", "description": "Code representing the assessment.", "is_primary_key": false},
      {"slug": "is_human_scored", "type": "INTEGER_UNSIGNED", "caption": "Is Human Scored", "description": "Flag indicating whether the item is scored manually by a human.", "is_primary_key": false},
      {"slug": "score_max", "type": "INTEGER_UNSIGNED", "caption": "Score Max", "description": "Maximum possible score for the item.", "is_primary_key": false},
      {"slug": "test_form_id", "type": "INTEGER_UNSIGNED", "caption": "Test Form Id", "description": "Identifier for the test form.", "is_primary_key": false},
      {"slug": "is_invalid", "type": "INTEGER_UNSIGNED", "caption": "Is Invalid", "description": "Flag indicating whether the test attempt is invalid.", "is_primary_key": false},
      {"slug": "taqr_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Question Response ID", "description": "Identifier for the test attempt question response.", "is_primary_key": true},
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        // Invalid responses are removed here (before filling in blanks)
        {
          "kind": "filter-col",
          "df_output": "item_responses",
          "config": {
            "df_input": "item_responses",
            "col": "is_invalid",
            "comparison": "equals",
            "value": 0
          }
        },
        // First join test attempts to the tqr, so that we get a record for each question in the test attempt
        // TODO: we should use the score_max from expected responses rather than from taqr/tqr
        {
          "kind": "restrict-cols",
          "df_output": "test_attempts",
          "config": {
            "df_input": "test_attempts",
            "cols": ["ta_id", "twtdar_id", "test_session_id", "test_form_id"],
          }
        },
        // Since working with partitions, must limit test attempts to only those in the current partition
        // TODO: it might be better to partition at the load_test_attempts asset rather than load_taqr
        {
          "kind": "filter-col",
          "df_output": "test_attempts",
          "config": {
            "df_input": "test_attempts",
            "col": "ta_id",
            "comparison": "in",
            "value_src": {"df": "item_responses", "col": "ta_id"}
          }
        },
        {
          "kind": "restrict-cols",
          "df_output": "twtar",
          "config": {
            "df_input": "twtar",
            "cols": ["twtar_id", "td_id", "test_window_id", "type_slug", "form_code"],
          }
        },
        {
          "kind": "join",
          "df_output": "test_attempts",
          "config": {
            "how": "inner",
            "left": "test_attempts",
            "right": "twtar",
            "left_on": "twtdar_id",
            "right_on": "twtar_id",
          }
        },
        {
          "kind": "restrict-cols",
          "df_output": "tqr",
          "config": {
            "df_input": "tqr",
            "cols": ["test_design_id", "item_id", "lang", "item_nbr", "score_max", "is_human_scored"],
          }
        },
        {
          "kind": "join",
          "df_output": "test_attempts",
          "config": {
            "how": "inner",
            "left": "test_attempts",
            "right": "tqr",
            "left_on": "td_id",
            "right_on": "test_design_id"
          }
        },
        { // TODO: this sort should not be required and is clobbered by the next join
          "kind": "sort-by",
          "df_output": "test_attempts",
          "config": {
            "df_input": "test_attempts",
            "cols": ["ta_id", "item_nbr", "item_id"],
            "ascending": [true, true, true]
          }
        },
        // Next, left join (tqr with test_attempt info) and item_responses to populate the responses
        {
          "kind": "join",
          "df_output": "item_responses",
          "config": {
            "how": "left",
            "left": "test_attempts",
            "right": "item_responses",
            "left_on": ["ta_id", "item_id"],
            "right_on": ["ta_id", "item_id"]
          }
        },
        // Fill in any missing responses with default values
        {
          "kind": "fill-na",
          "df_output": "item_responses",
          "config": {
            "df_input": "item_responses",
            "values": {
              "score": 0,
              "is_nr": 1,
              "is_invalid": 0,
              "formatted_response": "",
              "formatted_response_strict": "",
              "response_type": "",
              "is_score_override": 0,
            },
            "inplace": true,
          }
        },
        // Remove/mask formatted responses for human-scored questions (written responses)
        // NOTE: it may be preferable to do this at nf_omit
        {
          "kind": "set-where",
          "df_output": "item_responses",
          "config": {
            "df_input": "item_responses",
            "inplace": true,
            "condition": {
              "col": "is_human_scored",
              "comparison": "equals",
              "value": 1
            },
            "values": {
              "formatted_response": "human-scored",
              "formatted_response_strict": "human-scored",
            }
          }
        },
        {
          "kind": "rename-cols",
          "df_output": "item_responses",
          "config": {
            "df_input": "item_responses",
            "inplace": true,
            "new_names": {
              "ta_id": "test_attempt_id",
              "td_id": "test_design_id",
              "type_slug": "asmt_code",
            }
          }
        },
        // TODO: original also applied nr_overrides at this point
      ],
      "output": "item_responses"
    },
    "dependencySourcings": [
      {
        "param": "item_responses",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_responses_fixed"
        }
      },
      {
        "param": "tqr",
        "type": "dataframe",
        "config": {
          "asset": "trfm_tqr_score_max"
        }
      },
      {
        "param": "test_attempts",
        "type": "dataframe",
        "config": {
          "asset": "trfm_test_attempts_exceptions_applied"
        }
      },
      {
        "param": "twtar",
        "type": "dataframe",
        "config": {
          "asset": "load_twtar",
          "cols": ["twtar_id", "td_id"]
        }
      }
    ]
  },
  {
      "slug": "trfm_score_frequency",
      "caption": "score_frequency",
      "description": "(deprecated) Frequency of total scores for each test form",
      "structure": "dataframe",
      "method": "placeholder",
      "methodConfig": {},
      "dependencySourcings": [
          {
              "param": "trfm_item_max_scores",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_item_max_scores"
              }
          },
          {
            "param": "trfm_item_responses_nf_omit",
            "type": "dataframe",
            "config": {
                "asset": "trfm_item_responses_nf_omit"
            }
        }
      ]
  },
  {
      "slug": "trfm_cutoff_points",
      "caption": "cutoff_points",
      "schema": [
        {slug: "asmt_code", type: "STRING", caption: "Assessment Code", description: "Assessment Code"},
        {slug: "lang", type: "STRING", caption: "Language", description: "Assessment language."},
        {slug: "form_code", type: "STRING", caption: "Form Code", description: "Form Code"},
        {slug: "label", type: "STRING", caption: "Group Label", description: "Label to apply to records in this group."},
        {slug: "cutoff", type: "FLOAT", caption: "Cutoff", description: "Upper bound of the group."},
        {slug: "inclusive", type: "BOOL", caption: "is_inclusive", description: "Indicates the upper bound is included in the group."}
      ],
      "description": "Cuttoff points for high/med/low groups. These indicate the upper bound (inclusive) of each group.",
      "structure": "dataframe",
      "method": "transforms",
      "methodConfig": {
        "sequence": [
          {
            "kind": "cutoffs",
            "df_output": "cutoff_points",
            "config": {
              "df_input": "total_scores",
              "group_by": ["asmt_code", "lang", "form_code"],
              "col_target": "total_score",
              "upper_bounds": [
                { "label": "low", "percentile": 27, "inclusive": true },
                { "label": "mid", "percentile": 73, "inclusive": false },
                { "label": "high", "percentile": 100, "inclusive": true },
              ],
            }
          }
        ],
        "output": "cutoff_points"
      },
      "dependencySourcings": [
        {
          "param": "total_scores",
          "type": "dataframe",
          "config": {
              "asset": "trfm_test_attempt_total_scores"
          }
        },
      ]
  },
  {
      "slug": "trfm_test_attempt_total_scores_grouped",
      "caption": "test_attempt_total_scores_grouped",
      "schema": [
        {slug: "asmt_code", type: "STRING", caption: "Assessment Code", description: "Assessment Code"},
        {slug: "lang", type: "STRING", caption: "Language", description: "Assessment language."},
        {slug: "form_code", type: "STRING", caption: "Form Code", description: "Form Code"},
        {slug: "uid", type: "INTEGER_UNSIGNED", caption: "Student UID", description: "Vretta unique ID for a student."},
        {slug: "test_attempt_id", type: "INTEGER_UNSIGNED", caption: "Test attempt id", description: "Vretta unique ID for a test attempt", is_primary_key: true},
        {slug: "total_score", type: "FLOAT", caption: "Total score", description: "Total score on the test"},
        {slug: "total_score_max", type: "INTEGER_UNSIGNED", caption: "Maximum total score", description: "Maximum total score on the test"},
        {slug: "num_questions", type: "INTEGER_UNSIGNED", caption: "Number of questions", description: "Number of questions"},
        {slug: "group_level", type: "STRING", caption: "Group Level", description: "Group level (high, mid, low)"},
      ],
      "description": "Puts each test attempt in the high/mid/low group based on total_score and cutscore criteria",
      "structure": "dataframe",
      "method": "transforms",
      "methodConfig": {
        "sequence": [
          {
            "kind": "apply-cutscores",
            "df_output": "total_scores",
            "config": {
              "df_input": "total_scores",
              "group_by": ["asmt_code", "lang", "form_code"],
              "score_column": "total_score",
              "label_column": "group_level",
              "cutscore_defs": "cutoff_points",
            }
          }
        ],
        "output": "total_scores"
      },
      "dependencySourcings": [
        {
          "param": "total_scores",
          "type": "dataframe",
          "config": {
              "asset": "trfm_test_attempt_total_scores"
          }
        },
        {
          "param": "cutoff_points",
          "type": "dataframe",
          "config": {
              "asset": "trfm_cutoff_points"
          }
        },
      ]
  },
  {
      "slug": "trfm_check_tw_exceptions_students_applied",
      "caption": "check_tw_exceptions_students_applied",
      "description": "Provides a list of the student exceptions applied, along with affected test attempts",
      "isCheckAsset": true,
      "severity": CheckAssetSeverity.WARNING,
      "structure": "dataframe",
      "method": "transforms",
      "methodConfig": {
        sequence: [
          {
            kind: "filter-col",
            df_output: "tes",
            config: {
              df_input: "tes",
              col: "category",
              comparison: "equals",
              value: "COURSE_REMAP"
            }
          },
          {
            kind: "filter-col",
            df_output: "twtar",
            config: {
              df_input: "twtar",
              col: "is_marking_req",
              comparison: "equals",
              value: 0
            }
          },
          {
            kind: "rename-cols",
            df_output: "twtar",
            config: {
              df_input: "twtar",
              cols: [
                {
                  from: "type_slug",
                  to: "twtar_type_slug"
                },
                {
                  from: "twtar_id",
                  to: "twtdar_id"
                }
              ]
            }
          },
          {
            kind: "join",
            df_output: "test_info",
            config: {
              how: "inner",
              left: "twtar",
              right: "ta",
              left_on: "twtdar_id",
            }
          },
          {
            kind: "join",
            df_output: "discreps",
            config: {
              how: "inner",
              left: "tes",
              right: "test_info",
              left_on: ['uid', 'twtar_type_slug'],
            }
          },
        ],
        output: "discreps"
      },
      "dependencySourcings": [
          {
              "param": "tes",
              "type": "dataframe",
              "config": {
                  "asset": "load_db_tw_exceptions_students_applied"
              }
          },
          {
            "param": "twtar",
            "type": "dataframe",
            "config": {
                "asset": "load_twtar"
            }
          },
          {
            "param": "ta",
            "type": "dataframe",
            "config": {
                "asset": "load_db_ta_valid"
            }
          }
      ]
  },
  {
    "slug": "trfm_tw_exceptions_items_flagged",
    "caption": "tw_exceptions_items_flagged",
    "description": "", // TODO
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      sequence: [
        {
          kind: "restrict-cols",
          df_output: "trfm_check_max_scores",
          config: {
            df_input: "trfm_check_max_scores",
            cols: ["score_expected", "formatted_response", "item_id"]
          },
        },
        {
          kind: "rename-cols",
          df_output: "trfm_check_max_scores",
          config: {
            df_input: "trfm_check_max_scores",
            new_names: {
              "score_computed": "score_override",
              "formatted_response": "match_response_value"
            },
            inplace: true
          }
        },
        {
          kind: "restrict-cols",
          df_output: "lang",
          config: {
            df_input: "load_db_tqr",
            cols: ["item_id", "lang", "test_design_id"]
          },
        },
        {
          kind: "restrict-cols",
          df_output: "load_db_twtar",
          config: {
            df_input: "load_db_twtar",
            cols: ["td_id", "test_window_id"]
          },
        },
        {
          kind: "join",
          df_output: "result",
          config: {
            how: "inner",
            left: "trfm_check_max_scores",
            right: "lang",
            left_on: "item_id",
          }
        },
        {
          kind: "join",
          df_output: "result",
          config: {
            how: "inner",
            left: "load_db_twtar",
            right: "result",
            left_on: "td_id",
            right_on: "test_design_id"
          } // TODO: drop td_id and test_design_id
        },
      ],
      output: "result"
    },
    "dependencySourcings": [
      {
        "param": "trfm_check_max_scores",
        "type": "dataframe",
        "config": {
          "asset": "trfm_check_max_scores"
        }
      },
      {
        "param": "load_db_tqr",
        "type": "dataframe",
        "config": {
          "asset": "load_db_tqr"
        }
      },
      {
        "param": "load_db_twtar",
        "type": "dataframe",
        "config": {
          "asset": "load_twtar"
        }
      }
    ]
  },
  {
      "slug": "trfm_check_discrepant_tqers",
      "caption": "check_discrepant_tqers",
      "description": "Shows TQER records that assign multiple unrevoked scores and/or score_max values to the same response", // TODO
    "isCheckAsset": true,
    "severity": "warning",
      "structure": "dataframe",
      "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          kind: "group-by",
          df_output: "resp_counts",
          config: {
            df_input: "tqer",
            group_by: ["item_id", "lang", "formatted_response"],
            agg: [
              {
                col_new: "num_scores_per_response",
                agg_type: "nunique",
                col_target: "score",
              },
              {
                col_new: "num_max_score_per_response",
                agg_type: "nunique",
                col_target: "score_max",
              },
              {
                col_new: "tqer_ids",
                agg_type: "concat",
                col_target: "tqer_id",
              },
            ]
          },
        },
        // Apply messages to discrepancies
        {
          "kind": "set-where",
          "df_output": "resp_counts",
          "config": {
            "df_input": "resp_counts",
            "inplace": true,
            "condition": {
              "col": "num_scores_per_response",
              "comparison": "greater-than",
              "value": 1
            },
            "values": {
              "message1": "Multiple scores assigned to the same response. "
            }
          }
        },
        {
          "kind": "set-where",
          "df_output": "resp_counts",
          "config": {
            "df_input": "resp_counts",
            "inplace": true,
            "condition": {
              "col": "num_max_score_per_response",
              "comparison": "greater-than",
              "value": 1
            },
            "values": {
              "message2": "Multiple max_score values assigned to the same response. "
            }
          }
        },
        {
          kind: "map-col",
          df_output: "resp_counts",
          config: {
            df_input: "resp_counts",
            col_output: "message",
            source_cols: ["message1", "message2"],
            operation: "concat",
          }
        },
          {
            kind: "filter-col",
            df_output: "resp_counts",
            config: {
              df_input: "resp_counts",
              col: "message",
              comparison: "not-equal",
              value: ";",
            }
          },
        {
          kind: "restrict-cols",
          df_output: "resp_counts",
          config: {
            df_input: "resp_counts",
            cols: ["item_id", "lang", "formatted_response", "tqer_ids", "message"]
          }
        },
        // TODO: add authoring info
      ],
      "output": "resp_counts"
    },
      "dependencySourcings": [
          {
              "param": "tqer",
              "type": "dataframe",
              "config": {
                  "asset": "load_db_tqer"
              }
          },
      /* TODO: leaving out authoring group and test_question_set for now
          {
              "param": "tq",
              "type": "dataframe",
              "config": {
                  "asset": "load_db_tq"
              }
          },
          {
              "param": "tqs",
              "type": "dataframe",
              "config": {
                  "asset": "load_db_tqs"
              }
          },
          {
              "param": "ag",
              "type": "dataframe",
              "config": {
                  "asset": "load_db_ag"
              }
          }
      */
      ]
  },
  {
      "slug": "trfm_check_tqer_tei_score_discrepancies",
      "caption": "check_tqer_tei_score_discrepancies",
      "description": "Returns contradictions in scores between TQER and applied item exceptions", // TODO
      "isCheckAsset": true,
      "severity": CheckAssetSeverity.ERROR,
      "structure": "dataframe",
      "method": "transforms",
      "methodConfig": {
        sequence: [
          {
            kind: "rename-cols",
            df_output: "tei",
            config: {
              df_input: "tei",
              new_names: {
                "match_response_value": "formatted_response",
              }
            }
          },
          {
            kind: "join",
            df_output: "discrepancies",
            config: {
              how: "inner",
              left: "tqer",
              right: "tei",
              left_on: ["item_id", "formatted_response"],
            }
          },
          {
            kind: "filter-col",
            df_output: "discrepancies",
            config: {
              df_input: "discrepancies",
              col: "is_score_override",
              comparison: "equals",
              value: 1
            }
          },
          {
            kind: "filter-col",
            df_output: "discrepancies",
            config: {
              df_input: "discrepancies",
              col: "score",
              comparison: "not-equal",
              value_src: {"df": "discrepancies", "col": "score_override"}
            }
          },
          // {
          //   kind: "rename-cols",
          //   df_output: "discrepancies",
          //   config: {
          //     df_input: "discrepancies",
          //     new_names: { "lang_x": "lang" } // Lang_x will already be called "lang", but "lang_y" is "lang_right"
          //   }
          // },
          {
            kind: "restrict-cols",
            df_output: "discrepancies",
            config: {
              df_input: "discrepancies",
              cols: ["item_id', 'item_label', 'formatted_response', 'lang', 'score', 'score_max', 'is_score_override', 'score_override"]
            },
          },
        ],
        output: "discrepancies"
      },
      "dependencySourcings": [
          {
              "param": "tqer",
              "type": "dataframe",
              "config": {
                  "asset": "load_db_tqer"
              }
          },
          {
            "param": "tei",
            "type": "dataframe",
            "config": {
                "asset": "load_db_tw_exceptions_items_applied"
            }
          }
      ]
  },
  {
      "slug": "trfm_check_taqr_discrepant_scores_grouped",
      "caption": "check_taqr_discrepant_scores_grouped",
      "description": "Groups trfm_check_taqr_discrepant_scores to create an overview of the TAQR discrepancies", // TODO
      "isCheckAsset": true,
      "severity": CheckAssetSeverity.WARNING,
      "structure": "dataframe",
      "method": "transforms",
      "methodConfig": {
        sequence: [
          {
            kind: "group-by",
            df_output: "trfm_check_taqr_discrepant_scores",
            config: {
              df_input: "trfm_check_taqr_discrepant_scores",
              group_by: ['item_id', 'lang', 'formatted_response', 'tqer_score', 'taqr_score'],
              agg: [{
                col_new: "count",
                agg_type: "count",
                col_target: "test_attempt_id",
              }]
            },
          },
          {
            kind: "restrict-cols",
            df_output: "trfm_correct_tqer",
            config: {
              df_input: "trfm_correct_tqer",
              cols: ["item_id", "lang", "formatted_response"]
            },
          },
          {
            kind: "join",
            df_output: "result",
            config: {
              how: "left",
              left: "trfm_check_taqr_discrepant_scores",
              right: "trfm_correct_tqer",
              left_on: ['item_id', 'lang'],
            }
          },
        ],
        output: "result"
      },
      "dependencySourcings": [
          {
              "param": "trfm_check_taqr_discrepant_scores",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_check_taqr_discrepant_scores"
              }
          },
          {
            "param": "trfm_correct_tqer",
            "type": "dataframe",
            "config": {
                "asset": "trfm_correct_tqer"
            }
          }
      ]
  },
  {
      "slug": "load_db_form_designs",
      "caption": "form_designs",
      "description": "Form designs.",
      "structure": "dataframe",
    "schema": [
      {"slug": "twtar_id", "caption": "Test Window TD Alloc Rule ID", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window TD allocation rule.", },
      {"slug": "test_window_id", "caption": "Test Window Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window associated with the form design.", },
      {"slug": "test_term", "caption": "Test Term", "type": "STRING", "description": "Term or period for the test.", },
      {"slug": "test_design_id", "caption": "Test Design Id", "type": "INTEGER_UNSIGNED", "description": "Unique identifier for the test design.", },
      {"slug": "type_slug", "caption": "Type Slug", "type": "STRING", "description": "Slug representing the type of test or assessment.", },
      {"slug": "course_name_full", "caption": "Course Name Full", "type": "STRING", "description": "Full name of the course associated with the form design.", },
      {"slug": "course_code", "caption": "Course Code", "type": "STRING", "description": "Code representing the course.", },
      {"slug": "lang", "caption": "Language", "type": "STRING", "description": "Language for the form.", },
      {"slug": "form_code", "caption": "Form Code", "type": "STRING", "description": "Code representing the form design.", },
      {"slug": "component_slug", "caption": "Component Slug", "type": "STRING", "description": "Slug representing a component of the form design.", },
      {"slug": "is_secured", "caption": "Is Secured", "type": "BOOL_INT", "description": "Flag indicating whether the form design is secured.", },
      {"slug": "is_questionnaire", "caption": "Is Questionnaire", "type": "BOOL_INT", "description": "Indicates whether the form design is a questionnaire.", },
      {"slug": "test_design_id", "caption": "Test Design Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test design.", },
      {"slug": "tqr_ovrd_td_id", "caption": "Tqr Ovrd Td Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for test question register override test design.", },
    ],
      "method": "query",
      "methodConfig": {
        "querySlug": "SQL_03_ASMT_CONTENT_FORM_DESIGNS"
      },
      "dependencySourcings": [
          {
              "param": "twtar_ids",
              "type": "asset-col",
              "config": {
                  "asset": "load_twtar",
                  "col": "twtar_id"
              }
          },
      ]
  },
  {
      "slug": "trfm_check_tfmi",
      "caption": "check_tfmi",
      "description": "", // TODO
      "isCheckAsset": true,
      "severity": CheckAssetSeverity.WARNING,
      "structure": "dataframe",
      "method": "transforms",
      "methodConfig": {
        sequence: [
          {
            kind: "restrict-cols",
            df_output: "load_db_tfmi",
            config: {
              df_input: "load_db_tfmi",
              cols: ["test_panel_id", "question_id"]
            },
          },
          {
            kind: "restrict-cols",
            df_output: "load_db_tf",
            config: {
              df_input: "load_db_tf",
              cols: ["test_panel_id"]
            },
          },
          {
            kind: "join",
            df_output: "result",
            config: {
              how: "inner",
              left: "load_db_tf",
              right: "load_db_tfmi",
              left_on: "test_panel_id",
            }
          },
        ],
        output: "result"
      },
      "dependencySourcings": [
          {
              "param": "load_db_tfmi",
              "type": "dataframe",
              "config": {
                  "asset": "load_db_tfmi"
              }
          },
          {
            "param": "load_db_tf",
            "type": "dataframe",
            "config": {
                "asset": "load_db_tf"
            }
          }
      ]
  },
  {
      "slug": "trfm_blank_responses",
      "caption": "blank_responses",
      "description": "Saved responses that have is_nr = 1 (non-responses).",
      "structure": "dataframe",
    "schema": [
      {"slug": "taqr_id", "caption": "Test Attempt Question Response ID", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test attempt question response.", },
      {"slug": "ta_id", "caption": "Test Attempt Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test attempt.", },
      {"slug": "item_id", "caption": "Item Id", "type": "STRING", "description": "Identifier for the item.", },
      {"slug": "score", "caption": "Score", "type": "INTEGER_UNSIGNED", "description": "Score achieved for the blank response.", },
      {"slug": "weight", "caption": "Weight", "type": "INTEGER_UNSIGNED", "description": "Weight assigned to the blank response in scoring.", },
      {"slug": "is_nr", "caption": "Is No Response (NR)", "type": "INTEGER_UNSIGNED", "description": "Indicates if the response was not provided", },
      {"slug": "is_invalid", "caption": "Is Invalid", "type": "INTEGER_UNSIGNED", "description": "Indicates if the response is invalid", },
    ],
      "method": "transforms",
      "methodConfig": {
        sequence: [
          {
            kind: "filter-col",
            df_output: "load_taqr",
            config: {
              df_input: "load_taqr",
              col: "is_nr",
              comparison: "equals",
              value: 1
            }
          },
        ],
        output: "load_taqr"
      },
      "dependencySourcings": [
          {
              "param": "load_taqr",
              "type": "dataframe",
              "config": {
                  "asset": "load_taqr"
              }
          }
      ]
  },
  {
    "slug": "trfm_item_responses_fixed", // TODO: separate asset for student item exceptions
    "caption": "item_responses_fixed",
    "description": "Item responses with item exceptions (score overrides) applied.",
    "structure": "dataframe",
    "schema": [
      {"slug": "is_invalid", "caption": "Is Invalid", "type": "INTEGER_UNSIGNED", "description": "Flag indicating whether the response is invalid", },
      {"slug": "response_type", "caption": "Response Type", "type": "STRING", "description": "Type of the response, such as multiple choice or free text.", },
      {"slug": "formatted_response", "caption": "Formatted Response", "type": "STRING", "description": "The formatted version of the test response.", },
      {"slug": "item_id", "caption": "Item Id", "type": "STRING", "description": "Identifier for the test item.", },
      {"slug": "score", "caption": "Score", "type": "INTEGER_UNSIGNED", "description": "Score assigned to the response", },
      {"slug": "is_nr", "caption": "Is No Response (NR)", "type": "INTEGER_UNSIGNED", "description": "Flag indicating whether the response is a no-response.", },
      {"slug": "taqr_id", "caption": "Test Attempt Question Response ID", "type": "INTEGER_UNSIGNED", "description": "Identifier for the question response in a test attempt.", },
      {"slug": "formatted_response_strict", "caption": "Formatted Response Strict", "type": "STRING", "description": "Strictly formatted version of the response.", },
      {"slug": "is_score_override", "caption": "Is Score Override", "type": "INTEGER_UNSIGNED", "description": "Flag indicating whether the score has been overridden.", },
      {"slug": "weight", "caption": "Weight", "type": "INTEGER_UNSIGNED", "description": "Weight of the response for scoring purposes.", },
      {"slug": "ta_id", "caption": "Test Attempt Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test attempt associated with the response.", },
    ],
    "partitionBy": {
      "key": "partby_ta_ids",
    },
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        // TODO: use the consolidated student item exceptions asset
        {
          "kind": "replace-where",
          "df_output": "item_responses",
          "config": {
            "df_target": "item_responses",
            "df_source": "score_overrides",
            "target_key": ["item_id", "formatted_response",],
            "source_key": ["item_id", "match_response_value",],
            "target_col": "score",
            "source_col": "score_override",
            "indicator_col": "is_score_override",
          },
        },
      ],
      "output": "item_responses"
    },
    "dependencySourcings": [
      {
        "param": "item_responses",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_responses"
        }
      },
      {
        "param": "score_overrides",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_score_overrides_consolidated"
        }
      },
      // { // TODO: student item exceptions may be later or earlier asset
      //   "param": "load_db_tw_exceptions_student_items_applied",
      //   "type": "dataframe",
      //   "config": {
      //     "asset": "load_db_tw_exceptions_student_items_applied"
      //   }
      // },
      // { // TODO: used to get uid for student item exceptions, can be added to trfm_item_responses instead
      //   "param": "load_db_ta",
      //   "type": "dataframe",
      //   "config": {
      //     "asset": "load_db_ta"
      //   }
      // }
    ]
  },
  { // TODO: this needs to be retrieved from an API endpoint (and assessment profile)
      "slug": "load_spec_threshold_values", // No dependencies
      "caption": "spec_threshold_values",
      "description": "Response rate thresholds for inclusion in Field Test analysis.",
      "structure": "dataframe",
      "method": "placeholder",
      "methodConfig": {},
      "dependencySourcings": [
      ]
  },
  {
      "slug": "trfm_check_principal_kit_students",
      "caption": "principal_kit_students",
      "description": "Returns students who have attempts but are not in the window's associated principal kit",
      "isCheckAsset": true,
      "severity": CheckAssetSeverity.WARNING,
      "structure": "dataframe",
      "method": "transforms",
      "methodConfig": {
        sequence: [
          {
            kind: "filter-col",
            df_output: "trfm_student_attempts_consolidated",
            config: {
              df_input: "trfm_student_attempts_consolidated",
              col: "uid",
              comparison: "not-in",
              value_src: "student_uids"
            }
          },
        ],
        output: "trfm_student_attempts_consolidated"
      },
      "dependencySourcings": [
          {
            "param": "trfm_student_attempts_consolidated",
            "type": "dataframe",
            "config": {
                "asset": "trfm_student_attempts_consolidated"
            }
          },
          {
            "param": "student_uids",
            "type": "asset-col",
            "config": {
                "asset": "trfm_principal_kit_students",
                "col": "student_uid",
            }
        }
      ]
  },
  { // TODO: this will need to be retrieved from an API endpoint using the assessment profile
      "slug": "spec_exclusion_threshold", // No dependencies: these are hardcoded constants
      "caption": "spec_exclusion_threshold",
      "description": "Participation thresholds for field tests.",
      "schema": [
        {"slug": "is_stem", "caption": "Is Stem", "type": "BOOL_INT", "description": "Flag indicating whether an assessment is for a stem course.", },
        {"slug": "test_response_type", "caption": "Test Response Type", "type": "STRING", "description": "Response type for the test.", },
        {"slug": "max_omit_rate", "caption": "Max Omit Rate", "type": "FLOAT", "description": "Omit rate threshold value.", },
      ],
      "structure": "dataframe",
      "method": "api-method",
      "methodConfig": {
        "slug": "SQL_05B_SPEC_EXCLUSION_THRESHOLD",
      },
      "dependencySourcings": [
      ]
  },
  {
      "slug": "trfm_check_nr_response_discrepancies",
      "caption": "check_nr_response_discrepancies",
      "description": "Returns responses where formatted_response is empty but is_nr=0",
      "isCheckAsset": true,
      "severity": CheckAssetSeverity.ERROR,
      "structure": "dataframe",
      "partitionBy": {
        "key": "partby_ta_ids",
        "concatOut": true,
      },
      "method": "transforms",
      "methodConfig": {
        sequence: [
          {
            kind: "filter-col",
            df_output: "taqr",
            config: {
              df_input: "item_responses",
              col: 'NR',
              comparison: 'equals',
              value: 0
            }
          },
          {
            kind: "filter-col",
            df_output: "taqr",
            config: {
              df_input: "taqr",
              col: 'formatted_response',
              comparison: 'is-empty-string',
            }
          },
        ],
        output: "taqr"
      },
      "dependencySourcings": [
          {
              "param": "taqr",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_item_responses_nf_omit"
              }
          }
      ]
  },
  { // TODO: probably deprecated
      "slug": "load_db_item_scale_register_consolidated",
      "caption": "item_scale_register_consolidated",
      "description": "Item scale register",
      "structure": "dataframe",
      "method": "placeholder",
      "methodConfig": {},
      "dependencySourcings": [
          {
              "param": "load_db_twtar",
              "type": "dataframe",
              "config": {
                  "asset": "load_twtar"
              }
          },
          {
            "param": "load_db_tqr",
            "type": "dataframe",
            "config": {
                "asset": "load_db_tqr"
            }
          },
          {
            "param": "load_db_tw",
            "type": "dataframe",
            "config": {
                "asset": "load_db_tw"
            }
          },
      ]
  },
  {
      "slug": "trfm_check_part_marks_summary",
      "caption": "check_part_marks_summary",
      "description": "Summarizes trfm_check_part_marks by item", // TODO
      "isCheckAsset": true,
      "severity": CheckAssetSeverity.ERROR,
      "structure": "dataframe",
      "method": "transforms",
      "methodConfig": {
        sequence: [
          {
            kind: "group-by",
            df_output: "check_part_marks",
            config: {
              df_input: "check_part_marks",
              group_by: ["item_id", "type_slug", "formatted_response", "key_score", "score_max", "taqr_score", "part_marks_loc"],
              agg: [{
                col_new: "num_tests_affected",
                agg_type: "nunique",
                col_target: "test_attempt_id",
              }]
            },
          },
          {
            kind: "sort-by",
            df_output: "check_part_marks",
            config: {
              df_input: "check_part_marks",
              sort_by_columns: ["item_id", "formatted_response"],
              ascending: [true, true],
            }
          }
        ],
        output: "check_part_marks"
      },
      "dependencySourcings": [
          {
              "param": "check_part_marks",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_check_part_marks"
              }
          }
      ]
  },
  {
      "slug": "load_db_aw_accommodation_options",
      "caption": "aw_accommodation_options",
      "description": "Accommodation options in for the selected test windows.",
      "structure": "dataframe",
    "schema": [
      {"slug": "acc_id", "caption": "Accommodation Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the accommodation.", },
      {"slug": "accommodation_number", "caption": "Accommodation Number", "type": "INTEGER_UNSIGNED", "description": "Number representing the accommodation.", },
      {"slug": "accommodation_name", "caption": "Accommodation Name", "type": "STRING", "description": "Name of the accommodation.", },
      {"slug": "value_data_type", "caption": "Value Data Type", "type": "STRING", "description": "Data type of the accommodation value.", },
      {"slug": "acc_type_slug", "caption": "Accommodation Type Slug", "type": "STRING", "description": "Slug representing the accommodation type.", },
      {"slug": "sort_order", "caption": "Sort Order", "type": "INTEGER_UNSIGNED", "description": "Order for the sort.", },
    ],
      "method": "query",
      "methodConfig": {
        "querySlug": "SQL_02_ASMT_SPECS_ACCOMMODATION_OPTIONS",
      },
      "dependencySourcings": [
          {
              "param": "tw_type_slugs",
              "type": "asset-col",
              "config": {
                  "asset": "load_db_tw",
                  "col": "type_slug",
              }
          }
      ]
  },
  {
      "slug": "load_db_student_accommodations",
      "caption": "student_accommodations",
      "description": "Raw student accommodations.",
      "structure": "dataframe",
    "schema": [
      {"slug": "ua_id", "caption": "User Accommodation Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the student accommodation record.", },
      {"slug": "uid", "caption": "User Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the student.", },
      {"slug": "acc_id", "caption": "Accommodation Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the accommodation.", },
      {"slug": "accommodation_value", "caption": "Accommodation Value", "type": "STRING", "description": "Value assigned to the accommodation for the student.", },
    ],
      "method": "query-chunked",
      "methodConfig": {
        "querySlug": "SQL_04B_REGISTRATIONS_STUDENT_ACCOMMODATIONS",
        "chunkedParam": 'uids',
        "chunkSize": 5000,
        "makeDistinct": true
      },
      "dependencySourcings": [
          {
              "param": "acc_ids",
              "type": "asset-col",
              "config": {
                  "asset": "load_db_aw_accommodation_options",
                  "col": "acc_id",
              }
          },
          {
              "param": "uids",
              "type": "asset-col",
              "config": {
                  "asset": "load_db_students",
                  "col": "uid",
              }
          }
      ]
  },
  {
      "slug": "trfm_check_taqr_discrepant_scores",
      "caption": "check_taqr_discrepant_scores",
      "description": "Finds taqrs with the wrong score, based on tqer", // TODO
      "isCheckAsset": true,
      "severity": CheckAssetSeverity.WARNING,
      "structure": "dataframe",
      "method": "placeholder",
      "methodConfig": {},
      "dependencySourcings": [
          {
              "param": "trfm_item_responses_fixed",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_item_responses_fixed"
              }
          },
          {
            "param": "trfm_tqer",
            "type": "dataframe",
            "config": {
                "asset": "trfm_tqer"
            }
          },
          {
            "param": "trfm_check_discrepant_tqers",
            "type": "dataframe",
            "config": {
                "asset": "trfm_check_discrepant_tqers"
            }
          }
      ]
  },
  { // TODO: This will need to be retrieved from an API endpoint (assessment profiles)
      "slug": "trfm_exclusion_threshold",
      "caption": "exclusion_threshold",
      "description": "Attempt information with omit rate and exclude flag which suggests if they should be excluded from item statistics.",
      "schema": [
        {"slug": "twtdar_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window TD Alloc Rule ID", "description": "Identifier for the Test Window TD Allocation Rule associated with the test attempt.", "is_primary_key": false},
        {"slug":"asmt_code","type":"STRING","caption":"Assessment Code","description":"Code representing the assessment.","is_primary_key":false},
        {"slug": "test_design_id", "caption": "Test Design Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test design"},
        {"slug":"test_attempt_id","type":"INTEGER_UNSIGNED","caption":"Test Attempt Id","description":"Identifier for the specific test attempt.","is_primary_key":true},
        {"slug": "nr_sum", "caption": "No Response Sum", "type": "INTEGER_UNSIGNED", "description": "Count of no responses for an attempt."},
        {"slug": "n_items", "caption": "No Response Sum", "type": "INTEGER_UNSIGNED", "description": "Count of items for an attempt."},
        {"slug": "omit_rate", "caption": "Omit Rate", "type": "FLOAT", "description": "Omit rate for the attempt."},
        {"slug": "is_stem", "caption": "Is Stem", "type": "BOOL_INT", "description": "Flag indicating whether an assessment is for a stem course.", },
        {"slug": "exclude", "caption": "Exclude", "type": "BOOL_INT", "description": "Flag indicating whether an attempt should be excluded based on the exclusion rules.", },
      ],
      "structure": "dataframe",
      "method": "api-method",
      "methodConfig": {
        "slug": "SQL_05B_EXCLUSION_THRESHOLDS",
      },
      "partitionBy": {
        "key": "partby_ta_ids",
      },
      "dependencySourcings": [
          {
              "param": "item_responses_fixed_filled",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_item_responses_fixed_filled"
              }
          },
          {
            "param": "item_register",
            "type": "dataframe",
            "config": {
                "asset": "trfm_item_register_consolidated"
              }
          },
          {
            "param": "spec_exclusion_threshold",
            "type": "dataframe",
            "config": {
                "asset": "spec_exclusion_threshold"
              }
          },
          {
            "param": "is_field_test_override",
            "type": "job-config",
            "config": {
              "configSlug": "pipeline_config",
              "param": "is_field_test_override"
            }
          },
          {
            "param": "is_field_test",
            "type": "asset-col",
            "config": {
              "asset": "load_db_tw",
              "col": "is_field_test",
            }
          }
      ]
  },
  {
      "slug": "trfm_student_accommodations_consolidated",
      "caption": "student_accommodations_consolidated",
      "description": "Student accommodations, combined with the accommodation options, and no null records",
      "structure": "dataframe",
      schema: [
        {"slug": "ua_id", "type": "INTEGER_UNSIGNED", "caption": "User Accommodation Id", "description": "Identifier for the user accommodation record.", "is_primary_key": true},
        {"slug": "uid", "type": "INTEGER_UNSIGNED", "caption": "User Id", "description": "Identifier for the user who is associated with the accommodation.", "is_primary_key": false},
        {"slug": "stu_gov_id", "type": "STRING", "caption": "Student Government ID", "description": "Alberta Student Government ID.", "is_primary_key": false},
        {"slug": "acc_id", "type": "INTEGER_UNSIGNED", "caption": "Accommodation Id", "description": "Identifier for the accommodation.", "is_primary_key": false},
        {"slug": "accommodation_value", "type": "STRING", "caption": "Accommodation Value", "description": "Specific value for the accommodation.", "is_primary_key": false},
        {"slug": "accommodation_number", "type": "INTEGER_UNSIGNED", "caption": "Accommodation Number", "description": "Accommodation number", "is_primary_key": false},
        {"slug": "accommodation_name", "type": "STRING", "caption": "Accommodation Name", "description": "Name or description of the accommodation.", "is_primary_key": false},
        {"slug": "value_data_type", "type": "STRING", "caption": "Value Data Type", "description": "Data type for the accommodation value.", "is_primary_key": false},
        {"slug": "acc_type_slug", "type": "STRING", "caption": "Accommodation Type Slug", "description": "Slug or code representing type of accommodation.", "is_primary_key": false},
      ],
      "method": "transforms",
      "methodConfig": {
        sequence: [
          {
            kind: "restrict-cols",
            df_output: "students",
            config: {
              df_input: "students",
              cols: ["uid", "stu_gov_id"]
            }

          },
          {
            kind: "filter-col",
            df_output: "accoms",
            config: {
              df_input: "accoms",
              col: "accommodation_value",
              comparison: "non-null",
            }
          },
          {
            kind: "join",
            df_output: "accoms",
            config: {
              how: "inner",
              left: "accoms",
              right: "load_db_aw_accommodation_options",
              left_on: "acc_id",
              right_on: "acc_id",
            }
          },
          {
            kind: "join",
            df_output: "accoms",
            config: {
              how: "inner",
              left: "accoms",
              right: "students",
              left_on: "uid",
              right_on: "uid",
            }
          },
          {
            kind: "restrict-cols",
            df_output: "accoms",
            config: {
              df_input: "accoms",
              cols: ["ua_id", "uid", "stu_gov_id", "acc_id", "accommodation_value", "accommodation_number",  "accommodation_name", "value_data_type", "acc_type_slug",]
            },
          },
        ],
        output: "accoms"
      },
      "dependencySourcings": [
          {
              "param": "students",
              "type": "dataframe",
              "config": {
                  "asset": "load_db_students"
              }
          },
          {
            "param": "accoms",
            "type": "dataframe",
            "config": {
                "asset": "load_db_student_accommodations"
            }
          },
          {
            "param": "load_db_aw_accommodation_options",
            "type": "dataframe",
            "config": {
                "asset": "load_db_aw_accommodation_options"
            }
          }
      ]
  },
  {
    "slug": "trfm_test_attempt_total_scores",
    "caption": "total_scores",
    "description": "Total score for each test attempt, with some additional data used by statistics assets",
    "schema": [
      {"slug":"asmt_code","type":"STRING","caption":"Assessment Code","description":"Code representing the assessment.","is_primary_key":false},
      {"slug":"lang","type":"STRING","caption":"Language","description":"Language of the assessment.","is_primary_key":false},
      {"slug":"form_code","type":"STRING","caption":"Form Code","description":"Code representing the test form.","is_primary_key":false},
      {"slug":"uid","type":"INTEGER_UNSIGNED","caption":"User Id","description":"Identifier for the user associated with the test attempt.","is_primary_key":false},
      {"slug":"test_attempt_id","type":"INTEGER_UNSIGNED","caption":"Test Attempt Id","description":"Identifier for the specific test attempt.","is_primary_key":true},
      {"slug":"total_score","type":"INTEGER_UNSIGNED","caption":"Total Score","description":"Total score achieved by the user in the test attempt.","is_primary_key":false},
      {"slug":"total_score_max","type":"INTEGER_UNSIGNED","caption":"Total Score Max","description":"Maximum possible total score for the test attempt.","is_primary_key":false},
      {"slug":"num_questions","type":"INTEGER_UNSIGNED","caption":"Number of Questions","description":"Total number of questions included in the test attempt.","is_primary_key":false},
    ],

    "structure": "dataframe",
    "partitionBy": {
      "key": "test_form",
      "concatOut": true,
    },
    "method": "transforms",
    "methodConfig": {
      sequence: [
        {
          kind: "restrict-cols",
          df_output: "test_attempts",
          config: {
            df_input: "test_attempts",
            cols: ["uid", "ta_id"]
          },
        },
        {
          kind: "group-by",
          df_output: "item_responses",
          config: {
            df_input: "item_responses",
            group_by: ["asmt_code", "lang", "form_code", "test_attempt_id"],
            agg: [
              {
                col_new: "total_score",
                agg_type: "sum",
                col_target: "score",
              },
              {
                col_new: "total_score_max",
                agg_type: "sum",
                col_target: "score_max",
              },
              {
                col_new: "num_questions",
                agg_type: "nunique",
                col_target: "item_id",
              },
            ]
          },
        },
        {
          kind: "join",
          df_output: "item_responses",
          config: {
            how: "inner",
            left: "item_responses",
            right: "test_attempts",
            left_on: "test_attempt_id",
            right_on: "ta_id",
          }
        },
      ],
      output: "item_responses"
    },
      "dependencySourcings": [
          {
              "param": "item_responses",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_item_responses_nf_omit"
              }
          },
          {
            "param": "test_attempts",
            "type": "dataframe",
            "config": {
                "asset": "trfm_test_attempts_exceptions_applied"
            }
          }
      ]
  },
  {
      "slug": "trfm_check_max_scores",
      "caption": "check_max_scores",
      "description": "Finds where tqer is incorrectly denoting the correct answer, based on taqr",
      "isCheckAsset": true,
      "severity": CheckAssetSeverity.WARNING,
      "structure": "dataframe",
      "method": "placeholder",
      "methodConfig": {},
      "dependencySourcings": [
          {
              "param": "trfm_taqr_responses_formatted_2",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_taqr_responses_formatted_2"
              }
          },
          {
            "param": "trfm_keys",
            "type": "dataframe",
            "config": {
                "asset": "trfm_keys"
            }
          },
          {
            "param": "load_taqr",
            "type": "dataframe",
            "config": {
                "asset": "load_taqr"
            }
          }
      ]
  },
  {
      "slug": "trfm_pre_check_score_max_discrepancies",
      "caption": "pre_check_score_max_discrepancies",
      "description": "Returns the discrepancies after checking if all the records for a particular item id have the same score_max",
      "structure": "dataframe",
      "method": "transforms",
      "methodConfig": {
        sequence: [
          {
            kind: "filter-col",
            df_output: "df_item_responses",
            config: {
              df_input: "df_item_responses",
              col: "item_id",
              comparison: "in",
              value_src: "item_ids"
            }
          },
          {
            kind: "group-by",
            df_output: "df_item_responses",
            config: {
              df_input: "df_item_responses",
              group_by: ["item_id", "score", "score_max", "formatted_response"],
              agg: [{
                col_new: "count",
                agg_type: "count",
                col_target: "test_attempt_question_response_id",
              }]
            },
          },
          {
            kind: "group-by",
            df_output: "majority_score_max",
            config: {
              df_input: "df_item_responses",
              group_by: ["item_id"],
              agg: [{
                col_new: "majority_score_max",
                agg_type: "mode",
                col_target: "score_max",
              }]
            },
          },
          {
            kind: "join",
            df_output: "merged_df",
            config: {
              how: "inner",
              left: "df_item_responses",
              right: "majority_score_max",
              left_on: "item_id",
            }
          },
          {
            kind: "filter-col",
            df_output: "merged_df",
            config: {
              df_input: "merged_df",
              col: "score_max",
              comparison: "not-equal",
              value_src: {"df": "merged_df", "col": "majority_score_max"}
            }
          },
        ],
        output: "merged_df"
      },
      "dependencySourcings": [
          {
              "param": "df_item_responses",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_item_responses_fixed"
              }
          },
          {
            "param": "tqr",
            "type": "dataframe",
            "config": {
                "asset": "load_db_tqr"
            }
          }
      ]
  },
  {
    "slug": "load_db_td",
    "caption": "test_designs",
    "description": "Test design information (name and creation date)",
    "structure": "dataframe",
    "schema": [
      {"slug": "td_id", "caption": "Test Design Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test design.", "is_primary_key": true,},
      {"slug": "test_design_name", "caption": "Test Design Name", "type": "STRING", "description": "The name of the test design.", "is_primary_key": false,},
      {"slug": "created_on", "caption": "Created On", "type": "DATETIME", "description": "When the test design was created", "is_primary_key": false,},
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_03_ASMT_CONTENT_TEST_DESIGNS"
    },
    "dependencySourcings": [
      {
        "param": "td_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_twtar",
          "col": "td_id",
        }
      },
    ]
  },
  {
      "slug": "trfm_check_taqr_nr_score",
      "caption": "check_taqr_nr_score",
      "description": "Returns responses where is_nr=1 but the score is not 0",
      "isCheckAsset": true,
      "severity": CheckAssetSeverity.ERROR,
      "structure": "dataframe",
      "method": "transforms",
      "partitionBy": {
        "key": "partby_ta_ids",
        "concatOut": true,
      },
      "methodConfig": {
        sequence: [
          {
            kind: "filter-col",
            df_output: "item_responses",
            config: {
              df_input: "item_responses",
              col: "NR",
              comparison: "equals",
              value: 1
            }
          },
          {
            kind: "filter-col",
            df_output: "item_responses",
            config: {
              df_input: "item_responses",
              col: "score",
              comparison: "not-equal",
              value: 0
            }
          },
          {
            kind: "filter-col",
            df_output: "item_responses",
            config: {
              df_input: "item_responses",
              col: "score",
              comparison: "not-null",
            }
          },
        ],
        output: "item_responses"
      },
      "dependencySourcings": [
          {
              "param": "item_responses",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_item_responses_nf_omit"
              }
          }
      ]
  },
  {
      "slug": "check_cutoff_points",
      "caption": "check_cutoff_points",
      "description": "Validates score frequencies against cutoff points and flags test designs with high/low population discrepancies exceeding 5%.",
      "structure": "dataframe",
      "method": "placeholder",
      "methodConfig": {},
      "dependencySourcings": [
          {
              "param": "trfm_cutoff_points",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_cutoff_points"
              }
          },
          {
            "param": "trfm_score_frequency",
            "type": "dataframe",
            "config": {
                "asset": "trfm_score_frequency"
            }
          }
      ]
  },
  {
      "slug": "trfm_check_part_marks",
      "caption": "check_part_marks",
      "description": "Flags items that are either receiving partial marks in TAQR or TQER", // TODO
      "isCheckAsset": true,
      "severity": CheckAssetSeverity.ERROR,
      "structure": "dataframe",
      "method": "placeholder",
      "methodConfig": {},
      "dependencySourcings": [
          {
              "param": "trfm_check_part_marks",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_check_part_marks"
              }
          },
          {
            "param": "trfm_item_responses_fixed",
            "type": "dataframe",
            "config": {
                "asset": "trfm_item_responses_fixed"
            }
          }
      ]
  },
  {
    "slug": "trfm_form_stats",
    "caption": "form_stats",
    "description": "Calculates the overall score and enrollment statistics for each form",
    "structure": "dataframe",
    "schema": [
      {"slug": "test_design_id", "caption": "Test Design Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test design", },
      {"slug": "type_slug", "caption": "Type Slug", "type": "STRING", "description": "Slug representing the type of assessment.", },
      {"slug": "test_lang", "caption": "Test Lang", "type": "STRING", "description": "Language in which the test was administered.", },
      {"slug": "form_code", "caption": "Form Code", "type": "STRING", "description": "Identifier for the test form.", },
      {"slug": "course_name_full", "caption": "Course Name Full", "type": "STRING", "description": "Full name of the course associated with the assessment.", },
      {"slug": "course_code", "caption": "Course Code", "type": "STRING", "description": "Code identifying the course.", },
      {"slug": "is_cr", "caption": "Is Constructed Response", "type": "INTEGER_UNSIGNED", "description": "Indicates if the test includes constructed response questions", },
      {"slug": "num_attempts", "caption": "Number of Attempts", "type": "INTEGER_UNSIGNED", "description": "Total number of test attempts.", },
      {"slug": "mean", "caption": "Mean", "type": "FLOAT", "description": "Mean score for the test.", },
      {"slug": "variance", "caption": "Variance", "type": "FLOAT", "description": "Variance of the scores for the test.", },
      {"slug": "std_dev", "caption": "Standard Deviation", "type": "FLOAT", "description": "Standard deviation of the scores for the test.", },
      {"slug": "total_score_max", "caption": "Total Score Max", "type": "INTEGER_UNSIGNED", "description": "Maximum total score for the test.", },
      {"slug": "num_examinees", "caption": "Number of Examinees", "type": "INTEGER_UNSIGNED", "description": "Total number of examinees for the test.", },
      {"slug": "num_questions", "caption": "Number of Questions", "type": "INTEGER_UNSIGNED", "description": "Total number of questions in the test.", },
      {"slug": "mean_in_percent", "caption": "Mean In Percent", "type": "FLOAT", "description": "Mean score expressed as a percentage.", },
      {"slug": "std_dev_percent", "caption": "Standard Deviation Percent", "type": "FLOAT", "description": "Standard deviation expressed as a percentage.", },
      {"slug": "cutoff_low", "caption": "Cutoff Low", "type": "INTEGER_UNSIGNED", "description": "Lower cutoff score for categorization.", },
      {"slug": "cutoff_high", "caption": "Cutoff High", "type": "INTEGER_UNSIGNED", "description": "Upper cutoff score for categorization.", },
    ],
    "method": "transforms",
    "methodConfig": {
      sequence: [
        {
          kind: "group-by",
          df_output: "form_stats",
          config: {
            df_input: "total_scores",
            group_by: ["asmt_code", "lang", "form_code"],
            agg: [
              {
                col_new: "num_attempts",
                agg_type: "nunique",
                col_target: "test_attempt_id",
              },
              {
                col_new: "mean",
                agg_type: "mean",
                col_target: "total_score",
              },
              {
                col_new: "variance",
                agg_type: "variance",
                col_target: "total_score",
              },
              {
                col_new: "std_dev",
                agg_type: "stdev",
                col_target: "total_score",
              },
              {
                col_new: "total_score_max",
                agg_type: "max",
                col_target: "total_score_max",
              },
              {
                col_new: "num_examinees",
                agg_type: "nunique",
                col_target: "uid",
              },
              {
                col_new: "num_questions",
                agg_type: "max",
                col_target: "num_questions",
              },
            ]
          },
        },
        {
          kind: "map-col",
          df_output: "form_stats",
          config: {
            df_input: "form_stats",
            col_output: "mean_in_percent",
            source_cols: ["mean", "total_score_max"],
            operation: "percentage",
          }
        },
        {
          kind: "map-col",
          df_output: "form_stats",
          config: {
            df_input: "form_stats",
            col_output: "std_dev_percent",
            source_cols: ["std_dev", "total_score_max"], // NOTE: the original formula (sqrt( stdev / total_score_max ) * 100) does not make sense, so instead just scale by max score
            operation: "percentage",
          }
        },
        // Add the high/low cutoff points
        {
          kind: "filter-col",
          df_output: "low_cut",
          config: {
            df_input: "cutoffs",
            col: "label",
            comparison: "equals",
            value: "low"
          }
        },
        {
          kind: "rename-cols",
          df_output: "low_cut",
          config: {
            df_input: "low_cut",
            new_names: {
              "cutoff": "cutoff_low",
            }
          }
        },
        {
          kind: "filter-col",
          df_output: "high_cut",
          config: {
            df_input: "cutoffs",
            col: "label",
            comparison: "equals",
            value: "mid" // cutoffs are upper bounds, so this is between mid and high
          }
        },
        {
          kind: "rename-cols",
          df_output: "high_cut",
          config: {
            df_input: "high_cut",
            new_names: {
              "cutoff": "cutoff_high",
            }
          }
        },
        {
          kind: "join",
          df_output: "cutoffs",
          config: {
            how: "inner",
            left: "low_cut",
            right: "high_cut",
            left_on: ["asmt_code", "lang", "form_code"],
            right_on: ["asmt_code", "lang", "form_code"],
          }
        },
        {
          kind: "restrict-cols",
          df_output: "cutoffs",
          config: {
            df_input: "cutoffs",
            cols: ["asmt_code", "lang", "form_code", "cutoff_low", "cutoff_high"]
          }
        },
        {
          kind: "join",
          df_output: "form_stats",
          config: {
            how: "left",
            left: "form_stats",
            right: "cutoffs",
            left_on: ["asmt_code", "lang", "form_code"],
            right_on: ["asmt_code", "lang", "form_code"],
          }
        },
        // Get the test_design_id from the item responses
        // TODO: this could be from the item register or even twtar
        {
          kind: "group-by",
          df_output: "item_register",
          config: {
            df_input: "item_register",
            group_by: ["test_design_id", "type_slug", "test_lang", "form_code", "course_name_full", "course_code"],
            agg: [
              {
                col_new: "is_cr",
                agg_type: "max",
                col_target: "is_human_scored",
              },
            ]
          },
        },
        {
          kind: "drop-duplicates",
          df_output: "item_register",
          config: {
            df_input: "item_register"
          }
        },
        {
          kind: "join",
          df_output: "form_stats",
          config: {
            how: "left",  // TODO: can be inner, but that does not support multiple keys yet
            left: "item_register",
            right: "form_stats",
            left_on: ["type_slug", "test_lang", "form_code"],
            right_on: ["asmt_code", "lang", "form_code"],
          }
        },
        // TODO: Drop columns num_questions?
        // TODO: add cronbach alpha column
        // TODO: add course_name_full, course_code, test_term, is_cr?, num_examinees, cutoff_high, cutoff_low, test_window_id, std_error
      ],
      output: "form_stats"
    },
    "dependencySourcings": [
      {
        "param": "item_register",
        "type": "dataframe",
        "config": {
            "asset": "load_item_register_consolidated"
        }
      },
      {
        "param": "total_scores",
        "type": "dataframe",
        "config": {
          "asset": "trfm_test_attempt_total_scores"
        }
      },
      {
        "param": "cutoffs",
        "type": "dataframe",
        "config": {
          "asset": "trfm_cutoff_points"
        }
      },
    ]
  },
  {
    "slug": "trfm_form_stats_unused",
    "caption": "form_stats",
    "description": "Calculates the overall score and enrollment statistics for each form",
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      sequence: [
        {
          kind: "rename-cols",
          df_output: "tqr",
          config: {
            df_input: "tqr",
            new_names: {
              "type_slug": "asmt_code",
              "response_lang": "lang",
            },
            inplace: true
          }
        },
        {
          kind: "restrict-cols",
          df_output: "tqr",
          config: {
            df_input: "tqr",
            cols: ["asmt_code" ,"course_name_full", "course_code", "lang", "form_code", "test_term", "test_design_id", "item_id", "is_cr"]
          },
        },
        {
          kind: "drop-duplicates",
          df_output: "tqr",
          config: {
            df_input: "tqr"
          }
        },
        // TODO: this is an inneficient way to get the test window id for each test_design_id
        {
          kind: "restrict-cols",
          df_output: "tw",
          config: {
            df_input: "taqr",
            cols: ["test_design_id", "test_window_id"]
          },
        },
        {
          kind: "drop-duplicates",
          df_output: "tw",
          config: {
            df_input: "tw"
          }
        },
        {
          kind: "join",
          df_output: "result",
          config: {
            how: "inner",
            left: "n",
            right: "scores",
            left_on: ["test_design_id", "asmt_code", "lang", "form_code"],
            right_on: ["test_design_id", "asmt_code", "lang", "form_code"],
          }
        },
        {
          kind: "join",
          df_output: "result",
          config: {
            how: "left",
            left: "tqr",
            right: "result",
            left_on: ["test_design_id", "asmt_code", "lang", "form_code"],
            right_on: ["test_design_id", "asmt_code", "lang", "form_code"],
          }
        },
        // {
        //   kind: "join",
        //   df_output: "result",
        //   config: {
        //     how: "left",
        //     left: "result",
        //     right: "freq",
        //     left_on: ["test_design_id", "type_slug", "lang", "form_code"],
        //     right_on: ["test_design_id", "type_slug", "lang", "form_code"],
        //   }
        // },
        {
          kind: "join",
          df_output: "result",
          config: {
            how: "left",
            left: "result",
            right: "error_stats",
            left_on: ["test_design_id", "type_slug", "lang", "form_code"],
            right_on: ["test_design_id", "type_slug", "lang", "form_code"],
          }
        },
        // TODO: Merge in cronbach alpha
        {
          kind: "join",
          df_output: "result",
          config: {
            how: "left",
            left: "result",
            right: "tw",
            left_on: ["test_design_id"],
            right_on: ["test_design_id"],
          }
        }, // TODO: drop columns score, item_id
        {
          kind: "drop-duplicates",
          df_output: "result",
          config: {
            df_input: "result"
          }
        },
      ],
      output: "result"
    },
    "dependencySourcings": [
      {
        "param": "error_stats",
        "type": "dataframe",
        "config": {
          "asset": "trfm_form_info_error_stats"
        }
      },
      {
        "param": "scores",
        "type": "dataframe",
        "config": {
          "asset": "trfm_form_info_scores" // Reviewed
        }
      },
      {
        "param": "n",
        "type": "dataframe",
        "config": {
          "asset": "trfm_form_info_counts" // Reviewed
        }
      },
      {
        "param": "taqr",
        "type": "dataframe",
        "config": {
          "asset": "trfm_form_info_merged" // Reviewed
        }
      },
      // {
      //   "param": "freq",
      //   "type": "dataframe",
      //   "config": {
      //     "asset": "trfm_cutoff_points"
      //   }
      // },
      {
        "param": "tqr",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_register_consolidated"
        }
      },
    ]
  },
  {
      "slug": "trfm_taqr_discrepant_scores",
      "caption": "taqr_discrepant_scores",
      "description": "Finds taqrs with the wrong score, based on tqer",
      "structure": "dataframe",
      "method": "placeholder",
      "methodConfig": {},
      "dependencySourcings": [
          {
              "param": "trfm_taqr_responses_formatted",
              "type": "dataframe",
              "config": {
                  "asset": "trfm_taqr_responses_formatted_2"
              }
          },
          {
            "param": "load_taqr",
            "type": "dataframe",
            "config": {
                "asset": "load_taqr"
            }
          },
          {
            "param": "tqer",
            "type": "dataframe",
            "config": {
                "asset": "trfm_tqer"
            }
          },
          {
            "param": "trfm_check_discrepant_tqers",
            "type": "dataframe",
            "config": {
                "asset": "trfm_check_discrepant_tqers"
            }
          }
      ]
  },
  {
    "slug": "trfm_tqr_score_max",
    "caption": "test_question_register_score_max",
    "description": "The test question register 'load_db_tqr', but with max scores and tqer_ids for correct responses, for each item from expected responses.",
    "structure": "dataframe",
    schema: [
      {"slug": "tqr_id", "type": "INTEGER_UNSIGNED", "caption": "Test Question Register Id", "description": "Identifier for the Test Question Register (TQR) record.", "is_primary_key": true},
      {"slug": "item_id", "type": "INTEGER_UNSIGNED", "caption": "Item Id", "description": "Identifier for the specific item in the test question register.", "is_primary_key": false},
      {"slug": "item_name", "type": "STRING", "caption": "Item Name", "description": "Name or description of the item in the test question register.", "is_primary_key": false},
      {"slug": "lang", "type": "STRING", "caption": "Language", "description": "Language associated with the item.", "is_primary_key": false},
      {"slug": "test_design_id", "type": "INTEGER_UNSIGNED", "caption": "Test Design Id", "description": "Identifier for the test design associated with the item.", "is_primary_key": false},
      {"slug": "is_questionnaire", "type": "BOOL_INT", "caption": "Is Questionnaire", "description": "Flag indicating whether the item is part of a questionnaire.", "is_primary_key": false},
      {"slug": "is_reading_passage", "type": "INTEGER_UNSIGNED", "caption": "Is Reading Passage", "description": "Flag indicating whether the item is a reading passage.", "is_primary_key": false},
      {"slug": "score_profile_id", "type": "INTEGER_UNSIGNED", "caption": "Score Profile Id", "description": "Identifier for the score profile associated with the item.", "is_primary_key": false},
      {"slug": "score_points", "type": "STRING", "caption": "Score Points", "description": "The score points or value associated with the item.", "is_primary_key": false},
      {"slug": "is_human_scored", "type": "INTEGER_UNSIGNED", "caption": "Is Human Scored", "description": "Flag indicating whether the item is scored manually by a human.", "is_primary_key": false},
      {"slug": "expected_answer", "type": "STRING", "caption": "Expected Answer", "description": "The correct or expected answer for the item.", "is_primary_key": false},
      {"slug": "item_nbr", "type": "INTEGER_UNSIGNED", "caption": "Item Number", "description": "The sequential number of the item in the test question register.", "is_primary_key": false},
      {"slug": "report_label_short", "type": "STRING", "caption": "Report Label Short", "description": "Short label for the item.", "is_primary_key": false},
      {"slug": "score_max", "type": "INTEGER_UNSIGNED", "caption": "Score Max", "description": "Maximum possible score for the item.", "is_primary_key": false},
      {"slug": "correct_tqer_ids", "type": "STRING", "caption": "Correct Tqer Ids", "description": "A list of identifiers for the correct TQER records related to the item.", "is_primary_key": false},
    ],
    "method": "transforms",
    "methodConfig": {
      sequence: [
        {
          kind: "join",
          df_output: "tqr",
          config: {
            how: "left",
            left: "tqr",
            right: "tqer_score_max",
            left_on: ["item_id", "lang"],
            right_on: ["item_id", "lang"],
          }
        },
        {
          kind: "fill-na",
          df_output: "tqr",
          "config": {
            "df_input": "tqr",
            "inplace": true,
            "values": {
              "score_max": null,
              "correct_tqer_ids": null,
            }
          }
        },
      ],
      output: "tqr"
    },
    "dependencySourcings": [
      {
        "param": "tqr",
        "type": "dataframe",
        "config": {
          "asset": "load_db_tqr",
        }
      },
      {
        "param": "tqer_score_max",
        "type": "dataframe",
        "config": {
          "asset": "trfm_correct_tqer",
        }
      },
    ]
  },
  {
    "slug": "load_db_tqr",
    "caption": "test_question_register",
    "description": "The test question register (like item_register_consolidated, but without some additional metadata",
    "structure": "dataframe",
    "schema": [
      {"slug": "tqr_id", "type": "INTEGER_UNSIGNED", "caption": "Test Question Register Id", "description": "Unique identifier for the test question register.", "is_primary_key": true},
      {"slug": "item_id", "type": "INTEGER_UNSIGNED", "caption": "Item Id", "description": "Identifier for the item.", "is_primary_key": false},
      {"slug": "item_name", "type": "STRING", "caption": "Item Name", "description": "Name or label of the test item.", "is_primary_key": false},
      {"slug": "lang", "type": "STRING", "caption": "Language", "description": "Language of the item.", "is_primary_key": false},
      {"slug": "test_design_id", "type": "INTEGER_UNSIGNED", "caption": "Test Design Id", "description": "Identifier for the test design associated with the question.", "is_primary_key": false},
      {"slug": "is_questionnaire", "type": "BOOL_INT", "caption": "Is Questionnaire", "description": "Flag indicating if the item is a questionnaire.", "is_primary_key": false},
      {"slug": "is_reading_passage", "type": "INTEGER_UNSIGNED", "caption": "Is Reading Passage", "description": "Flag indicating if the item includes a reading passage.", "is_primary_key": false},
      {"slug": "score_profile_id", "type": "INTEGER_UNSIGNED", "caption": "Score Profile Id", "description": "Identifier for the scoring profile associated with the item.", "is_primary_key": false},
      {"slug": "score_points", "type": "STRING", "caption": "Score Points", "description": "Maximum score points allocated for the item.", "is_primary_key": false},
      {"slug": "is_human_scored", "type": "INTEGER_UNSIGNED", "caption": "Is Human Scored", "description": "Flag indicating whether the item is scored manually by a human.", "is_primary_key": false},
      {"slug": "expected_answer", "type": "STRING", "caption": "Expected Answer", "description": "Expected or correct answer for the test item.", "is_primary_key": false},
      {"slug": "item_nbr", "type": "INTEGER_UNSIGNED", "caption": "Item Number", "description": "Sequential number of the test item in the test design.", "is_primary_key": false},
      {"slug": "report_label_short", "type": "STRING", "caption": "Report Label Short", "description": "Short label for item.", "is_primary_key": false},
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_03_ASMT_CONTENT_ITEM_REGISTER"
    },
    "dependencySourcings": [
      {
        "param": "td_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_twtar",
          "col": "td_id",
        }
      },
    ]
  },
  {
    "slug": "load_db_tqer",
    "caption": "expected_responses",
    "description": "Loads the expected responses for questions in the item register",
    "structure": "dataframe",
    "schema": [
      {"slug": "tqer_id", "caption": "Test Question Expected Answers Id", "type": "INTEGER_UNSIGNED", "description": "Unique identifier for the expected answers of a test question.", },
      {"slug": "item_id", "caption": "Item Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test item.", },
      {"slug": "lang", "caption": "Language", "type": "STRING", "description": "Language of the expected answers.", },
      {"slug": "formatted_response", "caption": "Formatted Response", "type": "STRING", "description": "The formatted response for the test question.", },
      {"slug": "score", "caption": "Score", "type": "INTEGER_UNSIGNED", "description": "Score assigned to the response.", },
      {"slug": "score_max", "caption": "Score Max", "type": "INTEGER_UNSIGNED", "description": "Maximum possible score for the test question.", },
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_03_ASMT_CONTENT_ITEM_EXPECTED_RESPONSES"
    },
    "dependencySourcings": [
      {
        "param": "item_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_tqr",
          "col": "item_id",
        }
      },
    ]
  },
  {
    "slug": "trfm_correct_tqer",
    "caption": "correct_tqer",
    "description": "Ids for correct expected responses, and maximum scores for each item in the test window",
    "structure": "dataframe",
    "schema": [
      {slug: "item_id", type: "INTEGER_UNSIGNED", caption: "Item ID", description: "Item ID"},
      {slug: "lang", type: "STRING", caption: "Language", description: "Language"},
      {slug: "correct_tqer_ids", type: "STRING", caption: "Correct TQER IDs", description: "Comma separated list of tqer_ids for correct responses"},
      {slug: "score_max", type: "", caption: "INTEGER_UNSIGNED", description: "Maximum score for the item"},
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        // Filter to only correct responses
        {
          "kind": "map-col",
          "df_output": "tqer",
          "config": {
              df_input: "tqer",
              col_output: "match",
              source_cols: ["score", "score_max"],
              operation: "equal",
              value_true: 1,
              value_false: 0
          }
        },
        {
          "kind": "filter-col",
          "df_output": "tqer",
          "config": {
            "df_input": "tqer",
            "col": "match",
            "comparison": "equals",
            "value": 1
          }
        },
        // NOTE: dagster removes cases where score_max = 0 at this point, but jladan has decided to keep it for the item register
        {
          "kind": "group-by",
          "df_output": "tqer",
          "config": {
            "df_input": "tqer",
            "group_by": ["item_id", "lang"],
            "agg": [
              {
                "col_new": "score_max",
                "agg_type": "max",
                "col_target": "score_max"
              },
              {
                "col_new": "correct_tqer_ids",
                "agg_type": "concat",
                "col_target": "tqer_id"
              }
            ]
          }
        }
      ],
      "output": "tqer"
    },
    "dependencySourcings": [
      {
        "param": "tqer",
        "type": "dataframe",
        "config": {
          "asset": "load_db_tqer"
        }
      },
      {
        "param": "tqr",
        "type": "dataframe",
        "config": {
          "asset": "load_db_tqr"
        }
      }
    ]
  },
  {
    "slug": "load_db_tqer_item_exceptions",
    "caption": "tqer_item_exceptions",
    "description": "Loads item exceptions created by the test admins.",
    "structure": "dataframe",
    "schema": [
      {"slug": "item_id", "caption": "Item Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test item associated with the exception.", },
      {"slug": "lang", "caption": "Language", "type": "STRING", "description": "Language of the test item.", },
      {"slug": "match_response_value", "caption": "Match Response Value", "type": "STRING", "description": "Expected response value for matching exceptions.", },
      {"slug": "score_override", "caption": "Score Override", "type": "INTEGER_UNSIGNED", "description": "Score value used to override the original score for the item.", },
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_05B_TQER_ITEM_EXCEPTIONS"
    },
    "dependencySourcings": [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      },
      {
        "param": "item_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_tqr",
          "col": "item_id",
        }
      },
    ]
  },
  {
    "slug": "trfm_item_score_overrides_consolidated",
    "caption": "item_score_overrides_consolidated",
    "description": "Combines item score overrides from tqer and tw_exceptions_items for single applications",
    "schema": [
      {"slug":"item_id","type":"INTEGER_UNSIGNED","caption":"Item Id","description":"Identifier for the specific item.","is_primary_key":true},
      {"slug":"lang","type":"STRING","caption":"Language","description":"Language associated with the item.","is_primary_key":false},
      {"slug":"match_response_value","type":"STRING","caption":"Match Response Value","description":"Specific response value that needs to match for the score override to apply.","is_primary_key":false},
      {"slug":"score_override","type":"INTEGER_UNSIGNED","caption":"Score Override","description":"Value to which the score is overridden for the item.","is_primary_key":false},
    ],
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "filter-col",
          "df_output": "item_exceptions",
          "config": {
            "df_input": "item_exceptions",
            "col": 'is_score_override',
            "comparison": 'equals',
            "value": 1
          }
        },
        {
          "kind": "restrict-cols",
          "df_output": "item_exceptions",
          "config": {
            "df_input": "item_exceptions",
            // TODO: test_window_id is not in tqer will this cause a problem with multi-window exports?
            "cols": ["item_id", "lang", "match_response_value", "score_override"]
          }
        },
        {
          "kind": "concat",
          "df_output": "result",
          "config": {
            // NOTE: order of dfs is important to prioritize tqer when overriding
            "df_inputs": ["tqer_exceptions", "item_exceptions"]
          }
        },
      ],
      "output": "result"
    },
    "dependencySourcings": [
      {
        "param": "item_exceptions",
        "type": "dataframe",
        "config": {
          "asset": "load_tw_item_exceptions",
          "cols": ["item_id", "lang", "match_response_value", "score_override", "is_score_override"]
        }
      },
      {
        "param": "tqer_exceptions",
        "type": "dataframe",
        "config": {
          "asset": "load_db_tqer_item_exceptions",
          "cols": ["item_id", "lang", "match_response_value", "score_override"]
        }
      },
    ]
  },
  {
    "slug": "trfm_test_attempt_last_response",
    "caption": "test_attempt_last_response",
    "description": "The last responded question for each test attempt",
    "structure": "dataframe",
    "partitionBy": {
      "key": "partby_ta_ids",
    },
    "schema": [
      {"slug": "test_attempt_id", "caption": "Test Attempt Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test attempt", },
      {"slug": "last_response_nbr", "caption": "Last Response Number", "type": "INTEGER_UNSIGNED", "description": "The sequence number of the last recorded response.", },
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        // Filter out is_nr, group by ta_id, and take the max item_nbr
        {
          "kind": "filter-col",
          "df_output": "item_responses",
          "config": {
            "df_input": "item_responses",
            "col": 'is_nr',
            "comparison": 'equals',
            "value": 0
          }
        },
        {
          "kind": "group-by",
          "df_output": "item_responses",
          "config": {
            "df_input": "item_responses",
            "group_by": ["test_attempt_id"],
            "agg": [{
              "col_new": "last_response_nbr",
              "agg_type": "max",
              "col_target": "item_nbr"
            }]
          }
        },
      ],
      "output": "item_responses"
    },
    "dependencySourcings": [
      {
        "param": "item_responses",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_responses_fixed_filled"
        }
      },
    ]
  },
  { slug: "trfm_item_scale_stats",
    caption: "item_scale_stats",
    scopes: ["schools"],
    description: "Item scale statistics",
    structure: "dataframe",
    "partitionBy": {
      "key": "test_form",
      "concatOut": true,
    },
    "schema": [
      {"slug": "n_total", "caption": "Total Number of Students", "type": "INTEGER_UNSIGNED", "description": "Total number of students who attempted the item.", },
      {"slug": "test_design_id", "caption": "Test Design Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test design.", },
      {"slug": "n_mid", "caption": "Number of students in mid group", "type": "INTEGER_UNSIGNED", "description": "Number of students in the mid performance group", },
      {"slug": "rpb", "caption": "Point biserial correlation (rpb)", "type": "FLOAT", "description": "Point biserial correlation for the item", },
      {"slug": "n_high", "caption": "Number of students in high group", "type": "INTEGER_UNSIGNED", "description": "Number of students in the high performance group.", },
      {"slug": "asmt_code", "caption": "Assessment Code", "type": "STRING", "description": "Code identifying the assessment", },
      {"slug": "crpb", "caption": "Corrected Point biserial correlation (crpb)", "type": "FLOAT", "description": "Corrected point biserial correlation for the item.", },
      {"slug": "item_nbr", "caption": "Item Number", "type": "INTEGER_UNSIGNED", "description": "Sequence number of the item within the test.", },
      {"slug": "NR", "caption": "No Response", "type": "INTEGER_UNSIGNED", "description": "Number of students who provided no response to the item.", },
      {"slug": "NF", "caption": "Not Finished", "type": "INTEGER_UNSIGNED", "description": "Number of students who did not finish the item.", },
      {"slug": "n_low", "caption": "Number of students in low group", "type": "INTEGER_UNSIGNED", "description": "Number of students in the low performance group.", },
      {"slug": "p_mid", "caption": "P (Difficulty) Mid group", "type": "FLOAT", "description": "Difficulty for the mid group", },
      {"slug": "item_id", "caption": "Item Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the item being assessed", },
      {"slug": "lang", "caption": "Language", "type": "STRING", "description": "Language of the assessment", },
      {"slug": "form_code", "caption": "Form Code", "type": "STRING", "description": "Identifier for the test form.", },
      {"slug": "omit", "caption": "Omit", "type": "INTEGER_UNSIGNED", "description": "Number of students who omitted the item.", },
      {"slug": "mean_score", "caption": "Mean Score", "type": "FLOAT", "description": "Average score achieved by students for the item.", },
      {"slug": "p", "caption": "Difficulty (P)", "type": "FLOAT", "description": "Difficulty of the item", },
      {"slug": "iri", "caption": "Item Reliability Index", "type": "FLOAT", "description": "Reliability index of the item.", },
      {"slug": "p_high", "caption": "P (Difficulty) High group", "type": "FLOAT", "description": "Difficulty for the high group, ", },
      {"slug": "p_low", "caption": "P (Difficulty) Low group", "type": "FLOAT", "description": "Difficulty for the low group", },
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        // TODO: determine the correct columns to group by according to spec (this should also be centralized in writing)
        { // trfm_item_scale_counts
          kind: "group-by",
          df_output: "item_mean_scores",
          config: {
            df_input: "item_responses",
            group_by: ["asmt_code", "form_code", "item_id"],
            agg: [{
              col_new: "mean_score",
              agg_type: "mean",
              col_target: "score",
            },
            {
              col_new: "n_total",
              agg_type: "count",
            },
            {
              col_new: "NR",
              agg_type: "sum",
              col_target: "is_nr"
            },
            {
              col_new: "NF",
              agg_type: "sum",
              col_target: "NF"
            },
            {
              col_new: "omit",
              agg_type: "sum",
              col_target: "omit"
            }
          ],
          },
        },
        { // Add the biserials
          "kind": "join",
          "df_output": "item_mean_scores",
          "config": {
            "how": "inner",
            "left": "item_mean_scores",
            "right": "point_biserial",
            "left_on": ["asmt_code", "form_code", "item_id"],
            "right_on": ["asmt_code", "form_code", "item_id"],
          }
        },
        // Add the high/mid/low grouped statistics
        {
          "kind": "join",
          "df_output": "item_mean_scores",
          "config": {
            "how": "inner",
            "left": "item_mean_scores",
            "right": "grouped_stats",
            "left_on": ["asmt_code", "form_code", "item_id"],
            "right_on": ["asmt_code", "form_code", "item_id"],
          }
        },
        // Re-attach the item_nbr
        // TODO: wouldn't be needed if counts included item_nbr in the group
        {
          kind: "drop-duplicates",
          df_output: "item_info",
          config: {
            df_input: "item_responses",
            subset: ["asmt_code", "form_code", "item_id"],
          },
        },
        {
          kind: "restrict-cols",
          df_output: "item_info",
          config: {
            df_input: "item_info",
            cols: ["asmt_code", "form_code", "item_id", "item_nbr", "lang"],
          },
        },
        {
          kind: "join",
          df_output: "item_mean_scores",
          config: {
            how: "inner",
            left: "item_info",
            right: "item_mean_scores",
            left_on: ["asmt_code", "form_code", "item_id"]
          },
        },
      ],
      output: "item_mean_scores"
    },
    dependencySourcings: [
      { param: "item_responses",
        type: "dataframe",
        config: {
          asset: "trfm_item_responses_nf_omit",
          cols: ["td_id", "item_id", "type_slug", "is_nr"],
        },
      },
      {
        param: "point_biserial",
        type: "dataframe",
        config: {
          asset: "trfm_item_point_biserials",
        },
      },
      {
        param: "grouped_stats",
        type: "dataframe",
        config: {
          asset: "trfm_item_scale_stats_grouped",
        }
      },
    ],
  },
  { // TODO: requires a lot of work to get grouped counts
    slug: 'trfm_item_response_value_stats',
    caption: 'item_response_value_stats',
    scopes: ["schools"],
    description: "Response values statistics",
    structure: "dataframe",
    "partitionBy": {
      "key": "test_form",
      "concatOut": true,
    },
    "schema": [
      {"slug": "asmt_code", "caption": "Assessment Code", "type": "STRING", "description": "Code identifying the assessment.", },
      {"slug": "form_code", "caption": "Form Code", "type": "STRING", "description": "Identifier for the test form.", },
      {"slug": "item_id", "caption": "Item Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the item being assessed.", },
      {"slug": "item_nbr", "caption": "Item number", "type": "INTEGER_UNSIGNED", "description": "Order the item appears in the test design.", },
      {"slug": "formatted_response", "caption": "Formatted Response", "type": "STRING", "description": "Formatted version of the response value", },
      {"slug": "score", "caption": "Score", "type": "INTEGER_UNSIGNED", "description": "Score associated with the response value.", },
      {"slug": "score_max", "caption": "Max Score", "type": "INTEGER_UNSIGNED", "description": "Maximum score possible for the item", },
      {"slug": "total", "caption": "Prop Total", "type": "FLOAT", "description": "Proportion of this response for the item", },
      {"slug": "high", "caption": "Prop. High", "type": "FLOAT", "description": "Proportion of this response in the high group", },
      {"slug": "mid", "caption": "Prop. Mid", "type": "FLOAT", "description": "Proportion of this response in the mid group", },
      {"slug": "low", "caption": "Prop. Low", "type": "FLOAT", "description": "Proportion of this response in the low group", },
      {"slug": "total_score", "caption": "Mean total score", "type": "FLOAT", "description": "Mean total score for all attempts with this response", },
      {"slug": "n_total", "caption": "n_total", "type": "INTEGER_UNSIGNED", "description": "Number of this response to the question", },
      {"slug": "n_high", "caption": "n_high", "type": "INTEGER_UNSIGNED", "description": "Number of this response in the high group", },
      {"slug": "n_mid", "caption": "n_mid", "type": "INTEGER_UNSIGNED", "description": "Number of this response in the mid group", },
      {"slug": "n_low", "caption": "n_low", "type": "INTEGER_UNSIGNED", "description": "Number of this response in the low group", },
      {"slug": "N_total", "caption": "N_total", "type": "INTEGER_UNSIGNED", "description": "Total number of responses to this item", },
      {"slug": "N_high", "caption": "N_high", "type": "INTEGER_UNSIGNED", "description": "Number of responses to this item in the high group", },
      {"slug": "N_mid", "caption": "N_mid", "type": "INTEGER_UNSIGNED", "description": "Number of responses to this item in the mid group", },
      {"slug": "N_low", "caption": "N_low", "type": "INTEGER_UNSIGNED", "description": "Number of responses to this item in the low group", },
      {"slug": "DP", "caption": "Discriminating Power", "type": "FLOAT", "description": "Discriminating Power", },
      {"slug": "SE_of_DP", "caption": "Standard error of DP", "type": "FLOAT", "description": "Standard error of DP", },
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        { // Get item counts from scale stats
          kind: "restrict-cols",
          df_output: "item_counts",
          config: {
            df_input: "item_counts",
            cols: ["item_id", "item_nbr", "n_total", "n_high", "n_mid", "n_low"],
          }
        },
        {
          kind: "rename-cols",
          df_output: "item_counts",
          config: {
            df_input: "item_counts",
            inplace: true,
            new_names: {
              "n_total": "N_total",
              "n_high": "N_high",
              "n_mid": "N_mid",
              "n_low": "N_low",
            }
          }
        },
        { // limit columns on item_responses
          kind: "restrict-cols",
          df_output: "item_responses",
          config: {
            df_input: "item_responses",
            cols: ["asmt_code", "form_code", "test_attempt_id", "item_id", "formatted_response", "score", "score_max", "is_nr"],
          }
        },
        { // Fill in non-responses
          "kind": "set-where",
          "df_output": "item_responses",
          "config": {
            "df_input": "item_responses",
            "inplace": true,
            "condition": {
              "col": "is_nr",
              "comparison": "equals",
              "value": 1
            },
            "values": {
              "formatted_response": "no_response",
            }
          }
        },
        { // Join on group_level
          "kind": "restrict-cols",
          "df_output": "ta_groups",
          "config": {
            "df_input": "ta_groups",
            "cols": ["test_attempt_id", "total_score", "group_level"]
          }
        },
        {
          "kind": "join",
          "df_output": "item_responses",
          "config": {
            "how": "inner",
            "left": "item_responses",
            "right": "ta_groups",
            "left_on": "test_attempt_id",
            "right_on": "test_attempt_id",
          },
        },
        { // total response count and mean total score
          kind: "group-by",
          df_output: "total_counts",
          config: {
            df_input: "item_responses",
            group_by: ["item_id", "formatted_response", "score", "score_max"],
            agg: [
              { col_new: "n_total", agg_type: "count", col_target: "item_id", },
              { col_new: "total_score", agg_type: "mean", col_target: "total_score", },
            ]
          }
        },
        { // counts by group_level
          "kind": "pivot",
          "df_output": "item_responses",
          "config": {
            "df_input": "item_responses",
            "group_by": ["asmt_code", "form_code", "item_id", "formatted_response",],
            "index": "group_level",
            "value": "is_correct",
            "agg": "count",
            "col_prefix": "n_"
          },
        },
        { // fill in any missing counts
          "kind": "fill-na",
          "df_output": "item_responses",
          "config": {
            "df_input": "item_responses",
            "inplace": true,
            "values": {
              "n_high": 0,
              "n_mid": 0,
              "n_low": 0,
            }
          }
        },
        { // Join up data sources
          kind: "join",
          df_output: "item_responses",
          config: {
            how: "inner",
            left: "item_responses",
            right: "total_counts",
            left_on: ["item_id", "formatted_response"],
            right_on: ["item_id", "formatted_response"],
          }
        },
        {
          kind: "join",
          df_output: "item_responses",
          config: {
            how: "inner",
            left: "item_responses",
            right: "item_counts",
            left_on: ["item_id"],
            right_on: ["item_id"],
          }
        },
        // TODO: At this point all required columns are included, and only proportions, DP and SE of DP is needed
        {
          kind: "map-col",
          df_output: "item_responses",
          config: {
            df_input: "item_responses",
            col_output: "total",
            source_cols: ["n_total", "N_total"],
            operation: "divide",
          },
        },
        {
          kind: "map-col",
          df_output: "item_responses",
          config: {
            df_input: "item_responses",
            col_output: "high",
            source_cols: ["n_high", "N_high"],
            operation: "divide",
          },
        },
        {
          kind: "map-col",
          df_output: "item_responses",
          config: {
            df_input: "item_responses",
            col_output: "mid",
            source_cols: ["n_mid", "N_mid"],
            operation: "divide",
          },
        },
        {
          kind: "map-col",
          df_output: "item_responses",
          config: {
            df_input: "item_responses",
            col_output: "low",
            source_cols: ["n_low", "N_low"],
            operation: "divide",
          },
        },
        {
          kind: "map-col",
          df_output: "item_responses",
          config: {
            df_input: "item_responses",
            col_output: "DP",
            source_cols: ["high", "low"],
            operation: "subtract",
          },
        },
        {
          kind: "map-col",
          df_output: "item_responses",
          config: {
            df_input: "item_responses",
            col_output: "SE_of_DP",
            source_cols: ["high", "N_high", "low", "N_low"],
            operation: "dp-std-err",
          },
        },
      ],
      output: "item_responses"
    },
    dependencySourcings: [
      { param: "item_responses",
        type: "dataframe",
        config: {
          asset: "trfm_item_responses_nf_omit",
          cols: ["asmt_code", "form_code", "test_attempt_id", "item_id", "formatted_response", "score", "score_max"],
        },
      },
      { param: "item_counts",
        type: "dataframe",
        config: {
          asset: "trfm_item_scale_stats",
          cols: ["item_id", "item_nbr", "n_total", "n_high", "n_mid", "n_low"],
        },
      },
      {
        "param": "ta_groups",
        "type": "dataframe",
        "config": {
          "asset": "trfm_test_attempt_total_scores_grouped",
          "cols": ["test_attempt_id", "group_level"]
        }
      }
    ],
  },
  // Marking reports
  {
    slug: 'load_scoring_extract',
    caption: 'scoring_extract',
    description: "For interim marker reports: load scoring extract from marking_window_id",
    structure: "dataframe",
    schema: [
      {"slug": "test_term", "type": "STRING", "caption": "Test Term", "description": "Term in which the test is conducted.", "is_primary_key": false},
      {"slug": "marker_uid", "type": "INTEGER_UNSIGNED", "caption": "Marker User Id", "description": "Unique identifier for the marker", "is_primary_key": false},
      {"slug": "stu_gov_id", "type": "STRING", "caption": "Student Government Id", "description": "Alberta Student Government ID.", "is_primary_key": false},
      {"slug": "is_valid_before_rescore", "type": "INTEGER_UNSIGNED", "caption": "Is Valid Before Rescore", "description": "Indicates if the score was valid before the rescore was applied.", "is_primary_key": false},
      {"slug": "marker_read_id", "type": "INTEGER_UNSIGNED", "caption": "Marker Read Id", "description": "Unique identifier for the specific read by a marker.", "is_primary_key": false},
      {"slug": "is_read_rules_processed", "type": "INTEGER_UNSIGNED", "caption": "Is Read Rules Processed", "description": "Indicates whether the read rules were processed for this record.", "is_primary_key": false},
      {"slug": "is_scale_rescore_surpressed", "type": "INTEGER_UNSIGNED", "caption": "Is Scale Rescore Surpressed", "description": "Flag to indicate if the scale rescore was suppressed.", "is_primary_key": false},
      {"slug": "is_rescore_indic", "type": "INTEGER_UNSIGNED", "caption": "Is Rescore Indicator", "description": "Indicates if this record is marked for rescore.", "is_primary_key": false},
      {"slug": "from_read_rule", "type": "STRING", "caption": "From Read Rule", "description": "Specifies the source rule applied for this read.", "is_primary_key": false},
      {"slug": "test_window_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window Id", "description": "Unique identifier for the test window.", "is_primary_key": false},
      {"slug": "is_rescore", "type": "INTEGER_UNSIGNED", "caption": "Is Rescore", "description": "Indicates whether this is a rescore entry.", "is_primary_key": false},
      {"slug": "course_code", "type": "STRING", "caption": "Course Code", "description": "Code representing the course for which the scoring was done.", "is_primary_key": false},
      {"slug": "score_value", "type": "INTEGER_UNSIGNED", "caption": "Score Value", "description": "The score assigned for this item or response.", "is_primary_key": false},
      {"slug": "is_insufficient", "type": "INTEGER_UNSIGNED", "caption": "Is Insufficient", "description": "Flag indicating if the response was deemed insufficient.", "is_primary_key": false},
      {"slug": "read_rule_type", "type": "STRING", "caption": "Read Rule Type", "description": "Type of read rule applied.", "is_primary_key": false},
      {"slug": "item_nbr", "type": "INTEGER_UNSIGNED", "caption": "Item Number", "description": "Sequential number of the test item in the test design.", "is_primary_key": false},
      {"slug": "mcbr_id", "type": "INTEGER_UNSIGNED", "caption": "Marking Claimed Batch Response Id", "description": "Identifier for the batch response claimed during the marking process.", "is_primary_key": false},
      {"slug": "is_non_response", "type": "INTEGER_UNSIGNED", "caption": "Is Non Response", "description": "Indicates whether the response was classified as \"Non Response.\"", "is_primary_key": false},
      {"slug": "scale_report_label_short", "type": "STRING", "caption": "Scale Report Label Short", "description": "Short label for the scale.", "is_primary_key": false},
      {"slug": "type_slug", "type": "STRING", "caption": "Type Slug", "description": "Slug representing the type of assessment.", "is_primary_key": false},
      {"slug": "form_code", "type": "STRING", "caption": "Form Code", "description": "Code representing the test form.", "is_primary_key": false},
      {"slug": "is_local_mark", "type": "INTEGER_UNSIGNED", "caption": "Is Local Mark", "description": "Flag indicating if the score was a local mark.", "is_primary_key": false},
      {"slug": "is_deleted_cause_pending", "type": "INTEGER_UNSIGNED", "caption": "Is Deleted Cause Pending", "description": "Indicates if the record was marked as deleted due to pending cause.", "is_primary_key": false},
      {"slug": "stu_uid", "type": "INTEGER_UNSIGNED", "caption": "Student User Id", "description": "Unique identifier for the student.", "is_primary_key": false},
      {"slug": "school_code", "type": "STRING", "caption": "School Code", "description": "Code identifying the school of the student.", "is_primary_key": false},
      {"slug": "item_scale_nbr", "type": "INTEGER_UNSIGNED", "caption": "Item Scale Number", "description": "Number representing the scale used for scoring this item.", "is_primary_key": false},
      {"slug": "read_nbr", "type": "INTEGER_UNSIGNED", "caption": "Read Number", "description": "The number of times the item was read.", "is_primary_key": false},
      {"slug": "is_before_rescore", "type": "INTEGER_UNSIGNED", "caption": "Is Before Rescore", "description": "Indicates whether the entry was before the rescore process.", "is_primary_key": false},
      {"slug": "task_slug", "type": "STRING", "caption": "Task Slug", "description": "Identifier or slug representing the task.", "is_primary_key": false},
      {"slug": "is_scale_surpressed", "type": "INTEGER_UNSIGNED", "caption": "Is Scale Surpressed", "description": "Flag to indicate whether the scoring scale was suppressed.", "is_primary_key": false},
      {"slug": "is_sent_back", "type": "INTEGER_UNSIGNED", "caption": "Is Sent Back", "description": "Indicates if the response was sent back.", "is_primary_key": false},
      {"slug": "is_deleted", "type": "INTEGER_UNSIGNED", "caption": "Is Deleted", "description": "Flag indicating whether the record was deleted.", "is_primary_key": false},
      {"slug": "lang", "type": "STRING", "caption": "Language", "description": "Language in which the assessment was administered or scored.", "is_primary_key": false},
      {"slug": "component_slug", "type": "STRING", "caption": "Component Slug", "description": "Slug representing the component.", "is_primary_key": false},
      {"slug": "item_id", "type": "INTEGER_UNSIGNED", "caption": "Item Id", "description": "Unique identifier for the item.", "is_primary_key": false},
      {"slug": "is_attempt_withheld", "type": "INTEGER_UNSIGNED", "caption": "Is Attempt Withheld", "description": "Indicates if the scoring attempt was withheld.", "is_primary_key": false},
      {"slug": "marker_profile_id", "type": "STRING", "caption": "Marker Profile Id", "description": "Identifier for the marker's profile.", "is_primary_key": false},
      {"slug": "attempt_id", "type": "INTEGER_UNSIGNED", "caption": "Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": false},
      {"slug": "item_human_score_id", "type": "INTEGER_UNSIGNED", "caption": "Item Human Score Id", "description": "Identifier for the human-assigned score for this item.", "is_primary_key": false},
    ],
    method: 'api-request',
    methodConfig: {
      endpoint: 'public/scor-lead/window-stats',
      method: 'find',
      props: {'marking_window_id': ''}, // TODO: props handling needs to be refactored
      data: {},
    },
    dependencySourcings: [
      {
        "param": "marking_window_id",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "marking_window_id"
        }
      },
    ],
  },
  {
    slug: 'load_marker_user_roles',
    caption: 'marker_user_roles',
    description: "For interim marker reports: load user roles for markers based on marking_window_id",
    structure: "dataframe",
    schema: [
      {"slug": "uid", "type": "INTEGER_UNSIGNED", "caption": "User Id", "description": "Identifier for the user.", "is_primary_key": true},
      {"slug": "role_type", "type": "STRING", "caption": "Role Type", "description": "Role type for the user.", "is_primary_key": false},
    ],
    method: 'query',
    methodConfig: {
      querySlug: 'SQL_04A_GROUPS_MARKER_USER_ROLES',
    },
    dependencySourcings: [
      {
        "param": "marking_window_id",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "marking_window_id"
        }
      },
    ]
  },
  {
    slug: 'trfm_scoring_extract_filtered',
    caption: 'scoring_extract_filtered',
    description: "For interim marker reports: Filtered version of the scoring extract",
    structure: "dataframe",
    schema: [
      {"slug": "test_term", "type": "STRING", "caption": "Test Term", "description": "Term in which the test is conducted.", "is_primary_key": false},
      {"slug": "marker_uid", "type": "INTEGER_UNSIGNED", "caption": "Marker User Id", "description": "Unique identifier for the marker", "is_primary_key": false},
      {"slug": "stu_gov_id", "type": "STRING", "caption": "Student Government Id", "description": "Alberta Student Government ID.", "is_primary_key": false},
      {"slug": "is_valid_before_rescore", "type": "INTEGER_UNSIGNED", "caption": "Is Valid Before Rescore", "description": "Indicates if the score was valid before the rescore was applied.", "is_primary_key": false},
      {"slug": "marker_read_id", "type": "INTEGER_UNSIGNED", "caption": "Marker Read Id", "description": "Unique identifier for the specific read by a marker.", "is_primary_key": false},
      {"slug": "is_read_rules_processed", "type": "INTEGER_UNSIGNED", "caption": "Is Read Rules Processed", "description": "Indicates whether the read rules were processed for this record.", "is_primary_key": false},
      {"slug": "is_scale_rescore_surpressed", "type": "INTEGER_UNSIGNED", "caption": "Is Scale Rescore Surpressed", "description": "Flag to indicate if the scale rescore was suppressed.", "is_primary_key": false},
      {"slug": "is_rescore_indic", "type": "INTEGER_UNSIGNED", "caption": "Is Rescore Indicator", "description": "Indicates if this record is marked for rescore.", "is_primary_key": false},
      {"slug": "from_read_rule", "type": "STRING", "caption": "From Read Rule", "description": "Specifies the source rule applied for this read.", "is_primary_key": false},
      {"slug": "test_window_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window Id", "description": "Unique identifier for the test window.", "is_primary_key": false},
      {"slug": "is_rescore", "type": "INTEGER_UNSIGNED", "caption": "Is Rescore", "description": "Indicates whether this is a rescore entry.", "is_primary_key": false},
      {"slug": "course_code", "type": "STRING", "caption": "Course Code", "description": "Code representing the course for which the scoring was done.", "is_primary_key": false},
      {"slug": "score_value", "type": "INTEGER_UNSIGNED", "caption": "Score Value", "description": "The score assigned for this item or response.", "is_primary_key": false},
      {"slug": "is_insufficient", "type": "INTEGER_UNSIGNED", "caption": "Is Insufficient", "description": "Flag indicating if the response was deemed insufficient.", "is_primary_key": false},
      {"slug": "read_rule_type", "type": "STRING", "caption": "Read Rule Type", "description": "Type of read rule applied.", "is_primary_key": false},
      {"slug": "item_nbr", "type": "INTEGER_UNSIGNED", "caption": "Item Number", "description": "Sequential number of the test item in the test design.", "is_primary_key": false},
      {"slug": "mcbr_id", "type": "INTEGER_UNSIGNED", "caption": "Marking Claimed Batch Response Id", "description": "Identifier for the batch response claimed during the marking process.", "is_primary_key": false},
      {"slug": "is_non_response", "type": "INTEGER_UNSIGNED", "caption": "Is Non Response", "description": "Indicates whether the response was classified as \"Non Response.\"", "is_primary_key": false},
      {"slug": "scale_report_label_short", "type": "STRING", "caption": "Scale Report Label Short", "description": "Short label for the scale.", "is_primary_key": false},
      {"slug": "type_slug", "type": "STRING", "caption": "Type Slug", "description": "Slug representing the type of assessment.", "is_primary_key": false},
      {"slug": "form_code", "type": "STRING", "caption": "Form Code", "description": "Code representing the test form.", "is_primary_key": false},
      {"slug": "is_local_mark", "type": "INTEGER_UNSIGNED", "caption": "Is Local Mark", "description": "Flag indicating if the score was a local mark.", "is_primary_key": false},
      {"slug": "is_deleted_cause_pending", "type": "INTEGER_UNSIGNED", "caption": "Is Deleted Cause Pending", "description": "Indicates if the record was marked as deleted due to pending cause.", "is_primary_key": false},
      {"slug": "stu_uid", "type": "INTEGER_UNSIGNED", "caption": "Stu Uid", "description": "Unique identifier for the student.", "is_primary_key": false},
      {"slug": "school_code", "type": "STRING", "caption": "School Code", "description": "Code identifying the school of the student.", "is_primary_key": false},
      {"slug": "item_scale_nbr", "type": "INTEGER_UNSIGNED", "caption": "Item Scale Nbr", "description": "Number representing the scale used for scoring this item.", "is_primary_key": false},
      {"slug": "read_nbr", "type": "INTEGER_UNSIGNED", "caption": "Read Nbr", "description": "The number of times the item was read.", "is_primary_key": false},
      {"slug": "is_before_rescore", "type": "INTEGER_UNSIGNED", "caption": "Is Before Rescore", "description": "Indicates whether the entry was before the rescore process.", "is_primary_key": false},
      {"slug": "task_slug", "type": "STRING", "caption": "Task Slug", "description": "Identifier or slug representing the task.", "is_primary_key": false},
      {"slug": "is_scale_surpressed", "type": "INTEGER_UNSIGNED", "caption": "Is Scale Surpressed", "description": "Flag to indicate whether the scoring scale was suppressed.", "is_primary_key": false},
      {"slug": "is_sent_back", "type": "INTEGER_UNSIGNED", "caption": "Is Sent Back", "description": "Indicates if the response was sent back.", "is_primary_key": false},
      {"slug": "is_deleted", "type": "INTEGER_UNSIGNED", "caption": "Is Deleted", "description": "Flag indicating whether the record was deleted.", "is_primary_key": false},
      {"slug": "lang", "type": "STRING", "caption": "Lang", "description": "Language in which the assessment was administered or scored.", "is_primary_key": false},
      {"slug": "component_slug", "type": "STRING", "caption": "Component Slug", "description": "Slug representing the component.", "is_primary_key": false},
      {"slug": "item_id", "type": "INTEGER_UNSIGNED", "caption": "Item Id", "description": "Unique identifier for the item.", "is_primary_key": false},
      {"slug": "is_attempt_withheld", "type": "INTEGER_UNSIGNED", "caption": "Is Attempt Withheld", "description": "Indicates if the scoring attempt was withheld.", "is_primary_key": false},
      {"slug": "marker_profile_id", "type": "STRING", "caption": "Marker Profile Id", "description": "Identifier for the marker's profile.", "is_primary_key": false},
      {"slug": "attempt_id", "type": "INTEGER_UNSIGNED", "caption": "Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": false},
      {"slug": "item_human_score_id", "type": "INTEGER_UNSIGNED", "caption": "Item Human Score Id", "description": "Identifier for the human-assigned score for this item.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [{
        kind: "filter-col",
        df_output: "marker_user_roles",
        config: {
          df_input: "marker_user_roles",
          col: 'role_type',
          comparison: 'in',
          value: ['mrkg_ctrl', 'mrkg_supr']
        }
      },
      {
        kind: "filter-col",
        df_output: "result",
        config: {
          df_input: "scoring_extract",
          col: 'marker_uid',
          comparison: 'not-in',
          value_src: {"df": "marker_user_roles", "col": "uid"}
        }
      }
      ],
      output: "result"
    },
    dependencySourcings: [
      { param: "scoring_extract",
        type: "dataframe",
        config: {
          asset: "load_scoring_extract",
        },
      },
      { param: "marker_user_roles",
        type: "dataframe",
        config: {
          asset: "load_marker_user_roles",
        },
      },
    ],
  },
  {
    slug: 'trfm_marker_data',
    caption: 'marker_data',
    description: "For marker reports: Scoring extract filtered only for marker related data",
    structure: "dataframe",
    schema: [
      {"slug": "type_slug", "type": "STRING", "caption": "Type Slug", "description": "Slug representing the type of assessment.", "is_primary_key": false},
      {"slug": "attempt_id", "type": "INTEGER_UNSIGNED", "caption": "Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": false},
      {"slug": "item_id", "type": "INTEGER_UNSIGNED", "caption": "Item Id", "description": "Unique identifier for the item.", "is_primary_key": false},
      {"slug": "task_slug", "type": "STRING", "caption": "Task Slug", "description": "Identifier or slug representing the task.", "is_primary_key": false},
      {"slug": "marker_uid", "type": "INTEGER_UNSIGNED", "caption": "Marker Uid", "description": "Unique identifier for the marker", "is_primary_key": false},
      {"slug": "marker_read_id", "type": "INTEGER_UNSIGNED", "caption": "Marker Read Id", "description": "Unique identifier for the specific read by a marker.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [{
        kind: "filter-col",
        df_output: "scoring_extract",
        config: {
          df_input: "scoring_extract",
          col: 'is_deleted',
          comparison: 'equals',
          value: 0
        }
      },
      {
        kind: "restrict-cols",
        df_output: "result",
        config: {
          df_input: "scoring_extract",
          cols: ["type_slug", "attempt_id", "item_id", "task_slug", "marker_uid", "marker_read_id"],
        },
      },
      ],
      output: "result"
    },
    dependencySourcings: [
      { param: "scoring_extract",
        type: "dataframe",
        config: {
          asset: "trfm_scoring_extract_filtered",
        },
      },
    ],
  },
  {
    slug: 'trfm_marker_data_count',
    caption: 'marker_data_count',
    description: "For marker reports: Count of attempts marked for each marker",
    structure: "dataframe",
    schema: [
      {"slug": "marker_uid", "type": "INTEGER_UNSIGNED", "caption": "Marker User Id", "description": "Unique identifier for the marker.", "is_primary_key": true},
      {"slug": "attempts_marked", "type": "INTEGER_UNSIGNED", "caption": "Attempts Marked", "description": "The number of test attempts marked by the marker.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [{
        kind: "group-by",
        df_output: "result",
        config: {
          df_input: "marker_data",
          group_by: ["marker_uid"],
          agg: [{
            col_new: "attempts_marked",
            agg_type: "nunique",
            col_target: "marker_read_id",
          }]
        },
      },
      ],
      output: "result"
    },
    dependencySourcings: [
      { param: "marker_data",
        type: "dataframe",
        config: {
          asset: "trfm_marker_data",
        },
      },
    ],
  },
  {
    slug: 'trfm_marker_data_histogram',
    caption: 'marker_data_histogram',
    description: "For marker reports: Histogram data for number of attempts marked by markers",
    structure: "dataframe",
    schema: [
      {"slug": "x0", "type": "FLOAT", "caption": "x-value low", "description": "Lower bound of the x-axis range for the histogram data.", "is_primary_key": false},
      {"slug": "x1", "type": "FLOAT", "caption": "x-value high", "description": "Upper bound of the x-axis range for the histogram data.", "is_primary_key": false},
      {"slug": "y", "type": "FLOAT", "caption": "y value", "description": "Frequency or count corresponding to the x-axis range in the histogram data.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [{
        kind: "aggregate",
        df_output: "marker_data_counts",
        config: {
          df_input: "marker_data_counts",
          agg: [{
            col_new: "count",
            agg_type: "count",
            col_target: "attempts_marked",
          },
          {
            col_new: "mean",
            agg_type: "mean",
            col_target: "attempts_marked",
          },
          {
            col_new: "stdev",
            agg_type: "stdev",
            col_target: "attempts_marked",
          },
          ]
        },
      },
      {
        kind: "normed-histogram",
        df_output: "marker_data_counts",
        config: {
          df_input: "marker_data_counts",
          n_bins: 40,
        },
      },
      ],
      output: "marker_data_counts"
    },
    dependencySourcings: [
      { param: "marker_data_counts",
        type: "dataframe",
        config: {
          asset: "trfm_marker_data_count",
        },
      },
    ],
  },
  {
    slug: 'trfm_marker_scores',
    caption: 'marker_scores',
    description: "For marker reports: Scoring extract filtered for scores data",
    structure: "dataframe",
    schema: [
      {"slug": "type_slug", "type": "STRING", "caption": "Type Slug", "description": "Slug representing the type of assessment.", "is_primary_key": false},
      {"slug": "attempt_id", "type": "INTEGER_UNSIGNED", "caption": "Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": false},
      {"slug": "item_id", "type": "INTEGER_UNSIGNED", "caption": "Item Id", "description": "Unique identifier for the item.", "is_primary_key": false},
      {"slug": "task_slug", "type": "STRING", "caption": "Task Slug", "description": "Identifier or slug representing the task.", "is_primary_key": false},
      {"slug": "marker_uid", "type": "INTEGER_UNSIGNED", "caption": "Marker Uid", "description": "Unique identifier for the marker", "is_primary_key": false},
      {"slug": "scale_report_label_short", "type": "STRING", "caption": "Scale Report Label Short", "description": "Short label for the scale.", "is_primary_key": false},
      {"slug": "score_value", "type": "INTEGER_UNSIGNED", "caption": "Score Value", "description": "The score value assigned to the scale.", "is_primary_key": false},
      {"slug": "marker_read_id", "type": "INTEGER_UNSIGNED", "caption": "Marker Read Id", "description": "Unique identifier for the specific read by a marker.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [{
        kind: "filter-col",
        df_output: "scoring_extract",
        config: {
          df_input: "scoring_extract",
          col: 'is_deleted',
          comparison: 'equals',
          value: 0
        }
      },
      {
        kind: "filter-col",
        df_output: "scoring_extract",
        config: {
          df_input: "scoring_extract",
          col: 'score_value',
          comparison: 'not-equal',
          value: 0
        }
      },
      {
        kind: "filter-col",
        df_output: "scoring_extract",
        config: {
          df_input: "scoring_extract",
          col: 'score_value',
          comparison: 'non-null',
        }
      },
      {
        kind: "restrict-cols",
        df_output: "result",
        config: {
          df_input: "scoring_extract",
          cols: ["type_slug", "attempt_id", "item_id", "task_slug", "marker_uid", "scale_report_label_short", "score_value", "marker_read_id"],
        },
      },
      ],
      output: "result"
    },
    dependencySourcings: [
      { param: "scoring_extract",
        type: "dataframe",
        config: {
          asset: "trfm_scoring_extract_filtered",
        },
      },
    ],
  },
  {
    slug: 'trfm_marker_average_scores',
    caption: 'marker_average_scores',
    description: "For marker reports: Average score given by each marker for a read",
    structure: "dataframe",
    schema: [
      {"slug": "marker_uid", "type": "INTEGER_UNSIGNED", "caption": "Marker Uid", "description": "Unique identifier for the marker.", "is_primary_key": true},
      {"slug": "average_marker_score", "type": "FLOAT", "caption": "Average Marker Score", "description": "Average score given by the marker.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [{
        kind: "group-by",
        df_output: "marker_scores",
        config: {
          df_input: "marker_scores",
          group_by: ["marker_uid", "marker_read_id"],
          agg: [{
            col_new: "total_read_score",
            agg_type: "sum",
            col_target: "score_value",
          }]
        },
      },
      {
        kind: "group-by",
        df_output: "result",
        config: {
          df_input: "marker_scores",
          group_by: ["marker_uid"],
          agg: [{
            col_new: "average_marker_score",
            agg_type: "mean",
            col_target: "total_read_score",
          }]
        },
      },
      ],
      output: "result"
    },
    dependencySourcings: [
      { param: "marker_scores",
        type: "dataframe",
        config: {
          asset: "trfm_marker_scores",
        },
      },
    ],
  },
  {
    slug: 'trfm_average_scores_histogram',
    caption: 'average_scores_histogram',
    description: "For marker reports: Histogram data for average scores given by markers",
    structure: "dataframe",
    schema: [
      {"slug": "x0", "type": "FLOAT", "caption": "x-value low", "description": "Lower bound of the x-axis range for the histogram data.", "is_primary_key": false},
      {"slug": "x1", "type": "FLOAT", "caption": "x-value high", "description": "Upper bound of the x-axis range for the histogram data.", "is_primary_key": false},
      {"slug": "y", "type": "FLOAT", "caption": "y value", "description": "Frequency or count corresponding to the x-axis range in the histogram data.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [{
        kind: "aggregate",
        df_output: "marker_average_scores",
        config: {
          df_input: "marker_average_scores",
          agg: [{
            col_new: "count",
            agg_type: "count",
            col_target: "average_marker_score",
          },
          {
            col_new: "mean",
            agg_type: "mean",
            col_target: "average_marker_score",
          },
          {
            col_new: "stdev",
            agg_type: "stdev",
            col_target: "average_marker_score",
          },
          ]
        },
      },
      {
        kind: "normed-histogram",
        df_output: "marker_average_scores",
        config: {
          df_input: "marker_average_scores",
          n_bins: 40,
        },
      },
      ],
      output: "marker_average_scores"
    },
    dependencySourcings: [
      { param: "marker_average_scores",
        type: "dataframe",
        config: {
          asset: "trfm_marker_average_scores",
        },
      },
    ],
  },
  {
    slug: 'trfm_marker_average_scale_scores',
    caption: 'marker_average_scale_scores',
    description: "For marker reports: Average score given by each marker across scales",
    structure: "dataframe",
    method: 'transforms',
    methodConfig: {
      sequence: [
      {
        kind: "group-by",
        df_output: "result",
        config: {
          df_input: "marker_scores",
          group_by: ["marker_uid", "scale_report_label_short"],
          agg: [{
            col_new: "average_scale_score",
            agg_type: "mean",
            col_target: "score_value",
          }]
        },
      },
      ],
      output: "result"
    },
    dependencySourcings: [
      { param: "marker_scores",
        type: "dataframe",
        config: {
          asset: "trfm_marker_scores",
        },
      },
    ],
  },
  {
    slug: 'trfm_average_scale_score_box_and_whisker',
    caption: 'average_scale_score_box_and_whisker',
    description: "For marker reports: Transforms marker average scale scores into box-and-whisker plot statistics.",
    structure: "dataframe",
    schema: [
      {"slug": "scale_report_label_short", "type": "STRING", "caption": "Scale Report Label Short", "description": "Short label for the scale.", "is_primary_key": false},
      {"slug": "q1", "type": "FLOAT", "caption": "First quartile value", "description": "Value at the 17.5th percentile of the dataset.", "is_primary_key": false},
      {"slug": "q3", "type": "FLOAT", "caption": "Third quartile value", "description": "Value at the 82.5th percentile of the dataset.", "is_primary_key": false},
      {"slug": "min", "type": "FLOAT", "caption": "Minimum", "description": "Lowest value in the dataset.", "is_primary_key": false},
      {"slug": "max", "type": "FLOAT", "caption": "Maximum", "description": "Highest value in the dataset.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "group-by",
          df_output: "grouped_data",
          config: {
            df_input: "average_scale_scores",
            group_by: ["scale_report_label_short"],
            agg: [
              {
                col_new: "q1",
                agg_type: "quantile-17_5",
                col_target: "average_scale_score"
              },
              {
                col_new: "q3",
                agg_type: "quantile-82_5",
                col_target: "average_scale_score"
              },
              {
                col_new: "min",
                agg_type: "min",
                col_target: "average_scale_score"
              },
              {
                col_new: "max",
                agg_type: "max",
                col_target: "average_scale_score"
              }
            ]
          },
        }
      ],
      output: "grouped_data"
    },
    dependencySourcings: [
      {
        param: "average_scale_scores",
        type: "dataframe",
        config: {
          asset: "trfm_marker_average_scale_scores",
        },
      },
    ],
  },
  {
    slug: 'trfm_marker_read_data',
    caption: 'marker_read_data',
    description: "For marker reports: Read information for markers",
    structure: "dataframe",
    schema: [
      {"slug": "type_slug", "type": "STRING", "caption": "Type Slug", "description": "Slug representing the type of assessment.", "is_primary_key": false},
      {"slug": "attempt_id", "type": "INTEGER_UNSIGNED", "caption": "Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": false},
      {"slug": "marker_read_id", "type": "INTEGER_UNSIGNED", "caption": "Marker Read Id", "description": "Unique identifier for the marker read.", "is_primary_key": false},
      {"slug": "task_slug", "type": "STRING", "caption": "Task Slug", "description": "Identifier or slug representing the task.", "is_primary_key": false},
      {"slug": "marker_uid", "type": "INTEGER_UNSIGNED", "caption": "Marker Uid", "description": "Unique identifier for the marker", "is_primary_key": false},
      {"slug": "read_nbr", "type": "INTEGER_UNSIGNED", "caption": "Read Number", "description": "The number of times the item was read.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [{
        kind: "filter-col",
        df_output: "scoring_extract",
        config: {
          df_input: "scoring_extract",
          col: 'is_deleted',
          comparison: 'equals',
          value: 0
        }
      },
      {
        kind: "restrict-cols",
        df_output: "result",
        config: {
          df_input: "scoring_extract",
          cols: ["type_slug", "attempt_id", "marker_read_id", "task_slug", "marker_uid", "read_nbr"],
        },
      },
      ],
      output: "result"
    },
    dependencySourcings: [
      { param: "scoring_extract",
        type: "dataframe",
        config: {
          asset: "trfm_scoring_extract_filtered",
        },
      },
    ],
  },
  {
    slug: 'trfm_marker_third_read_rate',
    caption: 'marker_third_read_rate',
    description: "For marker reports: Calculates the third read rate for each marker",
    structure: "dataframe",
    schema: [
      {"slug": "marker_uid", "type": "INTEGER_UNSIGNED", "caption": "Marker Uid", "description": "Unique identifier for the marker.", "is_primary_key": true},
      {"slug": "third_reads_count", "type": "INTEGER_UNSIGNED", "caption": "Third Reads Count", "description": "Count of third reads by the marker.", "is_primary_key": false},
      {"slug": "attempts_marked", "type": "INTEGER_UNSIGNED", "caption": "Attempts Marked", "description": "Number of test attempts marked by the marker.", "is_primary_key": false},
      {"slug": "third_read_rate", "type": "FLOAT", "caption": "Third Read Rate", "description": "Percentage of marked attempts requiring a third read.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "filter-col",
          df_output: "read_data",
          config: {
            df_input: "read_data",
            col: 'read_nbr',
            comparison: 'equals',
            value: 3
          }
        },
        {
          kind: "group-by",
          df_output: "marker_third_read_count",
          config: {
            df_input: "read_data",
            group_by: ["marker_uid"],
            agg: [{
              col_new: "third_reads_count",
              agg_type: "nunique",
              col_target: "marker_read_id",
            }]
          },
        },
        {
          kind: "join",
          df_output: "marker_third_read_count",
          config: {
            how: "inner",
            left: "marker_third_read_count",
            right: "marker_data_count",
            left_on: "marker_uid",
          },
        },
        {
          kind: "map-col",
          df_output: "result",
          config: {
            df_input: "marker_third_read_count",
            col_output: "third_read_rate",
            source_cols: ["third_reads_count", "attempts_marked"],
            operation: "percentage",
          },
        }
      ],
        output: "result"
    },
    dependencySourcings: [
      { param: "read_data",
        type: "dataframe",
        config: {
          asset: "trfm_marker_read_data",
        },
      },
      { param: "marker_data_count",
        type: "dataframe",
        config: {
          asset: "trfm_marker_data_count",
        },
      },
    ],
  },
  {
    slug: 'trfm_marker_third_read_rate_hist',
    caption: 'marker_third_read_rate_hist',
    description: "For marker reports: Calculates the normed histogram data for third read rate",
    structure: "dataframe",
    schema: [
      {"slug": "x0", "type": "FLOAT", "caption": "x-value low", "description": "Lower bound of the x-axis range for the histogram data.", "is_primary_key": false},
      {"slug": "x1", "type": "FLOAT", "caption": "x-value high", "description": "Upper bound of the x-axis range for the histogram data.", "is_primary_key": false},
      {"slug": "y", "type": "FLOAT", "caption": "y value", "description": "Frequency or count corresponding to the x-axis range in the histogram data.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "aggregate",
          df_output: "read_rate_data",
          config: {
            df_input: "read_rate_data",
            agg: [{
              col_new: "count",
              agg_type: "count",
              col_target: "third_read_rate",
            },
            {
              col_new: "mean",
              agg_type: "mean",
              col_target: "third_read_rate",
            },
            {
              col_new: "stdev",
              agg_type: "stdev",
              col_target: "third_read_rate",
            },
            ]
          },
        },
        {
          kind: "normed-histogram",
          df_output: "result",
          config: {
            df_input: "read_rate_data",
            n_bins: 40,
          },
        },
      ],
        output: "result"
    },
    dependencySourcings: [
      { param: "read_rate_data",
        type: "dataframe",
        config: {
          asset: "trfm_marker_third_read_rate",
        },
      }
    ],
  },
  {
    slug: 'trfm_marker_scoring_category',
    caption: 'marker_scoring_category',
    description: "For marker reports: Distribution of scores awarded by scale for individual marker",
    structure: "dataframe",
    schema: [
      {"slug": "marker_uid", "type": "INTEGER_UNSIGNED", "caption": "Marker Uid", "description": "Unique identifier for the marker.", "is_primary_key": false},
      {"slug": "scale_report_label_short", "type": "STRING", "caption": "Scale Report Label Short", "description": "Short label for the scale.", "is_primary_key": false},
      {"slug": "score_value", "type": "INTEGER_UNSIGNED", "caption": "Score Value", "description": "The score value assigned to the scale.", "is_primary_key": false},
      {"slug": "count", "type": "INTEGER_UNSIGNED", "caption": "Count", "description": "Count of occurrences for the specific score_value.", "is_primary_key": false},
      {"slug": "total_count", "type": "INTEGER_UNSIGNED", "caption": "Total Count", "description": "Total count of all scores within the scoring category.", "is_primary_key": false},
      {"slug": "percentage", "type": "FLOAT", "caption": "Percentage", "description": "Percentage of the specific score value within the scoring category.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "group-by",
          df_output: "marker_scores_count",
          config: {
            df_input: "marker_scores",
            group_by: ["marker_uid", "scale_report_label_short", "score_value"],
            agg: [{
              col_new: "count",
              agg_type: "count",
              col_target: "marker_read_id",
            }]
          },
        },
        {
          kind: "group-by",
          df_output: "marker_scores_total_count",
          config: {
            df_input: "marker_scores_count",
            group_by: ["marker_uid", "scale_report_label_short"],
            agg: [{
              col_new: "total_count",
              agg_type: "sum",
              col_target: "count",
            }]
          },
        },
        {
          kind: "map-col",
          df_output: "marker_scores_count",
          config: {
            df_input: "marker_scores_count",
            col_output: "merge_key",
            source_cols: ["marker_uid", "scale_report_label_short"],
            operation: "concat",
          }
        },
        {
          kind: "map-col",
          df_output: "marker_scores_total_count",
          config: {
            df_input: "marker_scores_total_count",
            col_output: "merge_key",
            source_cols: ["marker_uid", "scale_report_label_short"],
            operation: "concat",
          }
        },
        {
          kind: "join",
          df_output: "merged_df",
          config: {
            how: "inner",
            left: "marker_scores_count",
            right: "marker_scores_total_count",
            left_on: "merge_key",
            right_on: "merge_key",
          }
        },
        {
          kind: "map-col",
          df_output: "merged_df",
          config: {
            df_input: "merged_df",
            col_output: "percentage",
            source_cols: ["count", "total_count"],
            operation: "percentage",
          }
        },
        {
          kind: "restrict-cols",
          df_output: "merged_df",
          config: {
            df_input: "merged_df",
            cols: ["marker_uid", "scale_report_label_short", "score_value", "count", "total_count", "percentage"]
          },
        },
      ],
      output: "merged_df"
    },
    dependencySourcings: [
      { param: "marker_scores",
        type: "dataframe",
        config: {
          asset: "trfm_marker_scores",
        },
      },
    ],
  },
  {
    slug: 'trfm_scores_by_scoring_category',
    caption: 'scores_by_scoring_category',
    description: "For marker reports: Distribution of scores awarded by scale on average by all markers",
    structure: "dataframe",
    schema: [
      {"slug": "scale_report_label_short", "type": "STRING", "caption": "Scale Report Label Short", "description": "Short label for the scale.", "is_primary_key": false},
      {"slug": "score_value", "type": "INTEGER_UNSIGNED", "caption": "Score Value", "description": "The score value assigned to the scale.", "is_primary_key": false},
      {"slug": "count", "type": "INTEGER_UNSIGNED", "caption": "Count", "description": "Count of occurrences for the specific score.", "is_primary_key": false},
      {"slug": "total_count", "type": "INTEGER_UNSIGNED", "caption": "Total Count", "description": "Total count of all scores within the scoring category.", "is_primary_key": false},
      {"slug": "percentage", "type": "FLOAT", "caption": "Percentage", "description": "Percentage of the specific score value.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "group-by",
          df_output: "scores_count",
          config: {
            df_input: "marker_scores",
            group_by: ["scale_report_label_short", "score_value"],
            agg: [{
              col_new: "count",
              agg_type: "count",
              col_target: "marker_read_id",
            }]
          },
        },
        {
          kind: "group-by",
          df_output: "scores_total_count",
          config: {
            df_input: "scores_count",
            group_by: ["scale_report_label_short"],
            agg: [{
              col_new: "total_count",
              agg_type: "sum",
              col_target: "count",
            }]
          },
        },
        {
          kind: "join",
          df_output: "merged_df",
          config: {
            how: "inner",
            left: "scores_count",
            right: "scores_total_count",
            left_on: "scale_report_label_short",
            right_on: "scale_report_label_short",
          }
        },
        {
          kind: "map-col",
          df_output: "merged_df",
          config: {
            df_input: "merged_df",
            col_output: "percentage",
            source_cols: ["count", "total_count"],
            operation: "percentage",
          }
        },
      ],
      output: "merged_df"
    },
    dependencySourcings: [
      { param: "marker_scores",
        type: "dataframe",
        config: {
          asset: "trfm_marker_scores",
        },
      },
    ],
  },
  {
    slug: "check_tqer_weight_discrepancies",
    caption: "Check TQER weight discrepancies",
    description: "Check TQER weight discrepancies",
    structure: "dataframe",
    isCheckAsset: true,
    severity: CheckAssetSeverity.WARNING,
    schema: [
      {"slug": "item_id", "type": "INTEGER_UNSIGNED", "caption": "Item Id", "description": "Identifier for the specific item.", "is_primary_key": true},
      {"slug": "lang", "type": "STRING", "caption": "Language", "description": "Language associated with the item.", "is_primary_key": false},
      {"slug": "n_score_max", "type": "INTEGER_UNSIGNED", "caption": "Number of Score Max", "description": "Count of records associated with that score max value", "is_primary_key": false},
      {"slug": "score_max", "type": "STRING", "caption": "Score Max", "description": "Maximum possible score for the item.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "drop-duplicates",
          df_output: "tqer_weights",
          config: {
            df_input: "tqer_weights",
            subset: ["item_id", "lang", "score_max"]
            },
        },
        {
          kind: "group-by",
          df_output: "tqer_weights",
          config: {
            df_input: "tqer_weights",
            group_by: ["item_id", "lang"],
            agg: [
              {
                col_new: "n_score_max",
                agg_type: "count",
                col_target: "score_max",
              },
              {
                col_new: "score_max",
                agg_type: "concat",
                col_target: "score_max",
              }
            ]
          },
        },
        {
          kind: "filter-col",
          df_output: "tqer_weights",
          config: {
            df_input: "tqer_weights",
            col: "n_score_max",
            comparison: "greater-than",
            value: 1,
          },
        }
      ],
      output: "tqer_weights"
    },
    dependencySourcings: [
      { param: "tqer_weights",
        type: "dataframe",
        config: {
          asset: "load_db_tqer",
        },
      },
    ],
  },
  {
    slug: "trfm_item_responses_correct_scores",
    caption: "item_responses_correct_scores",
    description: "Item responses with is_correct and total_score on test (partitioned by test form)",
    structure: "dataframe",
    "schema": [
      {"slug": "score", "caption": "Score", "type": "INTEGER_UNSIGNED", "description": "Score achieved for the item.", },
      {"slug": "score_max", "caption": "Score Max", "type": "INTEGER_UNSIGNED", "description": "Maximum possible score for the item.", },
      {"slug": "formatted_response", "caption": "Formatted Response", "type": "STRING", "description": "Formatted version of the response.", },
      {"slug": "test_design_id", "caption": "Test Design Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test design.", },
      {"slug": "item_id", "caption": "Item Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the item", },
      {"slug": "form_code", "caption": "Form Code", "type": "STRING", "description": "Identifier for the test form", },
      {"slug": "total_score", "caption": "Total Score", "type": "INTEGER_UNSIGNED", "description": "Total score achieved for the assessment", },
      {"slug": "asmt_code", "caption": "Assessment Code", "type": "STRING", "description": "Code identifying the assessment", },
      {"slug": "test_attempt_id", "caption": "Test Attempt Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test attempt", },
      {"slug": "is_correct", "caption": "Is Correct", "type": "INTEGER_UNSIGNED", "description": "Indicates if the response is correct", },
    ],
    // partitionOut: { // Partitioning by form at _nf_omit instead
    //   key: "test_form",
    //   field: "test_design_id",
    //   categorical: true,
    // },
    partitionBy: {
      key: "test_form",
    },
    method: "transforms",
    methodConfig: {
      sequence: [
        // Filter out is_human_scored
        {
          "kind": "filter-col",
          "df_output": "item_responses",
          "config": {
            "df_input": "item_responses",
            "col": "is_human_scored",
            "comparison": "equals",
            "value": 0
          }
        },
        // Restrict to relevant columns (to permit garbage collection)
        {
          "kind": "restrict-cols",
          "df_output": "item_responses",
          "config": {
            "df_input": "item_responses",
            "cols": ["asmt_code", "form_code", "test_design_id", "test_attempt_id", "item_id", "score", "score_max", "formatted_response"]
          }
        },
        // Determine if the score is correct
        {
          "kind": "map-col",
          "df_output": "item_responses",
          "config": {
            "df_input": "item_responses",
            "col_output": "is_correct",
            "source_cols": ["score", "score_max"],
            "operation": "equal",
            "value_true": 1,
            "value_false": 0
          },
        },
        // Add on total score
        {
          "kind": "restrict-cols",
          "df_output": "total_scores",
          "config": {
            "df_input": "total_scores",
            "cols": ["test_attempt_id", "total_score"]
          }
        },
        {
          "kind": "join",
          "df_output": "item_responses",
          "config": {
            "how": "inner",
            "left": "item_responses",
            "right": "total_scores",
            "left_on": "test_attempt_id",
            "right_on": "test_attempt_id",
          },
        },
      ],
      output: "item_responses"
    },
    dependencySourcings: [
      {
        param: "item_responses",
        type: "dataframe",
        config: {
          asset: "trfm_item_responses_nf_omit",
        }
      },
      {
        param: "total_scores",
        type: "dataframe",
        config: {
          asset: "trfm_test_attempt_total_scores",
        }
      }
    ],
  },
  { "slug": "trfm_item_point_biserials",
    "caption": "item_point_biserials",
    "description": "Point biserial correlation and corrected point biserial for all items.",
    "structure": "dataframe",
    "partitionBy": {
      "key": "test_form",
    },
    "schema": [
      {"slug": "iri", "caption": "Item Reliability Index", "type": "FLOAT", "description": "Reliability index of the item.", },
      {"slug": "test_design_id", "caption": "Test Design Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test design", },
      {"slug": "item_id", "caption": "Item Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the item being assessed", },
      {"slug": "form_code", "caption": "Form Code", "type": "STRING", "description": "Identifier for the test form.", },
      {"slug": "asmt_code", "caption": "Assessment Code", "type": "STRING", "description": "Code identifying the assessment", },
      {"slug": "p", "caption": "Difficulty (P)", "type": "FLOAT", "description": "Difficulty of the item, represented as the proportion of correct responses", },
      {"slug": "rpb", "caption": "Point biserial correlation (rpb)", "type": "FLOAT", "description": "Point biserial correlation for the item", },
      {"slug": "crpb", "caption": "Corrected Point biserial correlation (crpb)", "type": "FLOAT", "description": "Corrected point biserial correlation for the item", },
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "point-biserial",
          "df_output": "item_point_biserials",
          "config": {
            "df_input": "responses",
            "keep_cols": ["asmt_code", "form_code", "test_design_id"]
          },
        }
      ],
      "output": "item_point_biserials"
    },
    "dependencySourcings": [
      {
        "param": "responses",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_responses_correct_scores",
        }
      }
    ]
  },
  {
    "slug": "trfm_item_scale_stats_grouped",
    "caption": "item_scale_stats_grouped",
    "description": "Counts of item responses in each high/mid/low group",
    "structure": "dataframe",
    "partitionBy": {
      "key": "test_form",
    },
    "schema": [
      {"slug": "asmt_code", "caption": "Assessment Code", "type": "STRING", "description": "Code identifying the assessment", },
      {"slug": "form_code", "caption": "Form Code", "type": "STRING", "description": "Identifier for the test form", },
      {"slug": "item_id", "caption": "Item Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the item being assessed", },
      {"slug": "p_mid", "caption": "P (Difficulty) Mid group", "type": "FLOAT", "description": "Difficulty for the mid group", },
      {"slug": "p_low", "caption": "P (Difficulty) Low group", "type": "FLOAT", "description": "Difficulty for the low group", },
      {"slug": "n_high", "caption": "Number of students in high group", "type": "INTEGER_UNSIGNED", "description": "Number of students in the high performance group", },
      {"slug": "n_mid", "caption": "Number of students in mid group", "type": "INTEGER_UNSIGNED", "description": "Number of students in the mid performance group.", },
      {"slug": "n_low", "caption": "Number of students in low group", "type": "INTEGER_UNSIGNED", "description": "Number of students in the low performance group", },
      {"slug": "p_high", "caption": "P (Difficulty) High group", "type": "FLOAT", "description": "Difficulty for the high group", },
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "restrict-cols",
          "df_output": "ta_groups",
          "config": {
            "df_input": "ta_groups",
            "cols": ["test_attempt_id", "group_level"]
          }
        },
        {
          "kind": "join",
          "df_output": "responses",
          "config": {
            "how": "inner",
            "left": "responses",
            "right": "ta_groups",
            "left_on": "test_attempt_id",
            "right_on": "test_attempt_id",
          },
        },
        // TODO: these could be made into one by adding support for multiple aggregators in one pivot
        { // counts
          "kind": "pivot",
          "df_output": "counts",
          "config": {
            "df_input": "responses",
            "group_by": ["asmt_code", "form_code", "item_id"],
            "index": "group_level",
            "value": "is_correct",
            "agg": "count",
            "col_prefix": "n_"
          },
        },
        { // fill in any missing counts
          "kind": "fill-na",
          "df_output": "counts",
          "config": {
            "df_input": "counts",
            "inplace": true,
            "values": {
              "n_high": 0,
              "n_mid": 0,
              "n_low": 0,
            }
          }
        },
        { // Difficulties
          "kind": "pivot",
          "df_output": "responses",
          "config": {
            "df_input": "responses",
            "group_by": ["asmt_code", "form_code", "item_id"],
            "index": "group_level",
            "value": "is_correct",
            "agg": "mean",
            "col_prefix": "p_"
          },
        },
        {
          "kind": "join",
          "df_output": "responses",
          "config": {
            how: "inner",
            left: "counts",
            right: "responses",
            left_on: ["asmt_code", "form_code", "item_id"],
            right_on: ["asmt_code", "form_code", "item_id"],
          }
        }
      ],
      "output": "responses"
    },
    "dependencySourcings": [
      {
        "param": "responses",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_responses_correct_scores",
        }
      },
      {
        "param": "ta_groups",
        "type": "dataframe",
        "config": {
          "asset": "trfm_test_attempt_total_scores_grouped",
          "cols": ["test_attempt_id", "group_level"]
        }
      }
    ]
  },
  {
    "slug": "load_db_codebook_tables",
    "caption": "codebook_tables",
    "description": "Load the codebook table definitions",
    "structure": "dataframe",
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_00_META_CODEBOOK_TABLES"
    },
    "dependencySourcings": [
      {
        "param": "asset_slugs",
        "type": "job-config",
        "config": {
          "configSlug": "assets",
          "param": "slug"
        },
      },
    ]
  },
  {
    "slug": "load_db_codebook_table_fields",
    "caption": "codebook_table_fields",
    "description": "Load the codebook table field definitions",
    "structure": "dataframe",
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_00_META_CODEBOOK_TABLE_FIELDS"
    },
    "dependencySourcings": [
      {
        "param": "data_def_table_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_codebook_tables",
          "col": "id"
        },
      },
    ]
  },
  {
    "slug": "load_db_codebook_tables_full",
    "caption": "codebook_tables",
    "description": "Load the codebook table definitions",
    "structure": "dataframe",
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_00_META_CODEBOOK_TABLES"
    },
    "dependencySourcings": [
    ]
  },
  {
    "slug": "load_db_codebook_table_fields_full",
    "caption": "codebook_table_fields",
    "description": "Load the codebook table field definitions",
    "structure": "dataframe",
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_00_META_CODEBOOK_TABLE_FIELDS"
    },
    "dependencySourcings": [
      {
        "param": "data_def_table_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_codebook_tables_full",
          "col": "id"
        },
      },
    ]
  },
  {
    slug: 'trfm_principal_kit_meta',
    caption: "principal_kit_meta",
    description: "Information about the principal kits",
    structure: "dataframe",
    "schema": [
      {"slug": "id", "caption": "Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the principal kit meta", },
      {"slug": "is_sample", "caption": "Is Sample", "type": "BOOL_INT", "description": "Flag to check if it is a sample kit", },
      {"slug": "sd_code", "caption": "School District Code", "type": "STRING", "description": "School District Code", },
      {"slug": "sd_name", "caption": "School District Name", "type": "STRING", "description": "Name of the school district", },
      {"slug": "s_code", "caption": "School Code", "type": "STRING", "description": "Code for the school", },
      {"slug": "s_group_id", "caption": "School Group Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the school group", },
      {"slug": "s_id", "caption": "School Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the school group", },
      {"slug": "s_name", "caption": "School Name", "type": "STRING", "description": "Name of the school", },
      {"slug": "test_window_id", "caption": "Test Window Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window", },
      {"slug": "updated_on", "caption": "Updated On", "type": "DATETIME", "description": "Timestamp of updation", },
      {"slug": "created_on", "caption": "Created On", "type": "DATETIME", "description": "Timestamp of Creation", },
    ],
    method: 'api-method',
    methodConfig: {
      slug: 'SQL_05B_PRINCIPAL_KIT_META'
    },
    dependencySourcings: [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      },
      {
        "param": "include_sample_schools",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "include_sample_schools"
        }
      }
    ],
  },
  {
    slug: 'trfm_principal_kit_school_asmt',
    caption: 'principal_kit_school_asmt',
    description: "Schools with a principal kit and what assessment codes are included.",
    structure: "dataframe",
    "schema": [
      {"slug": "asmt_code", "caption": "Assessment Code", "type": "STRING", "description": "Code representing the assessment", },
      {"slug": "s_code", "caption": "School Code", "type": "STRING", "description": "Code for the school", },
      {"slug": "s_group_id", "caption": "School Group Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the school group", },
      {"slug": "s_id", "caption": "School Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the school group", },
      {"slug": "s_name", "caption": "School Name", "type": "STRING", "description": "Name of the school", },
      {"slug": "test_window_id", "caption": "Test Window Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window", },
    ],
    method: 'api-method',
    methodConfig: {
      slug: 'SQL_05B_PRINCIPAL_KIT_SCHOOL_ASMT'
    },
    dependencySourcings: [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      },
      {
        "param": "include_sample_schools",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "include_sample_schools"
        }
      }
    ],
  },
  {
    slug: 'load_pk_student_data',
    caption: 'pk_student_data',
    description: "Intermediate asset for pulling student information from the principal kits.",
    structure: "dataframe",
    "schema": [
      {"slug": "asmt_code", "caption": "Assessment Code", "type": "STRING", "description": "Code for the assessment", },
      {"slug": "is_absent", "caption": "Is Absent", "type": "BOOL_INT", "description": "Flag to check if the student is absent", },
      {"slug": "is_anomaly", "caption": "Is Anomaly", "type": "BOOL_INT", "description": "Flag to check if the student is an anomaly", },
      {"slug": "is_excused", "caption": "Is Excused", "type": "BOOL_INT", "description": "Flag to check if the student is excused", },
      {"slug": "is_other_class", "caption": "Is Other Class", "type": "BOOL_INT", "description": "Flag to check if the student is of other class", },
      {"slug": "is_transferred", "caption": "Is Transferred", "type": "BOOL_INT", "description": "Flag to check if the student is transferred to a different school", },
      {"slug": "notes", "caption": "Notes", "type": "STRING", "description": "Additional notes about the student", },
      {"slug": "student_uid", "caption": "Student User Id", "type": "INTEGER_UNSIGNED", "description": "User id of the student", },
      {"slug": "s_code", "caption": "School Code", "type": "STRING", "description": "School Code", },
      {"slug": "s_group_id", "caption": "School Group Id", "type": "INTEGER_UNSIGNED", "description": "School Group Id", },
      {"slug": "s_id", "caption": "School Id", "type": "INTEGER_UNSIGNED", "description": "School Id", },
      {"slug": "s_name", "caption": "School Name", "type": "STRING", "description": "Name of the school", },
      {"slug": "test_window_id", "caption": "Test Window Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window", },
    ],
    method: 'api-method',
    methodConfig: {
      slug: 'SQL_05B_PRINCIPAL_KIT_STUDENTS'
    },
    dependencySourcings: [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      },
      {
        "param": "include_sample_schools",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "include_sample_schools"
        }
      }
    ],
  },
  {
    "slug": "load_stu_gov_id_from_uid",
    "caption": "stu_gov_id_from_uid",
    "description": "For principak kits asset: Loads the government id (ASN) for all students in the principal kits.",
    "structure": "dataframe",
    "schema": [
      {"slug": "uid", "type": "STRING", "caption": "School Name", "description": "Name of the school.", "is_primary_key": false},
      {"slug": "stu_gov_id", "type": "STRING", "caption": "School Name", "description": "Name of the school.", "is_primary_key": false},
    ],
    "method": 'query-chunked',
    "methodConfig":  {
      "querySlug": 'SQL_04B_STU_GOV_ID_FROM_UID',
      "chunkedParam": 'uids',
      "chunkSize": 5000,
      "makeDistinct": true,
    },
    "dependencySourcings": [
      {
        "param": "uids",
        "type": "asset-col",
        "config": {
          "asset": "load_pk_student_data",
          "col": "student_uid"
        }
      }
    ]
  },
  {
    slug: 'trfm_principal_kit_students',
    caption: 'principal_kit_students',
    description: "Student information from the principal kits",
    structure: "dataframe",
    "schema": [
      {"slug": "asmt_code", "caption": "Assessment Code", "type": "STRING", "description": "Code for the assessment", },
      {"slug": "is_absent", "caption": "Is Absent", "type": "BOOL_INT", "description": "Flag to check if the student is absent", },
      {"slug": "is_anomaly", "caption": "Is Anomaly", "type": "BOOL_INT", "description": "Flag to check if the student is an anomaly", },
      {"slug": "is_excused", "caption": "Is Excused", "type": "BOOL_INT", "description": "Flag to check if the student is excused", },
      {"slug": "is_other_class", "caption": "Is Other Class", "type": "BOOL_INT", "description": "Flag to check if the student is of other class", },
      {"slug": "is_transferred", "caption": "Is Transferred", "type": "BOOL_INT", "description": "Flag to check if the student is transferred to a different school", },
      {"slug": "notes", "caption": "Notes", "type": "STRING", "description": "Additional notes about the student", },
      {"slug": "student_uid", "caption": "Student User Id", "type": "INTEGER_UNSIGNED", "description": "User id of the student", },
      {"slug": "stu_gov_id", "caption": "Student Government Id", "type": "STRING", "description": "Government ID of the student", },
      {"slug": "s_code", "caption": "School Code", "type": "STRING", "description": "School Code", },
      {"slug": "s_group_id", "caption": "School Group Id", "type": "INTEGER_UNSIGNED", "description": "School Group Id", },
      {"slug": "s_id", "caption": "School Id", "type": "INTEGER_UNSIGNED", "description": "School Id", },
      {"slug": "s_name", "caption": "School Name", "type": "STRING", "description": "Name of the school", },
      {"slug": "test_window_id", "caption": "Test Window Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test window", },
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "join",
          df_output: "pk_student_data",
          config: {
            how: "inner",
            left: "pk_student_data",
            right: "stu_gov_id_from_uid",
            left_on: "student_uid",
            right_on: "uid",
          }
        },
      ],
      output: "pk_student_data"
    },
    dependencySourcings: [
      {
        "param": "pk_student_data",
        "type": "dataframe",
        "config": {
          "asset": "load_pk_student_data",
        },
      },
      {
        "param": "stu_gov_id_from_uid",
        "type": "dataframe",
        "config": {
          "asset": "load_stu_gov_id_from_uid",
        },
      },
    ],
  },
  {
    "slug": "trfm_response_stats",
    "caption": "constructed_response_stats",
    "description": "Basic statistics for Constructed Responses",
    "structure": "dataframe",
    partitionBy: {
      key: 'partby_ta_ids',
      concatOut: true,
    },
    "schema": [
      {"slug": "character_count", "caption": "Character Count", "type": "INTEGER_UNSIGNED", "description": "The total number of characters in the response.", },
      {"slug": "form_code", "caption": "Form Code", "type": "STRING", "description": "Code identifying the form associated with the response.", },
      {"slug": "sentence_count", "caption": "Sentence Count", "type": "INTEGER_UNSIGNED", "description": "The total number of sentences in the response.", },
      {"slug": "test_attempt_id", "caption": "Test Attempt Id", "type": "INTEGER_UNSIGNED", "description": "Identifier for the test attempt", },
      {"slug": "test_attempt_question_response_id", "caption": "Test Attempt Question Response Id", "type": "INTEGER_UNSIGNED", "description": "The unique identifier for the question response in a test attempt.", },
      {"slug": "type_slug", "caption": "Type Slug", "type": "STRING", "description": "The category or type of the response", },
      {"slug": "word_count", "caption": "Word Count", "type": "INTEGER_UNSIGNED", "description": "The total number of words in the response.", },
    ],
    "method": "transforms",
    "methodConfig": {
      sequence: [
        // First filter to only include human scored responses (based on tqr)
        {
          "kind": "restrict-cols",
          "df_output": "tqr",
          "config": {
            "df_input": "tqr",
            "cols": ["item_id", "is_human_scored"]
          }
        },
        {
          "kind": "filter-col",
          "df_output": "tqr",
          "config": {
            "df_input": "tqr",
            "comparison": "equals",
            "col": "is_human_scored",
            "value": 1,
          }
        },
        {
          "kind": "join",
          "df_output": "responses",
          "config": {
            "how": "inner",
            "left": "responses",
            "right": "tqr",
            "left_on": ["item_id"],
            "right_on": ["item_id"],
          }
        },
        {
          "kind": "wr-stats",
          "df_output": "responses",
          "config": {
            "df_input": "responses",
            "column": "formatted_response"
          }
        },
        {
          "kind": "restrict-cols",
          "df_output": "responses",
          "config": {
            "df_input": "responses",
            "cols": [ "ta_id", "taqr_id", "character_count", "word_count", "sentence_count", ]
          }
        },
        {
          "kind": "restrict-cols",
          "df_output": "attempts",
          "config": {
            "df_input": "attempts",
            "cols": ["type_slug", "form_code", "test_attempt_id"]
          }
        },
        {
          "kind": "join",
          "df_output": "responses",
          "config": {
            "how": "inner",
            "left": "attempts",
            "right": "responses",
            "left_on": ["test_attempt_id"],
            "right_on": ["ta_id"],
          }
        },
      ],
      output: "responses"
    },
    "dependencySourcings": [
      {
        "param": "responses",
        "type": "dataframe",
        "config": {
          "asset": "trfm_item_responses_fixed",
        },
      },
      {
        "param": "attempts",
        "type": "dataframe",
        "config": {
          "asset": "trfm_student_attempts_consolidated",
        },
      },
      {
        "param": "tqr",
        "type": "dataframe",
        "config": {
          "asset": "trfm_tqr_score_max",
        },
      },
    ]
  },
  {
    "slug": "trfm_response_stats_summary",
    "caption": "resp_stats_summary",
    "description": "Counts the number of constructed responses for each form",
    "structure": "dataframe",
    "schema": [
      {"slug": "form_code", "caption": "Form Code", "type": "STRING", "description": "Code identifying the form", },
      {"slug": "n", "caption": "Number", "type": "INTEGER_UNSIGNED", "description": "The count of responses included in the summary.", },
      {"slug": "type_slug", "caption": "Type Slug", "type": "STRING", "description": "The category or type of responses summarized.", },
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "group-by",
          "df_output": "responses",
          "config": {
            "df_input": "responses",
            "group_by": ["type_slug", "form_code"],
            "agg": [
              {
                "col_new": "num_responses",
                "agg_type": "count",
                "col_target": "taqr_id"
              }
            ]
          }
        },
      ],
      "output": "responses",
    },
    "dependencySourcings": [
      {
        "param": "responses",
        "type": "dataframe",
        "config": {
          "asset": "trfm_response_stats",
        },
      },
    ]
  },
  {
    "slug": "trfm_response_stats_summary_min_10_words",
    "caption": "resp_stats_summary_min_10_words",
    "description": "Counts the number of constructed responses with fewer than 10 words for each form",
    "structure": "dataframe",
    "schema": [
      {"slug": "form_code", "caption": "Form Code", "type": "STRING", "description": "code identifying the form for responses", },
      {"slug": "n", "caption": "Number", "type": "INTEGER_UNSIGNED", "description": "The count of responses with a word count of at least 10.", },
      {"slug": "type_slug", "caption": "Type Slug", "type": "STRING", "description": "he category or type of responses summarized for those with at least 10 words.", },
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "filter-col",
          "df_output": "responses",
          "config": {
            "df_input": "responses",
            "col": "word_count",
            "comparison": "less-than",
            "value": 10,
          }
        },
        {
          "kind": "group-by",
          "df_output": "responses",
          "config": {
            "df_input": "responses",
            "group_by": ["type_slug", "form_code"],
            "agg": [
              {
                "col_new": "num_responses",
                "agg_type": "count",
                "col_target": "taqr_id"
              }
            ]
          }
        },
      ],
      "output": "responses",
    },
    "dependencySourcings": [
      {
        "param": "responses",
        "type": "dataframe",
        "config": {
          "asset": "trfm_response_stats",
        },
      },
    ]
  },
  {
    "slug": "trfm_student_attempts_consolidated",
    "caption": "student_attempts_consolidated",
    "description": "Student attempts with form and school information",
    "structure": "dataframe",
    "schema": [
      {"slug": "booklet_index", "caption": "Booklet Index", "type": "STRING", "description": "Index of the booklet", },
      {"slug": "closed_on", "caption": "Closed On", "type": "DATETIME", "description": "Date and time when the test attempt was closed", },
      {"slug": "component_slug", "caption": "Component Slug", "type": "STRING", "description": "Slug of the component", },
      {"slug": "course_code", "caption": "Course Code", "type": "STRING", "description": "Code of the course", },
      {"slug": "form_code", "caption": "Form Code", "type": "STRING", "description": "Code of the test form", },
      {"slug": "is_closed", "caption": "Is Closed", "type": "BOOL_INT", "description": "Flag indicating if the test attempt is closed", },
      {"slug": "is_invalid", "caption": "Is Invalid", "type": "BOOL_INT", "description": "Flag indicating if the test attempt is invalid", },
      {"slug": "is_sample", "caption": "Is Sample", "type": "BOOL_INT", "description": "Flag indicating if the response is a sample response", },
      {"slug": "is_sample_school", "caption": "Is Sample School", "type": "BOOL_INT", "description": "Flag indicating if the school is a sample school", },
      {"slug": "is_started", "caption": "Is Started", "type": "BOOL_INT", "description": "Flag indicating if the test attempt was started", },
      {"slug": "lang", "caption": "Language", "type": "STRING", "description": "The language of the test attempt", },
      {"slug": "school_authority_code", "caption": "School Authority Code", "type": "STRING", "description": "Code of the school authority", },
      {"slug": "school_code", "caption": "School Code", "type": "STRING", "description": "Code of the school", },
      {"slug": "started_on", "caption": "Started On", "type": "DATETIME", "description": "When the attempt was started", },
      {"slug": "stu_gov_id", "caption": "Student Government ID", "type": "STRING", "description": "Government ID of the student", },
      {"slug": "test_attempt_id", "caption": "Test Attempt ID", "type": "INTEGER_UNSIGNED", "description": "Primary key, and a foreign key referencing test_attempts", },
      {"slug": "ts_id", "caption": "Test Session ID", "type": "INTEGER_UNSIGNED", "description": "Foreign key referencing test_session ID", },
      {"slug": "twtar_id", "caption": "Test Window Test Design Allocation Rules ID", "type": "INTEGER_UNSIGNED", "description": "Foreign key referencing twtar ID", },
      {"slug": "type_slug", "caption": "Type Slug", "type": "STRING", "description": "A slug denoting the type of the response", },
      {"slug": "uid", "caption": "User ID", "type": "INTEGER_UNSIGNED", "description": "Foreign key referencing user ID", },
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        // First reduce to just the columns we need
        {
          "kind": "restrict-cols",
          "df_output": "attempts",
          "config": {
            "df_input": "attempts",
            "cols": ['uid', 'ta_id', 'test_session_id', 'is_invalid', 'is_closed', 'closed_on', 'started_on', 'twtdar_id']
          }
        },
        {
          "kind": "restrict-cols",
          "df_output": "twtar",
          "config": {
            "df_input": "twtar",
            "cols": ['twtar_id', 'type_slug', 'lang', 'form_code', 'is_sample', 'course_code_foreign', 'foreign_component_code']
          }
        },
        {
          "kind": "restrict-cols",
          "df_output": "students",
          "config": {
            "df_input": "students",
            "cols": ['uid', 'stu_gov_id']
          }
        },
        {
          "kind": "restrict-cols",
          "df_output": "admin_booklets",
          "config": {
            "df_input": "admin_booklets",
            "cols": ['uid', 'booklet_index', 'booklet_pasi_course_code'],
          }
        },
        // Now join all the tables
        {
          "kind": "join",
          "df_output": "attempts",
          "config": {
            "how": "inner",
            "left": "twtar",
            "right": "attempts",
            "left_on": ["twtar_id"],
            "right_on": ["twtdar_id"],
          }
        },
        {
          "kind": "join",
          "df_output": "attempts",
          "config": {
            "how": "left",
            "left": "attempts",
            "right": "students",
            "left_on": ["uid"],
            "right_on": ["uid"],
          }
        },
        // TODO: scts_school_meta, scts, and school_groupings come first
        {
          "kind": "join",
          "df_output": "attempts",
          "config": {
            "how": "left",
            "left": "attempts",
            "right": "admin_booklets",
            "left_on": ["uid", "course_code_foreign"],
            "right_on": ["uid", "booklet_pasi_course_code"],
          }
        },
        {
          "kind": "rename-cols",
          "df_output": "attempts",
          "config": {
            "df_input": "attempts",
            "new_names": {
              "ta_id": "test_attempt_id",
              "booklet_pasi_course_code": "booklet_pasi_course_code",
              "course_code_foreign": "course_code",
              "foreign_component_code": "component_code",
            },
          }
        },
      ],
      "output": "attempts"
    },
    "dependencySourcings": [
      {
        "param": "attempts",
        "type": "dataframe",
        "config": {
          "asset": "trfm_test_attempts_exceptions_applied",
          "cols": ['uid', 'ta_id', 'test_session_id', 'is_invalid', 'is_closed', 'closed_on', 'started_on', 'twtdar_id']
        },
      },
      {
        "param": "twtar",
        "type": "dataframe",
        "config": {
          "asset": "load_twtar",
          "cols": ['twtar_id', 'type_slug', 'lang', 'form_code', 'is_sample', 'course_code_foreign', 'foreign_component_code']
        },
      },
      {
        "param": "students",
        "type": "dataframe",
        "config": {
          "asset": "load_db_students",
          "cols": ['uid', 'stu_gov_id']
        },
      },
      {
        "param": "admin_booklets",
        "type": "dataframe",
        "config": {
          "asset": "load_db_student_admin_meta_booklets",
          "cols": ['uid', 'booklet_index', 'booklet_pasi_course_code'],
        },
      },
    ]
  },
  {
    "slug": "load_db_scts",
    "caption": "tw",
    "description": "Test window information for selected test windows",
    "structure": "dataframe",
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_05A_SCHOOL_CLASS_TEST_SESSION",
    },
    "dependencySourcings": [
      {
        "param": "sc_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_school_groupings",
          "col": "sc_id",
        }
      },
      {
        "param": "twtar_type_slugs",
        "type": "asset-col",
        "config": {
          "asset": "load_twtar",
          "col": "type_slug",
        }
      }
    ]
  },
  {
    "slug": "load_codebook_tables",
    "caption": "codebook_tables",
    "description": "Codebook: tables for current job.",
    "structure": "dataframe",
    "schema": [
      {slug: "slug", type: "STRING", caption: "Asset Slug", description: "Unique identifier for the asset in the export", is_primary_key: true, },
      {slug: "caption", type: "STRING", caption: "Caption", description: "Human-readable name for the asset", },
      {slug: "description", type: "STRING", caption: "Description", description: "Human-readable description for the asset", },
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
      {
        "kind": "restrict-cols",
        "df_output": "assets",
        "config": {
          "df_input": "assets",
          "cols": ['slug', 'caption', 'description']
        }
      }
      ],
      "output": "assets"
    },
    "dependencySourcings": [
      {
        "param": "assets",
        "type": "job-config",
        "config": {
          "configSlug": "assets",
        },
      },
    ]
  },
  {
    "slug": "load_codebook_table_fields",
    "caption": "codebook_table_fields",
    "description": "Codebook: table fields for current job.",
    "structure": "dataframe",
    "schema": [
      {slug: "asset_slug", type: "STRING", caption: "Asset Slug", description: "Unique identifier for the asset in the export", is_primary_key: true, },
      {slug: "slug", type: "STRING", caption: "Field Slug", description: "Field name for the asset", is_primary_key: true, },
      {slug: "caption", type: "STRING", caption: "Caption", description: "Human-readable name for the field", is_nullable: true},
      {slug: "description", type: "STRING", caption: "Description", description: "Human-readable description for the field", is_nullable: true},
      {slug: "is_primary_key", type: "BOOL_INT", caption: "Primary Key", description: "Whether the field is a primary key", is_nullable: true},
      {slug: "is_nullable", type: "BOOL_INT", caption: "Nullable", description: "Whether the field is nullable", is_nullable: true },
      {slug: "fk_table_slug", type: "STRING", caption: "Foreign Key Table Slug", description: "Asset slug the foreign key is referencing.", is_primary_key: false, is_nullable: true },
      {slug: "fk_field_slug", type: "STRING", caption: "Foreign Key Field Slug", description: "Field slug the foreign key is referencing.", is_primary_key: false, is_nullable: true },
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "fill-na",
          "df_output": "assets",
          "config": {
            "df_input": "assets",
            "values": {
              "caption": null,
              "description": null,
              "is_primary_key": false,
              "is_nullable": null,
              "fk_table_slug": null,
              "fk_field_slug": null,
            },
          }
        },
        {
          "kind": "map-col",
          "df_output": "assets",
          "config": {
            "df_input": "assets",
            "source_cols": ["is_primary_key"],
            "col_output": "is_primary_key",
            "operation": "convert-bool",
            "value_true": 1,
            "value_false": 0
          }
        },
      ],
      "output": "assets"
    },
    "dependencySourcings": [
      {
        "param": "assets",
        "type": "job-config",
        "config": {
          "configSlug": "assets",
          "param": "fields",
        },
      },
    ]
  },
  { // TODO: this can be used to replace values in item_responses and downstream
    "slug": "load_db_response_type_pattern_item_type",
    "caption": "response_type_pattern_item_type",
    "description": "Mapping between response types in item_responses assets, and the item_type.",
    "structure": "dataframe",
    "schema": [
      {slug: "item_type_slug", type: "STRING", caption: "Item Type Slug", description: "Item Type Slug", is_primary_key: false, is_nullable: false },
      {slug: "entry_type_slugs_pattern", type: "STRING", caption: "Entry Type Slugs Pattern", description: "Pattern of response_type in item_responses assets.", is_primary_key: true, is_nullable: false},
    ],
    "method": "query",
    "methodConfig": {
      "querySlug": "SQL_02_ASMT_SPECS_RESPONSE_TYPE_PATTERN_ITEM_TYPE",
    },
    "dependencySourcings": [
    ]
  },
  {
    slug: 'load_individual_student_exceptions',
    caption: 'individual_student_exceptions',
    description: "Individual student result overrides.",
    structure: "dataframe",
    schema: [
      {"slug": "test_window_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window Id", "description": "Identifier for the test window.", "is_primary_key": true},
      {"slug": "twtar_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window TD Alloc Rule ID", "description": "Identifier for the Test Window TD Alloc Rule.", "is_primary_key": false},
      {"slug": "uid", "type": "INTEGER_UNSIGNED", "caption": "Student User Id", "description": "Foreign key referencing the users table.", "is_primary_key": false},
      {"slug": "test_attempt_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": true},
      {"slug": "category", "caption": "Category", "type": "STRING", "description": "Category of the exception", },
      {"slug": "pct_score", "caption": "Percent Score", "type": "FLOAT", "description": "Percent score for the test attempt.", },
      {"slug": "outcome", "caption": "Outcome", "type": "STRING", "description": "Letter grade for the test attempt.", },
    ],
    method: 'query',
    methodConfig: {
      querySlug: 'SQL_04B_INDIVIDUAL_STUDENT_EXCEPTIONS',
    },
    dependencySourcings: [
      {
        "param": "tw_ids",
        "type": "job-config",
        "config": {
          "configSlug": "pipeline_config",
          "param": "test_window_ids"
        }
      },
    ]
  },
  {
    "slug": "load_report_attempt_detail",
    "caption": "report_attempt_detail",
    "description": "Test attempt detail for student reports.",
    "structure": "dataframe",
    "schema": [
      {"slug": "s_name", "type": "STRING", "caption": "School Name", "description": "Name of the school.", "is_primary_key": false},
      {"slug": "s_code", "type": "STRING", "caption": "School Code", "description": "Code of the school.", "is_primary_key": false},
      {"slug": "sd_code", "type": "STRING", "caption": "School District Code", "description": "Code of the school district.", "is_primary_key": false},
      {"slug": "sd_name", "type": "STRING", "caption": "School District Name", "description": "Name of the school district.", "is_primary_key": false},
      {"slug": "sc_name", "type": "STRING", "caption": "School Class Name", "description": "Name of the school class.", "is_primary_key": false},
      {"slug": "sc_id", "type": "INTEGER_UNSIGNED", "caption": "School Class Id", "description": "Identifier for the school class.", "is_primary_key": false},
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": false},
      {"slug": "form_code", "type": "STRING", "caption": "Form Code", "description": "The code associated with the test form.", "is_primary_key": false},
      {"slug": "student_uid", "type": "INTEGER_UNSIGNED", "caption": "Student User Id", "description": "Foreign key referencing the users table.", "is_primary_key": false},
      {"slug": "attempt_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": true},
      {"slug": "student_gov_id", "type": "INTEGER_UNSIGNED", "caption": "Student Government ID", "description": "Alberta Student Government ID.", "is_primary_key": false},
      {"slug": "gender", "type": "INTEGER_UNSIGNED", "caption": "Gender Code", "description": "Gender code for the student.", "is_primary_key": false, is_nullable: true},
      {"slug": "student_fname", "type": "STRING", "caption": "Student First Name", "description": "First name of the student.", "is_primary_key": false},
      {"slug": "student_lname", "type": "STRING", "caption": "Student Last Name", "description": "Last name of the student.", "is_primary_key": false},
      {"slug": "started_on_date", "type": "DATETIME", "caption": "Started On Date", "description": "Date and time when the test attempt was started.", "is_primary_key": false},
    ],
    "method": 'query-chunked',
    "methodConfig":  {
      "querySlug": 'SQL_05A_REPORT_ATTEMPTS_DETAIL_FROM_TA_ID',
      "chunkedParam": 'ta_ids',
      "chunkSize": 5000,
      "makeDistinct": true,
    },
    "dependencySourcings": [
      {
        "param": "ta_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_individual_student_exceptions",
          "col": "test_attempt_id"
        }
      }
    ]
  },
  {
    slug: 'trfm_individual_student_exceptions',
    caption: 'individual_student_exceptions',
    description: "Individual student result overrides with attempt/student metadata.",
    structure: "dataframe",
    schema: [
      {"slug": "test_window_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window Id", "description": "Identifier for the test window.", "is_primary_key": true},
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": false},
      {"slug": "form_code", "type": "STRING", "caption": "Form Code", "description": "The code associated with the test form.", "is_primary_key": false},
      {"slug": "twtar_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window TD Alloc Rule ID", "description": "Identifier for the Test Window TD Alloc Rule.", "is_primary_key": false},
      {"slug": "uid", "type": "INTEGER_UNSIGNED", "caption": "Student User Id", "description": "Foreign key referencing the users table.", "is_primary_key": false},
      {"slug": "test_attempt_id", "type": "INTEGER_UNSIGNED", "caption": "Test Attempt Id", "description": "Identifier for the test attempt.", "is_primary_key": true},
      {"slug": "category", "caption": "Category", "type": "STRING", "description": "Category of the exception", },
      {"slug": "pct_score", "caption": "Percent Score", "type": "FLOAT", "description": "Percent score for the test attempt.", },
      {"slug": "outcome", "caption": "Outcome", "type": "STRING", "description": "Letter grade for the test attempt.", },
      {"slug": "s_name", "type": "STRING", "caption": "School Name", "description": "Name of the school.", "is_primary_key": false},
      {"slug": "s_code", "type": "STRING", "caption": "School Code", "description": "Code of the school.", "is_primary_key": false},
      {"slug": "sd_code", "type": "STRING", "caption": "School District Code", "description": "Code of the school district.", "is_primary_key": false},
      {"slug": "sd_name", "type": "STRING", "caption": "School District Name", "description": "Name of the school district.", "is_primary_key": false},
      {"slug": "sc_name", "type": "STRING", "caption": "School Class Name", "description": "Name of the school class.", "is_primary_key": false},
      {"slug": "sc_id", "type": "INTEGER_UNSIGNED", "caption": "School Class Id", "description": "Identifier for the school class.", "is_primary_key": false},
      {"slug": "student_gov_id", "type": "INTEGER_UNSIGNED", "caption": "Student Government ID", "description": "Alberta Student Government ID.", "is_primary_key": false},
      {"slug": "gender", "type": "INTEGER_UNSIGNED", "caption": "Gender Code", "description": "Gender code for the student.", "is_primary_key": false, is_nullable: true},
      {"slug": "student_fname", "type": "STRING", "caption": "Student First Name", "description": "First name of the student.", "is_primary_key": false},
      {"slug": "student_lname", "type": "STRING", "caption": "Student Last Name", "description": "Last name of the student.", "is_primary_key": false},
      {"slug": "started_on_date", "type": "DATETIME", "caption": "Started On Date", "description": "Date and time when the test attempt was started.", "is_primary_key": false},
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
      {
        "kind": "restrict-cols",
        "df_output": "report_attempt_detail",
        "config": {
          "df_input": "report_attempt_detail",
            "cols": [
              's_name',
              's_code',
              'sd_code',
              'sd_name',
              'sc_name',
              'sc_id',
              'student_uid',
              'attempt_id',
              'assessment_code',
              'form_code',
              'student_gov_id',
              'gender',
              'student_fname',
              'student_lname',
              'started_on_date',
            ]
          }
        },
        {
          kind: "join",
          df_output: "results",
          config: {
            left: "results",
            right: "report_attempt_detail",
            how: "left",
            left_on: ["uid", "test_attempt_id"],
            right_on: ["student_uid", "attempt_id"],
          }
        },
      ],
      output: "results"
    },
    dependencySourcings: [
      {
        "param": "results",
        "type": "dataframe",
        "config": {
          "asset": "load_individual_student_exceptions",
        }
      },
      {
        "param": "report_attempt_detail",
        "type": "dataframe",
        "config": {
          "asset": "load_report_attempt_detail",
        }
      }
    ]
  },
  { // Table 1
    "slug": "trfm_students_with_standards",
    "caption": "students_with_standards",
    "description": "Percentage of passing attempts",
    "structure": "dataframe",
    "schema": [
      {"slug": "test_window_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window Id", "description": "Identifier for the test window.", "is_primary_key": true},
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": true},
      {"slug": "form_code", "type": "STRING", "caption": "Form Code", "description": "The code associated with the test form.", "is_primary_key": true},
      {"slug": "num_passed", "type": "INTEGER_UNSIGNED", "caption": "Number Passed", "description": "Number of attempts with a passing grade", "is_primary_key": false},
      {"slug": "total", "type": "INTEGER_UNSIGNED", "caption": "Total Count", "description": "Total number of attempts", "is_primary_key": false},
      {"slug": "percentage", "type": "FLOAT", "caption": "Percentage Passed", "description": "Percentage of attempts with a passing grade", "is_primary_key": false},
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "set-where",
          "df_output": "individual_student_exceptions",
          "config": {
            "df_input": "individual_student_exceptions",
            "inplace": true,
            "condition": {
              "col": "outcome",
              "comparison": "not-equal",
              "value": "F"
            },
            "values": {
              "is_pass": 1,
            }
          }
        },
        {
          "kind": "fill-na",
          "df_output": "individual_student_exceptions",
          "config": {
            "df_input": "individual_student_exceptions",
            "values": {
              "is_pass": 0,
            },
            "inplace": true,
          }
        },
        {
          kind: "group-by",
          df_output: "individual_student_exceptions",
          config: {
            df_input: "individual_student_exceptions",
            group_by: ["test_window_id", "assessment_code", "form_code"],
            agg: [{
              col_new: "num_passed",
              agg_type: "sum",
              col_target: "is_pass",
            },
            {
              col_new: "total",
              agg_type: "count",
              col_target: "outcome",
            },
          ]
          },
        },
        {
          kind: "map-col",
          df_output: "individual_student_exceptions",
          config: {
            df_input: "individual_student_exceptions",
            col_output: "percentage",
            source_cols: ["num_passed", "total"],
            operation: "percentage",
          }
        },
      ],
      "output": "individual_student_exceptions",
    },
    "dependencySourcings": [
      {
        "param": "individual_student_exceptions",
        "type": "dataframe",
        "config": {
          "asset": "trfm_individual_student_exceptions",
        },
      },
    ]
  },
  { // Table 2
    "slug": "trfm_outcome_percentage_distribution",
    "caption": "outcome_percentage_distribution",
    "description": "",
    "structure": "dataframe",
    "schema": [
      {"slug": "test_window_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window Id", "description": "Identifier for the test window.", "is_primary_key": true},
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": true},
      {"slug": "form_code", "type": "STRING", "caption": "Form Code", "description": "The code associated with the test form.", "is_primary_key": true},
      {"slug": "avg_score", "type": "FLOAT", "caption": "Average Pct Score", "description": "Average percent score an the assessment.", "is_primary_key": false},
      {"slug": "stdev_score", "type": "FLOAT", "caption": "Std Dev Pct Score", "description": "Standard deviation of percent score an the assessment.", "is_primary_key": false},
      {"slug": "N", "type": "INTEGER_UNSIGNED", "caption": "Total Count", "description": "Total number of attempts", "is_primary_key": false},
      {"slug": "num_A", "type": "INTEGER_UNSIGNED", "caption": "Number A", "description": "Number of attempts with a passing grade", "is_primary_key": false},
      {"slug": "num_B", "type": "INTEGER_UNSIGNED", "caption": "Number B", "description": "Number of attempts with a passing grade", "is_primary_key": false},
      {"slug": "num_C", "type": "INTEGER_UNSIGNED", "caption": "Number C", "description": "Number of attempts with a passing grade", "is_primary_key": false},
      {"slug": "num_D", "type": "INTEGER_UNSIGNED", "caption": "Number D", "description": "Number of attempts with a passing grade", "is_primary_key": false},
      {"slug": "num_F", "type": "INTEGER_UNSIGNED", "caption": "Number F", "description": "Number of attempts with a passing grade", "is_primary_key": false},
      {"slug": "percentage_A", "type": "FLOAT", "caption": "Percentage A", "description": "Percentage of attempts with a passing grade", "is_primary_key": false},
      {"slug": "percentage_B", "type": "FLOAT", "caption": "Percentage B", "description": "Percentage of attempts with a passing grade", "is_primary_key": false},
      {"slug": "percentage_C", "type": "FLOAT", "caption": "Percentage C", "description": "Percentage of attempts with a passing grade", "is_primary_key": false},
      {"slug": "percentage_D", "type": "FLOAT", "caption": "Percentage D", "description": "Percentage of attempts with a passing grade", "is_primary_key": false},
      {"slug": "percentage_F", "type": "FLOAT", "caption": "Percentage F", "description": "Percentage of attempts with a passing grade", "is_primary_key": false},
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "pivot",
          "df_output": "grouped",
          "config": {
            "df_input": "individual_student_exceptions",
            "group_by": ["test_window_id", "assessment_code", "form_code"],
            "index": "outcome",
            "value": "test_attempt_id",
            "agg": "count",
            "col_prefix": "n_"
          },
        },
        {
          kind: "group-by",
          df_output: "result",
          config: {
            df_input: "individual_student_exceptions",
            "group_by": ["test_window_id", "assessment_code", "form_code"],
            agg: [{
              col_new: "N",
              agg_type: "count",
              col_target: "test_attempt_id",
            },
            {
              col_new: "avg_score",
              agg_type: "mean",
              col_target: "pct_score",
            },
            {
              col_new: "stdev_score",
              agg_type: "stdev",
              col_target: "pct_score",
            },
          ]
          },
        },
        {
          kind: "join",
          df_output: "result",
          config: {
            how: "inner",
            left: "result",
            right: "grouped",
            left_on: ["test_window_id", "assessment_code", "form_code"],
            right_on: ["test_window_id", "assessment_code", "form_code"],
          }
        },
        {
          kind: "map-col",
          df_output: "result",
          config: {
            df_input: "result",
            col_output: "percentage_A",
            source_cols: ["n_A", "N"],
            operation: "percentage",
          }
        },
        {
          kind: "map-col",
          df_output: "result",
          config: {
            df_input: "result",
            col_output: "percentage_B",
            source_cols: ["n_B", "N"],
            operation: "percentage",
          }
        },
        {
          kind: "map-col",
          df_output: "result",
          config: {
            df_input: "result",
            col_output: "percentage_C",
            source_cols: ["n_C", "N"],
            operation: "percentage",
          }
        },
        {
          kind: "map-col",
          df_output: "result",
          config: {
            df_input: "result",
            col_output: "percentage_D",
            source_cols: ["n_D", "N"],
            operation: "percentage",
          }
        },
        {
          kind: "map-col",
          df_output: "result",
          config: {
            df_input: "result",
            col_output: "percentage_F",
            source_cols: ["n_F", "N"],
            operation: "percentage",
          }
        },
      ],
      "output": "result",
    },
    "dependencySourcings": [
      {
        "param": "individual_student_exceptions",
        "type": "dataframe",
        "config": {
          "asset": "trfm_individual_student_exceptions",
        },
      },
    ]
  },
  { // Table 3
    "slug": "trfm_students_with_standards_by_gender",
    "caption": "students_with_standards_by_gender",
    "description": "Percentage of passing attempts by gender.",
    "structure": "dataframe",
    "schema": [
      {"slug": "test_window_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window Id", "description": "Identifier for the test window.", "is_primary_key": true},
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": true},
      {"slug": "form_code", "type": "STRING", "caption": "Form Code", "description": "The code associated with the test form.", "is_primary_key": true},
      {"slug": "gender", "type": "INTEGER_UNSIGNED", "caption": "Gender Code", "description": "Gender code for the student.", "is_primary_key": false, is_nullable: true},
      {"slug": "num_passed", "type": "INTEGER_UNSIGNED", "caption": "Number Passed", "description": "Number of attempts with a passing grade", "is_primary_key": false},
      {"slug": "total", "type": "INTEGER_UNSIGNED", "caption": "Total Count", "description": "Total number of attempts", "is_primary_key": false},
      {"slug": "percentage", "type": "FLOAT", "caption": "Percentage Passed", "description": "Percentage of attempts with a passing grade", "is_primary_key": false},
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "set-where",
          "df_output": "individual_student_exceptions",
          "config": {
            "df_input": "individual_student_exceptions",
            "inplace": true,
            "condition": {
              "col": "outcome",
              "comparison": "not-equal",
              "value": "F"
            },
            "values": {
              "is_pass": 1,
            }
          }
        },
        {
          "kind": "fill-na",
          "df_output": "individual_student_exceptions",
          "config": {
            "df_input": "individual_student_exceptions",
            "values": {
              "is_pass": 0,
            },
            "inplace": true,
          }
        },
        {
          kind: "group-by",
          df_output: "individual_student_exceptions",
          config: {
            df_input: "individual_student_exceptions",
            group_by: ["test_window_id", "assessment_code", "form_code", "gender"],
            agg: [{
              col_new: "num_passed",
              agg_type: "sum",
              col_target: "is_pass",
            },
            {
              col_new: "total",
              agg_type: "count",
              col_target: "outcome",
            },
          ]
          },
        },
        {
          kind: "map-col",
          df_output: "individual_student_exceptions",
          config: {
            df_input: "individual_student_exceptions",
            col_output: "percentage",
            source_cols: ["num_passed", "total"],
            operation: "percentage",
          }
        },
      ],
      "output": "individual_student_exceptions",
    },
    "dependencySourcings": [
      {
        "param": "individual_student_exceptions",
        "type": "dataframe",
        "config": {
          "asset": "trfm_individual_student_exceptions",
        },
      },
    ]
  },
  { // school reports Table 2
    "slug": "trfm_outcome_percentage_distribution_by_gender",
    "caption": "outcome_percentage_distribution_by_gender",
    "description": "",
    "structure": "dataframe",
    "schema": [
      {"slug": "test_window_id", "type": "INTEGER_UNSIGNED", "caption": "Test Window Id", "description": "Identifier for the test window.", "is_primary_key": true},
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": true},
      {"slug": "form_code", "type": "STRING", "caption": "Form Code", "description": "The code associated with the test form.", "is_primary_key": true},
      {"slug": "gender", "type": "INTEGER_UNSIGNED", "caption": "Gender Code", "description": "Gender code for the student.", "is_primary_key": false, is_nullable: true},
      {"slug": "avg_score", "type": "FLOAT", "caption": "Average Pct Score", "description": "Average percent score an the assessment.", "is_primary_key": false},
      {"slug": "stdev_score", "type": "FLOAT", "caption": "Std Dev Pct Score", "description": "Standard deviation of percent score an the assessment.", "is_primary_key": false},
      {"slug": "N", "type": "INTEGER_UNSIGNED", "caption": "Total Count", "description": "Total number of attempts", "is_primary_key": false},
      {"slug": "num_A", "type": "INTEGER_UNSIGNED", "caption": "Number A", "description": "Number of attempts with a passing grade", "is_primary_key": false},
      {"slug": "num_B", "type": "INTEGER_UNSIGNED", "caption": "Number B", "description": "Number of attempts with a passing grade", "is_primary_key": false},
      {"slug": "num_C", "type": "INTEGER_UNSIGNED", "caption": "Number C", "description": "Number of attempts with a passing grade", "is_primary_key": false},
      {"slug": "num_D", "type": "INTEGER_UNSIGNED", "caption": "Number D", "description": "Number of attempts with a passing grade", "is_primary_key": false},
      {"slug": "num_F", "type": "INTEGER_UNSIGNED", "caption": "Number F", "description": "Number of attempts with a passing grade", "is_primary_key": false},
      {"slug": "percentage_A", "type": "FLOAT", "caption": "Percentage A", "description": "Percentage of attempts with a passing grade", "is_primary_key": false},
      {"slug": "percentage_B", "type": "FLOAT", "caption": "Percentage B", "description": "Percentage of attempts with a passing grade", "is_primary_key": false},
      {"slug": "percentage_C", "type": "FLOAT", "caption": "Percentage C", "description": "Percentage of attempts with a passing grade", "is_primary_key": false},
      {"slug": "percentage_D", "type": "FLOAT", "caption": "Percentage D", "description": "Percentage of attempts with a passing grade", "is_primary_key": false},
      {"slug": "percentage_F", "type": "FLOAT", "caption": "Percentage F", "description": "Percentage of attempts with a passing grade", "is_primary_key": false},
    ],
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "pivot",
          "df_output": "grouped",
          "config": {
            "df_input": "individual_student_exceptions",
            "group_by": ["test_window_id", "assessment_code", "form_code", "gender"],
            "index": "outcome",
            "value": "test_attempt_id",
            "agg": "count",
            "col_prefix": "n_"
          },
        },
        {
          kind: "group-by",
          df_output: "result",
          config: {
            df_input: "individual_student_exceptions",
            "group_by": ["test_window_id", "assessment_code", "form_code", "gender"],
            agg: [{
              col_new: "N",
              agg_type: "count",
              col_target: "test_attempt_id",
            },
            {
              col_new: "avg_score",
              agg_type: "mean",
              col_target: "pct_score",
            },
            {
              col_new: "stdev_score",
              agg_type: "stdev",
              col_target: "pct_score",
            },
          ]
          },
        },
        {
          kind: "join",
          df_output: "result",
          config: {
            how: "inner",
            left: "result",
            right: "grouped",
            left_on: ["test_window_id", "assessment_code", "form_code", "gender"],
            right_on: ["test_window_id", "assessment_code", "form_code", "gender"],
          }
        },
        {
          kind: "map-col",
          df_output: "result",
          config: {
            df_input: "result",
            col_output: "percentage_A",
            source_cols: ["n_A", "N"],
            operation: "percentage",
          }
        },
        {
          kind: "map-col",
          df_output: "result",
          config: {
            df_input: "result",
            col_output: "percentage_B",
            source_cols: ["n_B", "N"],
            operation: "percentage",
          }
        },
        {
          kind: "map-col",
          df_output: "result",
          config: {
            df_input: "result",
            col_output: "percentage_C",
            source_cols: ["n_C", "N"],
            operation: "percentage",
          }
        },
        {
          kind: "map-col",
          df_output: "result",
          config: {
            df_input: "result",
            col_output: "percentage_D",
            source_cols: ["n_D", "N"],
            operation: "percentage",
          }
        },
        {
          kind: "map-col",
          df_output: "result",
          config: {
            df_input: "result",
            col_output: "percentage_F",
            source_cols: ["n_F", "N"],
            operation: "percentage",
          }
        },
      ],
      "output": "result",
    },
    "dependencySourcings": [
      {
        "param": "individual_student_exceptions",
        "type": "dataframe",
        "config": {
          "asset": "trfm_individual_student_exceptions",
        },
      },
    ]
  },
  {
    slug: "trfm_check_missing_correct_response",
    caption: "Check missing correct response",
    description: "Identify machine-scored questions in the item_register where there is no correct response",
    structure: "dataframe",
    isCheckAsset: true,
    severity: CheckAssetSeverity.WARNING,
    schema: [
    ],
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "filter-col",
          df_output: "item_register",
          config: {
            df_input: "item_register",
            col: "is_human_scored",
            comparison: "not-equal",
            value: 1,
          },
        },
        {
          kind: "filter-col",
          df_output: "item_register",
          config: {
            df_input: "item_register",
            col: "correct_tqer_ids",
            comparison: "is-empty-string", // includes null and undefined
          },
        }
      ],
      output: "item_register"
    },
    dependencySourcings: [
      { param: "item_register",
        type: "dataframe",
        config: {
          asset: "trfm_item_register_consolidated",
        },
      },
    ],
  },
  {
    "slug": "load_ts_detail_from_ta_ids",
    "caption": "load_ts_detail_from_ta_ids",
    "description": "Test test session detail from TA ids",
    "storeCache": false,
    "structure": "dataframe",
    "method": 'query-chunked',
    "methodConfig":  {
      "querySlug": 'SQL_05A_TS_DETAIL_FROM_TA_ID',
      "chunkedParam": 'ta_ids',
      "chunkSize": 5000,
    },
    "dependencySourcings": [
      {
        "param": "ta_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_ta_from_tw",
          "col": "attempt_id"
        }
      },
    ]
  },
  {
    "slug": "scan_info_from_ta_id",
    "caption": "scan_info_from_ta_id",
    "description": "Pull all scan needed info from test attempts",
    "storeCache": false,
    "structure": "dataframe",
    "method": 'query-chunked',
    "methodConfig":  {
      "querySlug": 'SQL_07_SCAN_INFO_FROM_TA_ID',
      "chunkedParam": 'ta_ids',
      "chunkSize": 5000,
    },
    "dependencySourcings": [
      {
        "param": "ta_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_ta_from_tw",
          "col": "attempt_id"
        }
      }
    ]
  },
  {
    slug: 'load_scan_detail',
    caption: "load_scan_detail",
    description: "Scan detail",
    storeCache: false,
    structure: "dataframe",
    method: 'transforms',
    methodConfig: {
      sequence: [
        {
          kind: "join",
          df_output: "ta_detail",
          config: {
            how: "left",
            left: "ta_detail",
            right: "load_ts_detail_from_ta_ids",
            left_on: "ts_id",
            right_on: "ts_id",
          },
        },
        {
          kind: "join",
          df_output: "ta_detail",
          config: {
            df_input: "ta_ts_detail",
            how: "inner",
            left: "ta_detail",
            right: "scan_info_from_ta_id",
            left_on: "attempt_id",
            right_on: "attempt_id",
          },
        },
      ],
      output: "ta_detail"
    },
    dependencySourcings: [
      { param: "scan_info_from_ta_id",
        type: "dataframe",
        config: {
          asset: "scan_info_from_ta_id",
        }
      },
      { param: "ta_detail",
        type: "dataframe",
        config: {
          asset: "load_ta_detail",
        }
      },
      { param: "load_ts_detail_from_ta_ids",
        type: "dataframe",
        config: {
          asset: "load_ts_detail_from_ta_ids",
        }
      },
    ],
  },
  {
    slug: 'scan_detail_by_student',
    caption: "scan_detail_by_student",
    description: "Scan detail by student (and assessment type, class)",
    storeCache: false,
    structure: "dataframe",
    method: 'transforms',
    schema: [
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": false},
      {"slug": "student_uid", "type": "INTEGER_UNSIGNED", "caption": "Student User Id", "description": "Foreign key referencing the users table.", "is_primary_key": false},
      {"slug": "s_code", "type": "STRING", "caption": "School Code", "description": "Code of the school.", "is_primary_key": false},
      {"slug": "s_name", "type": "STRING", "caption": "School Name", "description": "Name of the school.", "is_primary_key": false},
      {"slug": "sd_code", "type": "STRING", "caption": "School District Code", "description": "Code of the school district.", "is_primary_key": false},
      {"slug": "sd_name", "type": "STRING", "caption": "School District Name", "description": "Name of the school district.", "is_primary_key": false},
      {"slug": "sc_name", "type": "STRING", "caption": "School Class Name", "description": "Name of the school class.", "is_primary_key": false},
      {"slug": "sc_id", "type": "INTEGER_UNSIGNED", "caption": "School Class Id", "description": "Identifier for the school class.", "is_primary_key": false},
      {"slug": "teacher_uid", "type": "INTEGER_UNSIGNED", "caption": "Teacher User Id", "description": "Teacher User Id", "is_primary_key": false},
      {"slug": "teacher_email", "type": "INTEGER_UNSIGNED", "caption": "Teacher Email", "description": "Teacher Email", "is_primary_key": false},
      {"slug": "student_gov_id", "type": "INTEGER_UNSIGNED", "caption": "Student Government ID", "description": "Alberta Student Government ID.", "is_primary_key": false},
      {"slug": "student_fname", "type": "STRING", "caption": "Student First Name", "description": "First name of the student.", "is_primary_key": false},
      {"slug": "student_lname", "type": "STRING", "caption": "Student Last Name", "description": "Last name of the student.", "is_primary_key": false},
      {"slug": "closed_on_date", "type": "DATETIME", "caption": "Closed On Date", "description": "When the attempt was submitted (if attempts in several sessions, use earliest time)", "is_primary_key": false},
      {"slug": "is_submitted", "type": "INTEGER_UNSIGNED", "caption": "Is Submitted", "description": "Flag indicating whether (any) attempt is submitted.", "is_primary_key": false},
      {"slug": "is_started", "type": "INTEGER_UNSIGNED", "caption": "Is Started", "description": "Has the student accessed (any) test attempt", "is_primary_key": false},
      {"slug": "num_scans_required", "type": "INTEGER_UNSIGNED", "caption": "Scans Required", "description": "Total number of scans required", "is_primary_key": false},
      {"slug": "num_scans_uploaded", "type": "INTEGER_UNSIGNED", "caption": "Scans Uploaded", "description": "Total number of scans uploaded", "is_primary_key": false},
      {"slug": "num_scans_missing", "type": "INTEGER_UNSIGNED", "caption": "Scans Missing", "description": "Total number of scans missing", "is_primary_key": false},
      {"slug": "first_scan_uploaded_on", "type": "INTEGER_UNSIGNED", "caption": "First Scan Uploaded Date", "description": "First Scan Uploaded Date", "is_primary_key": false},
      {"slug": "last_scan_uploaded_on", "type": "INTEGER_UNSIGNED", "caption": "Last Scan Uploaded Date", "description": "First Scan Uploaded Date", "is_primary_key": false},
      {"slug": "is_any_uploaded_student", "type": "INTEGER_UNSIGNED", "caption": "Is Any Uploaded", "description": "Has the student uploaded at least one scan", "is_primary_key": false},
    ],
    methodConfig: {
      sequence: [
        // Group by student (and assessment type, class) - student could have written in several test sessions so aggregate
        {
          kind: "group-by",
          df_output: "load_scan_detail",
          config: {
            df_input: "load_scan_detail",
            group_by: ["assessment_code", "student_uid", "s_code", "s_name", "sd_code", "sd_name", "sc_id", "sc_name", "teacher_uid", "teacher_email", "teacher_name",
              "student_uid", "student_gov_id", "student_fname", "student_lname"],
            agg: [
              {
                col_new: "is_started",
                agg_type: "max",
                col_target: "is_started",
              },
              {
                col_new: "is_submitted",
                agg_type: "max",
                col_target: "is_submitted",
              },
              {
                col_new: "closed_on_date",
                agg_type: "minDate",
                col_target: "closed_on_date",
              },
              {
                col_new: "num_scans_required",
                agg_type: "sum",
                col_target: "num_scans_required",
              },
              {
                col_new: "num_scans_uploaded",
                agg_type: "sum",
                col_target: "num_scans_uploaded",
              },
              {
                col_new: "num_scans_missing",
                agg_type: "sum",
                col_target: "num_scans_missing",
              },
              {
                col_new: "first_scan_uploaded_on",
                agg_type: "minDate",
                col_target: "first_scan_uploaded_on",
              },
              {
                col_new: "last_scan_uploaded_on",
                agg_type: "maxDate",
                col_target: "last_scan_uploaded_on",
              },
              {
                col_new: "is_any_uploaded_student",
                agg_type: "max",
                col_target: "is_any_uploaded",
              },
              {
                col_new: "is_any_scan_missing",
                agg_type: "max",
                col_target: "is_any_scan_missing",
              }
            ]
          },
        },
      ],
      output: "load_scan_detail"
    },
    dependencySourcings: [
      { param: "load_scan_detail",
        type: "dataframe",
        config: {
          asset: "load_scan_detail",
        }
      },

    ],

  },
  {
    slug: 'scan_detail_by_session',
    caption: "scan_detail_by_session",
    description: "Scan detail by Session",
    storeCache: false,
    structure: "dataframe",
    method: 'transforms',
    schema: [
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": false},
      {"slug": "ts_id", "type": "INTEGER_UNSIGNED", "caption": "Test Session Id", "description": "Identifier for the test session.", "is_primary_key": false},
      {"slug": "ts_name", "type": "INTEGER_UNSIGNED", "caption": "Test Session Name", "description": "Custom name for the test session.", "is_primary_key": false},
      {"slug": "ts_start", "type": "DATETIME", "caption": "Test Session Start", "description": "Start time of the test session.", "is_primary_key": false},
      {"slug": "num_students", "type": "INTEGER_UNSIGNED", "caption": "Number of Students", "description": "Number of students who will write the session.", "is_primary_key": false},
      {"slug": "num_students_started", "type": "INTEGER_UNSIGNED", "caption": "Number of Students Started", "description": "Number of students who started an attempt in the session.", "is_primary_key": false},
      {"slug": "num_students_submitted", "type": "INTEGER_UNSIGNED", "caption": "Number of Students Submitted", "description": "Number of students who submitted in the session.", "is_primary_key": false},
      {"slug": "s_code", "type": "STRING", "caption": "School Code", "description": "Code of the school.", "is_primary_key": false},
      {"slug": "s_name", "type": "STRING", "caption": "School Name", "description": "Name of the school.", "is_primary_key": false},
      {"slug": "sd_code", "type": "STRING", "caption": "School District Code", "description": "Code of the school district.", "is_primary_key": false},
      {"slug": "sd_name", "type": "STRING", "caption": "School District Name", "description": "Name of the school district.", "is_primary_key": false},
      {"slug": "sc_name", "type": "STRING", "caption": "School Class Name", "description": "Name of the school class.", "is_primary_key": false},
      {"slug": "sc_id", "type": "INTEGER_UNSIGNED", "caption": "School Class Id", "description": "Identifier for the school class.", "is_primary_key": false},
      {"slug": "teacher_uid", "type": "INTEGER_UNSIGNED", "caption": "Teacher User Id", "description": "Teacher User Id", "is_primary_key": false},
      {"slug": "teacher_email", "type": "INTEGER_UNSIGNED", "caption": "Teacher Email", "description": "Teacher Email", "is_primary_key": false},
      {"slug": "num_scans_required", "type": "INTEGER_UNSIGNED", "caption": "Scans Required", "description": "Total number of scans required", "is_primary_key": false},
      {"slug": "num_scans_uploaded", "type": "INTEGER_UNSIGNED", "caption": "Scans Uploaded", "description": "Total number of scans uploaded", "is_primary_key": false},
      {"slug": "num_scans_missing", "type": "INTEGER_UNSIGNED", "caption": "Scans Missing", "description": "Total number of scans missing", "is_primary_key": false},
      {"slug": "first_scan_uploaded_on", "type": "INTEGER_UNSIGNED", "caption": "First Scan Uploaded Date", "description": "First Scan Uploaded Date", "is_primary_key": false},
      {"slug": "last_scan_uploaded_on", "type": "INTEGER_UNSIGNED", "caption": "Last Scan Uploaded Date", "description": "First Scan Uploaded Date", "is_primary_key": false},
      {"slug": "is_any_uploaded_ts", "type": "INTEGER_UNSIGNED", "caption": "Is Any Uploaded", "description": "Has at least one scan been uploaded in the session.", "is_primary_key": false},
    ],
    methodConfig: {
      sequence: [
        {
          kind: "group-by",
          df_output: "load_scan_detail",
          config: {
            // First group by student and session (and associated info) in order to add up student counts
            df_input: "load_scan_detail",
            group_by: ["assessment_code",
              "ts_id", "ts_name",  "ts_start", "ts_is_closed", "num_students", "num_students_started", "num_students_submitted","teacher_uid", "teacher_email", "teacher_name",
              "s_code", "s_name", "sd_code", "sd_name", "sc_id", "sc_name", "student_uid"
            ],
            agg: [
              {
                col_new: "num_scans_required",
                agg_type: "sum",
                col_target: "num_scans_required",
              },
              {
                col_new: "num_scans_uploaded",
                agg_type: "sum",
                col_target: "num_scans_uploaded",
              },
              {
                col_new: "num_scans_missing",
                agg_type: "sum",
                col_target: "num_scans_missing",
              },
              {
                col_new: "first_scan_uploaded_on",
                agg_type: "minDate",
                col_target: "first_scan_uploaded_on",
              },
              {
                col_new: "last_scan_uploaded_on",
                agg_type: "maxDate",
                col_target: "last_scan_uploaded_on",
              },
              {
                col_new: "is_any_uploaded",
                agg_type: "max",
                col_target: "is_any_uploaded",
              },
              {
                col_new: "is_any_scan_missing",
                agg_type: "max",
                col_target: "is_any_scan_missing",
              }
            ]
          },
        },
        // Group by session and get total stats for students in session
        {
          kind: "group-by",
          df_output: "load_scan_detail",
          config: {
            df_input: "load_scan_detail",
            group_by: ["assessment_code",
              "ts_id", "ts_name",  "ts_start", "ts_is_closed", "num_students", "num_students_started", "num_students_submitted",
              "teacher_uid", "teacher_email", "s_code", "s_name", "sd_code", "sd_name", "sc_id", "sc_name",
            ],
            agg: [
              {
                col_new: "num_scans_required",
                agg_type: "sum",
                col_target: "num_scans_required",
              },
              {
                col_new: "num_scans_uploaded",
                agg_type: "sum",
                col_target: "num_scans_uploaded",
              },
              {
                col_new: "num_scans_missing",
                agg_type: "sum",
                col_target: "num_scans_missing",
              },
              {
                col_new: "first_scan_uploaded_on",
                agg_type: "minDate",
                col_target: "first_scan_uploaded_on",
              },
              {
                col_new: "last_scan_uploaded_on",
                agg_type: "maxDate",
                col_target: "last_scan_uploaded_on",
              },
              {
                col_new: "is_any_uploaded_ts",
                agg_type: "max",
                col_target: "is_any_uploaded",
              },
              {
                col_new: "num_students_uploaded",
                agg_type: "sum",
                col_target: "is_any_uploaded",
              },
              {
                col_new: "num_students_missing_scans",
                agg_type: "sum",
                col_target: "is_any_scan_missing",
              }
            ]
          },
        },
        {
          kind: "map-col",
          df_output: "load_scan_detail",
          config: {
            df_input: "load_scan_detail",
            col_output: "num_students_not_uploaded",
            source_cols: ["num_students", "num_students_uploaded"],
            operation: "subtract",
          }
        },
      ],
      output: "load_scan_detail"
    },
    dependencySourcings: [
      { param: "load_scan_detail",
        type: "dataframe",
        config: {
          asset: "load_scan_detail",
        }
      },
    ],
  },
  {
    slug: 'scan_detail_by_assessment',
    caption: "scan_detail_by_assessment",
    description: "Scan detail by Assessment",
    storeCache: false,
    structure: "dataframe",
    method: 'transforms',
    schema: [
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": false},
      {"slug": "num_districts", "type": "INTEGER_UNSIGNED", "caption": "Number of Districts", "description": "Number of districts in which students are writing this assessment.", "is_primary_key": false},
      {"slug": "num_schools", "type": "INTEGER_UNSIGNED", "caption": "Number of Schools", "description": "Number of schools in which students are writing this assessment.", "is_primary_key": false},
      {"slug": "num_classes", "type": "INTEGER_UNSIGNED", "caption": "Number of Classes", "description": "Number of classes in which students are writing this assessment.", "is_primary_key": false},
      {"slug": "num_scans_uploaded", "type": "INTEGER_UNSIGNED", "caption": "Scans Uploaded", "description": "Total number of scans uploaded", "is_primary_key": false},
      {"slug": "first_scan_uploaded_on", "type": "INTEGER_UNSIGNED", "caption": "First Scan Uploaded Date", "description": "First Scan Uploaded Date", "is_primary_key": false},
      {"slug": "last_scan_uploaded_on", "type": "INTEGER_UNSIGNED", "caption": "Last Scan Uploaded Date", "description": "First Scan Uploaded Date", "is_primary_key": false},
    ],
    methodConfig: {
      sequence: [
        {
          kind: "group-by",
          df_output: "load_scan_detail",
          config: {
            df_input: "load_scan_detail",
            group_by: ["assessment_code", "student_uid"],
            agg: [
              {
                col_new: "num_districts",
                agg_type: "nunique",
                col_target: "sd_code",
              },
              {
                col_new: "num_schools",
                agg_type: "nunique",
                col_target: "s_code",
              },
              {
                col_new: "num_classes",
                agg_type: "nunique",
                col_target: "sc_id",
              },
              {
                col_new: "num_scans_required",
                agg_type: "sum",
                col_target: "num_scans_required",
              },
              {
                col_new: "num_scans_uploaded",
                agg_type: "sum",
                col_target: "num_scans_uploaded",
              },
              {
                col_new: "num_scans_missing",
                agg_type: "sum",
                col_target: "num_scans_missing",
              },
              {
                col_new: "first_scan_uploaded_on",
                agg_type: "minDate",
                col_target: "first_scan_uploaded_on",
              },
              {
                col_new: "last_scan_uploaded_on",
                agg_type: "maxDate",
                col_target: "last_scan_uploaded_on",
              },
              {
                col_new: "is_any_uploaded",
                agg_type: "max",
                col_target: "is_any_uploaded",
              },
              {
                col_new: "is_any_scan_missing",
                agg_type: "max",
                col_target: "is_any_scan_missing",
              }
            ]
          }
        },
        // Group by assessment and add up counts
        {
          kind: "group-by",
          df_output: "load_scan_detail",
          config: {
            df_input: "load_scan_detail",
            group_by: ["assessment_code"],
            agg: [
              {
                col_new: "num_districts",
                agg_type: "max",
                col_target: "num_districts",
              },
              {
                col_new: "num_schools",
                agg_type: "max",
                col_target: "num_schools",
              },
              {
                col_new: "num_classes",
                agg_type: "max",
                col_target: "num_classes",
              },
              {
                col_new: "first_scan_uploaded_on",
                agg_type: "minDate",
                col_target: "first_scan_uploaded_on",
              },
              {
                col_new: "last_scan_uploaded_on",
                agg_type: "maxDate",
                col_target: "last_scan_uploaded_on",
              },
              {
                col_new: "num_students_uploaded",
                agg_type: "sum",
                col_target: "is_any_uploaded",
              },
              {
                col_new: "num_students",
                agg_type: "nunique",
                col_target: "student_uid",
              },
              {
                col_new: "num_scans_required",
                agg_type: "sum",
                col_target: "num_scans_required",
              },
              {
                col_new: "num_scans_missing",
                agg_type: "sum",
                col_target: "num_scans_missing",
              },
              {
                col_new: "num_scans_uploaded",
                agg_type: "sum",
                col_target: "num_scans_uploaded",
              },
              {
                col_new: "num_students_missing_scans",
                agg_type: "sum",
                col_target: "is_any_scan_missing",
              }
            ]
          },
        },
        {
          kind: "map-col",
          df_output: "load_scan_detail",
          config: {
            df_input: "load_scan_detail",
            col_output: "num_students_not_uploaded",
            source_cols: ["num_students", "num_students_uploaded"],
            operation: "subtract",
          }
        },
      ],
      output: "load_scan_detail"
    },
    dependencySourcings: [
      { param: "load_scan_detail",
        type: "dataframe",
        config: {
          asset: "load_scan_detail",
        }
      },
    ],
  },
  {
    slug: 'scan_detail_by_school',
    caption: "scan_detail_by_school",
    description: "Scan detail by School",
    storeCache: false,
    structure: "dataframe",
    method: 'transforms',
    schema: [
      {"slug": "assessment_code", "type": "STRING", "caption": "Assessment Code", "description": "Code for the assessment.", "is_primary_key": false},
      {"slug": "s_code", "type": "STRING", "caption": "School Code", "description": "Code of the school.", "is_primary_key": false},
      {"slug": "s_name", "type": "STRING", "caption": "School Name", "description": "Name of the school.", "is_primary_key": false},
      {"slug": "sd_code", "type": "STRING", "caption": "School District Code", "description": "Code of the school district.", "is_primary_key": false},
      {"slug": "sd_name", "type": "STRING", "caption": "School District Name", "description": "Name of the school district.", "is_primary_key": false},
      {"slug": "sc_name", "type": "STRING", "caption": "School Class Name", "description": "Name of the school class.", "is_primary_key": false},
      {"slug": "num_sessions", "type": "INTEGER_UNSIGNED", "caption": "Number of Sessions", "description": "Number of sessions in which students are writing this assessment.", "is_primary_key": false},
      {"slug": "num_sessions_any_uploaded", "type": "INTEGER_UNSIGNED", "caption": "Number of Sessions", "description": "Number of sessions in which students are writing this assessment and at least one scan has been uploaded.", "is_primary_key": false},
      {"slug": "num_students", "type": "INTEGER_UNSIGNED", "caption": "Number of Students", "description": "Number of students writing this assessment.", "is_primary_key": false},
      {"slug": "num_students_any_uploaded", "type": "INTEGER_UNSIGNED", "caption": "Number of Students", "description": "Number of students writing this assessment and for which at least one scan has been uploaded.", "is_primary_key": false},
      {"slug": "num_scans_uploaded", "type": "INTEGER_UNSIGNED", "caption": "Scans Uploaded", "description": "Total number of scans uploaded", "is_primary_key": false},
      {"slug": "first_scan_uploaded_on", "type": "INTEGER_UNSIGNED", "caption": "First Scan Uploaded Date", "description": "First Scan Uploaded Date", "is_primary_key": false},
      {"slug": "last_scan_uploaded_on", "type": "INTEGER_UNSIGNED", "caption": "Last Scan Uploaded Date", "description": "First Scan Uploaded Date", "is_primary_key": false},
    ],
    methodConfig: {
      // Start with stats by sessions and group them by assessment and school
      sequence: [
        {
          kind: "group-by",
          df_output: "scan_detail_by_session",
          config: {
            df_input: "scan_detail_by_session",
            group_by: ["assessment_code" ,"s_code", "s_name", "sd_code", "sd_name"],
            agg: [
              {
                col_new: "num_sessions_any_uploaded",
                agg_type: "sum",
                col_target: "is_any_uploaded_ts",
              },
              {
                col_new: "num_sessions",
                agg_type: "nunique",
                col_target: "ts_id",
              },
              {
                col_new: "num_scans_required",
                agg_type: "sum",
                col_target: "num_scans_required",
              },
              {
                col_new: "num_scans_missing",
                agg_type: "sum",
                col_target: "num_scans_missing",
              },
              {
                col_new: "num_scans_uploaded",
                agg_type: "sum",
                col_target: "num_scans_uploaded",
              },
              {
                col_new: "first_scan_uploaded_on",
                agg_type: "minDate",
                col_target: "first_scan_uploaded_on",
              },
              {
                col_new: "last_scan_uploaded_on",
                agg_type: "maxDate",
                col_target: "last_scan_uploaded_on",
              }
            ]
          },
        },
        // join details grouped by student, by class
        {
          kind: "join",
          df_output: "result_grouped",
          config: {
            how: "left",
            left: "scan_detail_by_session",
            right: "scan_detail_by_student",
            left_on: ["assessment_code" ,"s_code"],
            right_on: ["assessment_code" ,"s_code"],
          },
        },
        // Group by students in case there are duplicates (students writing in more than one class per school)
        {
          kind: "group-by",
          df_output: "result_grouped",
          config: {
            df_input: "result_grouped",
            group_by: ["assessment_code" ,"s_code", "s_name", "sd_code", "sd_name", "num_sessions_any_uploaded", "num_sessions", "num_scans_uploaded", "num_scans_required", "num_scans_missing", "first_scan_uploaded_on", "last_scan_uploaded_on", "student_uid"],
            agg: [
              {
                col_new: "is_any_uploaded_student",
                agg_type: "max",
                col_target: "is_any_uploaded_student",
              },
              {
                col_new: "is_any_scan_missing",
                agg_type: "max",
                col_target: "is_any_scan_missing",
              }
            ]
          },
        },
        // Regroup by assessment and school and add up student stats
        {
          kind: "group-by",
          df_output: "result_grouped",
          config: {
            df_input: "result_grouped",
            group_by: ["assessment_code" ,"s_code", "s_name", "sd_code", "sd_name", "num_sessions_any_uploaded", "num_sessions", "num_scans_uploaded", "num_scans_required", "num_scans_missing", "first_scan_uploaded_on", "last_scan_uploaded_on"],
            agg: [
              {
                col_new: "num_students_any_uploaded",
                agg_type: "sum",
                col_target: "is_any_uploaded_student",
              },
              {
                col_new: "num_students",
                agg_type: "nunique",
                col_target: "student_uid",
              },
              {
                col_new: "num_students_missing_scans",
                agg_type: "sum",
                col_target: "is_any_scan_missing",
              }
            ]
          },
        },
        {
          kind: "map-col",
          df_output: "result_grouped",
          config: {
            df_input: "result_grouped",
            col_output: "num_students_not_uploaded",
            source_cols: ["num_students", "num_students_any_uploaded"],
            operation: "subtract",
          }
        },
      ],
      output: "result_grouped"
    },
    dependencySourcings: [
      { param: "scan_detail_by_session",
        type: "dataframe",
        config: {
          asset: "scan_detail_by_session",
        }
      },
      { param: "scan_detail_by_student",
        type: "dataframe",
        config: {
          asset: "scan_detail_by_student",
        }
      },

    ],
  },
  {
    "slug": "scan_count_by_date",
    "caption": "scan_count_by_date",
    "description": "Count number of scan uploads by date (including discarded)",
    schema: [
      {"slug": "upload_date", "type": "STRING", "caption": "Date", "description": "Date", "is_primary_key": false},
      {"slug": "n_scans", "type": "STRING", "caption": "Number of Scans", "description": "Number of scan uploads on the date (includes scans that are later discarded when replaced by others)", "is_primary_key": false}
    ],
    "storeCache": false,
    "structure": "dataframe",
    "method": 'query-chunked',
    "methodConfig":  {
      "querySlug": 'SQL_07_SCAN_COUNT_BY_DATE_FROM_TA_ID',
      "chunkedParam": 'ta_ids',
      "chunkSize": 2500,
    },
    "dependencySourcings": [
      {
        "param": "ta_ids",
        "type": "asset-col",
        "config": {
          "asset": "load_ta_from_tw",
          "col": "attempt_id"
        }
      }
    ]
  },

  // authoring monitoring
  {
    slug: "load_tw_active",
    description: "Loads any test window that has not already closed (date_end > now(), is_qa=0).",
    structure: "dataframe",
    method: "query",
    methodConfig: {
      querySlug: "SQL_01_SYS_TEST_WINDOWS_ACTIVE",
    },
    dependencySourcings: [],
    schema: [
      { slug: "tw_id", type: "INTEGER_UNSIGNED", caption: "Test Window ID" },
      { slug: "tw_type_slug", type: "STRING", caption: "Type Slug" },
      { slug: "title", type: "STRING", caption: "Title" },
      { slug: "date_start", type: "DATETIME", caption: "Date Start" },
      { slug: "date_end", type: "DATETIME", caption: "Date End" },
    ],
  },
  {
    slug: "load_tw_ts_forms_inactive",
    isCheckAsset: true,
    caption: "Inactive/Renamed Assessment Codes in Scheduling",
    description: "Sessions scheduled on form allocations that have been unlinked or de-activated.",
    structure: "dataframe",
    method: "query",
    methodConfig: {
      querySlug: "SQL_05A_TA_FORMS_INACTIVE"
    },
    dependencySourcings: [
      {
        param: "test_window_ids",
        type: "asset-col",
        config: {
          asset: "load_tw_active",
          col: "tw_id"
        }
      }
    ],
  },
  {
    slug: "load_tw_ta_forms_superceded",
    caption: "Superceded Test Forms",
    description: "Test attempts for test designs that have since been updated (i.e., form TD < allocated TD).",
    structure: "dataframe",
    method: "query",
    methodConfig: {
      querySlug: "SQL_05A_TA_FORMS_SUPERCEDED"
    },
    schema: [
      { slug: "ta_id", type: "INTEGER_UNSIGNED" },
      { slug: "is_closed", type: "BOOL_INT" },
      { slug: "is_cancelled", type: "BOOL_INT" },
      { slug: "is_started", type: "BOOL_INT" },
      { slug: "is_any_in_class_started", type: "BOOL_INT" },
      { slug: "ts_id", type: "INTEGER_UNSIGNED" },
      { slug: "type_slug", type: "STRING" },
      { slug: "is_sample_school", type: "BOOL_INT" },
      { slug: "school_name", type: "STRING" },
      { slug: "date_time_start", type: "DATETIME" },
      { slug: "test_design_id", type: "INTEGER_UNSIGNED" },
      { slug: "test_design_id_new", type: "INTEGER_UNSIGNED" },
    ],
    dependencySourcings: [
      {
        param: "test_window_ids",
        type: "asset-col",
        config: {
          asset: "load_tw_active",
          col: "tw_id"
        }
      }
    ],
  },
  {
    slug: "trfm_tw_ta_forms_superceded_upcoming",
    isCheckAsset: true,
    caption: "Superceded Form Attempts (Upcoming Only)",
    description: "Filters superceded test attempts to show only those that are not closed or cancelled.",
    structure: "dataframe",
    method: "transforms",
    schema: [
      { slug: "ta_id", type: "INTEGER_UNSIGNED", caption: "Test Attempt ID" },
      { slug: "ts_id", type: "INTEGER_UNSIGNED", caption: "Test Session ID" },
      { slug: "type_slug", type: "STRING", caption: "Assessment Type" },
      { slug: "is_sample_school", type: "BOOL_INT" },
      { slug: "school_name", type: "STRING" },
      { slug: "date_time_start", type: "DATETIME" },
      { slug: "test_design_id", type: "INTEGER_UNSIGNED", caption: "Old Test Design ID" },
      { slug: "test_design_id_new", type: "INTEGER_UNSIGNED", caption: "New Test Design ID" }
    ],
    methodConfig: {
      sequence: [
        {
          kind: "filter-col",
          df_output: "df",
          config: {
            df_input: "df",
            col: "is_closed",
            comparison: "equals",
            value: 0
          }
        },
        {
          kind: "filter-col",
          df_output: "df",
          config: {
            df_input: "df",
            col: "is_cancelled",
            comparison: "equals",
            value: 0
          }
        },
        {
          kind: "filter-col",
          df_output: "df",
          config: {
            df_input: "df",
            col: "is_started",
            comparison: "equals",
            value: 0
          }
        },
        {
          kind: "filter-col",
          df_output: "df",
          config: {
            df_input: "df",
            col: "is_any_in_class_started",
            comparison: "equals",
            value: 0
          }
        },
        {
          kind: "filter-col",
          df_output: "df",
          config: {
            df_input: "df",
            col: "is_ts_twtar_slug_match",
            comparison: "equals",
            value: 1
          }
        },
      ],
      output: "df"
    },
    dependencySourcings: [
      {
        param: "df",
        type: "dataframe",
        config: {
          asset: "load_tw_ta_forms_superceded"
        }
      }
    ]
  },
  {
    slug: "trfm_tw_ta_forms_superceded_past",
    caption: "Superceded Form Attempts (Accessed)",
    description: "Filters superceded test attempts to those who have been accessed.",
    structure: "dataframe",
    method: "transforms",
    schema: [
      { slug: "ta_id", type: "INTEGER_UNSIGNED", caption: "Test Attempt ID" },
      { slug: "ts_id", type: "INTEGER_UNSIGNED", caption: "Test Session ID" },
      { slug: "type_slug", type: "STRING", caption: "Assessment Type" },
      { slug: "is_sample_school", type: "BOOL_INT" },
      { slug: "school_name", type: "STRING" },
      { slug: "date_time_start", type: "DATETIME" },
      { slug: "test_design_id", type: "INTEGER_UNSIGNED", caption: "Old Test Design ID" },
      { slug: "test_design_id_new", type: "INTEGER_UNSIGNED", caption: "New Test Design ID" }
    ],
    methodConfig: {
      sequence: [
        {
          kind: "filter-col",
          df_output: "df",
          config: {
            df_input: "df",
            col: "is_started",
            comparison: "equals",
            value: 1
          }
        }
      ],
      output: "df"
    },
    dependencySourcings: [
      {
        param: "df",
        type: "dataframe",
        config: {
          asset: "load_tw_ta_forms_superceded"
        }
      }
    ]
  },
  {
    slug: "trfm_tw_ta_forms_superceded_summary",
    caption: "Superceded Form Summary (upcoming, by Assessment Code)",
    isCheckAsset: true,
    description: "Summarizes test attempts with superceded forms by assessment type and test design.",
    structure: "dataframe",
    method: "transforms",
    schema: [
      { slug: "test_window_id", type: "INTEGER_UNSIGNED", caption: "Test Window ID" },
      { slug: "asmt_code", type: "STRING", caption: "Assessment Type" },
      { slug: "test_design_id", type: "INTEGER_UNSIGNED", caption: "Old Test Design ID" },
      { slug: "test_design_id_new", type: "INTEGER_UNSIGNED", caption: "New Test Design ID" },
      { slug: "n_sessions", type: "INTEGER_UNSIGNED", caption: "Number of Sessions" }
    ],
    methodConfig: {
      sequence: [
        {
          kind: "group-by",
          df_output: "df_summary",
          config: {
            df_input: "df_sessions",
            group_by: [
              "test_window_id", 
              "asmt_code", 
              "test_design_id", 
              "test_design_id_new"
            ],
            agg: [
              {
                col_new: "n_sessions",
                agg_type: "count",
                col_target: "ta_id"
              }
            ]
          }
        },
        {
          kind: "restrict-cols",
          df_output: "df_tw",
          config: {
            df_input: "df_tw",
            cols: [
              "tw_id",
              "tw_type_slug",
              "title",
            ]
          }
        },
        {
          kind: "join",
          df_output: "df_merged",
          config: {
            how: "left",
            left: "df_summary",
            right: "df_tw",
            left_on: "test_window_id",
            right_on: "tw_id"
          }
        },
  
      ],
      output: "df_merged"
    },
    dependencySourcings: [
      {
        param: "df_sessions",
        type: "dataframe",
        config: {
          asset: "load_sessions_superceded_forms"
        }
      },
      {
        param: "df_tw",
        type: "dataframe",
        config: {
          asset: "load_tw_active"
        }
      }
    ]
  },
  {
    slug: "trfm_tw_ta_forms_superceded_window_summary",
    isCheckAsset: true,
    caption: "Superceded Form Summary (upcoming, by Window)",
    description: "Summarizes the number of affected assessment codes and attempts with superceded forms, grouped by test window.",
    structure: "dataframe",
    method: "transforms",
    schema: [
      { slug: "test_window_id", type: "INTEGER_UNSIGNED", caption: "Test Window ID" },
      { slug: "tw_type_slug", type: "STRING", caption: "Test Window Type" },
      { slug: "title", type: "STRING", caption: "Test Window Title" },
      { slug: "n_assessments", type: "INTEGER_UNSIGNED", caption: "Number of Assessment Codes Affected" },
      { slug: "n_sessions", type: "INTEGER_UNSIGNED", caption: "Number of Sessions Affected" },
    ],
    methodConfig: {
      sequence: [
        {
          kind: "group-by",
          df_output: "df_summary_by_window",
          config: {
            df_input: "df",
            group_by: ["test_window_id", "tw_type_slug", "title"],
            agg: [
              {
                col_new: "n_assessments",
                agg_type: "nunique",
                col_target: "asmt_code"
              },
              {
                col_new: "n_sessions",
                agg_type: "sum",
                col_target: "n_sessions"
              },
            ]
          }
        }
      ],
      output: "df_summary_by_window"
    },
    dependencySourcings: [
      {
        param: "df",
        type: "dataframe",
        config: {
          asset: "trfm_tw_ta_forms_superceded_summary"
        }
      }
    ]
  },
  
  {
    slug: "load_sc_cf_slug_mismatch",
    isCheckAsset: true,
    caption: "Mismatched Assessment Codes in Class Common Forms",
    description: "Checks if the type_slug on school_class_common_forms disagrees with the one on the associated allocation rule (twtdar).",
    structure: "dataframe",
    method: "query",
    methodConfig: {
      querySlug: "SQL_04B_SC_CF_SLUG_MISMATCH"
    },
    schema: [ 
      { slug: "sccf_id", type: "INTEGER_UNSIGNED", caption: "Form Lock ID" },
      { slug: "school_class_id", type: "INTEGER_UNSIGNED", caption: "School Class ID" },
      { slug: "class_name", type: "STRING", caption: "Class Name" },
      { slug: "asmt_profile_short_name", type: "STRING", caption: "Assessment Profile Short Name" },
      { slug: "locked_on", type: "TIMESTAMP", caption: "Form Locked On" },
      { slug: "twtdar_id", type: "INTEGER_UNSIGNED", caption: "TWTAR ID" },
      { slug: "asmt_code_locked", type: "TEXT", caption: "Assessment Code (Locked Form)" },
      { slug: "asmt_code_linked", type: "TEXT", caption: "Assessment Code (Linked Allocation)" },
      { slug: "teacher_emails", type: "TEXT", caption: "Teacher Emails" },
      { slug: "old_slug_session_ids", type: "TEXT", caption: "Old Slug Session IDs" },
      { slug: "new_slug_session_ids", type: "TEXT", caption: "New Slug Session IDs" },
    ],
    dependencySourcings: [
      {
        param: "test_window_ids",
        type: "asset-col",
        config: {
          asset: "load_tw_active",
          col: "tw_id"
        }
      }
    ],
  },

  {
    slug: "load_items_created",
    caption: "Created Items",
    description: "List of test questions created in the last N days.",
    structure: "dataframe",
    method: "query",
    methodConfig: {
      querySlug: "SQL_03B_AUTH_MONIT_ITEMS_CREATED"
    },
    dependencySourcings: [
      {
        param: "days_back",
        type: "job-config",
        config: { configSlug: "pipeline_config", param: "days_back" }
      }
    ]
  },

  {
    slug: "load_items_deleted",
    caption: "Deleted Items (todo: not yet vetted...)",
    description: "List of test questions archived in the last N days.",
    structure: "dataframe",
    method: "query",
    methodConfig: {
      querySlug: "SQL_03B_AUTH_MONIT_ITEMS_DELETED"
    },
    dependencySourcings: [
      {
        param: "days_back",
        type: "job-config",
        config: { configSlug: "pipeline_config", param: "days_back" }
      }
    ]
  },
  {
    slug: "load_items_modified",
    caption: "Modified Items",
    description: "Summary of test question edits in the last N days.",
    structure: "dataframe",
    method: "query",
    methodConfig: {
      querySlug: "SQL_03B_AUTH_MONIT_ITEMS_MODIFIED"
    },
    dependencySourcings: [
      {
        param: "days_back",
        type: "job-config",
        config: { configSlug: "pipeline_config", param: "days_back" }
      }
    ]
  },
  {
    slug: "load_item_answers_added",
    caption: "Added Expected Answers",
    description: "Expected answers added by admin users in the last N days.",
    structure: "dataframe",
    method: "query",
    methodConfig: {
      querySlug: "SQL_03B_AUTH_MONIT_ITEM_ANSWERS_ADDED"
    },
    dependencySourcings: [
      {
        param: "days_back",
        type: "job-config",
        config: { configSlug: "pipeline_config", param: "days_back" }
      }
    ]
  },
  {
    slug: "load_item_answers_removed",
    caption: "Removed Expected Answers",
    description: "Expected answers revoked by admin users in the last N days.",
    structure: "dataframe",
    method: "query",
    methodConfig: {
      querySlug: "SQL_03B_AUTH_MONIT_ITEM_ANSWERS_REMOVED"
    },
    dependencySourcings: [
      {
        param: "days_back",
        type: "job-config",
        config: { configSlug: "pipeline_config", param: "days_back" }
      }
    ]
  },
  {
    slug: "load_frameworks",
    caption: "Frameworks",
    description: "List of framework updates including authoring group and save counts.",
    structure: "dataframe",
    method: "query",
    methodConfig: {
      querySlug: "SQL_03B_AUTH_MONIT_FRAMEWORKS"
    },
    dependencySourcings: [
      {
        param: "days_back",
        type: "job-config",
        config: { configSlug: "pipeline_config", param: "days_back" }
      }
    ]
  },
  {
    slug: "load_publishings",
    caption: "Publishings",
    description: "List of test designs published in the last N days with item set and author group info.",
    structure: "dataframe",
    method: "query",
    methodConfig: {
      querySlug: "SQL_03B_AUTH_MONIT_PUBLISHINGS"
    },
    dependencySourcings: [
      {
        param: "days_back",
        type: "job-config",
        config: { configSlug: "pipeline_config", param: "days_back" }
      }
    ]
  },
  {
    slug: "load_form_allocations",
    caption: "Form Allocations",
    description: "Changes to test window allocation rules affecting test designs, joined with authorship context.",
    structure: "dataframe",
    method: "query",
    schema: [ 
      { slug: "log_id", type: "INTEGER_UNSIGNED", caption: "Log Entry ID", description: "Unique identifier for the log entry." },
      { slug: "twtar_id", type: "INTEGER_UNSIGNED", caption: "Allocation Rule ID", description: "ID of the allocation rule being patched." },
      { slug: "patches__test_design_id", type: "INTEGER_UNSIGNED", caption: "Patched Test Design ID", description: "Extracted test design ID from the JSON patch content." },
      { slug: "patches__tqr_ovrd_td_id", type: "INTEGER_UNSIGNED", caption: "Patched TQR Override TD ID", description: "Extracted override test design ID for TQR from the JSON patch content." },
      { slug: "patched_on", type: "DATETIME", caption: "Patched On", description: "Timestamp when the patch was applied." },
      { slug: "patched_by_uid", type: "INTEGER_UNSIGNED", caption: "Patched By", description: "User ID of the person who applied the patch." },
      { slug: "new_test_design_id", type: "INTEGER_UNSIGNED", caption: "New Test Design ID", description: "The test design ID set in the patch." },
      { slug: "original_test_design_id", type: "INTEGER_UNSIGNED", caption: "Original Test Design ID", description: "The existing test design ID from the allocation rule." },
      { slug: "form_code", type: "STRING", caption: "Form Code", description: "Code of the form associated with the allocation rule." },
      { slug: "lang", type: "STRING", caption: "Language", description: "Language of the form allocation." },
      { slug: "type_slug", type: "STRING", caption: "Assessment Type", description: "Assessment type slug (e.g., 'ELA9')." },
      { slug: "is_sample", type: "BOOL_INT", caption: "Is Sample", description: "Flag indicating whether the form is a sample." },
      { slug: "is_questionnaire", type: "BOOL_INT", caption: "Is Questionnaire", description: "Whether this form is a questionnaire." },
      { slug: "is_marking_req", type: "BOOL_INT", caption: "Requires Marking", description: "Flag indicating if this form needs manual scoring." },
      { slug: "is_print", type: "BOOL_INT", caption: "Is Print", description: "Flag for print-based test forms." },
      { slug: "test_window_id", type: "INTEGER_UNSIGNED", caption: "Test Window ID", description: "ID of the test window associated with the form allocation." },
      { slug: "test_window_type", type: "STRING", caption: "Test Window Type", description: "Type slug for the test window." },
      { slug: "date_start", type: "DATETIME", caption: "Test Window Start", description: "Start date of the test window." },
      { slug: "date_end", type: "DATETIME", caption: "Test Window End", description: "End date of the test window." },
      { slug: "window_code", type: "STRING", caption: "Window Code", description: "Unique code identifying the test window." },
      { slug: "patches", type: "STRING", caption: "Patch Content", description: "JSON string describing all of the fields modified." }
    
    ],    
    methodConfig: {
      querySlug: "SQL_03B_AUTH_MONIT_FORM_ALLOCATIONS"
    },
    dependencySourcings: [
      {
        param: "test_window_ids",
        type: "asset-col",
        config: {
          asset: "load_tw_active",
          col: "tw_id"
        }
      },
      {
        param: "days_back",
        type: "job-config",
        config: { 
          configSlug: "pipeline_config", 
          param: "days_back" 
        }
      }
    ]
  },
  {
    slug: "load_recently_allocated",
    caption: "Recently Allocated Forms (possible partial duplicate of load_form_allocations)",
    description: "Allocation records from the past X days for test window TWTARs.",
    structure: "dataframe",
    method: "query",
    methodConfig: {
      querySlug: "SQL_03B_AUTH_MONIT_RECENTLY_ALLOCATED"
    },
    schema: [
      { slug: "test_window_id", type: "INTEGER_UNSIGNED", caption: "Test Window ID" },
      { slug: "title_en", type: "STRING", caption: "Window Title (en)" },
      { slug: "type_slug", type: "STRING", caption: "Type Slug" },
      { slug: "long_name", type: "STRING", caption: "Long Name" },
      { slug: "allocated_on", type: "DATETIME", caption: "Allocated On" },
      { slug: "allocated_by", type: "STRING", caption: "Allocated By (Email)" },
      { slug: "signoff_slug", type: "STRING", caption: "Signoff Slug" },
      { slug: "comment", type: "STRING", caption: "Signoff Comment" }
    ],
    dependencySourcings: [
      {
        param: "test_window_ids",
        type: "asset-col",
        config: {
          asset: "load_tw_active",
          col: "tw_id"
        }
      },
      {
        param: "days_back",
        type: "job-config",
        config: {
          configSlug: "pipeline_config",
          param: "days_back"
        }
      }
    ]
  },
  {
    slug: "load_recently_ready_for_struct_reviews",
    caption: "Recently Ready for Structural Reviews",
    description: "Records for test windows that have had a certain signoff in the last X days but not the final signoff.",
    structure: "dataframe",
    method: "query",
    methodConfig: {
      querySlug: "SQL_03B_AUTH_MONIT_RECENTLY_READY_FOR_STRUCT_REVIEWS"
    },
    schema: [
      { slug: "test_window_id", type: "INTEGER_UNSIGNED", caption: "Test Window ID" },
      { slug: "title_en", type: "STRING", caption: "Window Title (en)" },
      { slug: "type_slug", type: "STRING", caption: "Type Slug" },
      { slug: "is_active", type: "BOOL_INT", caption: "Is Active" },
      { slug: "signoff_ministry", type: "STRING", caption: "Ministry Signoff Comment" },
      { slug: "signoff_vretta", type: "STRING", caption: "Vretta Signoff Comment" }
    ],
    dependencySourcings: [
      {
        param: "test_window_ids",
        type: "asset-col",
        config: {
          asset: "load_tw_active",
          col: "tw_id"
        }
      },
      {
        param: "days_back",
        type: "job-config",
        config: {
          configSlug: "pipeline_config",
          param: "days_back"
        }
      }
    ]
  },
  {
    "slug": "trfm_tw_ta_forms_superceded_past__ts_ids",
    "caption": "Test Session IDs (Past Superceded Forms)",
    "description": "Distinct list of ts_id from trfm_tw_ta_forms_superceded_past",
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "restrict-cols",
          "df_output": "only_ts_id",
          "config": {
            "df_input": "past_forms",
            "cols": ["ts_id"]
          }
        },
        {
          "kind": "drop-duplicates",
          "df_output": "only_ts_id",
          "config": {
            "df_input": "only_ts_id",
            "subset": ["ts_id"]
          }
        }
      ],
      "output": "only_ts_id"
    },
    "dependencySourcings": [
      {
        "param": "past_forms",
        "type": "dataframe",
        "config": {
          "asset": "trfm_tw_ta_forms_superceded_past"
        }
      }
    ]
  },
  {
    "slug": "trfm_tw_ta_forms_superceded_upcoming__ts_ids",
    "caption": "Test Session IDs (Upcoming Superceded Forms)",
    "description": "Distinct list of ts_id from trfm_tw_ta_forms_superceded_upcoming",
    "structure": "dataframe",
    "method": "transforms",
    "methodConfig": {
      "sequence": [
        {
          "kind": "restrict-cols",
          "df_output": "only_ts_id",
          "config": {
            "df_input": "upcoming_forms",
            "cols": ["ts_id"]
          }
        },
        {
          "kind": "drop-duplicates",
          "df_output": "only_ts_id",
          "config": {
            "df_input": "only_ts_id",
            "subset": ["ts_id"]
          }
        }
      ],
      "output": "only_ts_id"
    },
    "dependencySourcings": [
      {
        "param": "upcoming_forms",
        "type": "dataframe",
        "config": {
          "asset": "trfm_tw_ta_forms_superceded_upcoming"
        }
      }
    ]
  },
  {
    slug: "load_sessions_superceded_forms",
    caption: "Superceded Form Summary (upcoming, by Session)",
    isCheckAsset: true,
    description: "Pulls school class sessions that are linked to revoked or outdated forms.",
    structure: "dataframe",
    schema: [
      { slug: "test_window_id", type: "INTEGER_UNSIGNED", caption: "Test Window ID" },
      { slug: "session_id", type: "INTEGER_UNSIGNED", caption: "Test Session ID" },
      { slug: "asmt_code", type: "STRING", caption: "Assessment Code" },
      { slug: "test_design_id", type: "INTEGER_UNSIGNED", caption: "Locked Test Design ID" },
      { slug: "test_design_id_new", type: "INTEGER_UNSIGNED", caption: "Allocated Test Design ID" },
      { slug: "class_name", type: "STRING", caption: "Class Name" },
      { slug: "ts_date_time_start", type: "DATETIME", caption: "Test Session Start" },
      { slug: "school_class_id", type: "INTEGER_UNSIGNED", caption: "School Class ID" },
      { slug: "sccf_created_on", type: "DATETIME", caption: "Class Form Locked On" },
      { slug: "sa_name", type: "STRING", caption: "School Authority Name" },
      { slug: "sa_code", type: "STRING", caption: "School Authority Code" },
      { slug: "s_name", type: "STRING", caption: "School Name" },
      { slug: "s_code", type: "STRING", caption: "School Code" },
        ],
    method: "query",
    methodConfig: {
      querySlug: "SQL_05A_SESSIONS_SUPERCEDED_FORMS"
    },
    dependencySourcings: [
      {
        param: "test_window_ids",
        type: "asset-col",
        config: {
          asset: "load_tw_active",
          col: "tw_id"
        }
      },
      {
        param: "exclude_ts_ids",
        type: "asset-col",
        config: {
          asset: "trfm_tw_ta_forms_superceded_past__ts_ids",
          col: "ts_id"
        }
      },
      {
        param: "include_ts_ids",
        type: "asset-col",
        config: {
          asset: "trfm_tw_ta_forms_superceded_upcoming__ts_ids",
          col: "ts_id"
        }
      }
    ]
  },
  {
    slug: "load_sessions_superceded_forms_to_attempts",
    caption: "Attempt to Assessment Code Remap Gap Check",
    isCheckAsset: true,
    description: "Checks for mislaingment between class form lock and attempt form allocation",
    structure: "dataframe",
    schema: [
      { slug: "test_window_id", type: "INTEGER_UNSIGNED", caption: "Test Window ID" },
      { slug: "session_id", type: "INTEGER_UNSIGNED", caption: "Test Session ID" },
      { slug: "sccf_id", type: "INTEGER_UNSIGNED", caption: "Class Form ID" },
      { slug: "school_class_id", type: "INTEGER_UNSIGNED", caption: "School Class ID" },
      { slug: "asmt_code", type: "STRING", caption: "Assessment Code" },
      { slug: "test_design_id_ta", type: "INTEGER_UNSIGNED", caption: "Attempt Test Design ID" },
      { slug: "test_design_id_sccf", type: "INTEGER_UNSIGNED", caption: "Class Form Test Design ID" },
      { slug: "test_design_id_new", type: "INTEGER_UNSIGNED", caption: "Allocated Test Design ID" },
      { slug: "n_attempts", type: "INTEGER_UNSIGNED", caption: "Number of Attempts" },
      { slug: "form_match_status", type: "STRING", caption: "Form Match Status" },
        ],
    method: "query",
    methodConfig: {
      querySlug: "SQL_05A_SESSIONS_SUPERCEDED_FORMS_TO_ATTEMPTS"
    },
    dependencySourcings: [
      {
        param: "ts_ids",
        type: "asset-col",
        config: {
          asset: "load_sessions_superceded_forms",
          col: "session_id"
        }
      },
    ]
  },

  {
    "slug": "load_db_school_administrators",
    "caption": "School Administrators",
    "description": "Loads all school administrators for the selected schools.",
    "structure": "dataframe",
    "method": "query-chunked",
    "methodConfig": {
      "querySlug": "SQL_04B_SCHOOL_ADMINISTRATORS",
      "chunkedParam": "s_gids",
      "chunkSize": 200
    },
    "schema": [
      {"slug": "uid", "type": "INTEGER_UNSIGNED", "caption": "User ID", "description": "Identifier for the administrator user."},
      {"slug": "s_gid", "type": "INTEGER_UNSIGNED", "caption": "School Group ID", "description": "Identifier for the school group the administrator is assigned to.", "fk_table_slug": "load_db_schools", "fk_field_slug": "s_gid"},
      {"slug": "email", "type": "STRING", "caption": "Email", "description": "Email address of the administrator."},
      {"slug": "first_name", "type": "STRING", "caption": "First Name", "description": "First name of the administrator."},
      {"slug": "last_name", "type": "STRING", "caption": "Last Name", "description": "Last name of the administrator."}
    ],
    "dependencySourcings": [
      {
        "param": "s_gids",
        "type": "asset-col",
        "config": {
          "asset": "load_db_schools",
          "col": "s_gid"
        }
      }
    ]
  },
  

  // next

]

const placeholder = [
  {
    "slug": "TODO",
    "caption": "",
    "description": "Placeholder for any dependencies still TODO",
    "structure": "dataframe",
    "method": "placeholder",
    "methodConfig": {
    },
    "dependencySourcings": [
      {
        "param": "TODO",
        "type": "dataframe",
        "config": {
          "asset": "TODO",
        },
      },
    ]
  },
]
