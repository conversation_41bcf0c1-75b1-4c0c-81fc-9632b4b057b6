import { Errors } from '../../../../../errors/general';
import { storeInS3, pullFromS3, AWS_CONFIG, STORAGE_BUCKET } from '../../../../upload/upload.listener';
import { DagAsset, DependencySourcingType } from '../model/assets';

import { Lambda } from 'aws-sdk';

import type {IRecord} from '../../data-export-queries/types/type';

const PACKAGE_LAMBDA_VERSION = "dag-packager:latest-dev";

export interface IRunState {
    state: any,
    pipeline_config: any,
    storagePath: string,
    dfCache: Map<string, any>,
    partitions: {[key:string]: IPartitonInstance},
    assetRef: Map<string, any>,
  }

export interface IPartitonInstance {
    partKey: string,
    field: string,
    filepath?: string,
    partitions: IPartDescription[]
}

export interface IPartDescription {
    name: string,
    path: string,
    partSlug: string,
    length: number,
    size: number,
}

/**
 * Store an asset in S3
 */
export const storeAsset = async (runState: IRunState, slug: string, data: string) => {
    // TODO: handle saving in dfCache in this function instead of separately
    const filepath = runState.storagePath + slug + '.json'
    await storeInS3(filepath, data);
    return filepath; // STORAGE_BUCKET_PREFIX + filepath
};

/**
 * Get an asset from cache with fallback to S3
 */
export const retrieveAsset = async (runState: IRunState, slug: string) => {
    let assetData = runState.dfCache.get(slug);
    if (!assetData) {
        console.log("pulling asset from S3 for column")
        const filepath = runState.storagePath + slug + '.json'
        try {
            const data = <Buffer>await pullFromS3(filepath);
            assetData = JSON.parse(data.toString());
        } catch (err) {
            throw new Error(`Unable to retrieve asset from S3: ${filepath}`);
        }
    }
    return assetData;
};

/**
*/
export function launchPackager(exportId: number) {
  const lambda = new Lambda({...AWS_CONFIG});
  const payload = {
    // TODO: remove hardcoding of api
    callbackDomain: "https://abed-uat-api.vretta.com",
    s3Bucket: STORAGE_BUCKET,
    exportId,
  };

  return new Promise((resolve, reject) => {
    lambda.invoke({
      FunctionName: PACKAGE_LAMBDA_VERSION,
      Payload: JSON.stringify(payload),
      InvocationType: 'Event',
    }, (err, data) => {
      if (err) {
        reject(err);
      }
      else {
        resolve(data);
      }
    });

  });
}

function assetFields(assetRef: Map<string, any>) {
  const assetFields = [];
  for (let asset of assetRef.values()) {
    const schema = asset.schema
    if (!schema) {
      assetFields.push({asset_slug: asset.slug, slug: "MISSING_FIELD_DEFINITIONS"})
      continue
    }
    for (let fieldDef of asset.schema) {
      assetFields.push({asset_slug: asset.slug, ...fieldDef});
    }
  }
  return assetFields

}

export const retrieveSourcing = async (runState: IRunState, sourcingDef: any) => {
    // TODO: handle additional properties of dependencySourcings
    // TODO: concatenate partitioned asset if it's required
    const { pipeline_config } = runState;
    let result;
    const dtype = sourcingDef.type;
    switch (dtype) {
        case DependencySourcingType.JOB_CONFIG:
            if (sourcingDef.config.configSlug == "assets") {
              if (sourcingDef.config.param == "slug") {
                result = [... runState.assetRef.keys()];
              }
              else if (sourcingDef.config.param == "fields") {
                result = assetFields(runState.assetRef);
              }
              else { // all of the assets
                result = [... runState.assetRef.values()];
              }
            }
            else {
                result = pipeline_config[sourcingDef.config.param || '']
            }
            break;
        case DependencySourcingType.ASSET_COL:
            {
                const { asset, col } = sourcingDef.config;
                let assetData = await retrieveAsset(runState, asset);
                result = assetData.map((r: IRecord) => r[col]);
            }
            break;
        case DependencySourcingType.DATAFRAME:
            {
                const { asset } = sourcingDef.config;
                let assetData = await retrieveAsset(runState, asset);
                result = assetData;
            }
            break;
        default:
            console.log(`Unrecognized sourcing type: ${dtype}`, sourcingDef);
            throw new Errors.Unprocessable(`Unrecognized sourcing type: ${dtype}`);
    }
    return result;
};

export async function retrievePartSourcing(runState: IRunState, sourcingDef: any, partKey: string, partSlug: string) {
    // TODO: handle additional properties of dependencySourcings
    const { pipeline_config } = runState;
    let result;
    const dtype = sourcingDef.type;
    switch (dtype) {
        case DependencySourcingType.JOB_CONFIG:
            result = pipeline_config[sourcingDef.config.param || '']
            break;
        case DependencySourcingType.ASSET_COL:
            {
                const { asset, col } = sourcingDef.config;
                const sourceAssetDef: DagAsset = runState.assetRef.get(asset);
                const hasPart = sourceAssetDef.partitionOut?.key === partKey || sourceAssetDef.partitionBy?.key === partKey;
                let assetSlug = asset;
                if (hasPart) {
                    assetSlug = `${asset}/${partSlug}`
                }
                let assetData = await retrieveAsset(runState, assetSlug);
                result = assetData.map((r: IRecord) => r[col]);
            }
            break;
        case DependencySourcingType.DATAFRAME:
            {
                const { asset } = sourcingDef.config;
                const sourceAssetDef: DagAsset = runState.assetRef.get(asset);
                const hasPart = sourceAssetDef.partitionOut?.key === partKey || sourceAssetDef.partitionBy?.key === partKey;
                let assetSlug = asset;
                if (hasPart) {
                    assetSlug = `${asset}/${partSlug}`
                }
                let assetData = await retrieveAsset(runState, assetSlug);
                result = assetData;
            }
            break;
        default:
            console.log(`Unrecognized sourcing type: ${dtype}`, sourcingDef);
            throw new Errors.Unprocessable(`Unrecognized sourcing type: ${dtype}`);
    }
    return result;
};

export const sourceDependencies = async (runState: IRunState, asset: DagAsset) => {
    const dependencies: any = {}
    for (let dep of asset.dependencySourcings) {
        dependencies[dep.param] = await retrieveSourcing(runState, dep);
    }

    return dependencies;
}

export async function sourcePartDependencies(runState: IRunState, asset: DagAsset, partKey: string, partSlug: string) {
    const dependencies: any = {}
    for (let dep of asset.dependencySourcings) {
        dependencies[dep.param] = await retrievePartSourcing(runState, dep, partKey, partSlug);
    }

    return dependencies;
}

export const partitionOut = async (runState: IRunState, asset: DagAsset, data: any[]) => {
    const { key, field, partSize, categorical } = asset.partitionOut;

    if (!partSize && !categorical) { // Must be either categorical or have a partition size
        throw new Error(`asset-def :: ${asset.slug} partitionOut missing either partSize or categorical`);
    } else if (!partSize === !categorical) { // cannot have both categorical and partition size
        throw new Error(`asset-def :: ${asset.slug} cannot be both categorical and have partSize`);
    }

    let parts: Map<string, any>;

    // Create the partitions
    if (partSize) {
        parts = partitionNumeric(key, field, partSize, data);
    } else if (categorical) {
        parts = partitionCategory(key, field, data);
    } else {
        throw new Error(`asset :: ${asset.slug} :: assertion failed both partSize and categorical falsey`)
    }

    // Store the partitions
    return storePartition(runState, asset, key, field, parts);
}

export async function repartitionOut(runState: IRunState, asset: DagAsset) {
    const { key, field, partSize, categorical } = asset.partitionOut;

    if (!partSize && !categorical) { // Must be either categorical or have a partition size
        throw new Error(`asset-def :: ${asset.slug} partitionOut missing either partSize or categorical`);
    } else if (!partSize === !categorical) { // cannot have both categorical and partition size
        throw new Error(`asset-def :: ${asset.slug} cannot be both categorical and have partSize`);
    }

    let updatePartition;
    if (partSize) {
        updatePartition = (inPartData: any[], partOuts: Map<string, any[]>) => {
            return partitionNumeric(key, field, partSize, inPartData, partOuts)
        }
    } else if (categorical) {
        updatePartition = (inPartData: any[], partOuts: Map<string, any[]>) => {
            return partitionCategory(key, field, inPartData, partOuts)
        }
    } else {
        throw new Error(`asset :: ${asset.slug} :: assertion failed both partSize and categorical falsey`)
    }

    const inPartKey = asset.partitionBy?.key;
    if (!inPartKey) {
        throw new Error(`impossible pathway :: ${asset.slug} :: trying to re-partition an unpartitioned asset`)
    }
    const inPartInfo = runState.partitions[inPartKey];

    let partOuts : Map<string, any> = new Map();
    for (let inPart of inPartInfo.partitions) {
        let partSlug = `${asset.slug}/${inPart.name}`
        const inPartData = await retrieveAsset(runState, partSlug)
        partOuts = updatePartition(inPartData, partOuts);
    }

    return storePartition(runState, asset, key, field, partOuts);
}

export async function storePartition(runState: IRunState, asset: DagAsset, key: string, field: string, parts: Map<string, any>) {
    const assetSlug = asset.slug;
    const partitionInfo: IPartitonInstance = {partKey: key, field: field, partitions: []};
    for (const [partName, part] of parts) {
        const partSlug = `${assetSlug}/${partName}`
        const jsonData = JSON.stringify(part);
        const path = await storeAsset(runState, partSlug, jsonData);
        partitionInfo.partitions.push({name: partName, path: path, partSlug, length: part.length, size: jsonData.length})
    }
    const partInfoPath = await storeAsset(runState, `${assetSlug}/${key}`, JSON.stringify(partitionInfo))
    return {
        partInfoPath,
        partitionInfo,
    }
}

export function partitionNumeric(key: string, field: string, partSize: number, data: any[], parts?: Map<string, any>) {
    if (!parts) {
        parts = new Map();
    }
    for (const record of data) {
        const partIdx = Math.floor( record[field] / partSize) * partSize;
        const partName = `${key}-${partIdx}`;
        const part = parts.get(partName) || [];
        part.push(record);
        parts.set(partName, part);
    }
  return parts;
}

export function partitionCategory(key: string, field: string, data: any[], parts?: Map<string, any>) {
    if (!parts) {
        parts = new Map();
    }
    for (const record of data) {
        const partIdx = record[field];
        const partName = `${key}-${partIdx}`;
        const part = parts.get(partName) || [];
        part.push(record);
        parts.set(partName, part);
    }
  return parts;
}

export async function concatPartitionedAsset(runState: IRunState, asset: DagAsset) {
  const partKey = asset.partitionBy?.key;
  if (!partKey) {
    throw new Error("Cannot concatenate asset without partition");
  }
  const partInfo = runState.partitions[partKey];
  if (!partInfo) {
    throw new Error(`specified partition ${partKey} does not exist`);
  }
  let records: any[] = [];
  for (let part of partInfo.partitions) {
    const partName = part.name;
    const partSlug = `${asset.slug}/${partName}`;
    const partRecords = await retrieveAsset(runState, partSlug);
    records = records.concat(partRecords);
  }
  const recordsJson = JSON.stringify(records);
  return {
    filepath: await storeAsset(runState, asset.slug, recordsJson),
    size: recordsJson.length,
    length: records.length,
  }
}
