import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawReadReporting } from '../../../../util/db-raw';
import { TEMP_DAG_ASSETS, DagAsset } from '../data-export/model/assets';
import { TEMP_DAG_JOBS, DagJobDef } from '../data-export/model/jobs';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';

interface Data {}

interface ServiceOptions {}

interface DagState {
  isComplete: boolean
  filepath?: string
  partitions?: {
    [key: string]: {
      partKey: string,
      filepath: string,
      field: string,
      partitions: IPartInfo[]
    }
  }
}

interface IPartInfo {
  name: string,
  path: string,
  // partSlug: string,
  // length: number,
  // size: number,
}

export class DataJobContents implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.BadRequest();
  }

  /** Returns the list of contents of a data export job
    *
    * Response payload
    * {
    *    outputAssets : assetState[]
    *    intermediateAssets : assetState[]
    *    checkAssets : assetState[]
    *    packageUrl? : string
    * }
    *
    * where assetState is
    * {
    *   slug : assetSlug,
    *   caption : assetDef?.caption,
    *   description : assetDef?.description,
    *   isRunning : boolean
    *   isComplete : boolean,
    *   progress : number (0-1),
    *   hasFile : boolean (true if there is a file containing the whole output)
    *   hasPartitions : boolean (true if there are partitions)
    *   partitions : list of all the partitions with their info
    * }
    *
    * In the UI, can maybe add a button to create the download link?
    */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    // TODO: the dag_config should include the job definition, not just the asset sequence
    const exportId = id;
    const eRecords = await dbRawReadReporting(this.app, {exportId},
      'select dej.dag_job_name, dej.dag_config, dej.dag_state, dej.dag_package_url from data_export_jobs dej where dej.id = :exportId');
    if (eRecords.length != 1) {
      throw new Errors.BadRequest('Unrecognized exportId');
    }

    const dagJobName =  <string> eRecords[0].dag_job_name;
    const dagConfig =  <{[key:string]: any}> JSON.parse(eRecords[0].dag_config);
    const dagState =  <{[key:string]: any}> JSON.parse(eRecords[0].dag_state);
    const packageUrl = <string> eRecords[0].dag_package_url;

    const expireSeconds = 60; // This is the default value, but explicit here if quick change needed

    let signedPackageUrl: string | undefined = undefined;
    if (packageUrl) {
        signedPackageUrl = generateS3DownloadUrl(packageUrl.replace('s3://storage.mathproficiencytest.ca/',''), expireSeconds)
    }

    if (!dagConfig){
      return {
        isEmpty: true,
        outputAssets: [],
        intermediateAssets: [],
        checkAssets: [],
        packageUrl: signedPackageUrl,
      }
    }

    // TODO: this will need to be pulled from the config after that is updated
    let jobDef: DagJobDef = TEMP_DAG_JOBS[dagJobName];
    if (dagConfig.v >= 2) {
      jobDef = dagConfig.jobDef;
    } else {
      jobDef = TEMP_DAG_JOBS[dagJobName];
    }
    let sequence: DagAsset[] = dagConfig.sequence;
    // TODO: when versioning of assets is complete this will likely change
    const assetDefMap = new Map(sequence.map( (assetDef: any) => {
      return [
        assetDef.slug,
        assetDef
      ];
    }));


    // TODO: check assets will need to distinguish between warning and error
    // TODO: Final summary in dag job (dagState.state): number of rows in each asset

    const allAssets = [];
    for (let assetSlug of Object.keys(dagState.state)) {
      allAssets.push( {
        ...cleanAssetState(assetSlug, dagState.state[assetSlug], assetDefMap),
      });
    }
    // Categorize the assets
    const outputAssets = [];
    const interAssets = [];
    const checkAssets = [];
    for (const asset of allAssets) {
      const assetDef = assetDefMap.get(asset.slug);
      if (assetDef && assetDef.isCheckAsset) {
        checkAssets.push(asset);
      } else if (jobDef.assets.includes(asset.slug)) {
        outputAssets.push(asset);
      } else {
        interAssets.push(asset);
      }
    }

    return {
      outputAssets,
      intermediateAssets: interAssets,
      checkAssets,
      packageUrl: signedPackageUrl,
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}

function cleanAssetState(assetSlug: any, assetState: any, assetDefMap: Map<string, any>) {
  const assetDef = assetDefMap.get(assetSlug);
  return {
    slug: assetSlug,
    caption: assetDef?.caption,
    description: assetDef?.description,
    isRunning: assetState.isStarted && !assetState.isComplete,
    isComplete: assetState.isComplete,
    progress: assetState.progress,
    timeMs: assetState.timeMs,
    numRecords: assetState.numRecords,
    hasFile: !!assetState.filepath,
    hasPartitions: !!assetState.partitions,
    partitions: cleanPartitions(assetState.partitions),
  }
}

function cleanPartitions(partitions: {[key: string]: any} | undefined) {
  if (!partitions) {
    return []
  }
  const cleaned = []
  for (let partKey in partitions) {
    cleaned.push({
      partKey,
      field: partitions[partKey].field,
      parts: partitions[partKey].partitions.map( (part: any) => {
        return {
          name: part.name,
          path: part.path,
          length: part.length,
        }
      })
    })
  }
  return cleaned;
}
