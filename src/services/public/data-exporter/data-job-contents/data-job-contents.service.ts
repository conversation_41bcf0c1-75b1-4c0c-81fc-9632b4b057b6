// Initializes the `public/data-exporter/data-job-contents` service on path `/public/data-exporter/data-job-contents`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { DataJobContents } from './data-job-contents.class';
import hooks from './data-job-contents.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/data-exporter/data-job-contents': DataJobContents & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/data-exporter/data-job-contents', new DataJobContents(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/data-exporter/data-job-contents');

  service.hooks(hooks);
}
