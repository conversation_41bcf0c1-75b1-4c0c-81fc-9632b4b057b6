// Initializes the `public/data-exporter/data-job-defs` service on path `/public/data-exporter/data-job-defs`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { DataJobDefs } from './data-job-defs.class';
import hooks from './data-job-defs.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/data-exporter/data-job-defs': DataJobDefs & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/data-exporter/data-job-defs', new DataJobDefs(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/data-exporter/data-job-defs');

  service.hooks(hooks);
}
