// Initializes the `public/dist-admin/data-exporter/pk-extraction` service on path `/public/dist-admin/data-exporter/pk-extraction`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { PkExtraction } from './pk-extraction.class';
import hooks from './pk-extraction.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/dist-admin/data-exporter/pk-extraction': PkExtraction & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/dist-admin/data-exporter/pk-extraction', new PkExtraction(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/dist-admin/data-exporter/pk-extraction');

  service.hooks(hooks);
}
