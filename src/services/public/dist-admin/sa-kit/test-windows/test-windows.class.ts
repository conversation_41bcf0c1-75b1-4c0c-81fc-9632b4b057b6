import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawReadReporting } from '../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class TestWindows implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    
    const schl_dist_group_id = params?.query?.schl_dist_group_id;
    if (!schl_dist_group_id){
      throw new Errors.BadRequest()
    }

    const records = await dbRawReadReporting(this.app, {}, `
      select id
           , title
           , academic_year
           , date_start
           , date_end 
           , type_slug 
           , test_ctrl_group_id
      from test_windows tw 
      where tw.is_sa_signoff_required = 1
        and is_qa  = 0 
        and is_active  = 1
      order by tw.date_end desc 
    `)

    for (let record of records){
      try {
        record.title_str = JSON.parse(record.title)['en'] // todo: take language from client or push this down to web client
      }
      catch(e){}
    }

    return records
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
