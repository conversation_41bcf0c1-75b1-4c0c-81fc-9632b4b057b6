// Initializes the `public/dist-admin/sa-kit/test-windows` service on path `/public/dist-admin/sa-kit/test-windows`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { TestWindows } from './test-windows.class';
import hooks from './test-windows.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/dist-admin/sa-kit/test-windows': TestWindows & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/dist-admin/sa-kit/test-windows', new TestWindows(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/dist-admin/sa-kit/test-windows');

  service.hooks(hooks);
}
