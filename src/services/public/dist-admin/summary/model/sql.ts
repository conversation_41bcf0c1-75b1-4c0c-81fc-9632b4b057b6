export const SD_DASHBOARD_ALLOWED_ROLES = [
    'schl_dist_admin',
    'schl_disct_curr_ele',
    'schl_disct_curr_sec',
    'school_district_curr'
]

export const SQL_SD_DASHBOARD = ` /* SQL_SD_DASHBOARD */
    select sd.id
         , sd.group_id
         , sd.foreign_id
         , sd.name
         , sd.brd_lang
         , sd.lockdown_file_kiosk_instr
         , sd.lockdown_file_kiosk
         , sd.lockdown_file_seb_instr
         , sd.lockdown_file_seb
    from school_districts sd
    join user_roles ur
        on ur.group_id = sd.group_id
        and ur.role_type in (:role_types)
        and ur.uid = :uid
        and ur.is_revoked = 0
    where sd.group_id = :sd_group_id
`