import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';
import { boolAsNum, numAsBool } from '../../../../util/param-sanitization';
import { Errors } from '../../../../errors/general';

interface Data {}

interface ServiceOptions {}

export class Checklist implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if (params){
      const uid = await currentUid(this.app, params);
      const group_id = id;
      const checklistRecords = <Paginated<any>> await this.app
        .service('db/read/user-group-checklist')
        .find({
          query: {
            $limit:1000,
            uid,
            group_id,
            is_revoked: 0
          }
        })
      return checklistRecords.data.map(record => {
        return {
          slug: record.slug,
          value: numAsBool(record.value)
        }
      });
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: any, params?: Params): Promise<Data> {
    if (params && data){
      const uid = await currentUid(this.app, params);
      const group_id = id;
      const slug = data.slug;
      const value =   (data.value);
      const existingRecords = <Paginated<any>> await this.app
        .service('db/read/user-group-checklist')
        .find({
          query: { 
            slug,
            uid,
            group_id,
            is_revoked: 0
          }
        });
      if (existingRecords.total > 0){
        const existingRecord = existingRecords.data[0]
        return this.app
          .service('db/write/user-group-checklist')
          .patch(existingRecord.id, {value});
      }
      return this.app
        .service('db/write/user-group-checklist')
        .create({uid, group_id, slug, value}); 
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
