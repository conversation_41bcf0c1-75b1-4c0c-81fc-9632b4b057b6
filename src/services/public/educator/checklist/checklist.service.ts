// Initializes the `public/educator/checklist` service on path `/public/educator/checklist`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Checklist } from './checklist.class';
import hooks from './checklist.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/checklist': Checklist & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/checklist', new Checklist(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/checklist');

  service.hooks(hooks);
}
