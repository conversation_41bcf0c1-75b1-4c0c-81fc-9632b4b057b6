// Initializes the `public/educator/class-archive` service on path `/public/educator/class-archive`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ClassArchive } from './class-archive.class';
import hooks from './class-archive.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/class-archive': ClassArchive & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/class-archive', new ClassArchive(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/class-archive');

  service.hooks(hooks);
}
