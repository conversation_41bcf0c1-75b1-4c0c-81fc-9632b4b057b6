// Initializes the `public/educator/class-assessments` service on path `/public/educator/class-assessments`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ClassAssessments } from './class-assessments.class';
import hooks from './class-assessments.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/class-assessments': ClassAssessments & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/class-assessments', new ClassAssessments(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/class-assessments');

  service.hooks(hooks);
}

