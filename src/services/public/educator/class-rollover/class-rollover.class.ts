import { STUDENT_ROLE_TYPES } from './../walk-in-students/walk-in-students.class';
import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ISchoolClassRef {
  group_id : number,
  schl_group_id : number,
  schl_dist_group_id : number,
  name: string,
  is_grouping : number,
  group_type : string,
  course_type : string,
  key: string,
  semester_id : number,
  cloned_sc_id: number,
  samename_sc_id: number,
}

interface ServiceOptions {}

export class ClassRollover implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {

    const {cloneScId} = <any> data;
    
    if (cloneScId && params){

      const created_by_uid = await currentUid(this.app, params);

      const classroomInfo:ISchoolClassRef = await dbRawReadSingle(this.app, {cloneScId}, `
        select sc.schl_group_id 
            , sc.schl_dist_group_id 
            , sc.group_id
            , sc.name
            , sc.is_grouping 
            , sc.group_type 
            , tw.type_slug course_type
            , sc.key
            , ss_next.id semester_id 
            , max(sc_cloned.id) cloned_sc_id
            , sc_samename.id  samename_sc_id
        from school_classes sc 
        join school_semesters ss 
          on ss.id = sc.semester_id 
        join test_windows tw 
          on tw.id = ss.test_window_id 
        join school_semesters ss_next 
          on ss_next.test_window_id = tw.next_tw_id 
        left join school_classes sc_samename
          on sc_samename.semester_id = ss_next.id 
          and sc_samename.name = sc.name 
          and sc.is_active = 1
        left join school_classes sc_cloned
          on sc_cloned.clone_src_sc_id = sc.id 
          and sc_cloned.is_active = 1
        where sc.id = :cloneScId
      `);

      if (classroomInfo.cloned_sc_id){
        throw new Errors.BadRequest('CLONED_ALREADY')
      }
      if (classroomInfo.samename_sc_id){
        throw new Errors.BadRequest('SC_SAME_NAME_NEW_TW')
      }

      const classRecord:{id:number, group_id:number} = await this.app.service('public/school-admin/classes').createClassAndAssignTeacher({
        schl_group_id: classroomInfo.schl_group_id,
        schl_dist_group_id: classroomInfo.schl_dist_group_id, 
        foreign_id: '',
        semester_id: classroomInfo.semester_id,
        group_type: classroomInfo.course_type,
        is_grouping: classroomInfo.is_grouping,
        name: classroomInfo.name,
        educator_id: created_by_uid,
        is_fi: 0,
        key:  classroomInfo.key
      })
      if (classRecord.id == -1 || !classRecord.id){
        throw new Errors.BadRequest('COULD_NOT_CLONE')
      }
      // track it as a clone 
      await this.app.service('db/write/school-classes').patch(classRecord.id, {clone_src_sc_id: cloneScId})
      
      // grant new role to prev students
      const roleRecords:{uid:number, role_type:string}[] = await dbRawRead(this.app, 
      {sc_group_id: classroomInfo.group_id, walkin_role_type: STUDENT_ROLE_TYPES.walk_in_student_role}, `
        select ur.id, ur.role_type, ur.uid, ur.expires_on 
        from user_roles ur 
        where ur.group_id = :sc_group_id
        and ur.is_revoked = 0
        and ur.role_type != :walkin_role_type
      `)
      for (let roleRecord of roleRecords){
        await this.app.service('auth/user-role-actions').assignUserRoleToGroup({
          uid: roleRecord.uid, 
          group_id: classRecord.group_id, 
          role_type: roleRecord.role_type, 
          created_by_uid,
          isCheckForExisting: (created_by_uid == roleRecord.uid)
        });
      }
      

    }


    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
