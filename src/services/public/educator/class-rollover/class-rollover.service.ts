// Initializes the `public/educator/class-rollover` service on path `/public/educator/class-rollover`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ClassRollover } from './class-rollover.class';
import hooks from './class-rollover.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/class-rollover': ClassRollover & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/class-rollover', new ClassRollover(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/class-rollover');

  service.hooks(hooks);
}
