export const SQL_STU_TEST_FORMS = `
-- SQL_STU_TEST_FORMS Benchmark 0.096s
SELECT tf.id AS tf_id,
    tf.file_path,
    ta.uid,
    ta.twtdar_order,
    ta.id as test_attempt_id
    FROM test_forms tf
    JOIN test_attempts ta ON ta.test_form_id = tf.id
    JOIN test_sessions ts ON ts.id = ta.test_session_id
    JOIN school_class_test_sessions scts ON scts.test_session_id = ts.id
    WHERE scts.test_session_id = :test_session_id
    AND ta.is_invalid != 1
`

export const SQL_TEST_ATTMPT_SUB_SESS_RECS = `
SELECT tf.id as tfId 
    , tf.file_path as testFormPath
    , ta.id as test_attempt_id
    , tsss.slug as tsss_slug
    , tsss.caption as tsss_caption
    , ta.uid
    , sc.group_type
FROM test_attempt_sub_sessions tass
JOIN test_attempts ta 
    ON ta.id = tass.test_attempt_id
JOIN test_forms tf 
    ON tf.id = ta.test_form_id
JOIN test_session_sub_sessions tsss 
    ON tsss.id = tass.sub_session_id
JOIN test_sessions ts
    ON ts.id = ta.test_session_id
JOIN school_class_test_sessions scts
    ON scts.test_session_id = ts.id
JOIN school_classes sc
    ON sc.id = scts.school_class_id
WHERE tass.id = ?
;`;

export const SQL_STU_SUB_SESSIONS_SCAN = `
-- SQL_STU_SUB_SESSIONS_SCAN Benchmark 0.033s
    SELECT tass.id as tass_id
        , ta.uid
        , tass.sections_allowed
        , tsss.id as tsss_id
        , tsss.twtdar_order
        , tsss.slug
        , tsss.caption
        , ta.id as test_attempt_id
        , ta.test_form_id
        , sc.group_type
    FROM test_attempt_sub_sessions tass
    JOIN test_attempts ta 
        ON ta.id = tass.test_attempt_id
    JOIN test_session_sub_sessions tsss 
        ON tsss.id = tass.sub_session_id
    JOIN test_sessions ts 
        ON ts.id = ta.test_session_id
    JOIN school_class_test_sessions scts 
        ON scts.test_session_id = ts.id
    JOIN school_classes sc 
        ON sc.id = scts.school_class_id
    WHERE scts.test_session_id = :test_session_id
    AND ta.is_invalid != 1
    AND tass.is_invalid != 1
;`


export const SQL_TFORM_SCAN_QUES = `
    SELECT tqr.question_id
        , tqr.question_label
        , tqr.scan_slug
    from test_question_register tqr
    join test_designs td on td.id = tqr.test_design_id
    join test_forms tf on td.id = tf.test_design_id
    where tf.id = :tfId
    and tqr.question_id in (:tfQuesIds);
`