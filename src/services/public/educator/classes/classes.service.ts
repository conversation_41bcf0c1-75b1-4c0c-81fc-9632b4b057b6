// Initializes the `public/educator/classes` service on path `/public/educator/classes`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Classes } from './classes.class';
import hooks from './classes.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/classes': Classes & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/classes', new Classes(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/classes');

  service.hooks(hooks);
}
