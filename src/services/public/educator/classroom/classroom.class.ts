import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';
import { Errors } from '../../../../errors/general';
import { DBD_U_GROUP_TYPES } from '../../../../constants/db-extracts';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead } from '../../../../util/db-raw';
import { ADMIN_ROLE_TYPES } from './model/constants';
import { generateAccessCode } from '../../../../util/secret-codes';
import { SQL_CLASSES_IN_SAME_ACTIVE_TW_WINDOW, SQL_CLASSES_IN_SAME_SEMESTER_TW_WINDOW } from '../../school-admin/classes/classes-sql';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';


interface Data {}

interface ServiceOptions {}

export class Classroom implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: {test_window_id: number, className: string, schl_group_id: number, key: string}, params?: Params): Promise<Data> {
    if (!params){
      throw new Errors.BadRequest();
    }
    const {test_window_id, className, schl_group_id, key} = data;
    const uid = await currentUid(this.app, params);
    const schoolLookup = await this.getSchoolByGroupId(schl_group_id);
    if(schoolLookup.length == 0){
      throw new Errors.BadRequest("INVALID_SCHL_GROUP_ID");
    }
    const school = schoolLookup[0];
    const schoolSemesterLookup = await this.getSchoolSemesterByTWId(test_window_id);
    if(schoolSemesterLookup.length == 0){
      throw new Errors.BadRequest("MISSING_SEMESTER");
    }
    const schoolSemester = schoolSemesterLookup[0];
    
    let query = SQL_CLASSES_IN_SAME_SEMESTER_TW_WINDOW
    if (await getSysConstNumeric(this.app, 'ENFORCE_STU_1_TW_CLASS')){
      query = SQL_CLASSES_IN_SAME_ACTIVE_TW_WINDOW
    }
    console.log(schoolSemester)
    const classesInSameSemesterTwWindow = await dbRawRead(
      this.app, 
      {semester_id: schoolSemester.id, name:className, schl_group_id}, 
      query
    );

    if (classesInSameSemesterTwWindow.length > 0) {
      throw new Errors.Forbidden('DUPL_CLASS_CODE');
    }

    const classGroup = await this.app.service('db/write/u-groups').create({
      group_type: DBD_U_GROUP_TYPES.school_class,
      description: className,
      created_by_uid: uid
    });

    const accessCode = await this.generateNewAccessCode();
    
    const classroom = await this.app.service('db/write/school-classes').create({
      schl_group_id: schl_group_id,
      schl_dist_group_id: school.schl_dist_group_id,
      name: className,
      semester_id: schoolSemester.id,
      is_grouping: 1,
      group_type: schoolSemester.type_slug,
      created_by_uid: uid,
      created_on: dbDateNow(this.app),
      access_code: accessCode,
      group_id: classGroup.id,
      is_active: 1,
      is_placeholder: 0,
      key 
    });

    await this.app
    .service('db/write/user-roles')
    .create({
      role_type: ADMIN_ROLE_TYPES.Educator,
      uid: uid,
      group_id: classGroup.id,
      created_by_uid: uid,
      });

    return classroom;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }

  async generateNewAccessCode(){
    let access_code = generateAccessCode(8);
    while((await this.app.service('public/school-admin/classes').validateUniqueClassAccessCode(access_code)).length > 0){
      access_code = generateAccessCode(8);
    }
    return access_code;
  }

  async getSchoolByGroupId(schl_group_id: number){
    return await dbRawRead(this.app, {schl_group_id}, `
      SELECT id, schl_dist_group_id
      FROM mpt_dev.schools s
      WHERE s.group_id = :schl_group_id
    ;`);
  }

  async getSchoolSemesterByTWId(test_window_id: number){
    return await dbRawRead(this.app, {test_window_id}, `
      SELECT ss.id, tw.type_slug
      FROM mpt_dev.school_semesters ss
      JOIN mpt_dev.test_windows tw on tw.id = ss.test_window_id
      WHERE ss.test_window_id = :test_window_id
    ;`);
  }
}
