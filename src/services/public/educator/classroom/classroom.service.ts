// Initializes the `public/educator/classroom` service on path `/public/educator/classroom`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Classroom } from './classroom.class';
import hooks from './classroom.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/classroom': Classroom & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/classroom', new Classroom(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/classroom');

  service.hooks(hooks);
}
