import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class ConfidentialityAgreement implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data> {
    if(!params || !params.query) throw new Errors.BadRequest();
    const {schl_group_id, slug} = params.query;
    if(schl_group_id && slug){
      const uid = await currentUid(this.app, params);
      const userHaveAccepted = await this.app
      .service('db/read/user-group-checklist')
      .db()
      .where('group_id'  , schl_group_id)
      .where('slug'      , slug)
      .where('uid'       , uid)
      .where('value'     , 1)
      .where('is_revoked', 0);
      //if the user has accepted then we return accepted, if not we return false.
      //this controls if the user will see the confidentiality popup or not.
      if(userHaveAccepted.length){
        return {userHaveAccepted: true};
      }else{
        return {userHaveAccepted: false};
      }
    }

    throw new Errors.BadRequest();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // this is only triggered when the user accepts the agreeement, frontend validations eliminates chances of invalid data
  // revocation is not requested for the meantime
  async patch (id: NullableId, data: any, params?: Params): Promise<Data> {
    if(!params || !params.query) throw new Errors.BadRequest();
    const {schl_group_id, slug} = params.query;
    const meta = data.meta;
    if(schl_group_id && slug && meta){
      const uid = await currentUid(this.app, params);
      try {
        await this.app
          .service('db/write/user-group-checklist')
          .create({ group_id: schl_group_id, meta, slug, uid, value: 1 });
      }catch(e: any){
        throw new Errors.BadRequest();
      }
    }
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
