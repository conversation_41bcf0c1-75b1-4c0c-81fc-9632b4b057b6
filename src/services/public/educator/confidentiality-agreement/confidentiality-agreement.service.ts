// Initializes the `public/educator/confidentiality-agreement` service on path `/public/educator/confidentiality-agreement`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ConfidentialityAgreement } from './confidentiality-agreement.class';
import hooks from './confidentiality-agreement.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/confidentiality-agreement': ConfidentialityAgreement & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/confidentiality-agreement', new ConfidentialityAgreement(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/confidentiality-agreement');

  service.hooks(hooks);
}
