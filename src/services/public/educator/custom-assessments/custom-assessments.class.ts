import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawReadReporting, dbRawWrite } from '../../../../util/db-raw';
import { SQL_TD_BY_ITEM_SET_ID, SQL_TWTAR_CUSTOM_BY_TW } from './model/sql';
import { CUSTOM_ASSESSMENT_TWTAR_TYPE_SLUG } from './model/const';

interface Data {}

interface ServiceOptions {}

export class CustomAssessments implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented('USE_NON_PUBLIC_METHODS');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented('USE_NON_PUBLIC_METHODS');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented('USE_NON_PUBLIC_METHODS');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented('USE_NON_PUBLIC_METHODS');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented('USE_NON_PUBLIC_METHODS');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented('USE_NON_PUBLIC_METHODS');
  }

  async ensureCustomTwtarRecord(created_by_uid:number, test_window_id:number, item_set_id:number){
    // ensure that there are published versions
    const tdRecords = await dbRawReadReporting(this.app, {item_set_id}, SQL_TD_BY_ITEM_SET_ID);
    if (!tdRecords.length){
      throw new Errors.BadRequest('NO_PUBLISHED_TEST_DESIGNS_FOUND');
    }
    const testDesign = tdRecords[0];
    const {td_id, item_set_name} = testDesign;
    // Pull twtdar if it exists (by item set ID and by ownership). If not create a record.
    let twtar = null;
    const twtarRecords = await dbRawReadReporting(this.app, {tw_id: test_window_id, item_set_id, created_by_uid}, SQL_TWTAR_CUSTOM_BY_TW);
    if (twtarRecords.length==0){
      twtar = await this.app.service('db/read/test-window-td-alloc-rules').create({
        custom_owner_uid: created_by_uid,
        long_name: item_set_name,
        test_design_id: td_id,
        test_window_id,
        item_set_id,
        is_custom: 1,
        is_revoked: 0,
        is_sample: 1,
        is_secured: 0,
        is_questionnaire: 0,
        is_active: 1,
        lang: 'en', // todo
        type_slug: CUSTOM_ASSESSMENT_TWTAR_TYPE_SLUG,
        slug: CUSTOM_ASSESSMENT_TWTAR_TYPE_SLUG,
      })
      twtar.twtar_id = twtar.id; // for consistency with other twtar records
    }
    else {
      twtar = twtarRecords[0];
    }

    if (twtar.twtar_id && (twtar.test_design_id !== td_id) ){
      // update the test design id
      twtar.twtar_id;
      await this.app.service('db/read/test-window-td-alloc-rules').patch(twtar.twtar_id, {
        test_design_id: td_id,
      });
    }
    return twtar
  }

}
// public/test-auth/test-designs