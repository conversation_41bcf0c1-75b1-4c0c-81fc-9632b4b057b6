// Initializes the `public/educator/custom-assessments` service on path `/public/educator/custom-assessments`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { CustomAssessments } from './custom-assessments.class';
import hooks from './custom-assessments.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/custom-assessments': CustomAssessments & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/custom-assessments', new CustomAssessments(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/custom-assessments');

  service.hooks(hooks);
}
