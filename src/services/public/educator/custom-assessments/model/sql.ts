export const SQL_TWTAR_CUSTOM_BY_TW = ` /* SQL_TWTAR_CUSTOM_BY_TW */
  select twtar.id twtar_id
       , twtar.test_design_id
  from test_window_td_alloc_rules twtar
  where twtar.test_window_id = :tw_id
    and twtar.is_custom = 1
    and twtar.item_set_id = :item_set_id
    and twtar.custom_owner_uid = :created_by_uid
  limit 50
`;

export const SQL_TD_BY_ITEM_SET_ID = ` /* SQL_TF_BY_ITEM_SET_ID */
    select td.id td_id
         , td.created_on 
         , tqs.name item_set_name
    from test_designs td
    join temp_question_set tqs 
        on tqs.id = td.source_item_set_id
    where td.source_item_set_id = :item_set_id
        and td.is_revoked = 0 
    order by td.id desc 
    limit 50
`;
