export const SQL_GET_TWTT_INFO_DIRECT = ` /* SQL_GET_TWTT_INFO_DIRECT*/ 
SELECT twtar.type_slug
     , twtt.caption_short 
FROM test_window_td_alloc_rules twtar 
left JOIN test_window_td_types twtt 
    ON twtt.type_slug = twtar.type_slug  
    AND twtt.test_window_id is null 
WHERE twtar.id = :twtar_id;
`

export const SQL_GET_TWTT_INFO = ` /* SQL_GET_TWTT_INFO Benchmark 0.030 s */ 
SELECT tqs.id item_set_id 
     , twtt.type_slug
     , twtt.caption_short 
FROM temp_question_set tqs 
JOIN test_window_td_alloc_rules twtar 
    ON twtar.type_slug = tqs.twtar_type_slug 
JOIN test_window_td_types twtt 
    ON twtt.type_slug = twtar.type_slug  
    AND twtt.test_window_id is null 
WHERE tqs.id = :id;
`

export const SQL_GET_TWTT_INFO_BACKUP = ` /* SQL_GET_TWTT_INFO_BACKUP*/ 
SELECT tqs.id item_set_id 
     , twtar.type_slug
     , twtt.caption_short 
FROM temp_question_set tqs 
left JOIN test_window_td_alloc_rules twtar 
    ON twtar.item_set_id = tqs.id
left JOIN test_window_td_types twtt 
    ON twtt.type_slug = twtar.type_slug  
    AND twtt.test_window_id is null 
WHERE tqs.id = :id;
`