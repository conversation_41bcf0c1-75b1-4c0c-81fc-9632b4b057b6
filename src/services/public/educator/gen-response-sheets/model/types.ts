import { FLAGS } from "../../../../../util/whiteLabelParser";

export interface ITwttInfo {
    type_slug: string;
    caption_short: string;
}
export interface IAuthPreviewParams {
    customAsset: ICustomAsset,
    taskNumber: string,
    classCode: string,
    schoolMident: string,
    studentName: string,
    asn: string,
    batch_alloc_policy_item_slug: string,
    whitelabelContext: FLAGS,
    accessCode: string,
    lang: string,
    twtarId?: number,
    frameworkId?: number,
}

export interface ICustomAsset {
    type: string;
    url: string;
    scale: number;
    offsetX: number;
    offsetY: number;
}