import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import PDFDocument, { fontSize } from 'pdfkit';
import { Errors } from '../../../../errors/general';
import { OutcomeValues } from '../../school-board/da-reports/da-reports.class';
import fs from 'fs'
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { BLOCK_TYPES, FONT_STYLES, FOOTER_CONTENT, FOOTER_HEADER, ILayoutConfig, ILayoutGraphs, ILayoutNode, ILayoutOverall, ILayoutText, INodeRefConfig, IRepProfileDB, OVERALL_CONTENT, OVERALL_HEADER, SQL_GET_LAYOUT_PROFILE_FROM_TWTDAR } from '../../test-auth/reporting-profiles/layout-profiles/model/types';

const TEMP_STUDENT_GOV_ID_LABEL = 'ASN'
const TEMP_SCHOOL_COD_LABEL = 'School Mident'

interface Data {}

interface ServiceOptions {}

type Student = {
  student_gov_id: string,
  first_name: string,
  last_name: string,
  outcome?: string,
  uid: number,
}

type SummaryReport = {
  data: any;
  date: string;
  students: any[],
  td_id: number,
  ts_ids: number[],
  testDesign: any;
  twtar_slug: string;
}

type PdfDomainInfo = Record<string, {
  score: number | null,
  cutInfo: {
    cut_score: number | null,
    long: string,
    short?: string,
    order: number,
    color: string,
  }[],
  weight: number,
}>

export enum DefaultCategories {
  OVERALL = "Overall",
  PER_DOMAIN = "Per Domain",
}

export interface IStudentReportData {
  firstName: string;
  lastName: string;
  asn: string;
  test_design_name: any;
  from: string;
  to: string | null;
  sessions: {
      month: string;
      domains: PdfDomainInfo;
  }[];
}

export interface IPDFSpecs {
  pageWidth: number;
  pageHeight: number;
  margin: number;
  domainNameWidth: number;
  availableWidth: number;
  monthNameWidth: number;
  sectionX: number;
  scoreX: number;
  moveDownRow: number;
  moveDownSection: number;
}

export class IndividualReport implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create_responseSheets (data: {
    students: Student[],
  }, params?: Params): Promise<Data> {

    const students = data.students;

    if (!students) {
      throw new Errors.BadRequest("MISSING DATA")
    }

    if (!params || !params.query) {
      throw new Errors.BadRequest("MISSING REQUIRED PARAMS")
    }

    const pdfBase64 = await this.generateResponseSheetsPdf(students); 

    return {
      pdfBase64,
    }
  }


  async create (data: {
    students: Student[],
    summary_reports: SummaryReport[],
    twtar_id?: number
  }, params?: Params): Promise<Data> {

    const students = data.students;
    const summaryReports = data.summary_reports;
    const twtar_id = data.twtar_id;


    if (!students) {
      throw new Errors.BadRequest("MISSING DATA")
    }

    if (!params || !params.query) {
      throw new Errors.BadRequest("MISSING REQUIRED PARAMS")
    }

    const { selectedCategory, lang } = params.query;

    let pdfBase64;

    if(twtar_id) {
      try {
        await this.getReportingProfile(twtar_id);
        pdfBase64 = await this.createPdfNew(students, summaryReports, selectedCategory, lang, twtar_id); 
      } catch (err) { // Run the old logic if reporting profile doesn't exist
        pdfBase64 = await this.createPdf(students, summaryReports, selectedCategory, lang); 
      }
    } else {
      pdfBase64 = await this.createPdf(students, summaryReports, selectedCategory, lang); 
    }


    return {
      pdfBase64,
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }

  getFont(fonts: {boldFont: string, normalFont: string}, fontStyle: FONT_STYLES) {
    try {
      if(fontStyle == 'bold') {
        return fonts.boldFont;
      }
  
      return fonts.normalFont;
    } catch (err) {
      throw new Errors.GeneralError('ERR_ISR_MISSING_FONTS')
    }
  }

  getNodeText(txtNodeRefs: INodeRefConfig, slug: string, lang: 'en' | 'fr') {
    return txtNodeRefs?.[slug]?.[lang] ?? 'N/A';
  }

  async getReportingProfile(twtar_id: number): Promise<{layoutConfig: ILayoutConfig, txtNodeRefs: INodeRefConfig}> {
    const reportingProfileRaw: IRepProfileDB = await dbRawReadSingle(this.app, {twtar_id}, SQL_GET_LAYOUT_PROFILE_FROM_TWTDAR);

    if(!reportingProfileRaw) {
      throw new Errors.BadRequest(`MISSING_REPORTING_PROFILE`);
    }

    try {
      const layoutConfig: ILayoutConfig = JSON.parse(reportingProfileRaw.layout_config)
      const txtNodeRefs: INodeRefConfig = JSON.parse(reportingProfileRaw.text_node_ref_config);

      return {layoutConfig, txtNodeRefs}
    } catch(err) {
      throw new Errors.GeneralError('PARSE_REPORTING_PROFILE_ERR', err);
    }
  }

  getPDFSpecs(doc: typeof PDFDocument): IPDFSpecs {
    const pageWidth = doc.page.width;
    const pageHeight = doc.page.height;
    const margin = 50;
    const domainNameWidth = 140;
    const availableWidth = pageWidth - margin * 2 - domainNameWidth;
    const monthNameWidth = 100;
    const sectionX = margin;
    const scoreX = sectionX + domainNameWidth;


    const moveDownRow = 0.5;
    const moveDownSection = 2;

    return {pageWidth, pageHeight, margin, domainNameWidth, availableWidth, monthNameWidth, sectionX, scoreX, moveDownRow, moveDownSection}
  }

  async createPdfNew(students:Student[], summaryReports:SummaryReport[], selectedCategory:string, lang: 'en' | 'fr', twtar_id: number) {
    if (!students || !summaryReports || ! twtar_id) {
      throw new Errors.BadRequest("MISSING DATA")
    }
    const doc = new PDFDocument();
    const translation = this.app.service('public/translation');
    const reportingProfile = await this.getReportingProfile(twtar_id);

    if(!reportingProfile) {
      throw new Errors.BadRequest(`MISSING_REPORTING_PROFILE_TWTAR_ID_${twtar_id}`);
    }

    const {layoutConfig, txtNodeRefs} = reportingProfile;
    const {layout, boldFont, normalFont} = layoutConfig
    const pdfSpecs = this.getPDFSpecs(doc);
    const {pageWidth, pageHeight, margin, domainNameWidth, availableWidth, monthNameWidth, sectionX, scoreX, moveDownRow, moveDownSection} = pdfSpecs;
    const fonts = {boldFont, normalFont}

    let currentY = doc.y;

    for (let i = 0; i < students.length; i++) {
      const student = students[i];

      if (i > 0) {
        doc.addPage();
      }

      // student info
      const {
        uid,
        first_name,
        last_name,
        student_gov_id,
      } = student

      const sessions = this.getSessions(summaryReports, selectedCategory, lang, uid);

      const from = new Intl.DateTimeFormat(lang, { year: 'numeric', month: 'long' }).format(new Date(summaryReports[0].date))
      const to = summaryReports[1] ? new Intl.DateTimeFormat(lang, { year: 'numeric', month: 'long' }).format(new Date(summaryReports[summaryReports.length - 1].date)) : null;
      const studentReportData: IStudentReportData = {
        firstName: first_name,
        lastName: last_name,
        asn: student_gov_id,
        test_design_name: summaryReports[0].testDesign.long_name,
        from,
        to,
        sessions,
      }

      const framework = JSON.parse(summaryReports[0].testDesign.framework);

      const studentReportSlug = await translation.getOneBySlug('lbl_student_report', lang);
      const dateToSlug = await translation.getOneBySlug('lbl_date_to', lang);

      // Rendering Dynamics
      layout.forEach((block) => {
        const blockY = doc.y; // Each section has a set "blockY" coordinate, representing where it started
        const columnHeights: number[] = []; // Used to store the heights of each column so that the next sections Y coordinate can be calculated

        if(block.blockType == BLOCK_TYPES.HEADER) {
          // Header: Title, Test Design Title with Date, and Student Details
          doc.fontSize(18)
          .text(studentReportSlug, { align: 'left' })
          .moveDown(moveDownRow);

          if (studentReportData.to) {
            doc.fontSize(10)
              .font(this.getFont(fonts, FONT_STYLES.REGULAR))
              .text(
                `${studentReportData.test_design_name} - ${studentReportData.from} ${dateToSlug} ${studentReportData.to}`,
                { align: 'left' }
              )
              .moveDown(moveDownRow);
          } else {
            doc.fontSize(10)
              .font(this.getFont(fonts, FONT_STYLES.REGULAR))
              .text(`${studentReportData.test_design_name} - ${studentReportData.from}`, { align: 'left' })
              .moveDown(moveDownRow);
          }

          doc.fontSize(10)
            .font(this.getFont(fonts, FONT_STYLES.REGULAR))
            .text(`${studentReportData.lastName}, ${studentReportData.firstName} (${studentReportData.asn})`, { align: 'left' })
            .moveDown(moveDownRow);
        } else if(block.blockType == BLOCK_TYPES.TEXT) {
          if(!block.columns) {
            return;
          }
          block.columns.forEach((column, colIndex) => {
            const colWidth = (pageWidth - (margin * 2)) / block.columns!.length;
            const colX = margin + (colWidth * colIndex); // The X coordinate for each column is based off the column width * index + the margin
  
            column.forEach((node: ILayoutNode, rowIndex) => {
              const row = node as ILayoutText;
              let rowY = doc.y;
              if(rowIndex == 0) {
                rowY = blockY; // This ensures that each column starts at the initial Y coordinate of the section.
              }
              const text = this.getNodeText(txtNodeRefs, row.slug, lang);
              doc
                .fontSize(row.fontSize)
                .font(this.getFont(fonts, row.fontStyle))
                .text(text, colX, rowY, {width: colWidth, align: row.align});
              
              doc.moveDown(moveDownRow);
            })
  
            const columnHeight = doc.y - blockY; // Get the height using the difference between section baseline and the current position after rendering the column
            columnHeights.push(columnHeight);
          })
        } else if(block.blockType == BLOCK_TYPES.DIVIDER) {
          doc.moveDown(1).moveTo(margin, doc.y).lineTo(pageWidth - margin, doc.y).stroke();
          doc.moveDown(moveDownRow);
        } else if(block.blockType == BLOCK_TYPES.OVERALL) {
          this.renderGraphs(doc, studentReportData, txtNodeRefs, pdfSpecs, fonts, lang, 'overall', block.data as ILayoutOverall, true);
        } else if(block.blockType == BLOCK_TYPES.GRAPHS) {
          this.renderGraphs(doc, studentReportData, txtNodeRefs, pdfSpecs, fonts, lang, 'overall', block.data as ILayoutGraphs, false);
        } else if(block.blockType == BLOCK_TYPES.FOOTER) {
          doc.moveDown(moveDownRow);
          currentY = doc.y;
          doc.fill('black').fontSize(12).font(this.getFont(fonts, FONT_STYLES.BOLD)).text(this.getNodeText(txtNodeRefs, FOOTER_HEADER, lang), margin, currentY).moveDown(0.5);
          doc.moveDown(moveDownRow);
          currentY = doc.y;
          doc.fontSize(10).font(this.getFont(fonts, FONT_STYLES.REGULAR)).text(this.getNodeText(txtNodeRefs, FOOTER_CONTENT, lang), margin, currentY ).moveDown(0.5)
        }

        if(columnHeights.length) {
          doc.y = blockY + Math.max(...columnHeights); // Set the new Y coordinate to the bottom of the longest column
        }
        // doc.moveDown(moveDownSection);
      })
    }
    doc.end();

  
    // Convert the PDF to a base64 string
    const chunks:any[] = [];
    doc.on('data', (chunk) => {
      chunks.push(chunk);
    });
    const pdfBase64 = await new Promise((resolve, reject) => {
      doc.on('end', () => {
        const buffer = Buffer.concat(chunks);
        const base64String = buffer.toString('base64');
        resolve(base64String);
      });
      doc.on('error', reject);
    });
  
    return pdfBase64
  }

  renderGraphs(doc: typeof PDFDocument, studentReportData: IStudentReportData, txtNodeRefs: INodeRefConfig, pdfSpecs: IPDFSpecs, fonts: {boldFont: string, normalFont: string}, lang: 'en' | 'fr', filterDomain?: string, blockData?: any, isOverall?: boolean) {
    const gaugeHeight = 20;
    const {pageWidth, pageHeight, margin, domainNameWidth, availableWidth, monthNameWidth, sectionX, scoreX, moveDownRow, moveDownSection} = pdfSpecs;
    let currentY = doc.y;
    if(blockData && blockData.marginTop) {
      doc.moveDown(blockData.marginTop);
    }
    for (const domainName in studentReportData.sessions[0].domains) {
      // Skip if filterdomain exists and
      // filter
      const filterIncludes = filterDomain && isOverall && domainName.toLowerCase() != filterDomain.toLowerCase(); // Only show filterDomain
      const filterExcept = filterDomain && !isOverall && domainName.toLowerCase() == filterDomain.toLowerCase(); // Show everything except filterDomain if not overall
      if(filterIncludes || filterExcept) {
        continue;
      }
      doc.x = margin;
      // Last index since the final result should be based on their last exam
      const lastIndex = studentReportData.sessions.length - 1

      const studentScore = studentReportData.sessions[lastIndex].domains[domainName].score
      const cutInfo = studentReportData.sessions[lastIndex].domains[domainName].cutInfo
      let selectedCutInfo = cutInfo[0]
      let maxCutScore = cutInfo[0]?.cut_score || null;

      // Determine cut score category for student
      for (let i = 0; i < cutInfo.length; i++) {
        const cutScore = cutInfo[i]?.cut_score;
         // Track the largest cut score
        if (cutScore !== null && (maxCutScore === null || cutScore > maxCutScore)) {
          maxCutScore = cutScore;
        }
        if (studentScore === null && cutScore === null) {
          selectedCutInfo = cutInfo[i]
        }
        if (studentScore === null || cutScore === null) {
          continue;
        }
        if (studentScore >= cutScore) {
          selectedCutInfo = cutInfo[i]
        }
      }

      // console.log('selectedCutInfo', selectedCutInfo)

      const startingDomainHeight = doc.y;
      const useOverall = isOverall && blockData && this.getNodeText(txtNodeRefs, OVERALL_CONTENT, lang) != 'N/A';
      const showUnits: boolean = blockData?.showUnits ?? false;
      const usePercentage = blockData?.usePercentage ?? false;
      const monthLabelX = useOverall ? margin : sectionX + domainNameWidth;
      const monthAlign = useOverall ? 'left' : 'right'

      if(useOverall) {
        doc.fontSize(blockData.titleFontSize)
          .fill('black')
          .font(this.getFont(fonts, FONT_STYLES.BOLD))
          .text(`${this.getNodeText(txtNodeRefs, blockData.titleSlug, lang)}: ${selectedCutInfo.long}`, margin, startingDomainHeight, { width: (pageWidth - margin * 2), align: 'center' });
          doc.moveDown(moveDownSection);
        } else {
        doc
          .fontSize(12)
          .font('Helvetica')
          .fill('black')
          .text(domainName,{width: domainNameWidth})
          .moveDown(0.25);
        doc
          .fontSize(8)
          .font('Helvetica')
          .fill(selectedCutInfo.color)
          .text(selectedCutInfo.long ,{width: domainNameWidth});
      }
      for (const session of studentReportData.sessions) {
        currentY = doc.y;

        doc.fontSize(12).fill('black').text(session.month, monthLabelX, doc.y, {width: monthNameWidth, align: monthAlign});
        const monthWidth = doc.widthOfString(session.month, { width: monthNameWidth, align: monthAlign });
        const domain = session.domains[domainName];
        const score = (domain && typeof domain.score === 'number' && domain.weight !== undefined)
        ? domain.score / domain.weight
        : null;
        
        const cutOffScore = maxCutScore ? maxCutScore / session.domains[domainName].weight : 0;
        
        // Calculate gauge dimensions and positions
        const gaugeWidth = useOverall ? (pageWidth - (margin * 2) - (monthWidth + 10)) :availableWidth - monthNameWidth - 20;
        const scoreWidth = score !== null ? gaugeWidth * score : 0;
        const gaugeX = useOverall ? margin + monthWidth + 10: scoreX + monthNameWidth + 10;
        const gaugeCutOffX = gaugeX + (gaugeWidth * cutOffScore);
        const dotScoreX = score !== null ? gaugeX + scoreWidth : 0;

        doc.y = currentY;
        const postGraphY = doc.y;

        doc.lineWidth(1).roundedRect(gaugeX, doc.y, gaugeWidth, gaugeHeight, gaugeHeight / 2).stroke('black');
        const gradient = doc.linearGradient(gaugeX, doc.y, gaugeX + gaugeWidth, doc.y);
        gradient.stop(0, '#FEDB37');
        gradient.stop(1, '#028500');
        doc.fill(gradient);
        doc.roundedRect(gaugeX, doc.y, gaugeWidth, gaugeHeight, gaugeHeight / 2).fill().stroke('black');
  
        doc.fill('black').moveTo(gaugeCutOffX, doc.y).lineTo(gaugeCutOffX, doc.y + gaugeHeight).stroke();


        if (score !== null) {
          currentY = doc.y;
          const dotColor = score < cutOffScore ? 'red' : 'black';
          doc.circle(dotScoreX, currentY + gaugeHeight / 2, 2).fillAndStroke(dotColor, dotColor);
        }

        const postGraphX = doc.x;

        if(showUnits) {
          const renderedScore = usePercentage ? `${score ? (score * 100).toFixed(2) : 0}` : `${domain.score ?? 0}`;
          const renderedCutScore = usePercentage ? `${cutOffScore ? (cutOffScore * 100).toFixed(2) : 0}` : `${maxCutScore ?? 0}`
          const renderedMaxPoints = usePercentage ? `100` : `${domain.weight ?? 0}`
          const scoreWidth = doc
            .fontSize(8)
            .fill('black')
            .font('Helvetica')
            .widthOfString(renderedScore);
          const scoreHeight = doc
          .fontSize(8)
          .fill('black')
          .font('Helvetica')
          .heightOfString(renderedScore);

          const cutScoreWidth = doc
            .fontSize(8)
            .fill('black')
            .font('Helvetica')
            .widthOfString(renderedCutScore);
          const cutScoreHeight = doc
          .fontSize(8)
          .fill('black')
          .font('Helvetica')
          .heightOfString(renderedCutScore);

          // Cut Score Unit
          doc
          .fontSize(10)
          .fill('black')
          .font('Helvetica')
          .text(renderedCutScore, gaugeCutOffX - (cutScoreWidth / 2), postGraphY - cutScoreHeight - 5);

          // Score Unit
          doc
          .fontSize(8)
          .fill('black')
          .font('Helvetica')
          .text(`${renderedScore}`, dotScoreX - scoreWidth - 5, (postGraphY + gaugeHeight / 2) - (scoreHeight/4), {width: 50});

          const zeroWidth = doc
            .fontSize(10)
            .fill('black')
            .font('Helvetica').widthOfString('0');

          // Min-Max score units
          doc
          .fontSize(10)
          .fill('black')
          .font('Helvetica')
          .text('0', gaugeX-zeroWidth, postGraphY + gaugeHeight, {align: "left"})
          
          doc
            .fontSize(10)
            .font('Helvetica')
            .text(`${renderedMaxPoints}`, gaugeX + gaugeWidth, postGraphY + gaugeHeight, {align: "left"})

          doc.x = postGraphX;
          doc.y = postGraphY;
        }


  
        doc.moveDown(3);
      }

      if(useOverall) {
        const fontOptions = {normalFont: 'Helvetica', boldFont: this.getFont(fonts, FONT_STYLES.BOLD)}
        const options = { width: (pageWidth - margin * 2), align: 'left'}
        currentY = doc.y;
        this.renderFormattedText(doc, blockData.contentFontSize, 'black', this.getNodeText(txtNodeRefs, blockData.contentSlug, lang), margin, currentY, options, fontOptions)
      }
      doc.moveDown(moveDownSection).moveTo(margin, doc.y).lineTo(pageWidth - margin, doc.y).stroke('black');
      doc.moveDown(1);

      // Logic for whether or not to start new page
      const endingDomainHeight = doc.y;
      const domainHeight = endingDomainHeight - startingDomainHeight;

      if (doc.y + domainHeight > pageHeight) {
        doc.addPage();
        doc.y = margin;
      }
    }
  }

  getSessions(summaryReports:SummaryReport[], selectedCategory:string, lang: string, uid: number) {
    const sessions: {
      month: string;
      domains: PdfDomainInfo;
    }[] = [];

    // order reports by time
    summaryReports.sort((a, b) => {
      const dateA = new Date(a.date).getTime();
      const dateB = new Date(b.date).getTime();
      return dateA - dateB;
    })
    for (let i = 0; i < summaryReports.length; i++) {
      const summaryReport = summaryReports[i]
      const domains:PdfDomainInfo = {} 

      if (selectedCategory == DefaultCategories.OVERALL) {
        for (const domain in summaryReport.data) {
          if (this.isPerDomain(domain)) {
            continue;
          }
          domains[domain] = {
            score: summaryReport.data[domain].details.students[uid],
            cutInfo: summaryReport.data[domain].details.cutOff,
            weight: summaryReport.data[domain].details.weight
          }
      }
      }
      else {
        domains[selectedCategory] = {
          score: summaryReport.data[selectedCategory].details.students[uid],
          cutInfo: summaryReport.data[selectedCategory].details.cutOff,
          weight: summaryReport.data[selectedCategory].details.weight
        };
      }
      const session = {
        month: new Date(summaryReport.date).toLocaleString(lang, { month: 'long' }),
        domains, 
      }
      sessions.push(session);
    }

    return sessions;
  }

  async createPdf(students:Student[], summaryReports:SummaryReport[], selectedCategory:string, lang: string) {
    if (!students || !summaryReports) {
      throw new Errors.BadRequest("MISSING DATA")
    }
    const doc = new PDFDocument();
    const translation = this.app.service('public/translation');

    const pageWidth = doc.page.width;
    const pageHeight = doc.page.height;
    const margin = 50;
    const domainNameWidth = 140;
    const availableWidth = pageWidth - margin * 2 - domainNameWidth;
    const monthNameWidth = 100;
    const startX = margin;
    const sectionX = startX;
    const scoreX = sectionX + domainNameWidth;
    const lineHeight = 10;
    let currentY = doc.y;

    for (let i = 0; i < students.length; i++) {
      const student = students[i];

      if (i > 0) {
        doc.addPage();
      }

      // student info
      const {
        uid,
        first_name,
        last_name,
        student_gov_id,
      } = student

      const sessions: {
        month: string;
        domains: PdfDomainInfo;
      }[] = [];
      // order reports by time
      summaryReports.sort((a, b) => {
        const dateA = new Date(a.date).getTime();
        const dateB = new Date(b.date).getTime();
        return dateA - dateB;
      })
      for (let i = 0; i < summaryReports.length; i++) {
        const summaryReport = summaryReports[i]
        const domains:PdfDomainInfo = {} 

        if (selectedCategory == DefaultCategories.OVERALL) {
          for (const domain in summaryReport.data) {
            if (this.isPerDomain(domain)) {
              continue;
            }
            domains[domain] = {
              score: summaryReport.data[domain].details.students[uid],
              cutInfo: summaryReport.data[domain].details.cutOff,
              weight: summaryReport.data[domain].details.weight
            }
        }
        }
        else {
          domains[selectedCategory] = {
            score: summaryReport.data[selectedCategory].details.students[uid],
            cutInfo: summaryReport.data[selectedCategory].details.cutOff,
            weight: summaryReport.data[selectedCategory].details.weight
          };
        }
        const session = {
          month: new Date(summaryReport.date).toLocaleString(lang, { month: 'long' }),
          domains, 
        }
        sessions.push(session);
      }
      const from = new Intl.DateTimeFormat(lang, { year: 'numeric', month: 'long' }).format(new Date(summaryReports[0].date))
      const to = summaryReports[1] ? new Intl.DateTimeFormat(lang, { year: 'numeric', month: 'long' }).format(new Date(summaryReports[summaryReports.length - 1].date)) : null;
      const studentReportData = {
        firstName: first_name,
        lastName: last_name,
        asn: student_gov_id,
        test_design_name: summaryReports[0].testDesign.long_name,
        from,
        to,
        sessions,
      }

      const framework = JSON.parse(summaryReports[0].testDesign.framework);

      const isrText = framework.isrText[lang]

      // Header: Title, Test Design Title with Date, and Student Details
      doc.fontSize(18)
        .text(await translation.getOneBySlug('lbl_student_report', lang), { align: 'left' })
        .moveDown(0.5);

      if (studentReportData.to) {
        doc.fontSize(10)
          .font('Helvetica')
          .text(
            `${studentReportData.test_design_name} - ${studentReportData.from} ${await translation.getOneBySlug('lbl_date_to', lang)} ${studentReportData.to}`,
            { align: 'left' }
          )
          .moveDown(0.5);
      } else {
        doc.fontSize(10)
          .font('Helvetica')
          .text(`${studentReportData.test_design_name} - ${studentReportData.from}`, { align: 'left' })
          .moveDown(0.5);
      }

      doc.fontSize(10)
        .font('Helvetica')
        .text(`${studentReportData.lastName}, ${studentReportData.firstName} (${studentReportData.asn})`, { align: 'left' })
        .moveDown(0.5);
        
      // Draw divider line
      doc.moveDown(1).moveTo(margin, doc.y).lineTo(pageWidth - margin, doc.y).stroke();

      doc.moveDown(0.5);
      // (Definition and Interpretation)
      const colWidth = (pageWidth - margin * 2) / 2 ;
      currentY = doc.y;

      doc.font('Helvetica-Bold')
        .text(isrText.definition.title, margin, currentY, { width: colWidth, align: 'left' });

      doc.font('Helvetica-Bold')
        .text(isrText.interpretation.title, margin + colWidth + 20, currentY, { width: colWidth, align: 'left' });

      doc.moveDown(0.5);
      currentY = doc.y;

      doc.fontSize(9)
        .font('Helvetica')
        .text(isrText.definition.content, margin, currentY, { width: colWidth, align: 'left' });
      const defHeight = doc.heightOfString(isrText.definition.content, { width: colWidth });
      doc.font('Helvetica')
        .text(isrText.interpretation.content, margin + colWidth + 20, currentY, { width: colWidth, align: 'left' });
      const intHeight = doc.heightOfString(isrText.interpretation.content, { width: colWidth });

      doc.y = currentY + Math.max(defHeight, intHeight);
      doc.moveDown(2);

      const gaugeHeight = 20;
      const gaugeYIncrement = gaugeHeight + 20;
      const domainLabelX = startX;

      for (const domainName in studentReportData.sessions[0].domains) {
        doc.x = margin;
        // Last index since the final result should be based on their last exam
        const lastIndex = sessions.length - 1

        const studentScore = studentReportData.sessions[lastIndex].domains[domainName].score
        const cutInfo = studentReportData.sessions[lastIndex].domains[domainName].cutInfo
        let selectedCutInfo = cutInfo[0]
        let maxCutScore = cutInfo[0]?.cut_score || null;

        // Determine cut score category for student
        for (let i = 0; i < cutInfo.length; i++) {
          const cutScore = cutInfo[i]?.cut_score;
           // Track the largest cut score
          if (cutScore !== null && (maxCutScore === null || cutScore > maxCutScore)) {
            maxCutScore = cutScore;
          }
          if (studentScore === null && cutScore === null) {
            selectedCutInfo = cutInfo[i]
          }
          if (studentScore === null || cutScore === null) {
            continue;
          }
          if (studentScore >= cutScore) {
            selectedCutInfo = cutInfo[i]
          }
        }

        // console.log('selectedCutInfo', selectedCutInfo)

        const startingDomainHeight = doc.y;
        const hasOverallText = domainName.toLowerCase() == 'overall' && isrText?.overall?.title && isrText?.overall?.content;
        const monthLabelX = hasOverallText ? margin : sectionX + domainNameWidth;
        const monthAlign = hasOverallText ? 'left' : 'right'

        if(hasOverallText) {
          doc.fontSize(10)
            .fill('black')
            .font('Helvetica-Bold')
            .text(`${isrText.overall.title}: ${selectedCutInfo.long}`, margin, startingDomainHeight, { width: (pageWidth - margin * 2), align: 'center' });
            doc.moveDown(2);
          } else {
          doc.fontSize(12).fill('black').text(domainName,{width: domainNameWidth}).moveDown(0.25);
          doc.fontSize(8).fill(selectedCutInfo.color).text(selectedCutInfo.long ,{width: domainNameWidth});
        }
        for (const session of studentReportData.sessions) {
          currentY = doc.y;

          doc.fontSize(12).fill('black').text(session.month, monthLabelX, doc.y, {width: monthNameWidth, align: monthAlign});
          const monthWidth = doc.widthOfString(session.month, { width: monthNameWidth, align: monthAlign });
          const domain = session.domains[domainName];
          const score = (domain && typeof domain.score === 'number' && domain.weight !== undefined)
          ? domain.score / domain.weight
          : null;
          
          const cutOffScore = maxCutScore ? maxCutScore / session.domains[domainName].weight : 0;
          
          // Calculate gauge dimensions and positions
          const gaugeWidth = hasOverallText ? (pageWidth - (margin * 2) - (monthWidth + 10)) :availableWidth - monthNameWidth - 20;
          const scoreWidth = score !== null ? gaugeWidth * score : 0;
          const gaugeX = hasOverallText ? margin + monthWidth + 10: scoreX + monthNameWidth + 10;
          const gaugeCutOffX = gaugeX + (gaugeWidth * cutOffScore);
          const dotScoreX = score !== null ? gaugeX + scoreWidth : 0;

          doc.y = currentY;
          doc.lineWidth(1).roundedRect(gaugeX, doc.y, gaugeWidth, gaugeHeight, gaugeHeight / 2).stroke('black');
          const gradient = doc.linearGradient(gaugeX, doc.y, gaugeX + gaugeWidth, doc.y);
          gradient.stop(0, '#FEDB37');
          gradient.stop(1, '#028500');
          doc.fill(gradient);
          doc.roundedRect(gaugeX, doc.y, gaugeWidth, gaugeHeight, gaugeHeight / 2).fill().stroke('black');
    
          doc.fill('black').moveTo(gaugeCutOffX, doc.y).lineTo(gaugeCutOffX, doc.y + gaugeHeight).stroke();
          if (score !== null) {
            const dotColor = score < cutOffScore ? 'red' : 'black';
            doc.circle(dotScoreX, doc.y + gaugeHeight / 2, 2).fillAndStroke(dotColor, dotColor);
          }
    
          doc.moveDown(3);
        }

        if(hasOverallText) {
          const fontOptions = {normalFont: 'Helvetica', boldFont: 'Helvetica-Bold'}
          const options = { width: (pageWidth - margin * 2), align: 'left'}
          currentY = doc.y;
          this.renderFormattedText(doc, 9, 'black', isrText.overall.content, margin, currentY, options, fontOptions)
        }
        doc.moveDown(2).moveTo(margin, doc.y).lineTo(pageWidth - margin, doc.y).stroke('black');
        doc.moveDown(1);

        // Logic for whether or not to start new page
        const endingDomainHeight = doc.y;
        const domainHeight = endingDomainHeight - startingDomainHeight;

        if (doc.y + domainHeight > pageHeight) {
          doc.addPage();
          doc.y = margin;
        }
      }
      doc.x = margin;
      doc.moveDown(0.5);
      currentY = doc.y;
      doc.fill('black').fontSize(12).font('Helvetica-Bold').text(isrText.footer.title, margin, currentY).moveDown(0.5);
      doc.moveDown(0.5);
      currentY = doc.y;
      doc.fontSize(10).font('Helvetica').text(isrText.footer.content, margin, currentY ).moveDown(0.5)
    }
    doc.end();

  
    // Convert the PDF to a base64 string
    const chunks:any[] = [];
    doc.on('data', (chunk) => {
      chunks.push(chunk);
    });
    const pdfBase64 = await new Promise((resolve, reject) => {
      doc.on('end', () => {
        const buffer = Buffer.concat(chunks);
        const base64String = buffer.toString('base64');
        resolve(base64String);
      });
      doc.on('error', reject);
    });
  
    return pdfBase64
  }
  
  /**
 * Renders text with dynamic inline formatting.
 * 
 * This function checks if the provided text contains <b>...</b> tags.
 * If it does, it splits the text into segments and switches between the normal
 * and bold fonts accordingly using PDFKit's {continued: true} inline text feature.
 *
 * @param {PDFDocument} doc - The PDFKit document instance.
 * @param {string} text - The text to render (may contain <b>...</b>).
 * @param {number} x - The x-coordinate for the text start.
 * @param {number} y - The y-coordinate for the text start.
 * @param {object} options - Options for text rendering (e.g., width, align, fontSize, fill).
 *                           You can also pass custom fonts via options.normalFont and options.boldFont.
 */
renderFormattedText(doc: typeof PDFDocument, fontSize: number, fill: string, text: string, x: number, y: number, options: any = {}, fontOptions?: {normalFont: string, boldFont: string}) {
  // Set default fonts if not provided in options.
  const normalFont = fontOptions?.normalFont ?? 'Helvetica';
  const boldFont = fontOptions?.boldFont ?? 'Helvetica-Bold';
  
  doc.fontSize(fontSize).fill(fill);

  // If there are no <b> tags, render the text normally.
  if (!text.includes('<b>')) {
    doc.font(normalFont).text(text, x, y, options);
    return;
  }
  
  // Set the base font and move to the initial position.
  doc.font(normalFont);
  doc.text('', x, y); // position the text cursor
  
  // Split the text by <b> and </b> tags.
  const segments = text.split(/(<\/?b>)/g);
  
  segments.forEach(segment => {
    if (segment === '<b>') {
      // Switch to bold font.
      doc.font(boldFont);
    } else if (segment === '</b>') {
      // Switch back to normal font.
      doc.font(normalFont);
    } else if (segment.length > 0) {
      // Render the text segment inline.
      doc.text(segment, { continued: true, ...options });
    }
  });
  
  // Finalize the inline text chain.
  doc.text('');
}

  
  async generateResponseSheetsPdf(studentData: any) {
    // Create a new PDF document
    const doc = new PDFDocument();
  
    // Path to save the PDF file
    const filePath = 'output.pdf';
  
    // Create a write stream for the PDF file
    const writeStream = fs.createWriteStream(filePath);
  
    // Pipe the PDF into the writable stream
    doc.pipe(writeStream);
  
    for (let i = 0; i < studentData.length; i++){
      const student = studentData[i];

      if (i > 0) {
        doc.addPage();
      }
    
      // Set some styling options
      const margin = 40;
      const lineHeight = 14;
      const stuInfoboxWidth = 180; // Width for each column
      const stuInfoboxHeight = 21; // Height for each row
      const padding = 5; // Padding inside each box
      const marginAfterSessionCode = 50
    
      // Define coordinates for the two columns and two rows
      const firstColX = margin + marginAfterSessionCode;
      const secondColX = margin + stuInfoboxWidth + marginAfterSessionCode + 2; // Add a small gap between columns
      const firstRowY = margin;
      const secondRowY = margin + stuInfoboxHeight + 2; // Add a small gap between rows

      // Draw the large letter "A"
      doc.fontSize(58).font("Helvetica-Bold").fillColor('black').text('A', margin, margin);
    
      // Draw gray boxes for the background
      doc.rect(firstColX, firstRowY, stuInfoboxWidth, stuInfoboxHeight).fill('#D3D3D3').stroke();
      doc.rect(secondColX, firstRowY, stuInfoboxWidth, stuInfoboxHeight).fill('#D3D3D3').stroke();
      doc.rect(firstColX, secondRowY, stuInfoboxWidth, stuInfoboxHeight).fill('#D3D3D3').stroke();
      doc.rect(secondColX, secondRowY, stuInfoboxWidth, stuInfoboxHeight).fill('#D3D3D3').stroke();

      const maskedStuNum = this.maskStudentNumber(student.student_gov_id)
      // Add text inside each box
      doc.fillColor('black').fontSize(10).font("Helvetica")
        .text(`Student Name: ${student.first_name} ${student.last_name}`, firstColX + padding, firstRowY + padding, { width: stuInfoboxWidth - 2 * padding })
        .text(TEMP_STUDENT_GOV_ID_LABEL+`: ${maskedStuNum}`, secondColX + padding, secondRowY + padding, { width: stuInfoboxWidth - 2 * padding })
        .text(`Class Code: ${student.class_code}`, secondColX + padding, firstRowY + padding, { width: stuInfoboxWidth - 2 * padding })
        .text(TEMP_SCHOOL_COD_LABEL + `: ${student.school_mident}`, firstColX + padding, secondRowY + padding, { width: stuInfoboxWidth - 2 * padding })
    
    
      // Add session information and prompt
      doc.fontSize(16).font('Helvetica-Bold').text(` ${student.session} - Reading Response Sheet`, margin + marginAfterSessionCode, margin + 5 * lineHeight);
    
      const boxX = margin;
      const boxY = margin + 9 * lineHeight; // Adjusted Y position to accommodate text above
      const boxWidth = doc.page.width - margin*2;
      const boxHeight = 300;
      const numberOfLines = 7;

      const imagePath = 'src/services/public/educator/individual-report/scan-marker.png';
      const imageHeight = 15 ; // Define desired image height
      const imageWidth = 15; // Define desired image width
      doc.image(imagePath, boxX, boxY - imageHeight - 15, { width: imageWidth, height: imageHeight });
      doc.image(imagePath, boxX + boxWidth - imageWidth, boxY - imageHeight - 15, { width: imageWidth, height: imageHeight });


      // Draw a box for the response area
      const p = (doc.page.width)
      doc.rect(boxX, boxY, boxWidth, boxHeight).stroke();
      
      // Add response area instructions
      doc.font("Helvetica").fontSize(9).text('Write your response on the lines below.', boxX + padding*2, boxY + padding*2);
      // Draw lines within the box for writing
      const lineMargin = 10; // Margin at the beginning and end of the line

      doc.strokeColor('#D3D3D3'); // Set the line color to gray
      const extraTop = 20
      for (let i = 1; i <= numberOfLines; i++) { // Start from 1 to avoid drawing a line at the top of the box
        const y = boxY + extraTop + i * lineHeight * 2.25;
        doc.moveTo(boxX + lineMargin, y) // Start line after the left margin
          .lineTo(boxX + boxWidth - lineMargin, y) // End line before the right margin
          .stroke();
      }
      
    
      doc.font("Helvetica").fontSize(9).text('Remember to go back to your device and click the button "Click here" when you have finished this question and then click "Submit".', boxX + padding*2, boxY + boxHeight  - 25,{
        width: 500,
        align: 'left'
      });
      doc.font('Helvetica-Bold').fontSize(18).text('Do not write in this area.', margin, boxY + boxHeight + 40, {align: 'center'});
  }
  
    // Finalize the PDF
    doc.end();
  
    // Wait for the file to be written
    await new Promise<void>((resolve, reject) => {
      writeStream.on('finish', () => resolve());
      writeStream.on('error', (err) => reject(err));
    });
  
    // Read the PDF file and encode it to Base64
    const buffer = fs.readFileSync(filePath);
    const base64String = buffer.toString('base64');
  
    return base64String;
  }
  
  maskStudentNumber(studentNumber:string) {
      let maskedStudentNumber = "";
      for (let i = 0; i < studentNumber.length; i++) {
          if (i >= 1 && i <= 5) {
              maskedStudentNumber += "*";
          } else {
              maskedStudentNumber += studentNumber[i];
          }
      }
      return maskedStudentNumber;
  }

  public isPerDomain(domain:string):boolean {
    return domain.includes(DefaultCategories.PER_DOMAIN)
  }

}
