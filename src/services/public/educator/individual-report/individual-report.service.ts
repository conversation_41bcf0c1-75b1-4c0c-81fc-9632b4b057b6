// Initializes the `individual-report` service on path `/public/educator`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { IndividualReport } from './individual-report.class';
import hooks from './individual-report.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/individual-report': IndividualReport & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/individual-report', new IndividualReport(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/individual-report');

  service.hooks(hooks);
}
