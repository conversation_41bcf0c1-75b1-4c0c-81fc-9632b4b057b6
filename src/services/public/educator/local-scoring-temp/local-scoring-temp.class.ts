import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {}

interface ServiceOptions {}

export class LocalScoringTemp implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const ts_id = id;
    const twAllowRecord = await dbRawReadSingle(this.app, {ts_id}, `
      select tw.is_allow_local_mark_edits 
      from test_sessions ts 
      join test_windows tw 
        on tw.id = ts.test_window_id  
      where ts.id = :ts_id
    `);
    if (twAllowRecord['is_allow_local_mark_edits'] == 0){
      throw new Errors.BadRequest('LOCAL_MARK_EDITS_DISABLED')
    }
    if (params && params.query){
      const updated_by_uid = await currentUid(this.app, params);
      const {attempt_id, item_id} = params.query
      const lookUpPayload = {ts_id, attempt_id, item_id}
      const updateTagPayload = {updated_by_uid, updated_on: dbDateNow(this.app)}
      data = JSON.stringify(data)
      const existingRecord = await dbRawReadSingle(this.app, lookUpPayload, `
        select id 
        from temp_local_scoring tls 
        where attempt_id = :attempt_id
          and item_id = :item_id
          and ts_id = :ts_id
      `)
      if (existingRecord){
        return this.app.service('db/write/temp-local-scoring').patch(existingRecord.id, {... updateTagPayload, data})
      }
      else {
        return this.app.service('db/write/temp-local-scoring').create({ ...lookUpPayload, ... updateTagPayload, data})
      }

    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
