import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { generateS3DownloadUrl } from '../../../../upload/upload.listener';
import { dbRawReadReporting, dbRawReadSingle } from '../../../../../util/db-raw';
import axios from 'axios';
import ExcelJS, { Workbook } from 'exceljs';
import { IResponse } from '../../../scor-lead/local-scoring-stats/local-scoring-stats.class';
import { PaginatedRows } from '../../../school-admin/bcgrad-students/sqls';
import { currentUid } from '../../../../../util/uid';
import { getSysConstNumeric } from '../../../../../util/sys-const-numeric';
import _ from 'lodash';

const IS_LOCAL_MARKING_REPORT_DISABLED = true;

interface Data { }

interface ServiceOptions { }

interface ISchoolMeta {
   name: string,
   foreign_id: number
}
interface IMarkingWindowMeta {
   marking_window_name: string,
   academic_year: string,
   lang: string,
}
interface IConfig {
   itemsNumber: number,
   totalScalesNumber: number,
   itemScaleNumberMap: Map<number, IScoreProfile[]>,
   schoolMeta: ISchoolMeta,
   markingWindowMeta: IMarkingWindowMeta,
   maxReadNumber: number,
   userMeta: IUserMeta
}
interface IUserMeta{
   id: number,
   first_name: string,
   last_name: string,
   contact_email: string,
}

interface IScoreProfile{
   name: string,
   itemId: number,
}

interface ITestWindow {
   test_window_title: string,
   marking_window_title: string,
   mw_id: number,
   created_on: string,
   academic_year: string,
   assessment_name: string,
   tw_id: number,
   type: string,
}


export class LocalScoringReports implements ServiceMethods<Data> {
   app: Application;
   options: ServiceOptions;

   constructor(options: ServiceOptions = {}, app: Application) {
      this.options = options;
      this.app = app;
   }

   // eslint-disable-next-line @typescript-eslint/no-unused-vars
   async find(params?: Params): Promise<Data> {
      if (!params || !params.query) {
         throw new Errors.BadRequest();
      }
      const ALLOW_LOCAL_SCORING_REPORT = await getSysConstNumeric(this.app, 'ALLOW_LOCAL_SCORING_REPORT');
      if(!ALLOW_LOCAL_SCORING_REPORT){
         return [];
      }
      const { academic_year, schl_group_id,  } = params.query;
      const focus_on_custom_assessments = false; // todo: need a clean way to set this, not assuming its going to come cleanly from the query parameters, have to be careful
      const uid = await currentUid(this.app, params);
      const testWindows: ITestWindow[] =  await dbRawReadReporting(this.app, {academic_year, uid}, `
      select tw.title test_window_title
         , mw.name marking_window_title
         , mw.id mw_id
         , mlse.created_on created_on
         , tw.academic_year academic_year
         , twtar.long_name assessment_name
         , tw.id tw_id
         , 'Local Marking Report' as type
      from marking_local_scoring_marker_exports mlsme 
      join marking_local_scoring_exports mlse 
         on mlsme.local_scoring_export_id = mlse.id
      join marking_window_test_window mwtw  
         on mlsme.marking_window_id = mwtw.marking_window_id 
         and mwtw.is_removed = 0
      join test_windows tw 
         on mwtw.test_window_id = tw.id 
         and tw.academic_year = (:academic_year)
         and tw.is_allow_local_scoring_report = 1
      join marking_windows mw 
         on mlsme.marking_window_id = mw.id
      join marking_window_items mwi 
         on mwi.marking_window_id = mw.id 
      join test_question_register tqr 
         on mwi.item_id = tqr.question_id 
      join test_window_td_alloc_rules twtar 
         on twtar.test_design_id = tqr.test_design_id 
         and twtar.test_window_id = tw.id
         and twtar.is_custom = ${ focus_on_custom_assessments ? '1' : '0' }

      where mlsme.uid = :uid  
         and mlsme.is_revoked = 0
         and mlse.is_revoked = 0
      group by mw.id, tw.id
      ;
      `)

      const testWindowWithReport: any[] = [];
      for (let testWindowChunk of _.chunk(testWindows, 1000)){
         for(let testWindow of testWindowChunk){
            const responses = await this.fetchLocalScoringReport(uid, testWindow.mw_id, testWindow.tw_id);
            if(responses.length > 0){
               testWindowWithReport.push(testWindow);
            }
         }
      }
      
      return testWindowWithReport;
   }

   async fetchLocalScoringReport(uid: number, mw_id: Id, tw_id:number){
      const localScoringReport = await dbRawReadSingle(this.app, { uid, mw_id }, `
         select mlse.* from marking_local_scoring_marker_exports mlsme 
         join marking_local_scoring_exports mlse 
            on mlsme.local_scoring_export_id = mlse.id 
         where mlsme.uid in (:uid)
            and mlsme.marking_window_id = :mw_id
            and mlsme.is_revoked = 0
            and mlse.is_revoked = 0
         order by mlse.created_on desc;
         `);

         const url = localScoringReport.url;
         // For support of absolute and relative path in s3
         localScoringReport.path = generateS3DownloadUrl(url.replace('s3://storage.mathproficiencytest.ca/', ''))
         let responses: IResponse[] = (await axios.get(localScoringReport.path, {})).data;
         
         // Filter by test window id
         if(tw_id){
            const batchGroupIds = responses.map((response)=>response.batchGroupId);
            // Find batch group ids within test window
            const batchGroupIdsInTestWindow = await dbRawReadReporting(this.app, {tw_id, batchGroupIds}, `
               select claimed_batch_group_id 
               from marking_claimed_batch_responses mcbr 
               join test_attempt_question_responses taqr  
                  on mcbr.taqr_id = taqr.id
               join test_attempts ta 
                  on ta.id = taqr.test_attempt_id 
               join test_window_td_alloc_rules twtar 
                  on ta.twtdar_id = twtar.id 
                  and twtar.test_window_id = :tw_id
               where claimed_batch_group_id in (:batchGroupIds)
                  and (mcbr.is_invalid = 0 or mcbr.is_invalid is null)
                  and mcbr.is_revoked = 0
               group by mcbr.claimed_batch_group_id;
               `)
            const validBatchGroupIds = batchGroupIdsInTestWindow.map((response)=>response.claimed_batch_group_id);
            responses = responses.filter((response)=>{
               return validBatchGroupIds.includes(response.batchGroupId);
            })
         }
         return responses;
   }

   // eslint-disable-next-line @typescript-eslint/no-unused-vars
   async get (id: Id, params?: Params): Promise<Data> {
      if (!params || !params.query) {
         throw new Errors.BadRequest();
      }
      const mw_id = id;
      const uid = await currentUid(this.app, params);
      const { schl_group_id, tw_id } = params.query;
      if (!mw_id || !schl_group_id) {
         throw new Errors.BadRequest("MISSING_PARAMS");
      }
      
      const responses = await this.fetchLocalScoringReport(uid, mw_id, tw_id)
      const filteredResponses = responses.filter((response)=>{
         let isMarkedByUid = false;
         response.reads.forEach((read)=>{
            if (read.marker_meta.uid == uid){
               isMarkedByUid =  true;
            }
         })
         return isMarkedByUid;
      })
      if(!filteredResponses.length){
         throw new Errors.BadRequest("NO_REPORT_FOUND");
      }
      const config: IConfig = await this.getConfig(filteredResponses, schl_group_id, mw_id, uid);
      const wb = await this.generateLocalScoringReport(filteredResponses, uid, config);
      const buffer = await wb.xlsx.writeBuffer();
      const base64String = Buffer.from(buffer).toString('base64');
      return {
         "buffer": base64String,
         "filename": `local_central_marking_report_mw_${mw_id}_${uid}`,
         "contentType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      }
   }

   async generateLocalScoringReport (responses: IResponse[], uid:Id, config: IConfig){
      return this.makeExcelJsWorkBook(responses, uid, config);
   }

   /**
    * Returns configuration for excel sheet generation, since number of items, scales, and read number affect the width of the excel sheet.
    * @param responses - IResponse object.
    * @param schoolCode - foreign id of the school to retrieve school meta data
    * @param mw_id - marking window id
    * @returns itemsNumber: Number of question items for marking of the assessment,
               totalScalesNumber: Total number of scales among all items,
               itemScaleNumberMap: A map to map how many scales per question item,
               schoolMeta: school meta data including school name and foreign id,
               markingWindowMeta: marking window meta data including marking window name and its academic year,
               maxReadNumber: Maximun read number of reads found,
    */
   async getConfig(responses: IResponse[], schoolCode: number, mw_id: Id, uid: number){
      const schoolMeta = await dbRawReadSingle(this.app, { schoolCode }, `
         select s.foreign_id
         , s.name 
         from schools s
         where s.group_id = :schoolCode;
         `);
      // To do: What if marking window is linked to two test window and different academic year
      const markingWindowMeta = await dbRawReadSingle(this.app, { mw_id }, `
         select mw.id marking_window_id
         , mw.name marking_window_name
         , tw.academic_year 
         , mw.lang
         from marking_windows mw 
         join marking_window_test_window mwtw 
               on mwtw.marking_window_id = mw.id 
            and mwtw.is_removed = 0
         join test_windows tw 
               on mwtw.test_window_id = tw.id
         where mw.id = :mw_id;
         `);
      const userMeta = await dbRawReadSingle(this.app, { uid }, `
         select id
         , first_name
         , last_name
         , contact_email
         from users
         where id = :uid
         ;`);
      let config: IConfig = {
         itemsNumber: 0,
         totalScalesNumber: 0,
         itemScaleNumberMap: new Map<number, IScoreProfile[]>(),
         schoolMeta,
         markingWindowMeta,
         userMeta,
         maxReadNumber: 0,
      }
      responses.forEach((response)=>{
         if(response.reads.length > config.maxReadNumber){
            config.maxReadNumber = response.reads.length;
         }
         response.reads.forEach((read)=>{
            let scalesNumber = 0;
            let itemsNumber = 0;
            const scales = read.scales.sort((a, b) => {return a.mwi_id - b.mwi_id});
            scales.forEach((scale)=>{
               if(!scale.is_non_scored_profile){
                  scalesNumber++;
               }
               if(!config.itemScaleNumberMap.get(scale.sync_batches_to_wmi_id)){
                  config.itemScaleNumberMap.set(scale.sync_batches_to_wmi_id, []);
                  itemsNumber++;
               }
               const mwiIds = config.itemScaleNumberMap.get(scale.sync_batches_to_wmi_id)!.map(item=>item.itemId);
               if(!mwiIds.includes(scale.mwi_id) && !scale.is_non_scored_profile){
                  config.itemScaleNumberMap.get(scale.sync_batches_to_wmi_id)!.push({
                     itemId: scale.mwi_id,
                     name: scale.score_profile_name!
                  });
               }
            })
            if(scalesNumber > config.totalScalesNumber){
               config.totalScalesNumber = scalesNumber;
            }
            if(itemsNumber > config.itemsNumber){
               config.itemsNumber = itemsNumber;
            }
         })
      })
      return config;
   }
   /**
    * Generate a excel work book object with ExcelJS by inputting cache information of local scoring report
    * @param responses - IResponse object.
    * @param uid - user id of the teacher
    * @param config - IConfig object for basic information to generate the excel
    * @returns ExcelJS.Workbook object
    */
   makeExcelJsWorkBook(responses: IResponse[], uid:Id, config: IConfig) : ExcelJS.Workbook{
      const wb = new ExcelJS.Workbook();
      wb.creator = 'Vretta Inc.';
      wb.lastModifiedBy = 'Vretta Inc.';
      wb.created = new Date();
      wb.modified = new Date();
      wb.lastPrinted = new Date();
      wb.calcProperties.fullCalcOnLoad = true;

      const worksheet = wb.addWorksheet(
         'Local Central Marking Report'
      );
      // Generate Title
      const centreOfSpreadsheet = Math.max((Math.floor(((config.maxReadNumber) * config.totalScalesNumber) - 1) / 2), 7); // Get middle of spreadsheet
      let subColumn = this.moveColumn('A', centreOfSpreadsheet); 
      let row = 1;
      worksheet.getCell(`${subColumn}${row++}`).value = config.markingWindowMeta.marking_window_name;
      subColumn = this.moveColumn(subColumn, 1); 
      worksheet.getCell(`${subColumn}${row++}`).value = "Local Marking Report";
      worksheet.getCell(`${subColumn}${row++}`).value = config.markingWindowMeta.academic_year;
      row++;
      // Generate Marker Meta
      const userMetaBoxes = [
         { title: "UID:", value: uid, titleWidth:1, valueWidth: 3    },
         { title: "Email:", value: config.userMeta.contact_email, titleWidth:1, valueWidth: 3 },
         { title: "Marker School Code:", value: config.schoolMeta.foreign_id, titleWidth:2, valueWidth: 2 },
         { title: "School:", value: config.schoolMeta.name, titleWidth:1, valueWidth: 1 },
      ]
      
      let column = 'A';
      userMetaBoxes.forEach((box)=>{
         worksheet.getCell(`${column}${row}`).value = box.title;
         column = this.moveColumn(column, box.titleWidth || 1);
         worksheet.getCell(`${column}${row}`).value = box.value;
         column = this.moveColumn(column, box.valueWidth || 1);
      })

      row++;
      column = 'A';
      let questionNumber = 1;
      let scaleNumber = 1;
      let maxColumn = column; // Calculate Width
      config.itemScaleNumberMap.forEach((items, syncToItemId)=>{
         
         items.forEach((item)=>{
            worksheet.getCell(`${column}${row}`).value = `Q${questionNumber}S${scaleNumber}: ${item.name}`;
            scaleNumber++;
            column = this.moveColumn(column, 4);
            
         });
         column = 'A';
         scaleNumber = 1;
         maxColumn = column;
         questionNumber++;
         row++;
      })
      row++;
      
      // Create Read Number Row
      
      column = 'E';
      for(let i = 1; i <= config.maxReadNumber; i++){
         switch(i){
            case config.maxReadNumber:
               worksheet.getCell(`${column}${row}`).value = "Final Scores";
               break;
            case 1:
               worksheet.getCell(`${column}${row}`).value = "Local Reading";
               break;
            case 2:
               worksheet.getCell(`${column}${row}`).value = "Central Reading";
               break;
            case 3:
               worksheet.getCell(`${column}${row}`).value = "Third Reading";
               break;
            case 4:
               worksheet.getCell(`${column}${row}`).value = "Fourth Reading";
               break;
         }
         
         let questionNumber = 1;
         let subColumn = column;
         config.itemScaleNumberMap.forEach((scales, itemNumber)=>{
            let scaleNumber = 1;
            worksheet.getCell(`${subColumn}${(row+1)}`).value = `Q${questionNumber}`;
            scales.forEach((scale)=>{
               worksheet.getCell(`${subColumn}${(row+2)}`).value = `S${scaleNumber}`;
               subColumn = this.moveColumn(subColumn,1);
               scaleNumber++;
            })
            questionNumber++;
         });
         column = this.moveColumn(column, config.totalScalesNumber);
      }
      worksheet.getCell(`${'A'}${(row+2)}`).value = `Student Name`;
      let hasProratedStudent = false;
      row += 3;
      responses.forEach((response)=>{
         column = this.moveColumn('E', config.totalScalesNumber);
         response.reads.forEach((read)=>{
            let isProrated = false;
            
            if(read.marker_meta.is_local_score){
               // Local Read
               let subColumn = 'E';
               const scales = read.scales.sort((a, b) => {return a.mwi_id - b.mwi_id});
               scales.forEach((scale)=>{
                  if(!scale.is_non_scored_profile){
                     if(scale.raw_score_value){
                        worksheet.getCell(`${subColumn}${(row)}`).value = `${scale.raw_score_value}`;
                     }
                     else if (scale.flag_slug || scale.score_slug){
                        worksheet.getCell(`${subColumn}${(row)}`).value = `0`;
                     }
                     subColumn= this.moveColumn(subColumn, 1);
                  }
                  if(scale.is_prorated){
                     isProrated = true;
                  }
               })
               //worksheet.getCell(`A${i}`).value = ;
            }
            else if(read.readId == 0){
               // Final Scores
               let subColumn = this.moveColumn('E', (config.maxReadNumber - 1) * config.totalScalesNumber);
               const scales = read.scales.sort((a, b) => {return a.mwi_id - b.mwi_id});
               scales.forEach((scale)=>{
                  if(!scale.is_non_scored_profile){
                     if(scale.raw_score_value){
                        worksheet.getCell(`${subColumn}${(row)}`).value = `${scale.raw_score_value}`;
                     }
                     else if ((scale.flag_slug || scale.score_slug) && !scale.is_scale_supressed){
                        worksheet.getCell(`${subColumn}${(row)}`).value = `0.0`;
                     }
                     subColumn= this.moveColumn(subColumn, 1);
                  }
                  if(scale.is_prorated){
                     isProrated = true;
                  }
               })
            }
            else{
               const scales = read.scales.sort((a, b) => {return a.mwi_id - b.mwi_id});
               scales.forEach((scale)=>{
                  if(!scale.is_non_scored_profile){
                     if(scale.raw_score_value){
                        worksheet.getCell(`${column}${(row)}`).value = `${scale.raw_score_value}`;
                     }
                     else if (scale.flag_slug || scale.score_slug){
                        worksheet.getCell(`${column}${(row)}`).value = `0`;
                     }
                     column = this.moveColumn(column, 1);
                  }
                  if(scale.is_prorated){
                     isProrated = true;
                  }
               })
            }
            worksheet.getCell(`${'A'}${(row)}`).value = `${response.student_meta.first_name} ${response.student_meta.last_name}${isProrated?"*":""}`;
            if(isProrated){
               hasProratedStudent = true;
            }
         })
         
         row++;
      })
      
      if(hasProratedStudent){
         row++;
         worksheet.getCell(`A${(row)}`).value = `* Scribed and transcribed papers were not scored on question 1 scale 5 and question 2 scale 2.`;
      }
      
      
      this.formatExcelJsWorkSheet(worksheet);
      return wb;
   }

   moveColumn(text: string, steps: number){
      for(let i = 0; i < steps; i++){
         text = this.nextChar(text);
      }
      return text;
   }

   nextChar(text: string){
      if(text[text.length - 1] == 'Z'){
         if(text.length == 1){
            return 'AA';
         }
         else{
            return String.fromCharCode(text.charCodeAt(0) + 1) + 'A';
         }
      }
      return text.slice(0, -1) + String.fromCharCode(text.charCodeAt(text.length - 1) + 1);
   }

   // eslint-disable-next-line @typescript-eslint/no-unused-vars
   async create (data: Data, params?: Params): Promise<Data> {
      throw new Errors.MethodNotAllowed();
   }

   // eslint-disable-next-line @typescript-eslint/no-unused-vars
   async update (id: NullableId, data: any, params?: Params): Promise<Data> {
      //Get Info
      throw new Errors.MethodNotAllowed();
   }

   // eslint-disable-next-line @typescript-eslint/no-unused-vars
   async patch (id: NullableId, data: Data, params: Params): Promise<Data> {
      throw new Errors.MethodNotAllowed();
   }

   // eslint-disable-next-line @typescript-eslint/no-unused-vars
   async remove(id: NullableId, params?: Params): Promise<Data> {
      throw new Errors.MethodNotAllowed();
   }

   formatExcelJsWorkSheet = (ws: ExcelJS.Worksheet) => {
      // ws.columns.forEach(function (column, i) {
      //     var maxLength = 0;
      //     column.eachCell!({ includeEmpty: false }, function (cell) {
      //         var columnLength = cell.value ? cell.value.toString().length : 10;
      //         if (columnLength > maxLength) {
      //             maxLength = columnLength;
      //         }
      //     });
      //     column.width = maxLength < 10 ? 15 : maxLength + 5;
      // });
      
      const count = ws.columnCount;
      const maxColumn = this.moveColumn('A', count); 
      const rowCount = ws.rowCount;
      ws.pageSetup.printArea = `A1:${maxColumn}${rowCount}`;
      ws.pageSetup.orientation = 'landscape';
      ws.pageSetup.fitToWidth = 1;
      ws.pageSetup.fitToPage = true;
      for(let i = 1; i <= 3; i++){
         if (ws.getRow(i)) {
            ws.getRow(i).font = { bold: true };
        }
      }
   }
}
