import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { LocalScoringReports } from './reports.class';
import hooks from './reports.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/educator/local-scoring/reports': LocalScoringReports & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/local-scoring/reports', new LocalScoringReports(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/local-scoring/reports');

  service.hooks(hooks);
}
