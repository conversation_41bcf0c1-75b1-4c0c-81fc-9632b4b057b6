import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead, dbRawReadReporting, dbRawReadSingle } from '../../../../../util/db-raw';
import { currentUid } from '../../../../../util/uid';
import { dbDateNow } from '../../../../../util/db-dates';
import { LOCAL_SCORING_FRAMEWORK, LOCAL_SCORING_PROFILE_FLAGS, LOCAL_SCORING_PROFILE_GROUP, LOCAL_SCORING_SCALES, LOCAL_SCORING_TQSI_MAP, LOCAL_SCORING_TW } from '../../../../../sql/local-scoring';

interface Data {}

interface ServiceOptions {}

interface IMbap {
  long_name: string,
  is_active: number,
  test_design_id:number,
  item_ids: string,
  batch_alloc_policy_id:number,
  batch_alloc_policy_item_slug: string,
  read_rules:string,
  tqsi_ids: string
}

export class LocalScoringFramework implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {ts_id, td_id, slug} = params.query;
      const uid = await currentUid(this.app, params);
      
      if(!ts_id || !td_id || !slug){
        throw new Errors.BadRequest("MISSING_PARAMS")
      }
      let localScoringFrameworks = [];
      const testWindows = await dbRawReadReporting(this.app, {ts_id}, LOCAL_SCORING_TW);
      if(testWindows.length == 0){
        throw new Errors.BadRequest("TEST_WINDOW_NOT_FOUND")
      }
      const tw_id = testWindows[0].test_window_id 
      
      const markingClaimBatchPolicies:IMbap[] = await dbRawReadReporting(this.app, {tw_id, slug}, LOCAL_SCORING_FRAMEWORK)
      if(markingClaimBatchPolicies.length == 0){
        throw new Errors.NotFound("MISSING_MBAP")
      }
      for (const markingClaimBatchPolicy of markingClaimBatchPolicies){
        const LocalScoringFramework = await this.convertMbapToLocalScoringFramework(markingClaimBatchPolicy)
        localScoringFrameworks.push(LocalScoringFramework)
      }
      
      
      return localScoringFrameworks;
    }
    return [];
  }
  async convertMbapToLocalScoringFramework(mbap :IMbap){
    //Parse data from string
    const mbapReadRule = JSON.parse(mbap.read_rules);
    const mbapItemIds = mbap.item_ids.split(",")
    const tqsiIds = mbap.tqsi_ids.split(",");
    const tasiMap = await dbRawRead(this.app, {mbapItemIds, tqsi_ids: tqsiIds}, LOCAL_SCORING_TQSI_MAP);
    const mbapSlugItemIdMap = new Map();
    for(let i = 0; i <  tasiMap.length; i++){
      mbapSlugItemIdMap.set(tasiMap[i].batch_alloc_policy_item_slug, tasiMap[i].item_id);
    }
    if(mbapReadRule && mbapReadRule.marking_window_items && mbapReadRule.marking_window_items.score_profile_groups.length > 0){
      let items = [];
      let totalWeight = 0;
      for (const score_profile_group of mbapReadRule.marking_window_items.score_profile_groups){
        // get first scale weight found
        const item_slug = score_profile_group.item_slug;
        const item_id = mbapSlugItemIdMap.get(item_slug);
        const scoreProfileGroup =  await dbRawReadSingle(this.app, {score_profile_group_id: score_profile_group.score_profile_group_id}, LOCAL_SCORING_PROFILE_GROUP);
        const caption = scoreProfileGroup.group_name;
        const weight: number = await this.getScoringItemWeight(mbapReadRule.read_rules.scales, score_profile_group.item_slug, score_profile_group.score_profile_ids)
        const flags = await dbRawRead(this.app, {score_profile_ids: score_profile_group.score_profile_ids}, LOCAL_SCORING_PROFILE_FLAGS);
        const topFlags = flags.map(flag => {return {scoreCode:flag.slug, caption:flag.slug }});
        const scoringScales = await dbRawRead(this.app, {score_profile_ids: score_profile_group.score_profile_ids}, LOCAL_SCORING_SCALES);
        let scales = [];
        for (let i = 0; i < scoringScales.length; i++){
          if(i==0){
            scales.push({
              scaleCode: scoringScales[i].scaleCode,
              caption: scoringScales[i].caption,
              maxScoreValue: scoringScales[i].scoreValue,
              scoreGuide_1: scoringScales[i].guide1_url,
              scoreGuide_2: scoringScales[i].guide2_url,
              scoreOptions: [{scoreCode: scoringScales[i].scoreCode, scoreValue: scoringScales[i].scoreValue}],
              weight: await this.getSingleScoringItemWeight(mbapReadRule.read_rules.scales, score_profile_group.item_slug, scoringScales[i].msp_id)
            })
          }
          else{
            if(scoringScales[i].msp_order == scoringScales[i - 1].msp_order){
              scales[scales.length - 1].maxScoreValue =  scoringScales[i].scoreValue;
              scales[scales.length - 1].scoreOptions.push({scoreCode: scoringScales[i].scoreCode, scoreValue: scoringScales[i].scoreValue})
            }
            else{
              scales.push({
                scaleCode: scoringScales[i].scaleCode,
                caption: scoringScales[i].caption,
                maxScoreValue: scoringScales[i].scoreValue,
                scoreOptions: [{scoreCode: scoringScales[i].scoreCode, scoreValue: scoringScales[i].scoreValue}],
                weight: await this.getSingleScoringItemWeight(mbapReadRule.read_rules.scales, score_profile_group.item_slug, scoringScales[i].msp_id)
              })
            }
          }
        }
        totalWeight += +weight;
        items.push({
          item_id,
          caption,
          weight: weight, 
          topFlags,
          scales
        })
      }
     
      return {
        totalWeight: totalWeight,
        scalesMap: mbapReadRule.scales_map || null,
        items
      }
    }
    else{
      throw new Errors.NotFound("MISSING_SCORING_PROFILE_GROUP")
    }
  }

  async getScoringItemWeight(scales: any[], item_slug: string, score_profile_ids: number[]){
    let itemWeight = 0;
    for (const scale of scales) {
      if(scale.item_slug == item_slug && score_profile_ids.includes(scale.score_profile_id)){
        itemWeight += +scale.weight;
      }
    }
    // Can either be integer or decimal
    return Math.round(itemWeight * 100) / 100;
  }

  async getSingleScoringItemWeight(scales: any[], item_slug: string, score_profile_id: number){
    for (const scale of scales) {
      if(scale.item_slug == item_slug && score_profile_id == scale.score_profile_id){
        return scale.weight;
      }
    }
    return 0;
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
     throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
     throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
     throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
     throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
     throw new Errors.MethodNotAllowed();
  }
}
