// Initializes the `public/educator/local-scoring-temp` service on path `/public/educator/local-scoring-temp`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { LocalScoringFramework } from './scoring-framework.class';
import hooks from './scoring-framework.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/educator/local-scoring/scoring-framework': LocalScoringFramework & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/local-scoring/scoring-framework', new LocalScoringFramework(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/local-scoring/scoring-framework');

  service.hooks(hooks);
}
