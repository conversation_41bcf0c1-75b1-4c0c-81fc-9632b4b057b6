// Initializes the `public/educator/pasi_statuses` service on path `/public/educator/pasi-statuses`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { PasiStatuses } from './pasi_statuses.class';
import hooks from './pasi_statuses.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/pasi-statuses': PasiStatuses & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/pasi-statuses', new PasiStatuses(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/pasi-statuses');

  service.hooks(hooks);
}
