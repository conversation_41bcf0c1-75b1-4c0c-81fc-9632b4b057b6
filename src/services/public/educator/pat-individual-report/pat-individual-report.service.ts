// Initializes the `pat-individual-report` service on path `/public/educator/individual-report`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { PatIndividualReport } from './pat-individual-report.class';
import hooks from './pat-individual-report.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/pat-individual-report': PatIndividualReport & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/pat-individual-report', new PatIndividualReport(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/pat-individual-report');

  service.hooks(hooks);
}
