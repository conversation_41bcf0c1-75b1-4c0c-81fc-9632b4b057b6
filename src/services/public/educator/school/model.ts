export interface IDBSchoolClass_View1 {
    id: number,
    group_id: number,
    schl_group_id: number,
    schl_dist_group_id: number,
    name: string,
    access_code: string,
    notes: string,
    group_type: string,
    is_grouping: number,
    semester_id: string,
    test_window_id: number,
    is_placeholder: number,
    is_fi: number,
    foreign_id: string,
    student_identifier_key: string,
    course_name_short: string,
    course_name_full: string,
    allow_ISR?:number,
}

export interface IDBUmCaptions {
    um_key: string,
    lang: string,
    use_case_slug: string,
    caption : string,
}