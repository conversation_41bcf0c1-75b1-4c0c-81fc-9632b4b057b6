import { DBD_U_ROLE_TYPES } from "../../../../../constants/db-extracts"

export const SQL_SCHL_STU = `
    -- find school students
    SELECT u.first_name 
        , u.last_name 
        , ur.uid 
        , um.value studentIdentificationNumber
    FROM user_roles ur 
    JOIN users u 
        on u.id = ur.uid 
    join user_metas um on um.uid = ur.uid
    JOIN schools s 
        on s.group_id = ur.group_id 	
    WHERE s.group_id = :schl_group_id 
    AND ur.role_type = '${DBD_U_ROLE_TYPES.schl_student}'
    AND ur.is_revoked != 1    	    
    AND um.key = "StudentIdentificationNumber"
;`

export const SQL_TW_USER_META = (keys? : string[]) => `
    -- find users meta from uid
    SELECT uid
        , key_namespace
        , \`key\`
        , value
        , test_window_id
    FROM tw_user_metas tum 
    WHERE tum.uid in (:uids)
    ${ keys ? `AND tum.\`key\` in (:keys)` : '' }    
;`
