// Initializes the `score-entry` service on path `/src/services/public/educator`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ScoreEntry } from './score-entry.class';
import hooks from './score-entry.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/score-entry': ScoreEntry & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/score-entry', new ScoreEntry(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/score-entry');

  service.hooks(hooks);
}
