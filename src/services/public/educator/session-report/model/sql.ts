export const SQL_TEST_DESIGN_BY_TS = ` /* SQL_TEST_DESIGN_BY_TS */
      select  twtar.test_design_id 
          , twtar.slug
          , twtar.id twtar_id 
          , twtar.is_custom
          , tf.id tf_id 
          , tf.lang 
          , tf.file_path 
          , twtt.is_perusal_allow
          , twtt.perusal_configs
          , twtar.perusal_type
          , twtar.perusal_offset_hours
          , twtar.perusal_duration_hours
          , twtar.perusal_date_start
          , twtar.perusal_date_end
          , twtt.is_local_score
          , twtt.is_download_results
          , twtt.is_bulk_print
          , tw.type_slug group_type
          , ts.closed_on
          , ts.date_time_start as ts_date_time_start
          , min(ta.started_on) as first_ta_started_on
          , CASE WHEN (tw.is_active = 1 and tw.date_start < now() and tw.date_end > now()) THEN 1 ELSE 0 END is_tw_current
      from school_class_test_sessions scts
      join test_sessions ts 
        on ts.id = scts.test_session_id
      join test_window_td_alloc_rules twtar
        on (
          twtar.type_slug = scts.slug
          and ts.test_window_id = twtar.test_window_id
          and twtar.is_custom = 0
        ) or (
          twtar.id = ts.twtdar_id
          and twtar.is_custom = 1
        )
      join test_forms tf
        on tf.test_design_id = twtar.test_design_id
      left join test_windows tw
        on tw.id = twtar.test_window_id
      left join test_window_td_types twtt
        on twtt.type_slug = twtar.type_slug
        and twtt.test_window_id is null
        and twtt.is_revoked = 0
      left join test_attempts ta
        on ta.test_session_id = ts.id
        and ta.is_invalid = 0
      where  scts.test_session_id = :test_session_id
      group by tf.id
`

export const SQL_TAQR_BY_TS = ` /* SQL_TAQR_BY_TS */
  select  tqr.question_id test_question_id 
      , tqr.question_label
      , twtar.test_window_id 
      , twtar.test_design_id 
      , tf.test_design_id response_test_design_id
      , twtar.slug
      , twtar.lang
      , twtar.id twtar_id 
      , ta.uid
      , ta.id attempt_id
      , ta.is_submitted
      , taqr.score 
      , IFNULL(tqr.score_points, taqr.weight) weight
      , taqr.response_raw 
      , taqr.updated_on 
  from school_class_test_sessions scts
  join test_sessions ts 
    on ts.id = scts.test_session_id
  join test_window_td_alloc_rules twtar
    on (
      twtar.type_slug = scts.slug
      and ts.test_window_id = twtar.test_window_id
      and twtar.is_custom = 0
    ) or (
      twtar.id = ts.twtdar_id
      and twtar.is_custom = 1
    )
  join test_question_register tqr
    on tqr.test_design_id = twtar.test_design_id
  join test_attempts ta 
    on ta.twtdar_id = twtar.id
    and ta.test_session_id = scts.test_session_id
    and ta.is_invalid = 0
  join test_forms tf
    on tf.id = ta.test_form_id
  join test_attempt_question_responses taqr 
    on taqr.test_attempt_id = ta.id 
    and taqr.is_invalid = 0
    and taqr.test_question_id = tqr.question_id
  where scts.test_session_id = :test_session_id
  group by taqr.id
  order by tqr.question_label -- todo: order should be from the form, might be cached in TQR (but doesnt have to be for this purpose)
`

export const SQL_TAQR_BY_TS_LOOSE = ` /* SQL_TAQR_BY_TS_LOOSE */
  select taqr.test_question_id test_question_id 
       , tq.question_label question_label
       , twtar.test_window_id 
       , twtar.test_design_id 
       , tf.test_design_id response_test_design_id
       , twtar.slug
       , twtar.lang
       , twtar.id twtar_id 
       , ta.uid
       , ta.id attempt_id
       , ta.is_submitted
       , taqr.score 
       , taqr.weight
       , taqr.response_raw 
       , taqr.updated_on 
  from school_class_test_sessions scts
  join test_sessions ts 
    on ts.id = scts.test_session_id
  join test_window_td_alloc_rules twtar
    on (
      twtar.type_slug = scts.slug
      and ts.test_window_id = twtar.test_window_id
      and twtar.is_custom = 0
    ) or (
      twtar.id = ts.twtdar_id
      and twtar.is_custom = 1
    )
  join test_attempts ta 
    on ta.twtdar_id = twtar.id
    and ta.test_session_id = scts.test_session_id
    and ta.is_invalid = 0
  join test_forms tf
    on tf.id = ta.test_form_id
  join test_attempt_question_responses taqr 
    on taqr.test_attempt_id = ta.id 
    and taqr.is_invalid = 0
  join test_questions tq
    on tq.id = taqr.test_question_id
  where scts.test_session_id = :test_session_id
  group by taqr.id
  order by tq.question_label 
`

export const SQL_TQ_BY_TS = ` /* SQL_TQ_BY_TS */
  select  tq.id
        , tq.config
        , tqr.tqsi_id
        , tqsi.is_human_scored
        , 1 weight -- todo weight should not be fixed to 1 for all items
  from test_question_register tqr
  join test_questions tq 
    on tq.id = tqr.question_id
  left join test_question_scoring_info tqsi 
    on tqsi.id = tqr.tqsi_id
  where tqr.test_design_id in (:test_design_ids)
`

export const SQL_TQ_BY_TQ_ID = ` /* SQL_TQ_BY_TQ_ID */
  select  tq.id
        , tq.config
        , NULL tqsi_id
        , 0 is_human_scored
        , 1 weight -- todo weight should not be fixed to 1 for all items
  from test_questions tq 
  where tq.id in (:item_ids)
`