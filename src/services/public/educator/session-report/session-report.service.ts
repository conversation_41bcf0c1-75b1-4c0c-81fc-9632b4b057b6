// Initializes the `public/educator/session-report` service on path `/public/educator/session-report`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { SessionReport } from './session-report.class';
import hooks from './session-report.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/session-report': SessionReport & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/session-report', new SessionReport(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/session-report');

  service.hooks(hooks);
}
