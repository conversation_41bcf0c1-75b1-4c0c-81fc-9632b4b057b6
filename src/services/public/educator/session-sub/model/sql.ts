import { DBD_U_ROLE_TYPES } from "../../../../../constants/db-extracts";

export const SQL_STU_RECORDS = `
  select ta.id as test_attempt_id
       , u.id as uid
       , tass.id as tass_id
  from users u
  left join test_attempts ta
    on ta.uid = u.id
  and ta.test_session_id = :test_session_id
  left join test_attempt_sub_sessions tass
    on tass.test_attempt_id = ta.id
    and tass.is_invalid != 1
  where u.id IN (:studentUids)
  group by u.id
`;

export const SQL_STUDENT_SUB_SESSIONS = `
  SELECT tass.* -- todo:ambig enumerate cols
       , CASE WHEN tass.sections_allowed IS NULL THEN tsss.sections_allowed ELSE tass.sections_allowed END as sections_allowed
       , tsss.slug as subsession_slug 
  from test_attempt_sub_sessions tass
  join test_session_sub_sessions tsss 
    on tass.sub_session_id = tsss.id
  where uid in (:studentUids)
    and tass.test_session_id = :test_session_id
    and is_invalid != 1;
`

export const SQL_SUB_SESSION_PRESETS = `
  SELECT 
      tsssp.sub_session_slug as slug
    , tsssp.caption
    , tsssp.sub_session_order as twtdar_order
    , tsssp.sections
  FROM test_session_sub_sessions_presets tsssp
  where tsssp.assessment_slug = :slug
`

// todo:DUPE seems to be deprecated
export const SQL_SUB_SESSION_SLUG = `
  select tsss.slug 
  from test_attempt_sub_sessions tass
  join test_session_sub_sessions tsss
    on tsss.id = tass.sub_session_id
  where tass.test_session_id = :test_session_id
    and tass.uid IN (:studentUids)
    and tass.sub_session_id = :subSessionId
    and tass.is_invalid != 1;
`

export const SQL_TASS_STUDENTS = `
  select ta.id as test_attempt_id
       , u.id as uid
       , tass.id as tass_id
  from users u
  left join test_attempts ta
    on ta.uid = u.id
    and ta.test_session_id = :test_session_id
  left join test_attempt_sub_sessions tass
    on tass.test_attempt_id = ta.id
    and tass.is_invalid != 1
  where u.id IN (:studentUids)
  group by u.id
`

export const SQL_EXISTING_UNLOCKED_ATTEMPT = `
SELECT 
    ta.uid, 
    sc.name as className
  FROM 
    mpt_dev.test_attempts ta
  JOIN mpt_dev.test_sessions ts 
    ON ts.id = ta.test_session_id 
    AND ts.is_closed = 0
  JOIN mpt_dev.school_class_test_sessions scts 
    ON scts.test_session_id = ts.id
  JOIN mpt_dev.school_classes sc 
    ON sc.id = scts.school_class_id
  JOIN mpt_dev.test_window_td_alloc_rules twtdar 
    ON twtdar.slug = scts.slug 
    AND twtdar.test_window_id = ts.test_window_id 
    AND twtdar.is_sample = 0
    and twtdar.is_custom = 0
  JOIN mpt_dev.user_roles ur
    ON ur.uid = ta.uid
    AND ur.group_id = sc.group_id
    AND ur.is_revoked = 0
    AND ur.role_type = '${DBD_U_ROLE_TYPES.schl_student}'
  WHERE 
    ta.uid IN (:studentUids)
    AND ta.is_invalid = 0
    AND scts.slug = :slug 
    AND ta.active_sub_session_id is not null;
  ;
`

export const SQL_CLASS_STUDENTS = (isForced:boolean = false) => `
  select ur.uid
  from school_classes sc
  join user_roles ur
    on ur.group_id = sc.group_id
    and ur.role_type = '${DBD_U_ROLE_TYPES.schl_student}'
    and ur.is_revoked = 0
  where sc.id = :school_class_id
  ${ isForced ? '' : `and sc.is_active = 1` }
`

export const SQL_CLASS_GUEST_STUDENTS = (isForced:boolean = false) => `
  select ur.uid
  from school_classes isc
  join school_classes_guest scg 
    on scg.invig_sc_group_id = isc.group_id 
    and scg.is_revoked != 1
  join school_classes sc 
    on sc.group_id = scg.guest_sc_group_id 
    ${ isForced ? '' : `and sc.is_active = 1` }
  join user_roles ur 
    on ur.group_id = sc.group_id 
    and ur.role_type = '${DBD_U_ROLE_TYPES.schl_student}' 
    and ur.is_revoked = 0
  where isc.id = :school_class_id
`

export const SQL_ACTIVE_SUB_SESSIONS = `
  select ts.id, ta.active_sub_session_id
  from mpt_dev.test_sessions ts
  join mpt_dev.test_attempts ta on ta.test_session_id = ts.id
  where ts.is_closed = 0
    and ts.is_cancelled = 0
    and ta.uid IN (:studentUids)
    and ta.active_sub_session_id IS NOT NULL;
`

export const SQL_COMPLETED_SUB_SESSIONS = `
  SELECT tass.is_submitted
       , scts.slug as session_type
       , tass.test_session_id
       , tsss.caption as sub_session_caption
       ,tsss.slug as sub_session_slug
  FROM mpt_dev.school_class_test_sessions scts
  join mpt_dev.test_attempt_sub_sessions tass 
    on tass.test_session_id = scts.test_session_id
  join mpt_dev.test_session_sub_sessions tsss 
    on tsss.id = tass.sub_session_id
  where scts.school_class_id = :classId
      and tass.uid in (:studentUids)
      and tass.is_submitted = 1
      AND tass.is_invalid != 1
`