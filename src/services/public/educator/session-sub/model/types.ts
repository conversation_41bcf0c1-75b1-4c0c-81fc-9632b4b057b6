export interface ISubSessionPartial{
  session_rescheduled:boolean,
  test_session_id:string,
  date_time_start:string,
  date_time_end?:string,
  pjSessionStartDate?:any,
  pjSessionEndDate?:any,
  asmtSlug:string,
}

export interface SubSessionRecord {
  id: number;
  order: number;
  slug: string;
  caption: string;
  date_time_start: string; // or Date, based on the format
  duration_hours: number;
  twtdar_order: number;
  is_last: boolean;
}
  
export interface StudentSubSessionState {
  uid: number;
  subSessions: SubSessionStateDetail[];
  attempt_id?: number;
  last_touch_on?: string; // or Date, based on the format
  time_ext_m?: number;
  active_sub_session_id?: number;
  section_index?: number;
  question_index?: number;
  question_caption?: string;
  is_submitted?: boolean;
  is_paused?: boolean;
  is_ta_unsubmit_pending?: boolean;
  hasOldTestForm?: boolean;
  canUpdateTestForm?: boolean;
  module_id?: number; // Adjust the type based on actual data type
  isAbsent?: boolean, // added afterwards
  started_on?: string, // added afterwards
  // Additional properties as per requirement
}

interface SubSessionDefBase {
  slug: string,
  caption: string,
  twtdar_order: number,
}
export interface SubSessionDefRecord extends SubSessionDefBase {
  sections: string, // json number[]
}
export interface SubSessionDef extends SubSessionDefBase {
  sections?: number[],
  is_last?:number; 
}

export interface SubSessionStateDetail {
  tass_id: number,
  subsession_slug: string,
  test_attempt_id: number,
  is_submitted: number,
  num_responses: number,
  started_on: string, 
  last_locked_on: string,
  last_touch_on: string,
  subtracted_time: number,
  sections_allowed: any[],
}

export interface SubSessionsInfo {
  studentStates: { [key: string]: StudentSubSessionState };
  subSessionRecords: SubSessionRecord[];
}
  