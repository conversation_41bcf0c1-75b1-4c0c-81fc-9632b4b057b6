import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawReadSingle } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

enum EUpdateOptions {
  ADD = 'ADD', 
  SET = 'SET',
}

export class SessionTime implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const {updateOption, updateVal} = <any> data;
    const ts_id = id
    let time_ext_m = 0;
    if (ts_id){
      if (updateOption == EUpdateOptions.ADD){
        const ts = await dbRawReadSingle(this.app, {ts_id:id}, `select id, time_ext_m from test_sessions ts where ts.id = :ts_id`)
        time_ext_m = (+ts.time_ext_m + (+updateVal))
      }
      else {
        time_ext_m = updateVal
      }
      await this.app.service('db/write/test-sessions').patch(id, {time_ext_m})
      return <any> {time_ext_m};
    }
    throw new Errors.BadRequest('NO_TS_ID')
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

}
