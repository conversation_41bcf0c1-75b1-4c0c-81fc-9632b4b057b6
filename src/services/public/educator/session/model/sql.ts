import { CLASS_TYPES, STUDENT_ROLE_TYPES } from "../../walk-in-students/model/constants"


export const SQL_UPDATE_TS_CLOSE = ` /* SQL_UPDATE_TS_CLOSE */
  UPDATE test_sessions
  SET is_closed = 1
    , closed_on = NOW()
  WHERE id IN (:ts_ids)
`

export const SQL_TS_INFO = `
  select scts.slug
        , ts.id
        , ts.test_session_group_id
        , ts.instit_group_id
        , ts.schl_group_id
        , ts.test_session_setup_id
        , ts.test_window_id
        , ts.name_custom
        , ts.delivery_format
        , ts.is_hidden
        , ts.date_time_start
        , ts.date_time_end
        , ts.is_cancelled
        , ts.cancelled_on
        , ts.access_code
        , ts.status
        , ts.is_closed
        , ts.time_shift_m
        , ts.time_ext_m
        , ts.is_paused
        , ts.is_seb_disabled
        , ts.is_access_code_enabled
        , ts.is_seb_hash_disabled
        , ts.is_soft_lock_enabled
        , ts.is_soft_lock_disabled
        , ts.closed_on
        , ts.created_on
        , ts.invigLang
        , ts.is_students_initing
        , ts.duration_m
        , ts.is_duration_enforced
        , ts.active_sections
        , ts.is_scheduled
        , ts.is_score_entry
        , ts.twtdar_id
  from test_sessions as ts
  join school_class_test_sessions as scts
    on scts.test_session_id = ts.id
  where ts.id = :test_session_id
`

export const SQL_TWTAR_DIRECT = ` /*SQL_TWTAR_DIRECT*/
  select twtar.id
        , twtar.slug
        , twtar.test_design_id
        , twtar.is_custom
        , twtar.subsession_meta
        , twtar.test_duration
        , twtar.test_date_start 
        , twtar.test_date_end
        , twtar.is_schedule_range
        , twtar.test_window_id 
        , twtar.resp_sheet_config
        , tw.date_start tw_date_start
        , tw.date_end tw_date_end
        , tw.is_allow_teacher_single_scan_upload tw_is_allow_teacher_single_scan_upload
        , tw.is_allow_teacher_bulk_scan_upload tw_is_allow_teacher_bulk_scan_upload
  from test_window_td_alloc_rules twtar 
  join test_windows tw
    on twtar.test_window_id = tw.id
  where twtar.id = :twtar_id
`

export const SQL_TS_TWTAR = `
  -- SQL_TS_TWTAR, < 0.1 sec
  select twtar.id
        , twtar.slug
        , twtar.test_design_id
        , twtar.test_duration
        , twtar.test_date_start 
        , twtar.test_date_end
        , twtar.is_schedule_range
        , twtar.test_window_id 
        , twtar.resp_sheet_config
        , tw.date_start tw_date_start
        , tw.date_end tw_date_end
        , tw.is_allow_teacher_single_scan_upload tw_is_allow_teacher_single_scan_upload
        , tw.is_allow_teacher_bulk_scan_upload tw_is_allow_teacher_bulk_scan_upload
  from test_sessions ts
  join school_class_test_sessions scts 
    on scts.test_session_id = ts.id
  join test_window_td_alloc_rules twtar 
    on twtar.type_slug = scts.slug 
    and twtar.test_window_id = ts.test_window_id 
    and twtar.is_custom = 0
  join test_windows tw
    on twtar.test_window_id = tw.id
  left join school_class_common_forms sccf 
	  on sccf.school_class_id = scts.school_class_id
    and sccf.type_slug = scts.slug
    and sccf.twtdar_id = twtar.id
    and sccf.is_revoked != 1
  where ts.id = :test_session_id
  and ((twtar.is_classroom_common_form = 0 and twtar.is_active = 1) or (sccf.id is not null))
  group by twtar.id;
`

export const SQL_PREV_TASS = `
  select tass.*, tsss.slug
  from test_attempt_sub_sessions tass
  join test_session_sub_sessions tsss
    on tsss.id = tass.sub_session_id
  where test_attempt_id  = :previousAttemptId
  and tass.is_invalid != 1
`

export const SQL_CURR_TASS = `
  select tass.*
  from test_attempt_sub_sessions tass
  where tass.test_attempt_id in (:currentTestAttemptIds)
    and tass.is_invalid != 1
`

export const SQL_ACTIVE_TASS_BY_TA = `
  SELECT tass.*
       , tsss.order as tsss_order
  FROM test_attempt_sub_sessions tass
  join test_session_sub_sessions tsss 
    on tsss.id = tass.sub_session_id
  where tass.test_attempt_id = :ta_id
`

export const SQL_TASR_BY_TA = `
  SELECT tasr.* 
  from test_attempt_scan_responses tasr
  join test_attempt_question_responses taqr 
    on tasr.taqr_id = taqr.id
  where taqr.test_attempt_id = :ta_id
    and tasr.is_discarded != 1
`

export const SQL_STU_ATTEMPT_STATUS = `
  SELECT ta.id
       , ta.uid
       , ta.is_paused
       , ta.is_soft_lock_disabled
       , ta.time_ext_m
  FROM test_attempts ta
  JOIN test_sessions as ts
    on ts.id = ta.test_session_id
  WHERE ts.id = :session_id
    and ta.active_sub_session_id IS NOT NULL
  group by ta.id
`
export const SQL_ACTIVE_TSSS_BY_SC = `
  select ts.id as test_session_id
       , ta.uid as student_uid
       , ta.twtdar_order
       , ta.active_sub_session_id
       , scts.school_class_id
       , scts.slug as session_type
       , tsss.caption as current_session
       , tsss.slug as sub_session_slug
       , tsss.order as sub_session_order
  from test_sessions ts
  join test_attempts ta 
    on ta.test_session_id = ts.id
  join school_class_test_sessions scts 
    on scts.test_session_id = ts.id
  join test_session_sub_sessions tsss 
    on tsss.id = ta.active_sub_session_id
  where ts.is_closed = 0
    and ts.is_cancelled = 0
    and scts.school_class_id = :school_class_id
    and ta.is_invalid != 1
    and ta.active_sub_session_id IS NOT NULL
`

export const SQL_ASMT_NO_TD_ORDER = `
  SELECT ac.is_no_td_order 
  FROM assessment_components ac
  JOIN school_class_test_sessions scts 
    ON ac.assessment_code = scts.slug
  JOIN test_sessions ts 
    ON scts.test_session_id = ts.id
  WHERE ts.id = :test_session_id
    AND ac.test_window_id = ts.test_window_id
`

// todo: we will likely need custom assessments here at some point, need to check how this is used downstream
// we know that it is used in api--abed/src/services/public/educator/session/session.class.ts
export const SQL_ALLOC_RULE_TW_SLUG = `
  select twtar.*
  from test_window_td_alloc_rules twtar
  where twtar.test_window_id = :test_window_id
    and twtar.type_slug = :slug
    and twtar.is_custom = 0
`

// todo: we will likely need custom assessments here at some point, need to check how this is used downstream
// we know that it is used in api--abed/src/services/public/educator/session/session.class.ts
export const SQL_ALLOC_RULE_ASMTCODE_TW_SLUG = `
  select twtdar.*
       , ac.is_no_td_order
  from test_window_td_alloc_rules twtdar
  left join assessment_components ac
    ON ac.assessment_code = twtdar.type_slug
    AND ac.test_window_id = twtdar.test_window_id
  where test_window_id = :test_window_id
    and type_slug = :slug
    and twtdar.is_custom = 0
`

export const SQL_SC_NAMESPACE = ` /* SQL_SC_NAMESPACE */
      select sc.key
      from school_classes sc
      where sc.schl_group_id = :schl_group_id
        and sc.is_active = 1
`

export const SQL_STU_LANG = `
  select ur.uid
       , um.id
       , um.value
  from user_roles ur
  left join user_metas um
    on um.uid = ur.uid
  and um.key_namespace = :key_namespace
  and um.key = :key
  where ur.group_id = :school_class_group_id
    and ur.role_type = 'schl_student'
    and ur.is_revoked != 1
    and um.value is null
  group by ur.uid
`

export const SQL_STU_LANG_GUEST = `
  select ur.uid
       , um.id
       , um.value
       , gscl.lang
  from school_classes sc
  join school_classes_guest scg 
    on scg.invig_sc_group_id = sc.group_id 
    and scg.is_revoked != 1
  join school_classes sc2 
    on sc2.group_id = scg.guest_sc_group_id
  join schools gscl 
    on gscl.group_id = sc2.schl_group_id
  join user_roles ur 
    on ur.group_id = scg.guest_sc_group_id 
    and ur.role_type = 'schl_student' 
    and ur.is_revoked != 1
  left join user_metas um
    on um.uid = ur.uid
    and um.key_namespace = :key_namespace
    and um.key = :key
  where sc.group_id = :school_class_group_id
    and sc.is_active = 1
    and um.value is null
  group by ur.uid
`

export const SQL_EXISTING_TS = `
  select ts.id
  from school_class_test_sessions scts
  join test_sessions ts 
    on ts.id = scts.test_session_id
  where ts.is_closed = 0
    and ts.is_cancelled = 0
    and scts.school_class_id = :school_class_id
`

export const SQL_MISSING_STU_SCH = `
  select ur.*
       , sc.schl_group_id
  from user_roles ur
  join school_classes sc
    on sc.group_id = ur.group_id
    and sc.id = :school_class_id
  left join user_roles ur2
    on ur2.uid = ur.uid
    and ur2.group_id = sc.schl_group_id
    and ur2.is_revoked != 1
  where ur.group_id = sc.group_id
    and ur.role_type = 'schl_student'
    and ur.is_revoked != 1
    and ur2.id is null
`

export const SQL_SC_TS = () => `
  select scts.school_class_id
       , scts.test_session_id
       , scts.slug
       , scts.caption
       , ts.date_time_start
       , ts.access_code
       , ts.test_window_id
       , twtdar.is_sample
  from school_class_test_sessions scts
  join test_sessions ts on ts.id = scts.test_session_id
  left join test_window_td_alloc_rules twtdar
     on twtdar.type_slug = scts.slug
    and twtdar.test_window_id = ts.test_window_id
    and twtdar.is_custom = 0
  where scts.school_class_id = :school_class_id
    and ts.is_cancelled = 0
    and ts.is_closed = 0
    and scts.slug = :slug
`

export const SQL_STU_ABSENT_BY_TS = `
    SELECT ta.uid
          , ifnull(tum_absent.value, '0') is_absent
    FROM test_attempts ta
    JOIN test_sessions as ts
      on ts.id = ta.test_session_id
    LEFT JOIN tw_user_metas tum_absent
      on tum_absent.uid = ta.uid
      and tum_absent.test_window_id = :test_window_id
      and tum_absent.asmt_type_slug = :asmt_type_slug
      and tum_absent.key = :twum_absent_key
      and tum_absent.key_namespace = :twum_namespace
    WHERE ts.id = :session_id
    group by ta.uid
`


export const SQL_COMPLETED_SUB_SESSION = (isSecure:boolean) => {
  return `
    SELECT tass.is_submitted
        , tass.uid as student_uid
        , scts.slug as session_type
        , tass.test_session_id
        , tsss.caption as sub_session_caption
        , tsss.slug as sub_session_slug
        , tsss.order as sub_session_order
    FROM school_class_test_sessions scts
    join test_attempt_sub_sessions tass
      on tass.test_session_id = scts.test_session_id
    join test_session_sub_sessions tsss
      on tsss.id = tass.sub_session_id
    join test_attempts ta
      on ta.id = tass.test_attempt_id
      and ta.is_invalid = 0
    join test_sessions ts
      on ts.id = ta.test_session_id
    join test_window_td_alloc_rules twdtar
      on twdtar.type_slug = scts.slug
      and twdtar.test_window_id = ts.test_window_id
      and twdtar.is_custom = 0
    where scts.school_class_id = :school_class_id
      and tass.is_submitted = 1
      and tass.is_invalid != 1
      ${isSecure ? `and twdtar.is_secured = 1` : ''}
    group by test_session_id,sub_session_caption, student_uid
  ;`
}

export const SQL_USER_METAS = ` /* SQL_USER_METAS */ 
  select um.uid
       , um.key_namespace
       , um.key
       , um.value
  from user_metas um
  where um.uid in (:walkinUIDs)
`

export const SQL_USER_METAS_ALT2 = ` /* SQL_USER_METAS_ALT2 */
  select um.id
        , um.uid
        , um.key_namespace
        , um.key
        , um.created_on
        , count(um.id) n
  from user_metas um
  where um.uid in (:uids)
  group by um.uid, um.key_namespace, um.key
`

export const SQL_TW_USER_METAS = `
  select um.uid
       , um.key_namespace
       , um.key
       , um.meta
       , um.value
  from tw_user_metas um
  where um.uid IN (:walkinUIDs)
`

export const SQL_SC_GUEST_EXISTING = `
  SELECT sc.id 
       , sc.group_id 
       , sc.access_code 
       , sc.group_type
  FROM school_classes_guest scg
  JOIN school_classes sci 
    ON  sci.group_id = scg.invig_sc_group_id 
    AND sci.group_id = :targetClassGroupID
    AND sci.is_active = 1
  JOIN school_classes sc 
    ON  sc.group_id = scg.guest_sc_group_id 
    AND sc.schl_group_id = :originSchlGroupID
    AND sc.group_type = '${CLASS_TYPES.WALKIN_GUEST}'
    AND sc.is_active = 1
  WHERE 
    scg.is_revoked != 1
`

export const SQL_SC_FROM_UID = (useSchlGroupID: boolean) => `
  SELECT ss.*
  FROM user_roles ur
  JOIN school_classes ss
    ON ur.group_id = ${useSchlGroupID ? 'ss.schl_group_id' : 'ss.group_id'}
  WHERE uid = :studentUid
    AND ur.is_revoked = 0
  LIMIT 1
`;



export const SQL_CURR_ATTEMPTS = `
  select ta.*
  from test_attempts ta
  where ta.test_session_id  = :test_session_id
    and ta.uid = :uid
    and ta.is_invalid != 1
    and ta.twtdar_order = :twtdar_order
`

export const SQL_PREV_ATTEMPTS = `
  select ta.uid
       , ta.id
       , ta.twtdar_order
       , ta.test_session_id
       , scts.slug
       , twtar.is_sample
       , twtar.is_secured
  from test_attempts ta
  join school_class_test_sessions scts
    on scts.test_session_id = ta.test_session_id
  join test_sessions ts
    on scts.test_session_id = ts.id
    and ts.test_window_id = :test_window_id
  join test_window_td_alloc_rules twtar
    on twtar.type_slug  = scts.slug
    and twtar.test_window_id = ts.test_window_id
    and twtar.is_custom = 0
  join test_windows tw
    on tw.id = ts.test_window_id
    and tw.date_end > now()
  where ta.uid = :uid
    and ta.twtdar_order = :twtdar_order
    and ta.is_invalid != 1
    and scts.slug = :slug
    and twtar.is_sample = 0
  order by ta.created_on
`

export const SQL_PREV_STU_ROLES = (enforceOneclassPerStudent:boolean) => `
  select ur.id
       , ur.uid
       , ur.group_id
       , role_type
  from user_roles ur 
  join school_classes sc 
    on sc.group_id = ur.group_id 
    and sc.schl_group_id = :schl_group_id
  join school_semesters ss 
    on ss.id = sc.semester_id and ss.id = :semesterId
  where ur.uid = :studentUid
    and ur.is_removed = 0
    and ur.role_type in ('${STUDENT_ROLE_TYPES.walk_in_student_role}' ${enforceOneclassPerStudent ? `, '${STUDENT_ROLE_TYPES.regular_student_role}'` : ''})
`

// ur.group_id can represent either schl_group_id or schl_class_group_id so below query can be used in variety of places
export const SQL_STU_ROLES = `
  select ur.id
  from user_roles ur 
  where ur.uid = :studentUid
    and ur.group_id = :group_id
    and ur.is_revoked = 0
    and ur.role_type = '${STUDENT_ROLE_TYPES.regular_student_role}'
`

export const SQL_GUEST_STU_ROLE_EXIST = `
  select sc.group_id AS guest_class_group_id
  from user_roles ur
  join school_classes sc 
    on sc.group_id = ur.group_id
    AND sc.group_type = '${CLASS_TYPES.WALKIN_GUEST}'
  join school_classes_guest scg 
    on sc.group_id = scg.guest_sc_group_id 
    and scg.invig_sc_group_id = :group_id
  where ur.uid = :studentUid
    and ur.is_revoked = 0
    and ur.role_type = '${STUDENT_ROLE_TYPES.regular_student_role}'
`

export const SQL_SC_CLASSES = `
  SELECT sc.group_id as schl_class_group_id
       , sc.schl_group_id
       , sc.semester_id
       , sc.id as sc_id
  FROM school_classes sc
  where group_id = :sch_class_group_id
`

// -- todo:why are we passing uid in twice like this
export const SQL_WALKIN_STUDENTS = (oneUID:boolean) => `
  SELECT urt.uid as id 
       , urt.uid as uid
       , um3.value as StudentIdentificationNumber
       , u.first_name
       , u.middle_name
       , u.last_name
       , sc.group_id
       , u.is_PASI_student
       , CAST('0' AS UNSIGNED) AS AcceptedByTeacher
       , CAST(urt.is_revoked AS UNSIGNED) AS RejectedByTeacher
       , um4.value as DateofBirth
       , (CASE WHEN schl_ur.id IS NOT NULL THEN 0 ELSE 1 END) AS is_guest
  FROM mpt_dev.school_classes sc
  JOIN mpt_dev.user_roles urt ON urt.group_id = sc.group_id
    AND urt.role_type = :role_type
    AND urt.is_removed = 0
  JOIN mpt_dev.users u ON urt.uid = u.id
  JOIN mpt_dev.user_metas um3 ON um3.uid = urt.uid
    AND um3.key = 'StudentIdentificationNumber'
  JOIN mpt_dev.user_metas um4 ON um4.uid = urt.uid
    AND um4.key = 'DateofBirth'
  LEFT JOIN mpt_dev.user_roles schl_ur
    ON  schl_ur.group_id = sc.schl_group_id
    AND schl_ur.uid = urt.uid
    AND schl_ur.is_revoked = 0
  WHERE sc.id = :classId 
    AND sc.is_active = 1
    ${oneUID ? `AND urt.uid = :uid` : ``}
  GROUP BY urt.uid
`
export const SQL_QUESTION_REQUIRING_SCAN = `
  SELECT tqr.question_id as item_id
    , tqsi.is_paper_response 
    , tqsi.batch_alloc_policy_item_slug 
    , tqsi.resp_sheet_config 
    , mbap.read_rules
    , tqsi.batch_alloc_policy_id
  FROM test_question_register tqr
  INNER JOIN test_questions tq ON tqr.question_id = tq.id 
  INNER JOIN test_question_scoring_info tqsi ON tqr.tqsi_id = tqsi.id 
  INNER JOIN marking_batch_alloc_policies mbap ON mbap.id = tqsi.batch_alloc_policy_id
  WHERE tqr.test_design_id in (:test_design_id)
    AND tqsi.is_paper_response = 1 
  ORDER BY student_question;
`
export const SQL_TEST_SESSION_DATE_MATRIX = (focus_on_custom_assessments:boolean) => ` /* SQL_TEST_SESSION_DATE_MATRIX */ 
  select 
    tw.date_start tw_date_start
    , twtar.test_date_start twtar_date_start
    , tw.date_end tw_date_end
    , twtar.test_window_id
    , twtar.type_slug type_slug
    , twtar.is_date_restricted 
    , twtar.is_sample 
    , twtar.is_secured
    , twtar.is_schedule_range
    , twtar.test_date_end twtar_test_date_end
    , twtar.is_scheduled
  from test_windows tw 
  join test_window_td_alloc_rules twtar 
    on tw.id = twtar.test_window_id 
    and twtar.is_custom = ${ focus_on_custom_assessments ? '1' : '0' }
  where tw.id = :test_window_id
  and twtar.type_slug = :slug
`

export const SQL_DEFAULT_ASMT_DESIGN_CAPTION = `  /* SQL_DEFAULT_ASMT_DESIGN_CAPTION */ 
  select twtt.caption_short asmt_design_caption 
  from school_class_test_sessions scts
  join test_sessions ts 
      on ts.id = scts.test_session_id
  left join test_window_td_types twtt
      on twtt.type_slug = scts.slug
      and twtt.test_window_id is null
      and twtt.is_revoked = 0
  where ts.id = :ts_id
`

export const SQL_TS_DATE_TIME_START = ` /* SQL_TS_DATE_TIME_START */
    select date_time_start 
    from test_sessions ts 
    where ts.id = :ts_id
`

export const SQL_DATE_EXCEPTION_TS = ` /* SQL_DATE_EXCEPTION_TS */
    select 
      tsa.date_start_override date_start_override
    from test_sessions ts 
    join school_class_test_sessions scts 
      on scts.test_session_id  = ts.id 
    join school_classes sc 
      on sc.id = scts.school_class_id 
    join schools s 
      on s.group_id = sc.schl_group_id 
    join twtdar_schools_allowed tsa 
      on tsa.school_id = s.id 
      and tsa.test_window_id = ts.test_window_id 
    where (scts.slug = tsa.type_slug OR tsa.is_all_type_slugs = 1)
      and tsa.is_date_override = 1 
      and tsa.is_revoked = 0
      and scts.test_session_id = :ts_id
`

export const SQL_DATE_EXCEPTION_ASMT_CODE_SC = ` /* SQL_DATE_EXCEPTION_ASMT_CODE_SC */
  select
    tsa.date_start_override date_start_override
  from school_classes sc
  join schools s
    on s.group_id = sc.schl_group_id
  join twtdar_schools_allowed tsa
    on tsa.school_id = s.id
    and tsa.is_date_override = 1
    and tsa.is_revoked = 0
    and (tsa.type_slug = :slug OR tsa.is_all_type_slugs = 1)
    and sc.schl_group_id = :schl_group_id
  group by tsa.id
`


export const SQL_TS_DATE_MATRIX = ` /* SQL_TS_DATE_MATRIX */
  select scts.test_session_id
      , tw.date_start tw_date_start
      , twtar.test_date_start twtar_date_start
      , tw.date_end tw_date_end
      , ts.test_window_id
      , scts.slug type_slug
      , twtar.is_date_restricted 
      , twtar.is_sample 
      , twtar.is_secured
      , twtar.is_schedule_range
      , twtar.test_date_end twtar_test_date_end
      , twtar.is_scheduled 
  from test_sessions ts 
  join school_class_test_sessions scts 
    on scts.test_session_id  = ts.id 
  join test_windows tw 
    on tw.id = ts.test_window_id 
  join test_window_td_alloc_rules twtar 
    on twtar.type_slug = scts.slug 
    and twtar.test_window_id = ts.test_window_id
  join school_classes sc 
    on sc.id = scts.school_class_id 
  join schools s 
    on s.group_id = sc.schl_group_id 
  where ts.id = :ts_id
`

export const SQL_SCTS_BY_TS = ` /* SQL_SCTS_BY_TS */
  select scts.id
       , scts.school_class_id
       , scts.test_session_id
       , scts.slug
       , scts.caption
  from school_class_test_sessions scts
  where scts.test_session_id = :ts_id
`

// todo: do we really need all of these fields in the context in which this query is currently used?
export const SQL_TWTAR_COMMON_FORM_BY_TW = (focus_on_custom_assessments:boolean) => ` /* SQL_TWTAR_BY_TW */
  select  twtdar.id
        , twtdar.type_slug
        , twtdar.slug
        , twtdar.long_name
        , twtdar.test_design_id
  from test_window_td_alloc_rules twtdar
  where twtdar.test_window_id = :tw_id
    and twtdar.is_classroom_common_form = 1
    and twtdar.is_custom = ${ focus_on_custom_assessments ? '1' : '0' }
`;
/* 
   excluded fields:


        , test_window_id
        , num_sub_sessions
        , is_active
        , is_secured
        , is_outside_window
        , order
        , user_metas_filter
        , generate_report
        , is_scheduled
        , form_code
        , is_date_restricted
        , cloned_from_id
        , subsession_meta
        , can_credential
        , is_questionnaire
        , is_sample
        , is_classroom_common_form
        , td_assigned_by_uid
        , td_assigned_on
        , lang
        , accomm_user_meta_constraint
        , assessment_def_id
        , component_slug
        , is_alternative
        , is_pipeline_exclude
        , max_condition_on_option
        , req_sd_lang
        , req_sd_lang_not
        , tqr_ovrd_td_id
        , test_duration
        , test_date_start
        , is_field_test
        , component_code
        , selection_order
        , is_school_allowed_strict
        , is_sample_time_enforced
        , hard_close_on
        , is_active_for_qa
        , is_dictionary_enabled
        , is_swap_risk
        , is_fi
        , is_schedule_range
        , test_date_end
        , cover_page_configs
        , styling_configs
        , print_configs
        , is_print
        , test_design_configs
        , is_active_for_scheduled_access
        , is_active_previously
        , signed_off_for_release_on
        , signed_off_for_release_by_uid
        , is_marking_req
        , is_active_for_auth
        , item_set_id
        , is_revoked
        , revoked_on
        , revoked_by_uid
        , cut_score_def_id
        , is_score_entry
        , exclusion_rule_def_id
        , is_session_name_excluded
        , is_session_duration_excluded
        , resp_sheet_config
        , is_twtar_longname_enforced
        , reporting_profile_id
        , perusal_date_start
        , perusal_date_end
        , perusal_type
        , perusal_offset_hours
        , perusal_duration_hours
        , sub_window_code
        , is_custom
        , custom_owner_uid
        , custom_owner_group_id
        , created_on
*/

// todo: dangerous to be pulling by slug vs type_slug, room for confusion
export const SQL_TWTAR_COMMON_FORM_BY_SLUG = (focus_on_custom_assessments:boolean) => ` /* SQL_TWTAR_BY_SLUG */
  select twtdar.id
       , twtdar.type_slug
       , twtdar.slug
       , twtdar.long_name
       , twtdar.test_design_id
  from test_window_td_alloc_rules twtdar
  where twtdar.test_window_id = :tw_id
    and twtdar.is_classroom_common_form = 1
    and twtdar.slug = :slug
    and twtdar.is_custom = ${ focus_on_custom_assessments ? '1' : '0' }
  group by twtdar.slug
`


export const SQL_SC_COMMON_FORM_BY_TWTAR = ` /* SQL_SC_COMMON_FORM_BY_TWTAR */
  select sccf.id
       , sccf.school_class_id
       , sccf.twtdar_id
       , sccf.test_form_id
       , sccf.type_slug
       , sccf.created_on
       , sccf.created_by_uid
  from school_class_common_forms sccf
  where sccf.school_class_id = :sc_id
    and sccf.twtdar_id = :twtar_id
    and sccf.is_revoked != 1
`


export const SQL_SC_COMMON_FORM_BY_TWTAR_SLUG = ` /* SQL_SC_COMMON_FORM_BY_TWTAR_SLUG */
  select sccf.id
       , sccf.school_class_id
       , sccf.twtdar_id
       , sccf.test_form_id
       , sccf.type_slug
       , sccf.created_on
       , sccf.created_by_uid
  from school_class_common_forms sccf
  where sccf.school_class_id = :sc_id
    and sccf.type_slug = :slug
    and sccf.is_revoked != 1
`

export const SQL_TF_ID_BY_TD = ` /* SQL_TF_ID_BY_TD */
  select tf.id
  from test_forms tf
  where tf.test_design_id = :td_id
`

