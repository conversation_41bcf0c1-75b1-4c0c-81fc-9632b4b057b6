import { SubSessionsInfo } from "../../session-sub/model/types";

export interface ISession {
  school_class_id: number,
  slug: string,
  caption: string,
  scheduled_time:ISessionTime,
  date_time_end?: string,
  isScheduled: boolean,
  sessionName?:string
  isRemovePrev?: boolean;
  is_fi?: boolean;
  duration_m?: number;
  capacity?: number;
  isCustomAssessment?: boolean;
  custom_item_set_id?: number;
  custom_test_design_id?: number; // deprecated for creation
}

export interface ActiveSubSession {
  test_session_id: number;
  student_uid: string;
  twtdar_order: number;
  active_sub_session_id: number;
  school_class_id: number;
  session_type: string;
  current_session: string;
  sub_session_slug: string;
  sub_session_order: number;
}

export interface CompletedSubSession {
  is_submitted: boolean;
  student_uid: string;
  session_type: string;
  test_session_id: number;
  sub_session_caption: string;
  sub_session_slug: string;
  sub_session_order: number;
}

export interface TsTwtar {
  is_schedule_range: boolean;
  id: number,
  slug: string,
  test_duration: number | null,
  test_date_start: string,
  test_date_end: string,
  tw_date_start: string,
  tw_date_end: string,
  test_design_id: number,
  resp_sheet_config: string,
  tw_is_allow_teacher_single_scan_upload: number,
  tw_is_allow_teacher_bulk_scan_upload: number,
}

export type ISessionTime = string[];

export interface IWalkInStudentState { 
  students: any[]; 
  student_metas: any[]; 
  tw_student_metas: any[]; 
}

export interface ISessionState {
  is_paused: boolean;
  id: number;
  name_custom: string;
  date_time_start: string; // or Date, based on the format
  is_schedule_range: boolean;
  time_ext_m: number;
  duration_m: number;
  isDurationEnforced: boolean;
  activeSubSessions: ActiveSubSession[];
  completedSubSessions: CompletedSubSession[];
  subSessions?: SubSessionsInfo;
  is_no_td_order: boolean; // Adjust the type based on the actual data type
  asmt_slug: string;
  test_attempt_test_status: any; // Adjust the type based on the actual data type
  walkin_students: IWalkInStudentState;
  is_soft_lock_enabled?: number; //deprecated
  is_soft_lock_disabled: number;
  date_exceptions: any[];
  twtar_test_date_start: string;
  twtar_test_date_end: string;
  tw_date_start: string;
  tw_date_end: string;
  tw_resp_sheet_config?: string;
  is_scan_session: boolean;
  is_score_entry?: number,
  questionsForScan: IQuestionScan[],
  tw_is_allow_teacher_single_scan_upload?: boolean,
  tw_is_allow_teacher_bulk_scan_upload?: boolean,
}

export interface IQuestionScan {
  item_id: number;
  is_paper_response: string;
  batch_alloc_policy_item_slug: string;
  resp_sheet_config?: string;
  read_rules: string;
  question_label: string;
}

export interface ISessionCreationReq {
  school_class_id:number, 
  slug:string, 
  caption:string, 
  sessionName?:string, 
  duration_m?:number, 
  isRemovePrev?:boolean, 
  isScheduled:boolean, 
  is_fi:boolean, 
  capacity:number, 
  isCustomAssessment:boolean, 
  custom_item_set_id?:number,
  custom_test_design_id?:number, // deprecated for creation
  date_time_end?:string,
  scheduled_time?:string[],
}

export interface ISessionCreationIntermediateCtx {
  isForced:boolean,
  isAnySecured:boolean,
  isAnyScheduled:boolean,
  isScheduled:boolean,
  defaultDateStart:string | undefined,
  isScoreEntry:number,
  created_by_uid: number,
  delivery_format:string,
  /// input carry over
  school_class_group_id:number,
  scheduled_time?:string[] , // not seeing enough trnasformation for this to be safely converted to date_time_start
  date_time_start: string | any, // using this to store database timestamp... probably dont need to
  date_time_end?:string,
  custom_test_design_id?:number,
  ///....
  schl_group_id:number, 
  test_window_id:number, 
  twtar_id?:number, 
  slug:string, 
  access_code: string,
  name_custom?: string,
  // from the created test session record
  test_session_id:any, // todo: id, date_time_start, 
  // caption:string, 
  // sessionName?:string, 
  // duration_m?:number, 
  // isRemovePrev?:boolean, 
  // isScheduled:boolean, 
  // is_fi:boolean, 
}

export interface ISessionCreationResponse {
  // note: removed the array of subSessionRecords, I don't think these are used in the response
  test_session_id: number,
  date_time_start: string,
  school_class_id:number,
  slug:string,
  caption:string,
  access_code:string,
  name_custom: string,
  asmt_design_caption: string,
}