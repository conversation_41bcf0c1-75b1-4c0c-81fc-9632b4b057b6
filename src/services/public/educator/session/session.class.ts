import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { DBD_U_GROUP_TYPES, DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { generateAccessCode } from '../../../../util/secret-codes';
import { currentUid } from '../../../../util/uid';
import { dbDateOffsetHours, dbDateNow, dbDateSetDateTime } from '../../../../util/db-dates';
import { Knex } from 'knex';
import { EDeliveryOption } from '../../../db/schemas/test_sessions.schema';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../util/db-raw';
import { techReadinessFlags, techReadiPrimaryGoMode, techReadiJuniorGoMode, techReadiGoMode, techReadiOSSLTGoMode, techReadinessFlagsRequired } from '../../../../constants/g9-constants';
import { AssessmentType, getTestControllerGroupId } from '../../bc-admin-coordinator/test-window/assessment-type';
import moment from 'moment';
import logger from '../../../../logger';
import { randArrEntry } from '../../../../util/random';
import { FLAGS, isABED } from '../../../../util/whiteLabelParser';
import { TWUM_KEYS, TWUM_NAMESPACES } from '../../school-admin/student-tw/util/util.class';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';
import { ISession, ActiveSubSession, CompletedSubSession,ISessionTime, TsTwtar, ISessionState, IQuestionScan, ISessionCreationReq, ISessionCreationResponse, ISessionCreationIntermediateCtx } from './model/types';
import { SQL_ACTIVE_TASS_BY_TA, SQL_ACTIVE_TSSS_BY_SC, SQL_ALLOC_RULE_ASMTCODE_TW_SLUG, SQL_ALLOC_RULE_TW_SLUG, SQL_ASMT_NO_TD_ORDER, SQL_COMPLETED_SUB_SESSION, SQL_DATE_EXCEPTION_TS, SQL_DEFAULT_ASMT_DESIGN_CAPTION, SQL_EXISTING_TS, SQL_MISSING_STU_SCH, SQL_QUESTION_REQUIRING_SCAN, SQL_SC_COMMON_FORM_BY_TWTAR, SQL_SC_COMMON_FORM_BY_TWTAR_SLUG, SQL_SC_NAMESPACE, SQL_SC_TS, SQL_SCTS_BY_TS, SQL_STU_ABSENT_BY_TS, SQL_STU_ATTEMPT_STATUS, SQL_STU_LANG, SQL_STU_LANG_GUEST, SQL_TASR_BY_TA, SQL_TEST_SESSION_DATE_MATRIX, SQL_TS_DATE_MATRIX, SQL_TS_DATE_TIME_START, SQL_TS_INFO, SQL_TS_TWTAR, SQL_UPDATE_TS_CLOSE, SQL_USER_METAS, SQL_USER_METAS_ALT2, SQL_TWTAR_COMMON_FORM_BY_TW, SQL_TWTAR_COMMON_FORM_BY_SLUG, SQL_TF_ID_BY_TD, SQL_DATE_EXCEPTION_ASMT_CODE_SC, SQL_TWTAR_DIRECT } from './model/sql';
import { StudentSubSessionState, SubSessionsInfo } from '../session-sub/model/types';
import { BadRequest } from '@feathersjs/errors';
import { CUSTOM_ASSESSMENT_TWTAR_TYPE_SLUG } from '../custom-assessments/model/const';

let STANDARD_TIMEZONE = 'America/Toronto';

interface Data {
  school_class_id: number,
  slug: string,
  caption: string,
  scheduled_time:ISessionTime,
  isScheduled: boolean,
  isRemovePrev?: boolean;
}

interface ServiceOptions { }

export class Session implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    const whiteLabel = this.app.get('whiteLabel');
    if (isABED(whiteLabel as FLAGS)) {
      STANDARD_TIMEZONE = 'America/Edmonton';
    }
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    // list of active sessions for a class
    const { school_class_group_id,school_class_id,slug } = (<any>params).query;
    // long polling
    if (params) {
      const activeSubSessions = await this.getActiveSubSessions(school_class_id);
      const completedSubSessions = await this.getCompletedSubSessions(school_class_id, true);
      const sctsRecords =  <any[]>await this.app.service('db/read/school-class-test-sessions').find({ query: { school_class_id,slug }, paginate: false });
      let subSessions = [];
      if(sctsRecords.length > 0){
        const sctsRecord = sctsRecords[0];
        subSessions =  <any[]>await this.app.service('db/read/test-session-sub-sessions').find({ query: {test_session_id:sctsRecord.test_session_id }, paginate: false });
      }

      return <any>{
        activeSubSessions,
        completedSubSessions,
        subSessions
      }
    }
    throw new Errors.BadRequest();
  }

  private async getSchoolGroupByClassroomId(school_class_id:number){
    const schoolClassRecord = <any>await this.app
      .service('db/read/school-classes')
      .get(school_class_id);
    return schoolClassRecord.schl_group_id;
  }

  async create (data: Data, params?: Params): Promise<Data> {
    const { school_class_group_id }: { school_class_group_id:number } = (<any>params).query;
    if (params) {
      const created_by_uid = await currentUid(this.app, params); // should be currentUid
      return <any> this.createSessionForEducator(created_by_uid, <any> data, school_class_group_id);
      // return this.createSessionBc(created_by_uid, data, AssessmentType.GRAD);
    }
    return <any>{};
  }

  private async generateSessionAccessCode(schl_group_id?:number){
    if (!schl_group_id) {
      throw new Errors.GeneralError('MISSING_GROUP_ID');
    }
    let access_code:string = '';
    for (let i = 0; i < 100; i++) {
      access_code = generateAccessCode(4);
      const previousCodeMatches = <Paginated<any>>await this.app
        .service('db/write/test-sessions')
        .find({
          query: {
            access_code,
            schl_group_id,
            is_cancelled: 0,
            is_closed: 0,
          }
        });
      if (previousCodeMatches.total === 0) {
        break;
      }
    }
    return access_code;
  }

  async getActiveTestWindowForClass(school_class_id:number, test_ctrl_group_id?:number){
    // logger.silly({ school_class_id, test_ctrl_group_id })
      // const testWindowRecords = await dbRawRead(this.app, [school_class_id, test_ctrl_group_id], `
      //   select tw.id, tw.date_start, tw.date_end, tw.is_active
      //   from school_classes sc
      //   join school_semesters ss on ss.id = sc.semester_id
      //   join test_windows tw on tw.id = ss.test_window_id
      //   where sc.id = ?
      //     and tw.test_ctrl_group_id = ?
      //     and tw.is_active = 1
      // ;`)
      const schoolClassRecord = <any>await this.app
        .service('db/read/school-classes')
        .get(school_class_id);

      const semester_id = schoolClassRecord.semester_id
      //get the class's semester
      const semesterRecord = <any>await this.app
        .service('db/read/school-semesters')
        .get(semester_id);

      const test_window_id = semesterRecord.test_window_id;

      const testWindowRecord =  await this.app
        .service('db/read/test-windows')
        .get(test_window_id);

      if (!testWindowRecord){
        throw new Errors.GeneralError('NO_ACTIVE_WINDOW')
      }
      return testWindowRecord.id;
  }

  async ensureSchoolUserRoles(school_class_id: number, created_by_uid:any){
    const missingStudentSchoolRecords = await dbRawRead(this.app, 
      {school_class_id}, 
      SQL_MISSING_STU_SCH
    );

    await Promise.all( missingStudentSchoolRecords.map( async record =>{
      await this.app
          .service('db/write/user-roles')
          .create({
            role_type: 'schl_student',
            uid:record.uid,
            group_id:record.schl_group_id,
            created_on: dbDateNow(this.app),
            created_by_uid: created_by_uid
          });
    }))
  }

  async ensureStudentLang(school_class_group_id: number, schl_group_id: number) {
    //get the key_namespace from schl_group_id
    // TODO find a better way to get the key_namespace for NBED
    let findKeyNamespace  = await dbRawRead(this.app, {schl_group_id}, SQL_SC_NAMESPACE)
    const foundKeyNamespace = findKeyNamespace.map(schlclass => schlclass.key)[0]
    let key_namespace = 'eqao_dyn' // todo: why is this hardcoded
    if(foundKeyNamespace === "NBED_UserId"){key_namespace = 'nbed_dyn'} // todo: why is this hardcoded
    const key = 'Lang'
    const studentLangRecords = await dbRawRead(this.app, 
      {key_namespace, key, school_class_group_id}, 
      SQL_STU_LANG
    );

    if (studentLangRecords.length) {
      const schoolRecord = <Paginated<any>>await this.app
        .service('db/read/schools')
        .find({
          query: {
            group_id: schl_group_id
          }
        })
      const school = schoolRecord.data[0];
      let value = school.lang || 'en';
      value = value.toLowerCase()
      for (let i = 0; i < studentLangRecords.length; i++) {
        const record = studentLangRecords[i];
        const uid = record.uid;
        try {
          await this.app
            .service('db/write/user-metas')
            .create({
              uid,
              key_namespace,
              key,
              value
            });
        }
        catch(e){
          console.log('UM filtering issue')
        }
      }
      const uids = studentLangRecords.map(ur => ur.uid)
      await this.removeRecentlyAddedDuplicateUserMetaRecords(uids)
    }

    // todo:CONSISTENCY these results were never going anywhere, so commented out for now
    //ensure guest class lang
    // const guestStudentLangRecords = await dbRawRead(this.app, 
    //   {key_namespace, key, school_class_group_id}, 
    //   SQL_STU_LANG_GUEST
    // );

  }

  private async removeRecentlyAddedDuplicateUserMetaRecords(uids:number[]){
    // using write db because looking to find recently added ones
    const existingRecords = await dbRawWrite(this.app, {uids},  SQL_USER_METAS_ALT2)
    // todo: remove... 
  }

  private async closeExistingSessions(school_class_id:number){
    const existingSessions = await dbRawRead(this.app, 
      {school_class_id}, 
      SQL_EXISTING_TS
    );
    const testSessionsToClose = existingSessions.map(r => r.id);
    if (testSessionsToClose.length) {
      const ts_ids = testSessionsToClose
      await dbRawWrite(this.app, {ts_ids}, SQL_UPDATE_TS_CLOSE);
    }
  }


  async validateTechnicalReadinessStatus(schl_group_id?:number,slug?:string) {
    //overriding this to allow session creation
    return;
    if (!slug) {
      throw new Errors.GeneralError('MISSING_ASMT_CODE');
    }
    const db:Knex = this.app.get('knexClientRead');
    const getData = async (props: any[], query: string) => {
      const res = await db.raw(query, props);
      return <any[]>res[0];
    }
    let isGoMode = techReadiGoMode;
    if(slug === "PRIMARY_OPERATIONAL"){
      isGoMode = techReadiPrimaryGoMode;
    }
    if(slug === "JUNIOR_OPERATIONAL"){
      isGoMode = techReadiJuniorGoMode;
    }
    if(slug === "G9_OPERATIONAL"){
      isGoMode = techReadiGoMode;
    }
    if(slug === "OSSLT_OPERATIONAL"){
      isGoMode = techReadiOSSLTGoMode
    }
    const techReadiRecords = await dbRawRead(this.app, [isGoMode, schl_group_id], `
      select *
      from (
        select uid, group_id, value
        from user_group_checklist ugc
        where slug = ?
          and group_id = ?
          and value != 0
        group by uid, group_id
      ) ugc
    ;`);
    const goMode = techReadiRecords[0]
    if (!goMode|| goMode.value === 0) {
      throw new Errors.Forbidden('TECH_READI_PENDING')
    }
  }

  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed;
  }
  public async getActiveSubSessions(school_class_id: number) {
    const active_sub_sessions:ActiveSubSession[] = await dbRawRead(this.app, 
      {school_class_id}, 
      SQL_ACTIVE_TSSS_BY_SC
    );
    return active_sub_sessions;
  }
  public async getCompletedSubSessions(school_class_id: number, isSecure: boolean = true) {
    const completed_sub_sessions:CompletedSubSession[] = await dbRawRead(
      this.app, 
      {school_class_id}, 
      SQL_COMPLETED_SUB_SESSION(isSecure),
    );
    return completed_sub_sessions;
  }

  // for pausing
  async get(id: Id, params?: Params): Promise<Data> {
    const { school_class_group_id, school_class_id } = (<any>params).query;
    // logger.silly({ school_class_id });
    // long polling
    if (params) {
      // return this.getSessionInfo(id, school_class_id);
      // const testSession = await this.app.service('db/read/test-sessions').get(id);
      const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
      await this.ensureSchool_class_common_forms(id, userInfo.uid)
      await this.ensureSchoolUserRoles(school_class_id, userInfo.uid);
      return this.ensureSessionInit(id, school_class_id, userInfo.uid, false);
    }
    throw new Errors.BadRequest();
  }

  async getTwtarDirect(twtar_id:Id){
    const twtarRecords:TsTwtar[] = await dbRawRead(this.app, {twtar_id}, SQL_TWTAR_DIRECT)
    return twtarRecords[0];
  }
  async getTsTwtar(test_session_id:Id){
    const twtarRecords:TsTwtar[] = await dbRawRead(
      this.app, 
      {test_session_id}, 
      SQL_TS_TWTAR
    )
    return twtarRecords[0];
  }
  /** @deprecated  now we count directly with the checkQuestionForScan Function*/ 
  async getTwtt(twtar_id:Id){
    const twttRecord =  await dbRawRead(this.app, [twtar_id], `
    select twtt.is_scan
    from test_window_td_types twtt
    where twtt.type_slug = ?
    ;`)
    return twttRecord[0]
  }
  
  async checkQuestionsForScan(test_design_id:number): Promise<IQuestionScan[]>{
    return await dbRawRead(this.app,{test_design_id},SQL_QUESTION_REQUIRING_SCAN)
  }

  async ensureSessionInit(test_session_id:Id, school_class_id:number, created_by_uid?:number, isForced?:boolean){
    const testSession = await dbRawReadSingle(this.app, {test_session_id}, SQL_TS_INFO)
    let twtarRecord;
    if (testSession.twtdar_id){
      twtarRecord = await this.getTwtarDirect(testSession.twtdar_id) || {};
    }
    else {
      twtarRecord = await this.getTsTwtar(test_session_id) || {};
    }

    // const twttRecord = await this.getTwtt(twtarRecord.slug)
    let test_design_id = testSession.custom_test_design_id || twtarRecord.test_design_id;
    const questionsForScan = await this.checkQuestionsForScan(test_design_id);
    const date_exceptions = await this.getDateException(test_session_id);
    const activeSubSessions = await this.getActiveSubSessions(school_class_id);
    const completedSubSessions = await this.getCompletedSubSessions(school_class_id, true);
    const subSessions = await this.app.service('public/educator/session-sub').getSubSessions(testSession.id, isForced);
    const walkin_students = await this.app.service('public/educator/walk-in-students').getWalkinStudents(school_class_id);
    if (subSessions && !testSession.twtdar_id){
      await this.applyTsStudentMeta(subSessions.studentStates, test_session_id);
    }
    const is_no_td_order = await this.getIsNoTdOrder(testSession.id);
    const test_attempt_test_status = await this.getTestAttemptTestStatus(<number>test_session_id)

    const duration_m = +(testSession.duration_m || twtarRecord.test_duration || 0);
    const isDurationEnforced = duration_m > 0

    for (const question of questionsForScan){
      if(question.read_rules && question.resp_sheet_config){
        const parsedConfig = JSON.parse(question.resp_sheet_config);
        question.question_label =  parsedConfig?.taskNumber ;
        delete question.resp_sheet_config;
      }
    }
    const response:ISessionState = {
      is_paused: (testSession.is_paused == 1),
      id: testSession.id,
      name_custom: testSession.name_custom,
      // is_soft_lock_enabled: testSession.is_soft_lock_enabled,
      is_soft_lock_disabled: testSession.is_soft_lock_disabled,
      date_time_start: (testSession.date_time_start),
      is_schedule_range: twtarRecord.is_schedule_range,
      time_ext_m: +(testSession.time_ext_m || 0),
      // is score entry flag should be pulled from twtar
      duration_m,
      isDurationEnforced,
      activeSubSessions,
      completedSubSessions,
      subSessions,
      is_no_td_order,
      asmt_slug: testSession.slug,
      test_attempt_test_status,
      walkin_students,
      date_exceptions,
      twtar_test_date_start: twtarRecord.test_date_start,
      twtar_test_date_end: twtarRecord.test_date_end,
      tw_date_start: twtarRecord.tw_date_start,
      tw_date_end: twtarRecord.tw_date_end,
      tw_resp_sheet_config: twtarRecord.resp_sheet_config ? JSON.parse(twtarRecord.resp_sheet_config): null,
      tw_is_allow_teacher_single_scan_upload: !!twtarRecord.tw_is_allow_teacher_single_scan_upload,
      tw_is_allow_teacher_bulk_scan_upload: !!twtarRecord.tw_is_allow_teacher_bulk_scan_upload,
      is_scan_session: questionsForScan.length > 0,
      questionsForScan,
      is_score_entry: testSession.is_score_entry,
    }

    return <any>response;
  }

  async applyTsStudentMeta(studentStates:{ [key: string]: StudentSubSessionState }, test_session_id:Id){
    const tw_session_student_meta = await this.getTestSessionStudentMetaData(<number>test_session_id)
    for (let student_meta of tw_session_student_meta){
      const studentState = studentStates[student_meta.uid];
      if (studentState){
        studentState.isAbsent = (student_meta?.is_absent == '1')
      }
    }
  }

  async getTestSessionStudentMetaData(session_id: number) {

    const {test_window_id, asmt_type_slug} = await this.app
      .service('public/educator/student/session')
      .getTsUmMeta(session_id)

    const queryProps = {
      session_id,
      test_window_id,
      asmt_type_slug,
      twum_namespace: TWUM_NAMESPACES.ASMT_SESSION,
      twum_absent_key: TWUM_KEYS.ABSENT,
    }

    return <{uid: number, is_absent:string}[]> await dbRawRead(this.app, 
      queryProps, 
      SQL_STU_ABSENT_BY_TS
    )
  }

  async getTestAttemptTestStatus(session_id: number) {
    return dbRawRead(this.app, {session_id}, SQL_STU_ATTEMPT_STATUS)
  }

  async validateSessionDateTime(ts_id:number, date_time_start:string) {
    // get test window and  twtar from test session
    // note: this query is a bit messy as it will list some duplicate data (tw_dates repeat for every date exception) and it can be more complicated if there is a field test
    const testSessionDateMatrix = await dbRawReadSingle(this.app, {ts_id}, SQL_TS_DATE_MATRIX);
    // find date exceptions for schools
    const date_exceptions = await this.getDateException(ts_id)
    // identify if this is a sample session or field test session
    if(testSessionDateMatrix.is_sample == 0 && testSessionDateMatrix.is_scheduled == 1){
      // check standard date range for the twtar target (take min/max)
      const new_date_time_start = new Date(date_time_start)
      // Check exception exact match
      const is_exact_exception_date = await this.isExactDateException(new_date_time_start, testSessionDateMatrix, date_exceptions)
      if(!is_exact_exception_date){
      if(!testSessionDateMatrix.is_schedule_range || testSessionDateMatrix.is_schedule_range == 0){
        throw new BadRequest("SCHEDULE_CHANGE_NOT_ALLOWED")
      }
      await this.verifySessionDateStart(new_date_time_start, testSessionDateMatrix, date_exceptions)
      await this.verifySessionDateEnd(new_date_time_start, testSessionDateMatrix)
      }
    }
  }

  async getDateException(ts_id: number | string){
    return await dbRawRead(this.app, {ts_id}, SQL_DATE_EXCEPTION_TS)
  }

<<<<<<< HEAD
  async getDateExceptionSessionCreation(slug: string, schl_group_id: number, test_window_id: number){
    return await dbRawRead(this.app, {slug, schl_group_id, test_window_id}, `
    select 
    tsa.date_start_override date_start_override
    from school_classes sc 
    join schools s 
      on s.group_id = sc.schl_group_id 
    join twtdar_schools_allowed tsa 
      on tsa.school_id = s.id 
      and tsa.is_date_override = 1 
      and tsa.is_revoked = 0
      and (tsa.type_slug = :slug OR tsa.is_all_type_slugs = 1)
      and sc.schl_group_id = :schl_group_id
      and tsa.test_window_id = :test_window_id
    group by tsa.id
  `)
||||||| a3b376e2d0
  async getDateExceptionSessionCreation(slug: string, schl_group_id: number){
    return await dbRawRead(this.app, {slug, schl_group_id}, `
    select 
    tsa.date_start_override date_start_override
    from school_classes sc 
    join schools s 
      on s.group_id = sc.schl_group_id 
    join twtdar_schools_allowed tsa 
      on tsa.school_id = s.id 
      and tsa.is_date_override = 1 
      and tsa.is_revoked = 0
      and (tsa.type_slug = :slug OR tsa.is_all_type_slugs = 1)
      and sc.schl_group_id = :schl_group_id
    group by tsa.id
  `)
=======
  async getDateExceptionSessionCreation(slug: string, schl_group_id: number){
    return await dbRawRead(this.app, {slug, schl_group_id}, SQL_DATE_EXCEPTION_ASMT_CODE_SC)
>>>>>>> release/abed
  }

  async isExactDateException(new_date_time_start: Date, testSessionDateMatrix: any, date_exceptions: any[]){
    for (const date_exception of date_exceptions){
      if(date_exception.date_start_override && new Date(date_exception.date_start_override).getTime() == new Date(new_date_time_start).getTime()){
        return true;
      }
    }
    if(testSessionDateMatrix.twtar_date_start && new Date(testSessionDateMatrix.twtar_date_start).getTime() == new Date(new_date_time_start).getTime()){
      return true;
    }
    return false;
  }

  async verifySessionDateStart(new_date_time_start: Date, testSessionDateMatrix: any, date_exceptions: any[]){
    let min_dates_allowed: number[] = [];
    for (const date_exception of date_exceptions){
      if(date_exception.date_start_override){
        min_dates_allowed.push(new Date(date_exception.date_start_override).getTime()) 
      }
    }
    if(testSessionDateMatrix.tw_date_start){
      if(testSessionDateMatrix.twtar_date_start){
        min_dates_allowed.push(Math.max(new Date(testSessionDateMatrix.tw_date_start).getTime(), new Date(testSessionDateMatrix.twtar_date_start).getTime()));
      }
      else{
        min_dates_allowed.push(new Date(testSessionDateMatrix.tw_date_start).getTime());
      }
      }
      if(min_dates_allowed.length > 0){
        // get min of date allowed array
        const min_date_allowed =new Date(Math.min.apply(null,min_dates_allowed));
        if(new_date_time_start < min_date_allowed){
          throw new BadRequest("SCHEDULED_TIME_OUT_OF_TW_RANGE")
      }
    }
  }

  async verifySessionDateEnd(new_date_time_start: Date, testSessionDateMatrix: any){
    if(testSessionDateMatrix.tw_date_end){
      const tw_date_end = new Date(testSessionDateMatrix.tw_date_end)
      if(new_date_time_start > tw_date_end){
        throw new BadRequest("SCHEDULED_TIME_OUT_OF_TW_RANGE")
      }
    }
    if(testSessionDateMatrix.is_schedule_range == 1 && testSessionDateMatrix.twtar_test_date_end){
      const twtar_test_date_end = new Date(testSessionDateMatrix.twtar_test_date_end)
      if(new_date_time_start > twtar_test_date_end){
        throw new BadRequest("SCHEDULED_TIME_OUT_OF_TW_RANGE")
      }
    }
  }

  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    const { school_class_group_id } = (<any>params).query;
    const { is_paused, date_time_start } = <any>data;
    const payload: any = {};
    if (is_paused == undefined && date_time_start == undefined){
      throw new BadRequest("MISSING_PARAMS")
    }
    if (is_paused !== undefined) {
      payload.is_paused = is_paused ? 1 : 0;
    }
    if(date_time_start !== undefined && id) {
      // if (await getSysConstNumeric(this.app, 'DISABLE_TS_DATE_MOD')){
      //   throw new Errors.Forbidden('http://localhost:4200/')
      // }
      await this.validateSessionDateTime(+id, date_time_start)
      const scheduledStandardStartTimeMoment = moment.tz(date_time_start, STANDARD_TIMEZONE);
      const scheduledUTCStartTimeMoment = scheduledStandardStartTimeMoment.utc();
      const scheduledUTCStartTime = scheduledUTCStartTimeMoment.format('YYYY-MM-DDTHH:mm:ss');
      payload.date_time_start = dbDateSetDateTime(this.app, 0, scheduledUTCStartTime);
    }
    await this.app.service('db/write/test-sessions').patch(id, payload);
    return data;
  }

  // #2021-11-16-unclear-conflict
  async closeAttemptAndSubmitSubsessions(test_session_id:number){

    //get all the test attempt subsession in the test session
    const activeStudentsTestAttemptSubsession = await dbRawRead(this.app, [test_session_id],
      `SELECT * FROM mpt_dev.test_attempt_sub_sessions
        where test_session_id= ?
          and is_invalid != 1
    `);

    // submit test attempt sub session if student have touch it
    activeStudentsTestAttemptSubsession.forEach( async attemptsubsession =>{
      if(attemptsubsession.started_on != null || attemptsubsession.last_touch_on != null){
        if(attemptsubsession.is_submitted != 1){
          this.app.service('db/write/test-attempt-sub-sessions')
              .patch(attemptsubsession.id,{is_submitted: 1} )
        }
      }
    })

    //get all the test attempt
    const activetestAttempts = await dbRawRead(this.app, [test_session_id],
      `SELECT * FROM mpt_dev.test_attempts
        where test_session_id= ?
          and is_invalid != 1
    ;`)

    activetestAttempts.forEach( async attempt =>{
      //get the test attempt subsession order
      const testAttemptSubsessions = await dbRawRead(this.app, 
        {ta_id: attempt.id},
        SQL_ACTIVE_TASS_BY_TA
      );
      let lastTestAttemptSubsession:any;
      testAttemptSubsessions.forEach( tass =>{
        if(!lastTestAttemptSubsession || lastTestAttemptSubsession.tsss_order < tass.tsss_order ){
          lastTestAttemptSubsession = tass
        }
      })
      // 1. submit test attempt if student touch it and last test attemptsubsession is submitted
      // 2. only close the test attempt if the student have never touch it
      // 3. do nothing if the student have touch the test but does not touch the last sub session
      if((attempt.started_on != null || attempt.last_touch_on != null) && lastTestAttemptSubsession && lastTestAttemptSubsession.is_submitted == 1){
        this.app.service('db/write/test-attempts')
          .patch(attempt.id,{is_submitted: 1, is_closed: 1, closed_on: dbDateNow(this.app), submitted_test_session_id: test_session_id} )
      }else if (attempt.started_on == null && attempt.last_touch_on == null) {
        this.app.service('db/write/test-attempts')
            .patch(attempt.id,{is_closed: 1, closed_on: dbDateNow(this.app)} )
      }

      // mark the tasr (scanning) records that were uploaded as test_session_submitted
      if (attempt.twtdar_order === 0) {
        const tasrRecords = await dbRawRead(this.app, 
          {ta_id:attempt.id},
          SQL_TASR_BY_TA
        );

        await Promise.all(tasrRecords.map(async (tasr) => {
          await this.app
            .service('db/read/test-attempt-scan-responses')
            .db()
            .where('id', tasr.id)
            .update({
              is_test_session_submitted: 1
            });
        }));
      }
    })
  }
  // end of #2021-11-16-unclear-conflict

  async remove(id: NullableId, params?: Params): Promise<any> {
    const { school_class_group_id,is_session_completed, is_score_entry, sc_id } = (<any>params).query;
    const isScoreEntry = (is_score_entry == 'true');

    if (id && params) {
      this.closeAttemptAndSubmitSubsessions(<number>id);
      const uid = await currentUid(this.app, params);
      await this.app
        .service('db/write/test-sessions')
        .patch(id, {
          is_closed: 1,
          closed_on: dbDateNow(this.app),
        });
      if(is_session_completed){
        // NO LONGER AUTO-GENERATING REPORTS!
      }
      // if (isScoreEntry){
      //   await this.closeScoreEntrySession(id, sc_id)
      // } 
      return <any>{ id }
    }
    throw new Errors.BadRequest('MISSING_TEST_SESSION_ID');
  }

  async getIsNoTdOrder(test_session_id: Id) {
    const isNoTdOrderRes = await dbRawRead(this.app, {test_session_id}, SQL_ASMT_NO_TD_ORDER)
    if(isNoTdOrderRes?.length) {
      return isNoTdOrderRes[0].is_no_td_order;
    }
    return undefined;
  }

  async createSessionForEducator(created_by_uid: number, data: ISession, school_class_group_id: number, isForced:boolean=false, ) {

    const ctxInput:Partial<ISessionCreationReq> = data;
    const ctx:Partial<ISessionCreationIntermediateCtx> = {};
    const ctxOutput:Partial<ISessionCreationResponse> = {};

    // validate required fields
    if(!ctxInput.school_class_id) { throw new Errors.BadRequest('MISSING_CLASS_ID');}
    if(!ctxInput.slug) {            throw new Errors.BadRequest('MISSING_ASMT_CODE');}

    // instantiate ctx
    ctx.created_by_uid = created_by_uid;
    ctx.isForced = isForced;
    ctx.school_class_group_id = school_class_group_id
    ctx.delivery_format = EDeliveryOption.SCHOOL;
    ctx.schl_group_id = await this.getSchoolGroupByClassroomId(ctxInput.school_class_id);
    ctx.test_window_id = await this.getActiveTestWindowForClass(ctxInput.school_class_id); // default for now
    ctx.access_code = await this.generateSessionAccessCode(ctx.schl_group_id); // these access codes are not actually used
    ctx.slug = ctxInput.slug;

    // create session (based on custom vs. standard)
    if (ctxInput.isCustomAssessment){
      await this.setupCustomAssessmentCtx(ctx, ctxInput);
      await this.createSessionAndSubsessionRecords(ctx, ctxInput);
    }
    else {
      await this.determineTwtarRecord(ctx, ctxInput)
      if(!ctx.isForced) {                      await this.validateMultipleSessions(ctxInput.school_class_id, ctx.slug)}
      if (ctx.isAnySecured && !ctx.isForced) { await this.validateTechnicalReadinessStatus(ctx.schl_group_id, ctx.slug) }
      await this.determineTwtarSessionDate(ctx, ctxInput)
      await this.createSessionAndSubsessionRecords(ctx, ctxInput)
      await this.ensureSessionDatePostCreation(ctx) // can override `testSessionRecord.date_time_start`
      await this.ensureSchool_class_common_forms(ctx.test_session_id, ctx.created_by_uid);
    } 

    // output 
    await this.packageSessionCreationResponse(ctxOutput, ctx, ctxInput);
    return ctxOutput;
  }

  async packageSessionCreationResponse(ctxOutput:Partial<ISessionCreationResponse>, ctx:Partial<ISessionCreationIntermediateCtx>, ctxInput:Partial<ISessionCreationReq>){
    ctxOutput.test_session_id = ctx.test_session_id 
    ctxOutput.date_time_start = ctx.date_time_start 
    ctxOutput.school_class_id = ctxInput.school_class_id
    ctxOutput.slug = ctx.slug,
    ctxOutput.caption = ctxInput.caption,
    ctxOutput.access_code = ctx.access_code,
    ctxOutput.name_custom = ctxInput.sessionName,
    this.determineSessionUIDisplayInfo(ctxOutput, ctx);
  }

  async determineSessionUIDisplayInfo(ctxOutput:Partial<ISessionCreationResponse>, ctx:Partial<ISessionCreationIntermediateCtx>){
    const recordCaption = await dbRawReadSingle(this.app, { ts_id: ctx.test_session_id }, SQL_DEFAULT_ASMT_DESIGN_CAPTION) // this is a hack to help support the UI getting an accurate caption to display
    ctxOutput.asmt_design_caption = recordCaption.asmt_design_caption;
  }

  async setupCustomAssessmentCtx(ctx:Partial<ISessionCreationIntermediateCtx>, ctxInput:Partial<ISessionCreationReq>){

    // for now, constants 
    ctx.date_time_start = dbDateNow(this.app);
    ctx.date_time_end = undefined;
    ctx.isScheduled = false;
    ctx.scheduled_time = undefined;
    ctx.isAnyScheduled = false;
    ctx.defaultDateStart = undefined;
    ctx.isScoreEntry = 0;
    ctx.isForced = true;
    ctx.isAnySecured = false;
    ctx.isAnyScheduled = false;
    ctx.defaultDateStart = undefined;
    ctx.isScoreEntry = 0;
    ctx.slug = CUSTOM_ASSESSMENT_TWTAR_TYPE_SLUG;

    // ensure that the custom twtar record exists
    if (!(ctx.created_by_uid && ctx.test_window_id && ctxInput.custom_item_set_id)){
      throw new Errors.BadRequest('MISSING_CUSTOM_ASMT_PARAMS');
    }
    const twtar = await this.app.service('public/educator/custom-assessments')
      .ensureCustomTwtarRecord(ctx.created_by_uid, ctx.test_window_id, ctxInput.custom_item_set_id)
    
    ctx.twtar_id = twtar.twtar_id;
    ctx.custom_test_design_id = twtar.test_design_id;
  }

  async determineTwtarRecord(ctx:Partial<ISessionCreationIntermediateCtx>, ctxInput:Partial<ISessionCreationReq>){
    const {test_window_id} = ctx;
    const {slug} = ctxInput;
    // retrieve twtar records
    const test_window_td_alloc_rules = await dbRawRead(this.app, {test_window_id, slug}, SQL_ALLOC_RULE_TW_SLUG);
    // interpret available test window allocation rule entries
    ctx.isAnySecured = false;
    ctx.isAnyScheduled = false;
    // ctx.defaultDateStart;
    ctx.isScoreEntry = 0;
    test_window_td_alloc_rules.forEach(rule => {
      if (rule.is_secured){
        ctx.isAnySecured = true;
      }
      if (rule.is_scheduled==1){
        ctx.isAnyScheduled = true;
        ctx.defaultDateStart = rule.test_date_start
      }
      if (rule.is_score_entry == 1) {
        ctx.isScoreEntry = 1;
      }
    })
  }

  async determineTwtarSessionDate(ctx:Partial<ISessionCreationIntermediateCtx>, ctxInput:Partial<ISessionCreationReq>){
    // this does not apply to custom assessments
    const focus_on_custom_assessments = false; // todo: need a clean way to set this, not assuming its going to come cleanly from the query parameters, have to be careful

    // todo: there is no reason to overload these variables with sometimes a an array of dates and othertimes a single string
    // refactor: simplify scheduled dates logic during scheduled creation (no need for overloaded variable here)
    // https://bubo.vretta.com/vea/project-management/vretta-project-notes/vea-abed/-/issues/11288

    // check to see if this is a scheduled session
    // const {assessments} = await this.app.service('public/educator/class-assessments').getAllocWithOverridesByClass(school_class_id, slug)
    // const allocRule = assessments[0];
    // let date_time_start:string | any = allocRule.test_date_start; //todo: the date provided by the UI should be validated against the options
    ctx.date_time_end = ctxInput.date_time_end
    ctx.scheduled_time = ctxInput.scheduled_time
    if (ctx.isAnyScheduled && ctx.scheduled_time) {
      // todo: the date provided by the UI should be validated against the options
      ctx.date_time_start = ctxInput.scheduled_time
    }
    else {
      ctx.date_time_start = dbDateNow(this.app); // todo: also no need to voerload this variable with either date now vs. a simple string, since we act on it later
      ctx.date_time_end = undefined;
    }

    const {test_window_id, schl_group_id} = ctx;
    const {slug} = ctxInput;
    if (slug && schl_group_id){ // can only run date exceptions for known assessment codes
      const date_exceptions = await this.getDateExceptionSessionCreation(slug,  schl_group_id)
      const testSessionDateMatrix = await dbRawReadSingle(this.app, {test_window_id, slug}, SQL_TEST_SESSION_DATE_MATRIX(focus_on_custom_assessments));
      const is_exact_exception_date = await this.isExactDateException(ctx.date_time_start, testSessionDateMatrix, date_exceptions)
      if(!is_exact_exception_date && ctx.isScheduled){
          const new_date_time_start = new Date(ctx.date_time_start)
          // console.log({ new_date_time_start, date_exceptions, testSessionDateMatrix });
          await this.verifySessionDateStart(new_date_time_start, testSessionDateMatrix, date_exceptions)
          await this.verifySessionDateEnd(new_date_time_start, testSessionDateMatrix)
      }
<<<<<<< HEAD
      if (rule.is_scheduled==1){
        isAnyScheduled = true;
        defaultDateStart = rule.test_date_start
      }
      if (rule.is_score_entry == 1) {
        isScoreEntry = 1;
      }
    })
    if (isAnySecured && !isForced) {
      await this.validateTechnicalReadinessStatus(schl_group_id,slug);
    }
    //test_window_td_alloc_rules
    const date_exceptions = await this.getDateExceptionSessionCreation(slug,  schl_group_id, test_window_id)
    const testSessionDateMatrix = await dbRawReadSingle(this.app, {test_window_id, slug}, `
      select 
        tw.date_start tw_date_start
        , twtar.test_date_start twtar_date_start
        , tw.date_end tw_date_end
        , twtar.test_window_id
        , twtar.type_slug type_slug
        , twtar.is_date_restricted 
        , twtar.is_sample 
        , twtar.is_secured
        , twtar.is_schedule_range
        , twtar.test_date_end twtar_test_date_end
        , twtar.is_scheduled
      from test_windows tw 
      join test_window_td_alloc_rules twtar 
        on tw.id = twtar.test_window_id 
      where tw.id = :test_window_id
      and twtar.type_slug = :slug
    `);
    const is_exact_exception_date = await this.isExactDateException(date_time_start, testSessionDateMatrix, date_exceptions)
    
      if(!is_exact_exception_date){
        const new_date_time_start = new Date(date_time_start)
        await this.verifySessionDateStart(new_date_time_start, testSessionDateMatrix, date_exceptions)
        await this.verifySessionDateEnd(new_date_time_start, testSessionDateMatrix)
||||||| a3b376e2d0
      if (rule.is_scheduled==1){
        isAnyScheduled = true;
        defaultDateStart = rule.test_date_start
      }
      if (rule.is_score_entry == 1) {
        isScoreEntry = 1;
      }
    })
    if (isAnySecured && !isForced) {
      await this.validateTechnicalReadinessStatus(schl_group_id,slug);
    }
    //test_window_td_alloc_rules
    const date_exceptions = await this.getDateExceptionSessionCreation(slug,  schl_group_id)
    const testSessionDateMatrix = await dbRawReadSingle(this.app, {test_window_id, slug}, `
      select 
        tw.date_start tw_date_start
        , twtar.test_date_start twtar_date_start
        , tw.date_end tw_date_end
        , twtar.test_window_id
        , twtar.type_slug type_slug
        , twtar.is_date_restricted 
        , twtar.is_sample 
        , twtar.is_secured
        , twtar.is_schedule_range
        , twtar.test_date_end twtar_test_date_end
        , twtar.is_scheduled
      from test_windows tw 
      join test_window_td_alloc_rules twtar 
        on tw.id = twtar.test_window_id 
      where tw.id = :test_window_id
      and twtar.type_slug = :slug
    `);
    const is_exact_exception_date = await this.isExactDateException(date_time_start, testSessionDateMatrix, date_exceptions)
    
      if(!is_exact_exception_date){
        const new_date_time_start = new Date(date_time_start)
        await this.verifySessionDateStart(new_date_time_start, testSessionDateMatrix, date_exceptions)
        await this.verifySessionDateEnd(new_date_time_start, testSessionDateMatrix)
=======
>>>>>>> release/abed
    }
  }

  async createSessionAndSubsessionRecords( ctx:Partial<ISessionCreationIntermediateCtx>, ctxInput:Partial<ISessionCreationReq> ) {

    const testSessionGroup = await this.app
      .service('db/write/u-groups')
      .create({
        group_type: DBD_U_GROUP_TYPES.mpt_test_session,
        created_by_uid: ctx.created_by_uid,
      });
    
    const test_session_group_id = testSessionGroup.id;

    const testSessionRecord = await this.app
      .service('db/write/test-sessions')
      .create({
        test_session_group_id,
        is_access_code_enabled: true,
        date_time_end:   ctxInput.date_time_end,
        name_custom:     ctxInput.sessionName,
        duration_m:      ctxInput.duration_m || null,
        capacity:        ctxInput.capacity || 0,
        test_window_id:        ctx.test_window_id,
        schl_group_id:         ctx.schl_group_id,
        twtdar_id:             ctx.twtar_id, // usually blank, unless custom assessment
        delivery_format:       ctx.delivery_format,
        date_time_start:       ctx.date_time_start,
        access_code:           ctx.access_code,
        is_scheduled:          ctx.isScheduled ? 1 : 0,
        is_score_entry:        ctx.isScoreEntry,
      })

    const test_session_id = testSessionRecord.id;
    ctx.date_time_start = testSessionRecord.date_time_start; // temporay: since it is using db object for `now()`
    ctx.test_session_id = test_session_id
    
    await this.app
      .service('db/write/school-class-test-sessions')
      .create({
        school_class_id: ctxInput.school_class_id,
        test_session_id,
        slug: ctx.slug,
        caption: ctxInput.caption,
    });

    let subSessionRecords
    if(ctx.isScheduled && ctx.scheduled_time){
      subSessionRecords = await this.app.service('public/educator/session-sub').initSubSessionRecords(test_session_id, ctx.scheduled_time)
    }
    else{
      subSessionRecords = await this.app.service('public/educator/session-sub').initSubSessionRecords(test_session_id, undefined, ctxInput.is_fi)
    }

    return {testSessionRecord, subSessionRecords};
  }

  // can override `testSessionRecord.date_time_start`
  async ensureSessionDatePostCreation(ctx:Partial<ISessionCreationIntermediateCtx>){
    const ts_id = ctx.test_session_id
    const {isAnyScheduled, defaultDateStart} = ctx;
    // validate date 
    try {
      if (isAnyScheduled && defaultDateStart){
        // todo: when check for invalid dates, using mysql engine for the validation, could be done more efficiently
        const tsDateRecord = await dbRawReadSingle(this.app, {ts_id }, SQL_TS_DATE_TIME_START)
        if (tsDateRecord && !tsDateRecord.date_time_start){
          await this.app
            .service('db/write/test-sessions')
            .patch(ts_id, {
              date_time_start: defaultDateStart,
            })
            ctx.date_time_start = defaultDateStart
        }
      }
    }
    catch(e){}
  }

  /** 
   * Checks if multiple sessions are allowed for the same slug
   * @throws {Errors.BadRequest} If there's an ongoing sample or live assessment within same slug
   */
  public async validateMultipleSessions(school_class_id: Id, slug?: string) {
    if (!slug) {
      throw new Errors.GeneralError('MISSING_ASMT_CODE');
    }
    const ALLOW_ONE_SESSION_PER_SLUG  = await getSysConstNumeric(this.app, "ALLOW_ONE_SESSION_PER_SLUG", true);  
    const classes_sessions = await dbRawRead(this.app, 
      {school_class_id, slug}, 
      SQL_SC_TS()
    );

    if(!ALLOW_ONE_SESSION_PER_SLUG || classes_sessions.length === 0) {
      return // Successful Validation
    }

    for (const session of classes_sessions) {
      if (session.is_sample) {
        throw new Errors.BadRequest('ONGOING_SAMPLE_ASSESSMENT');
      } else {
        throw new Errors.BadRequest('ONGOING_LIVE_ASSESSMENT');
      }
    }
  }

  async createSessionForSchool(created_by_uid: number, data: ISession, assessmentType: AssessmentType, testWindowId?: number) {
    const test_ctrl_group_id = getTestControllerGroupId(assessmentType); // hard coded to G9 for now
      const delivery_format = EDeliveryOption.SCHOOL;
      const { school_class_id, slug, caption, isRemovePrev, isScheduled } = data;

      // check to see if this is a scheduled session
      let date_time_start
      if (isScheduled) {
        date_time_start = dbDateSetDateTime(this.app, 4 ,data.scheduled_time[0] || data.scheduled_time[1])// todo: this time zone offset cannot be fixed to 4: https://bubo.vretta.com/vea/project-management/vretta-project-notes/vea-abed/-/issues/3142
      }
      else {
        date_time_start = dbDateNow(this.app);
      }

      const schl_group_id = await this.getSchoolGroupByClassroomId(school_class_id);
      // await this.ensureStudentLang(schl_group_id); // todo: this can be long running!
      let test_window_id;
      if (testWindowId) {
        test_window_id = testWindowId;
      } else {
        const activeTestWindows = await this.app.service('public/bc-admin-coordinator/test-window').findCurrentTestWindows(assessmentType);
        if (activeTestWindows.length === 0) throw new Error('No active test windows.');
        test_window_id = activeTestWindows[0].id;
      }

      // let test_window_id = await this.getActiveTestWindowForClass(school_class_id, test_ctrl_group_id); // default for now
      logger.silly('creating test session for test window %s', test_window_id)
      const test_window_td_alloc_rules = await dbRawRead(this.app, 
        {test_window_id, slug}, 
        SQL_ALLOC_RULE_ASMTCODE_TW_SLUG
      );

      // const is_no_td_order = !!test_window_td_alloc_rules[0].is_no_td_order;  //may need this
      let isAnySecured = false;
      test_window_td_alloc_rules.forEach(rule => {
        if (rule.is_secured){
          isAnySecured = true;
        }
      })
      if (isAnySecured) {
        await this.validateTechnicalReadinessStatus(schl_group_id,slug);
      }

      const testSessionGroup = await this.app
        .service('db/write/u-groups')
        .create({
          group_type: DBD_U_GROUP_TYPES.mpt_test_session,
          created_by_uid,
        });
      const test_session_group_id = testSessionGroup.id;
      let access_code: string = await this.generateSessionAccessCode(schl_group_id);

      const testSession = await this.app
        .service('db/write/test-sessions')
        .create({
          test_session_group_id,
          test_window_id,
          schl_group_id,
          delivery_format,
          date_time_start,
          access_code,
          is_access_code_enabled: true,
        })
      const test_session_id = testSession.id;
      logger.silly('created test session id %s', test_session_id);
      await this.app
        .service('db/write/school-class-test-sessions')
        .create({
          school_class_id,
          test_session_id,
          slug,
          caption,
        });
       let subSessionRecords
       if(isScheduled){
        subSessionRecords = await this.app.service('public/educator/session-sub').initSubSessionRecords(test_session_id, data.scheduled_time)
       }
       else{
        logger.silly('init subsession');
        subSessionRecords = await this.app.service('public/educator/session-sub').initSubSessionRecords(test_session_id, undefined)
       }

      return <any>{
        ...subSessionRecords,
        test_session_id,
        date_time_start: testSession.date_time_start,
        school_class_id,
        slug,
        caption,
        access_code
      };
  }

  //seems like a depricated function
  async getSessionInfo(id: Id, school_class_id: number, isSecure: boolean = true) {
    const testSession = await this.app.service('db/read/test-sessions').get(id);
      const activeSubSessions = await this.getActiveSubSessions(school_class_id);
      const completedSubSessions = await this.getCompletedSubSessions(school_class_id, isSecure);
      const schl_group_id = await this.getSchoolGroupByClassroomId(school_class_id);
      // await this.ensureStudentLang(schl_group_id);
      const subSessions = await this.app.service('public/educator/session-sub').getSubSessions(id);
      return <any>{
        is_paused: (testSession.is_paused == 1),
        id:testSession.id,
        date_time_start: (testSession.date_time_start),
        activeSubSessions,
        completedSubSessions,
        subSessions,
      }
  }

  async ensureSchool_class_common_forms(test_session_id:any, created_by_uid:any){
    console.log({test_session_id})
    const testSession = await this.app.service('db/read/test-sessions').get(test_session_id);
    const schoolClassTestSession = await dbRawRead(this.app, {ts_id: testSession.id}, SQL_SCTS_BY_TS);

    const sctsRecord = schoolClassTestSession[0]
    const asmtTypeSlug = sctsRecord.slug
    const schoolClassID = sctsRecord.school_class_id;
    const whiteLabel = this.app.get('whiteLabel');
    const focus_on_custom_assessments = false; // todo: need a clean way to set this, not assuming its going to come cleanly from the query parameters, have to be careful

    if (!isABED(whiteLabel as FLAGS)) {
      // console.log("here")
      const test_window_td_alloc_rules = await dbRawRead(this.app, {tw_id: testSession.test_window_id}, SQL_TWTAR_COMMON_FORM_BY_TW(focus_on_custom_assessments));
      test_window_td_alloc_rules.forEach( async twtdar => {
        const school_class_common_form = await dbRawRead(this.app, {sc_id:schoolClassID , twtar_id:twtdar.id}, SQL_SC_COMMON_FORM_BY_TWTAR);
        if(school_class_common_form.length === 0){
          const testFormRefs = <Paginated<any>> await this.app
            .service('db/read/test-forms')
            .find({
              query: {
                $select: ['id', 'lang'],
                test_design_id: twtdar.test_design_id,
                is_revoked: 0,
                $limit: 1000,
              }
            });

          const testFormSelection = randArrEntry(testFormRefs.data);

          await this.app
          .service('db/write/school-class-common-forms')
          .create({
            school_class_id:schoolClassID,
            twtdar_id: twtdar.id,
            test_form_id:testFormSelection.id,
            created_by_uid: created_by_uid,
            is_revoked:0
          });
        }
      })
    }
    else{
      const test_window_td_alloc_rules_by_slug = await dbRawRead(this.app, {tw_id:testSession.test_window_id, slug:asmtTypeSlug}, SQL_TWTAR_COMMON_FORM_BY_SLUG(focus_on_custom_assessments));
      // console.log({ test_window_td_alloc_rules_by_slug });
      // console.log({ testSession, asmtTypeSlug });
      for (let twtdar of test_window_td_alloc_rules_by_slug){
        // Finding common set of twtdar in school class
        // Todo: think of in_active twtdar
        const school_class_common_twtdar = await dbRawRead(this.app, {sc_id:schoolClassID , slug:twtdar.slug}, SQL_SC_COMMON_FORM_BY_TWTAR_SLUG);
        console.log({ school_class_common_twtdar });
        // If no results, create new sccf for that type slug (chooseing random twtdar id)
        const focus_on_custom_assessments = false; // todo: need a clean way to set this, not assuming its going to come cleanly from the query parameters, have to be careful
        if(school_class_common_twtdar.length === 0){
          const twtdarRefs = await dbRawRead(this.app, {tw_id:testSession.test_window_id ,slug:twtdar.slug}, SQL_TWTAR_COMMON_FORM_BY_SLUG(focus_on_custom_assessments));
          // Get random twtdar for the slug
          const twtdarSelection = randArrEntry(twtdarRefs);
          let test_design_id = twtdarSelection.test_design_id;
          // Get test form for selected twtdar id
          const testForm = await dbRawReadSingle(this.app, {td_id:test_design_id}, SQL_TF_ID_BY_TD)
          await this.app
            .service('db/write/school-class-common-forms')
            .create({
              school_class_id:schoolClassID,
              twtdar_id: twtdarSelection.id,
              test_form_id:testForm.id,
              created_by_uid: created_by_uid,
              type_slug: twtdarSelection.type_slug,
              is_revoked:0
            });
        }
      }
    }
  }
}
