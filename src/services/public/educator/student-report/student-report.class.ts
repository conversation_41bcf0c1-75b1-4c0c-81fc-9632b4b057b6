import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from "axios";
import { Errors } from '../../../../errors/general';
import { IStudentReportInput, IStudentItemPerf, IItemLabelMap, IItemParamsRef, ICutScores, IItemParams } from './model/input';
import { G9_CURRIC_MAPPINGS } from './model/curric-mappings';
import { IStudentReportOutput, IStudentG9Report } from './model/ouput';
import { QuestionResponse } from '../../mrkg-mrkr/question-response/question-response.class';
import { currentUid } from '../../../../util/uid';
import { Knex } from 'knex';
import { dbRawRead } from '../../../../util/db-raw';
import e from 'compression';
import logger from '../../../../logger';
const _ = require('lodash');

interface Data {}

interface ServiceOptions {}
const G9_MIN_STANDARD_LEVEL = 3;
const G9_MAX_STANDARD_LEVEL = 4;

export class StudentReport implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [
      // await this.generateReportByAttemptId(4569, 21),
      // await this.generateReportByAttemptId(4584, 21),
      // await this.generateReportByAttemptId(4589, 21),
    ];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    const {school_class_group_id} = (<any>params).query;
    // todo: validate teacher access
    // todo: attempt fetch of report for student
    // generate report if it has not been generated already
    // todo: get attempt by student (and by slug of the attempt)... reject any attempt that is not in a closed test session
    if (params){
      const teacher_uid = await currentUid(this.app, params);
      const student_uid = id;
      const focus_on_custom_assessments = false; // todo: need a clean way to set this, not assuming its going to come cleanly from the query parameters, have to be careful

      const records = await dbRawRead(this.app, [student_uid, teacher_uid], `
        select ta.id
        from school_classes sc
        join user_roles urs
          on sc.group_id = urs.group_id
          and sc.is_active = 1
        and urs.role_type = 'schl_student'
        and urs.is_revoked != 1
        and urs.uid = ?
        join user_roles urt
          on sc.group_id = urt.group_id
        and urt.role_type = 'schl_teacher'
        and urt.is_revoked != 1
        and urt.uid = ?
        join school_class_test_sessions scts
          on scts.school_class_id = sc.id
        and slug = 'G9_OPERATIONAL'
        join test_attempts ta
          on ta.test_session_id = scts.test_session_id
          and ta.uid = urs.uid
        join test_window_td_alloc_rules twtdar
          on ta.twtdar_id = twtdar.id
          and twtdar.generate_report = 1
          and twtdar.is_custom = ${ focus_on_custom_assessments ? '1' : '0' }
        join test_attempt_question_responses taqr
          on taqr.test_attempt_id = ta.id
        join test_sessions ts
          on ts.id =  ta.test_session_id
         and ts.is_closed = 1
        group by ta.id
        order by ta.id desc
      ;`);
      if (records.length === 0){
        throw new Errors.NotFound('NO_SUBMISSION')
      }
      const attemptId = records[0].id
      const report = await this.generateReportByAttemptId(attemptId, teacher_uid);
      if (report){
        return report;
      }
      else{
        throw new Errors.BadGateway('COULD_NOT_PROCESS_REPORT');
      }
    }
    throw new Errors.BadRequest();
  }

  renderReportOutputSample():IStudentG9Report {
    ///
    const overallLevel = 2;
    const overallDecimalLevel = 2.5014051;
    return {
      numQAns: 49,
      numQTotal: 50,
      overallLevel:overallLevel,
      isAboveStandard: overallLevel >= G9_MIN_STANDARD_LEVEL,
      overallDisplay:(overallDecimalLevel/5)*100,
      curriculumStrands:[
        {id:'Ac-AG', isSuccessful:false},
        {id:'Ac-LR', isSuccessful:false},
        {id:'Ac-MG', isSuccessful:true},
        {id:'Ac-NA', isSuccessful:false},
      ],
      skillCategories: [
        {id:'KU/CC', isSuccessful:false},
        {id:'AP/MA', isSuccessful:false},
        {id:'TH/HP', isSuccessful:false},
      ]
    }
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    // return this.updateRoutings();
    // return <any> this.generateReportByAttemptId(4884, 21, true);
    // return this.regenerateAllReports();
    // return this.validateAllReports();
    // return <any> this.compareReports([5615, 5613, 5600, 5010, 5002, 4642, 4431]);
    return { id };
  }

  async compareReports(attemptIds:number[]){
    return Promise.all(attemptIds.map(async attempt_id => {
      const row:any = { attempt_id, }
      const prevStudentReportRecords =  await this.retrievePreviousReports([attempt_id]);
      try {
        if (prevStudentReportRecords.length > 0){
          const prevStudentReportRecord = prevStudentReportRecords[0];
          const newStudentReportRecord = (await this._generateReportByAttemptId(attempt_id, 21)).submissionPayload;
          const extractProps = (studentReportRecord:any) => {
            const props:any = {}
            const data_raw = JSON.parse(studentReportRecord.data_raw);
            Object.keys(data_raw).forEach(dataType => {
              const data = data_raw[dataType];
              const dataOE = data.OverallExpectationCode;
              const dataSC = data.CognitiveSkill;
              props[dataType+'_overall'] = data.overall;
              Object.keys(dataOE).forEach(oe => props[dataType+'_OE_'+oe] = dataOE[oe] );
              Object.keys(dataSC).forEach(sc => props[dataType+'_SC_'+sc] = dataOE[sc] );
            })
            return props;
          }
          const oldProps = extractProps(prevStudentReportRecord);
          const newProps = extractProps(newStudentReportRecord);

          Object.keys(oldProps).forEach(prop => {
            row[prop+'_pre'] = oldProps[prop];
            row[prop+'_post'] = newProps[prop];
          })
        }
      }
      catch(e){}

      return row
    }))
  }

  async retrievePreviousReports(attempt_ids:number[]){
    // one report per attempt, most recent
    const records = await dbRawRead(this.app, [attempt_ids], `
      select * from student_reports where id in (
        select max(id) as id
        from student_reports sr
        where sr.attempt_id IN (?)
          and sr.is_isr = 1
        group by attempt_id
      )
    ;`);
    return records;
  }

  async updateRoutings(){
    const records = await dbRawRead(this.app, [], `
    select id, attempt_id 
    from student_reports sr 
    where sr.v = 2
      and sr.is_isr = 1
      and sr.routings is null
    ;`);
    for (let i=0; i<records.length; i++){
      logger.silly('updated routing %d', i);
      const record = records[i];
      const report = await this.generatePreReportByAttemptId(<number> record.attempt_id, true);
      await this.app
        .service('db/write/student-reports')
        .patch(record.id, {
          routings: JSON.stringify(report.routings)
        })
    }
    return []
  }

  private async getStudentPerfs(test_attempt_id:number) : Promise<IStudentItemPerf[]> {
    await this.app.service('public/student/session-question').removeDuplicateQuestionAttemptRecords(test_attempt_id);
    const taqr = <any[]> await this.app
      .service('db/read/test-attempt-question-responses')
      .find({query:{
        $select:[
          'id',
          'test_question_id',
          'response_raw'
        ],
        is_invalid: 0,
        test_attempt_id
      }, paginate:false});
    const perfs:IStudentItemPerf[] = [];
    let isAnyCorrect:boolean = false;
    taqr.forEach(qr => {
      if (qr.test_question_id && qr.test_question_id != 0){
        const {isCorrect, isFilled} = this.processResponseIsCorrect(qr.response_raw);
        const perf:IStudentItemPerf = {
          taqr_id: qr.id,
          question_id: +qr.test_question_id,
          isCorrect,
          isFilled
        }
        if (isCorrect){
          isAnyCorrect = true;
        }
        perfs.push(perf);
      }
    });
    if (!isAnyCorrect && perfs.length > 0){
      perfs[0].isCorrect = true; // they will still get a level 0, but this just makes it not give an error
    }
    return perfs
  }

  public processResponseIsCorrect(responseRaw?:string, taqrId?:number) : {isCorrect: boolean, isFilled:boolean, isResponded:boolean} {
    let isCorrect = false;
    let isFilled = false;
    let isResponded = false;
    if (responseRaw){
      const qRes = JSON.parse(responseRaw);
      let isAllCorrect = true;
      let isAnyFilled = false;
      let isAnyResponded = false;
      const entries = Object.keys(qRes);
      if (entries.length > 0){
        entries.forEach(entryId => {
          if(entryId === '__meta'){
            return;
          }
          const eRes = qRes[entryId];
          isAllCorrect = isAllCorrect && eRes.isCorrect;
          isAnyFilled = isAnyFilled || eRes.isFilled;
          isAnyResponded = isAnyResponded || eRes.isResponded;
          if (eRes.isFilled && !eRes.isResponded) {
            logger.silly('taqr entry isFilled but not isResponded', {
              taqrId,
              entryId,
              responseRaw
            });
          }
          // isAllCorrect = isAllCorrect && this.isEResCorrect(eRes, eAns);
        });
        isCorrect = isAllCorrect;
        isFilled = isAnyFilled;
        isResponded = isAnyResponded || isAnyFilled;
      }
    }
    return {isCorrect, isFilled, isResponded};
  }

  // private isEResCorrect(eRes, eAns) {
  //   if (eRes && eRes.selections) {
  //     if (eRes.selections[0] && eRes.selections[0].i === eAns.optionIndex) {
  //       return true;
  //     }
  //   }
  //   return false;
  // }

  private async getFrameworkForAttempt(testAttemptRecord:any){
    const testFormRecord =  await this.app.service('db/read/test-forms').get(testAttemptRecord.test_form_id);
    const testDesignRecord =  await this.app.service('db/read/test-designs').get(testFormRecord.test_design_id);
    let framework;
    try {
      framework = JSON.parse(testDesignRecord.framework);
    }
    catch(e){
      throw new Errors.BadGateway('COULD_NOT_READ_FRAMEWORK')
    }
    const testForm = await this.app.service('public/student/session').getTestDesign(testFormRecord.file_path)
    return {
      testForm,
      framework
    };
  }

  async extractQuestionsFromTestForm(testForm:any, questionIds:any[]){
    const question_item_map: IItemLabelMap[] = [];
    const item_params: IItemParamsRef = {};
    const qIds: string[] = questionIds; // Object.keys(testForm.questionDb);
    // .map
    const questionRecords = <any[]> await this.app
      .service('db/read/test-questions')
      .find({query:{
        $select: ['id', 'question_label', 'config'],
        id:{$in:qIds},
      }, paginate: false})
      questionRecords.forEach(question => {
        const question_label:string = question.question_label;
        try {
          const config = JSON.parse(question.config)
          if (config.meta){
            const params:IItemParams = {
              SLOPE: ''+config.meta['irt_a'],
              THRSHOLD: ''+config.meta['irt_b'],
              CognitiveSkill: ''+config.meta['SC'],
              OverallExpectationCode: this.renderOverallExpectationCode(config.meta['OE']),
            }
            let isAllUndefined = true; // temporary... should have a more explicit determination of what is mandatory among these params
            Object.values(params).forEach(val => {
              if (val !== undefined){
                isAllUndefined = false;
              }
            })
            if (!isAllUndefined){
              question_item_map.push({
                id: +question.id,
                question_label,
              });
              item_params[question_label] = params;
            }
          }
        }catch(e){}
      })
    return {question_item_map, item_params};
  }

  private renderOverallExpectationCode(val:string){
    return G9_CURRIC_MAPPINGS[val] || val;;
  }

  processIRTOutput(processing:IStudentReportOutput, numQAns:number, numQTotal:number){
    const overallLevel = Math.max(0, Math.min(processing.LEVEL.overall, G9_MAX_STANDARD_LEVEL));
    const overallDecimalLevel = Math.max(0, Math.min(processing.DECIMAL_LEVEL.overall, G9_MAX_STANDARD_LEVEL+1));
    const _curriculumStrands = processing.LEVEL.OverallExpectationCode;
    const _skillCategories = processing.LEVEL.CognitiveSkill;
    const curriculumStrands = Object.keys(_curriculumStrands).map(id => Object({
      id,
      isSuccessful: _curriculumStrands[id] >= G9_MIN_STANDARD_LEVEL
    }))
    const skillCategories = Object.keys(_skillCategories).map(id => Object({
      id,
      isSuccessful: _skillCategories[id] >= G9_MIN_STANDARD_LEVEL
    }))
    const report:IStudentG9Report =  {
      numQAns,
      numQTotal,
      overallLevel:overallLevel,
      isAboveStandard: overallLevel >= G9_MIN_STANDARD_LEVEL,
      overallDisplay:(overallDecimalLevel/ (G9_MAX_STANDARD_LEVEL+1))*100,
      curriculumStrands,
      skillCategories,
    };
    return report;
  }

  private async generatePreReportByAttemptId(test_attempt_id:number, imputeModuleId:boolean=false){
    const MIN_Q_PER_ASMT = 50;

    const testAttemptRecord = await this.app.service('db/read/test-attempts').get(test_attempt_id);
    const {testForm, framework} = await this.getFrameworkForAttempt(testAttemptRecord);
    const cut_scores:ICutScores = framework.panelAssembly.cut_scores;
    const student_perf:IStudentItemPerf[] = await this.getStudentPerfs(test_attempt_id);
    const numQAns = student_perf.filter(perf => perf.isFilled).length;
    let questionIds = student_perf.map(perf => +perf.question_id); // temp: this should be done from within the test form
    let routings:{[moduleId:string]:string} = {};
    if (imputeModuleId && false){
      const findBestModuleMatch = (modules:any[]) => {
        let bestCandidateModule:any;
        let bestCandidateCount:number = 0;
        modules.forEach(module => {
          let count = _.intersection(questionIds, module.questions).length;
          if (!bestCandidateModule || count > bestCandidateCount){
            bestCandidateCount = count;
            bestCandidateModule = module;
          }
        });
        return bestCandidateModule;
      }

      const modulesCompleted = [
        testForm.panelModules[0],
        findBestModuleMatch([
          testForm.panelModules[1],
          testForm.panelModules[2],
          testForm.panelModules[3],
        ]),
        testForm.panelModules[4],
        findBestModuleMatch([
          testForm.panelModules[5],
          testForm.panelModules[6],
          testForm.panelModules[7],
        ]),
      ];

      // the following should really be embedded in the module object...
      const moduleIdToDifficulty = (moduleId:number) => {
        switch(moduleId){
          case 1: return 'med';
          case 2: return 'low';
          case 3: return 'med';
          case 4: return 'high';
          case 5: return 'med';
          case 6: return 'low';
          case 7: return 'med';
          case 8: return 'high';
          default: return 'error';
        }
      }
      routings['stage_2'] = moduleIdToDifficulty(modulesCompleted[1].moduleId);
      routings['stage_4'] = moduleIdToDifficulty(modulesCompleted[3].moduleId);

      const taqrRef = new Map();
      student_perf.forEach(taqrRecord => {
        taqrRef.set(taqrRecord.question_id, taqrRecord.taqr_id);
      })

      let backfill:number[] = [];
      await Promise.all(modulesCompleted.map(async (module, section_index) => {
        const module_id = module.moduleId;
        let orderedQuestions:number[] = [];
        // i cannot rely on the order of the items in the panel (that doesn)
        module.questions.map((questionId:number) =>{
          const taqr_id = taqrRef.get(questionId);
          if (taqr_id){
            orderedQuestions.push(taqr_id);
          }
          else{
            backfill.push(questionId);
          }
        });
        orderedQuestions = _.orderBy(orderedQuestions);
        return Promise.all(orderedQuestions.map(async (taqr_id:number, section_form_index:number) => {
          return this.app
            .service('db/write/test-attempt-question-responses')
            .patch(taqr_id, {
              module_id,
              section_id: section_index+1,
              section_form_order: section_form_index + 1,
            })
        }))
      }))
      questionIds = questionIds.concat(backfill);
      backfill.forEach(question_id => {
        student_perf.push({question_id, isCorrect:false, isFilled: false})
      })
    }

    if (questionIds.length < MIN_Q_PER_ASMT){
      const allQs = Object.keys(testForm.questionDb).map(id => +id);
      const backfillPool:number[] = _.difference(allQs,questionIds);
      const len = MIN_Q_PER_ASMT - questionIds.length;
      const backfillSlice = backfillPool.splice(0, len);
      questionIds = questionIds.concat(backfillSlice);
      backfillSlice.forEach(question_id => {
        student_perf.push({question_id, isCorrect:false, isFilled: false})
      })
    }
    const {question_item_map, item_params} = await this.extractQuestionsFromTestForm(testForm, questionIds);
    // to do: fill student_perf with entires for questions that were not yet answered

    cut_scores["0"] = -4.0; // temp, this seems to be left out in some frameworks
    const studentResultsInput:IStudentReportInput = {
      student_perf,
      question_item_map,
      item_params,
      cut_scores,
      // extra
      routings,
      uid: testAttemptRecord.uid,
      attempt_id: testAttemptRecord.id,
      numQAns,
      numQTotal:questionIds.length,
    }
    return studentResultsInput
  }

  async regenerateAllReports(){
    const records = await dbRawRead(this.app, [], `
      select ta.id
      from school_class_test_sessions scts
      join school_classes sc on sc.id = scts.school_class_id
      join school_districts sd on sd.group_id = sc.schl_dist_group_id
      join test_sessions ts on ts.id = scts.test_session_id
      join test_attempts ta on ta.test_session_id = scts.test_session_id
      where scts.slug = 'G9_OPERATIONAL'
        and sd.foreign_id != 11
        and ts.is_closed = 1
        and sc.is_active = 1
    ;`);
    const errors = [];
    const stepSize = 20;
    for (let i=0; i<records.length; i+=stepSize){
      logger.silly('regenerating %d of %d', i, records.length);
      const recordsSlice = records.slice(i, i+stepSize);
      await Promise.all(recordsSlice.map(async record => {
        logger.silly('regenerating %d of %d --- %d', i, records.length, record.id);
        try {
          await this.generateReportByAttemptId(record.id, 21);
        }
        catch(e){
          errors.push(e);
          return {};
        }
      }));
    }
    return records;
  }

  async validateAllReports(){
    const records = await dbRawRead(this.app, [], `
      select sr.id, sr.attempt_id, sr.uid, sr.data 
      from student_reports sr
      where sr.is_isr = 1
      group by sr.attempt_id 
    ;`) ;
    const stepSize = 20;
    const res:number[] = [];
    for (let i=0; i<records.length; i+=stepSize){
      logger.silly('validating %d of %d', i, records.length);
      const recordsSlice = records.slice(i, i+stepSize);
      await Promise.all(recordsSlice.map(async record => {
        logger.silly('validating %d of %d --- %d', i, records.length, record.id);
        const data = JSON.parse(record.data);
        const data2 = await this.generateReportByAttemptId(record.attempt_id, 21);
        if (data2){
          const renderval = (data:IStudentG9Report) => [data.overallLevel, data.overallDisplay].join(';')
          const payload = {
            overall: renderval(data),
            overall_validated: renderval(data2),
          }
          await this.app.service('db/write/student-reports').patch(record.id, payload);
          if (payload.overall != payload.overall_validated){
            res.push(record.id);
          }
        }
      }))
    }
    return res;
  }

  async _generateReportByAttemptId(test_attempt_id:number, created_by_uid:number){
    const studentResultsInput:IStudentReportInput = await this.generatePreReportByAttemptId(test_attempt_id, true);
    if (studentResultsInput.student_perf.length === 0){
      return {report:null, submissionPayload:null};
    }
    const processing:IStudentReportOutput = await this.processReportInput(studentResultsInput);
    const report:IStudentG9Report = this.processIRTOutput(processing, studentResultsInput.numQAns, studentResultsInput.numQTotal);
    const submissionPayload = {
      uid: studentResultsInput.uid,
      attempt_id: studentResultsInput.attempt_id,
      created_by_uid,
      routings: JSON.stringify(studentResultsInput.routings),
      data: JSON.stringify(report),
      data_raw: JSON.stringify(processing),
      overall: report.overallLevel,
      v: 3,
    };
    return {report, submissionPayload}
  }

  async generateReportByAttemptId(test_attempt_id:number, created_by_uid:number, isAutoGen:boolean=false){
    const {submissionPayload, report} = await this._generateReportByAttemptId(test_attempt_id, created_by_uid)
    if (submissionPayload){
      await this.app
        .service('db/write/student-reports')
        .create({
          ... submissionPayload,
          is_auto: isAutoGen ? 1 : 0,
          is_isr: 1,
        });
    }
    return report;
  }

  private async processReportInput(studentResultsInput:IStudentReportInput) : Promise<IStudentReportOutput> {
    const processingEndPoint = 'https://sg9fl8i3bd.execute-api.ca-central-1.amazonaws.com/default/getIrtLevelDetails_v2';
    return axios
      .post(processingEndPoint, {
        student_perf: studentResultsInput.student_perf,
        question_item_map: studentResultsInput.question_item_map,
        item_params: studentResultsInput.item_params,
        cut_scores: studentResultsInput.cut_scores,
      })
      .then((res:{data:IStudentReportOutput}) => {
        return res.data;
      })
      .catch(e => {
        throw new Errors.BadGateway('COULD_NOT_PROCESS_REPORT')
      });
  }

}
