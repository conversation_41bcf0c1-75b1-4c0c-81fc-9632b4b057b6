export const SQL_SCORE_ENTRY_TS_ID_WITH_TWTAR_INFO = `
     select ts.id
        , twtar.slug
        , twtar.test_design_id 
        , twtar.type_slug
        , twtar.long_name
        , twtar.id twtar_id 
        , tf.id tf_id 
        , tf.lang 
        , tf.file_path 
        , td.framework
        , td.source_item_set_id
        , ecd.link
        , twtar.test_date_start
        , twtar.cut_score_def_id
      from school_classes sc 
      join school_class_test_sessions scts on scts.school_class_id = sc.id 
      join test_sessions ts on ts.id = scts.test_session_id 
      join test_window_td_alloc_rules twtar
          on twtar.type_slug = scts.slug
          and ts.test_window_id = twtar.test_window_id
           and twtar.is_custom = 0
      join test_designs td on td.id = twtar.test_design_id 
      join test_forms tf
          on tf.test_design_id = twtar.test_design_id
      left join eys_category_descriptors ecd on ecd.source_item_set_id = td.source_item_set_id
            and ecd.is_revoked = 0
      where sc.id = ?
      and twtar.is_score_entry = 1
`

export const SQL_STU_TAQRS = `
    select ta.uid, taqr.* from test_attempts ta
        join test_forms tf on tf.id = ta.test_form_id 
        join test_designs td on td.id = tf.test_design_id 
        LEFT JOIN mpt_dev.test_attempt_question_responses taqr
          ON taqr.test_attempt_id = ta.id
        where ta.test_session_id in (?)
        and ta.is_invalid = 0    
        group by taqr.id
`

export const SQL_CSD = `
    SELECT * from cut_score_definitions csd where csd.id = ?
`