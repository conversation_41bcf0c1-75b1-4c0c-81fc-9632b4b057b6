import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import axios from 'axios';
import { dbRawRead } from '../../../../util/db-raw';
import { DefaultCategories } from '../individual-report/individual-report.class';
import { OutcomeValues } from '../../school-board/da-reports/da-reports.class';
import { SQL_CSD, SQL_SCORE_ENTRY_TS_ID_WITH_TWTAR_INFO, SQL_STU_TAQRS } from './model/sql';
interface Data {}

interface ServiceOptions {}

export type ReportDetails = {
  students: Record<number, number | null>,
  cutOff: any,
  weight: number,
  description?: string,
}

// export type SummaryReportData = {
//   [category: string]: number | ReportDetails;
// }

export class SummaryReport implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data> {
    if (!params || !params.query) {
      throw new Errors.BadRequest("REQ_PARAMS_MISSING")
    }
    const { sc_id, ts_id } = params.query

    const focus_on_custom_assessments = false; // todo: need a clean way to set this, not assuming its going to come cleanly from the query parameters, have to be careful
    
    const test_designs = await dbRawRead(this.app, {ts_id}, `
      select twtar.test_design_id 
          , twtar.slug
          , twtar.id twtar_id 
          , tf.id tf_id 
          , tf.lang 
          , tf.file_path 
          , twtt.is_perusal_allow
          , twtt.is_local_score
          , twtt.is_download_datas
          , twtt.is_bulk_print
      from school_class_test_sessions scts
      join test_sessions ts 
        on ts.id = scts.test_session_id
      join test_window_td_alloc_rules twtar
        on twtar.type_slug = scts.slug
        and ts.test_window_id = twtar.test_window_id
        and twtar.is_custom = ${ focus_on_custom_assessments ? '1' : '0' }
      join test_forms tf
        on tf.test_design_id = twtar.test_design_id
      left join test_window_td_types twtt
        on twtt.type_slug = twtar.type_slug
        and twtt.test_window_id is null
        and twtt.is_revoked = 0
      where  scts.test_session_id = :ts_id
      group by tf.id
    `);

    for (let tf of test_designs){
      const formUrl = generateS3DownloadUrl(tf.file_path, 60);
      const formData = await axios.get(formUrl, {});
      tf.testForm = formData?.data;
    }
    // TODO: Need to confirm if score-entry will always just have one test design (don't see a reason for multiple)
    const summaryReport = (await dbRawRead(this.app, [sc_id, ts_id, test_designs[0].slug], `
        SELECT sr.data FROM summary_reports sr 
        WHERE sr.sc_id = ?
        AND sr.ts_id = ?
        AND sr.twtar_slug = ?
      `));

    if (summaryReport.length == 0) {
      throw new Errors.BadRequest("MISSING_SUMMARY_REPORT")
    }
    
    return {
      data: JSON.parse(summaryReport[0].data),
      test_designs,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if (!id || !params || !params.query) {
      throw new Errors.BadRequest("MISSING SCHOOL CLASS ID / PARAMS")
    }
    const sc_id = +id;

    const { lang } = params.query
    const testSessions = await dbRawRead(this.app, [sc_id], SQL_SCORE_ENTRY_TS_ID_WITH_TWTAR_INFO)
    if (testSessions.length == 0) {
      throw new Errors.BadRequest("NO_SESSIONS")
    }

    const groupedSessions: Record<string, any[]> = {};
    for (const session of testSessions) {
      const { type_slug } = session;
      if (!groupedSessions[type_slug]) {
        groupedSessions[type_slug] = [];
      }
      groupedSessions[type_slug].push(session);
    }
    const summaryReports:any[] = [];

    for (const type_slug in groupedSessions) {
      const sessions = groupedSessions[type_slug];
      if (!sessions[0].cut_score_def_id) {
        continue;
      }
      const sessionIds = sessions.map(session => +session.id)
      const summaryData: Record<string, any> = {};
      // Student records
      const studentQuestionResponses = await dbRawRead(this.app, [sessionIds], SQL_STU_TAQRS)
      const responsesByUID = {} as Record<string, any>;

      studentQuestionResponses.forEach(response => {
          const uid = response.uid;
          if (!responsesByUID[uid]) {
              responsesByUID[uid] = {};
          }
          // Push the response into the array corresponding to the UID
          responsesByUID[uid][response.test_question_id] = response;
      });
      let students = [];
      const uids = Object.keys(responsesByUID)
      if (uids.length > 0) {
        students = await dbRawRead(this.app, {uids}, `
          select u.id uid
              , u.first_name 
              , u.last_name 
              , um_dob.value dob
              , um_sin.value student_gov_id
          from users u 
          left join user_metas um_dob 
            on um_dob.uid = u.id 
            and um_dob.key = 'DateofBirth' -- todo:WHITELABEL
          left join user_metas um_sin 
            on um_sin.uid = u.id 
            and um_sin.key in ('StudentIdentificationNumber', 'TestTakerIdNumber') -- todo:WHITELABEL
          where u.id in (:uids)
          order by u.last_name, u.first_name
        `)
      }

      // Can just use first session index since they are already grouped by twtar slug which is tied to 1 test design
      const formUrl = generateS3DownloadUrl(sessions[0].file_path, 60);
      const formData = await axios.get(formUrl, {});
      const testDesign = {} as any;
      testDesign.testForm = formData?.data;
      testDesign.long_name = sessions[0].long_name
      testDesign.framework = sessions[0].framework
      const ecd = sessions[0].link
      if (ecd) {
        testDesign.categoryDescriptorsUrl = ecd
      }
      const framework = JSON.parse(sessions[0].framework);
      const sections = framework.partitions

      const cutScoreConfig = JSON.parse((await dbRawRead(this.app, [sessions[0].cut_score_def_id], SQL_CSD))[0].cut_config)[lang]

      const overallData = {
        details: {
          students: {},
          cutOff: [],
          weight: 0,
        }
      }
      summaryData[DefaultCategories.OVERALL] = overallData;

      for (let j = 0; j < sections.length; j++) {
        const sectionQuestions = framework.sectionItems[sections[j].id].questions
        
        const testQuestionId = framework.sectionItems[sections[j].id].questions[0].id
        const test_question = (await dbRawRead(this.app, [+testQuestionId], `
          SELECT * from test_questions tq where tq.id = ?
        `))[0]
        if (!test_question) {
          throw new Errors.BadRequest("MISSING_TEST_QUESTION_FOR_SCORE_ENTRY")
        }
        const test_question_config = JSON.parse(test_question.config)

        const weight = +test_question_config.content[0].scoreWeight
        const sectionCutOffs = cutScoreConfig[sections[j].sectionSlug];
        const description = test_question_config.meta.Description
        if (!sectionCutOffs) {
          throw new Errors.BadRequest("MISSING_CUT_SCORE_FOR_SECTION")
        }
        summaryData[sections[j].description] = {
          details: {
            students: {},
            cutOff: sectionCutOffs,
            weight,
            description
          }
        };
        // order section cutoffs by smallest to largest
        sectionCutOffs.sort((a:any, b:any) => {
          const cutScoreA = a.cut_score != null ? a.cut_score : -Infinity;
          const cutScoreB = b.cut_score != null ? b.cut_score : -Infinity;
          return cutScoreA - cutScoreB;
      });
        for (let i = 0; i < sectionCutOffs.length; i++) {
          if (!summaryData[DefaultCategories.OVERALL][sectionCutOffs[i].long]) {
            summaryData[DefaultCategories.OVERALL][sectionCutOffs[i].long] = 0
          }
          summaryData[sections[j].description][sectionCutOffs[i].long] = 0
        }

        // Add to overall
        (summaryData[DefaultCategories.OVERALL].details as ReportDetails).weight += weight

        for (const uid in responsesByUID) {
          let studentSectionMark: number | null = 0;
          sectionQuestions.forEach((question: { id: number; }) => {
            if (responsesByUID[uid]?.[question.id]?.score != null) {
              studentSectionMark = responsesByUID[uid][question.id].score;
              let outcomeKey
              for (let i = 0; i < sectionCutOffs.length; i++) {
                if (sectionCutOffs[i].cut_score == null) {
                  continue;
                }
                  if (studentSectionMark !== null && studentSectionMark >= sectionCutOffs[i].cut_score) {
                      outcomeKey = sectionCutOffs[i].long; // Match found, set outcome
                  }
              }
              if (outcomeKey) {
                if (!summaryData[sections[j].description][outcomeKey]) {
                  summaryData[sections[j].description][outcomeKey] = 0;
                }
                (summaryData[sections[j].description][outcomeKey] as number)++;
              }
          
          } else {
            if (!summaryData[sections[j].description][OutcomeValues.NO_ATTEMPT]) {
              summaryData[sections[j].description][OutcomeValues.NO_ATTEMPT] = 0
            }
            summaryData[sections[j].description][OutcomeValues.NO_ATTEMPT]++;
            studentSectionMark = null;
          }
          
          })
          
          summaryData[sections[j].description].details.students[+uid] = studentSectionMark
          // Add to overall data
          if (summaryData[DefaultCategories.OVERALL].details.students[+uid] != null) {
            summaryData[DefaultCategories.OVERALL].details.students[+uid]! += studentSectionMark;
          }
          else {
            summaryData[DefaultCategories.OVERALL].details.students[+uid] = studentSectionMark;
          }
        }
      }

      // Determine data for overallData
      const totalCutOff = {} as any
      for (const section in cutScoreConfig) {
        for (let i = 0; i < cutScoreConfig[section].length; i++) {
          if (!totalCutOff[cutScoreConfig[section][i].long]) {
            totalCutOff[cutScoreConfig[section][i].long] = {
              color: cutScoreConfig[section][i].color,
              cut_score: 0
            }
          }
          // For null cut score 
          if (cutScoreConfig[section][i].cut_score == null) {
            totalCutOff[cutScoreConfig[section][i].long].cut_score = null;
            continue;
          }
          totalCutOff[cutScoreConfig[section][i].long].cut_score += cutScoreConfig[section][i].cut_score
        }
      }

      for (const outcome in totalCutOff) {
        summaryData[DefaultCategories.OVERALL].details.cutOff.push({
          long: outcome,
          cut_score: totalCutOff[outcome].cut_score,
          color: totalCutOff[outcome].color
        })
      }
      for (const uid in summaryData[DefaultCategories.OVERALL].details.students) {
        const studentTotalMark = summaryData[DefaultCategories.OVERALL].details.students[+uid]

        if (studentTotalMark == undefined) {
          summaryData[DefaultCategories.OVERALL][OutcomeValues.NO_ATTEMPT]++
        }
        else {
          let outcomeKey
          for (const outcome in totalCutOff) {
            if (totalCutOff[outcome] == null) {
              continue;
            }
            if (studentTotalMark >= totalCutOff[outcome].cut_score) {
              outcomeKey = outcome;
            }
          }
          if (outcomeKey) {
            summaryData[DefaultCategories.OVERALL][outcomeKey]++
          }
        }
      }

      summaryReports.push({
        data: summaryData,
        students,
        td_id: sessions[0].test_design_id,
        testDesign,
        ts_ids: sessionIds,
        twtarSlug: type_slug,
        date: sessions[0].test_date_start,
      });
    }

    return {
      summaryReports,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
