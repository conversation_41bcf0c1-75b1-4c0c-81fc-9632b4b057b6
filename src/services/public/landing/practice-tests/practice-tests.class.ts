import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class PracticeTests implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    
    const group_type_slug = params?.query?.group_type_slug || '_DEFAULT_'
    const isAllowQa = (params?.query?.is_allow_qa == '1')

    return dbRawRead(this.app, {group_type_slug}, `
      select twtar.id twtar_id 
           , tw.id tw_id 
           , tw.title as test_window_title
           , tw.type_slug as tw_type_slug
           , twtar.long_name 
           , twtar.test_design_id 
           , td.source_item_set_id 
           , twtar.lang 
           , twtt.resource_td_id
           , twtt.resource_caption
           , td2.source_item_set_id as resource_item_set_id
      from test_windows tw 
      join tw_ctrl_group_slugs tcgs
        on tcgs.ctrl_group_id = tw.test_ctrl_group_id
        and tcgs.slug = :group_type_slug
      join test_window_td_alloc_rules twtar 
        on twtar.test_window_id = tw.id 
        and twtar.is_custom = 0
      join test_designs td 
        on td.id = twtar.test_design_id 
      left join test_window_td_types twtt
        on twtt.type_slug = twtar.type_slug
        and twtt.test_window_id is null
        and twtt.is_revoked = 0
      left join test_designs td2 
        on td2.id = twtt.resource_td_id
      where tw.is_active = 1
        and tw.is_public_practice = 1
        and twtar.is_active = 1
        ${ isAllowQa ? '' : `and tw.is_qa = 0` }
    `)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return this.app.service('public/school-admin/school-access').remove(id, params)
  }
}
