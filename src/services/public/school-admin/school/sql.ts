export const SQL_TW_TYPE_SLUG_MAPPINGS = ` -- SQL_TW_TYPE_SLUG_MAPPINGS
  select type_slug 
  from type_slug_mappings
`;

// todo:SPECIFY_SELECT_COLS
export const SQL_UNACCEPTED_TW = ` -- SQL_UNACCEPTED_TW
  SELECT tw.*
  FROM test_windows tw
  LEFT OUTER JOIN test_window_admins_accepted twaa
    ON (tw.id = twaa.test_window_id)
    AND twaa.uid = :uid
    AND twaa.is_revoked = 0
  WHERE twaa.id IS NULL
    AND tw.is_active = 1
    AND tw.date_end > NOW()
`;

// todo:SPECIFY_SELECT_COLS
export const SQL_ACTIVE_SC_BY_GID = ` -- SQL_ACTIVE_SC_BY_GID
  SELECT sc.*
  FROM school_classes sc
  WHERE group_id = :group_id
    AND is_active = 1
`

// todo:SPECIFY_SELECT_COLS
// this allows both teachers and school admins (or anyone in the school which is a problem)
export const SQL_S_BY_UR = ` -- SQL_S_BY_UR
  select s.*
  from schools s
  join user_roles ur
    on  ur.group_id = s.group_id
    and uid = :uid
    and ur.is_revoked != 1
  where s.group_id = :group_id
`

// todo:SPECIFY_SELECT_COLS
export const SQL_S_PAY_AGR_BY_S_GID = ` -- SQL_S_PAY_AGR_BY_S_GID
  select *
  from school_payment_agreements
  where school_group_id = :s_group_id
    and is_revoked != 1
`

// todo:SPECIFY_SELECT_COLS
export const SQL_SD_BY_GID = ` -- SQL_SD_BY_GID
  SELECT * 
  FROM school_districts 
  where group_id = :schl_dist_group_id;
`

// todo:SPECIFY_SELECT_COLS
export const SQL_TW_BY_TYPE_SLUGS = (isAllowQaWindows:boolean) => ` -- SQL_TW_BY_TYPE_SLUGS
  select tw.*
       , ap.price_per_student
       , abs(UNIX_TIMESTAMP(date_start) - UNIX_TIMESTAMP(now())) tw_start_proximity_rank
  from test_windows tw
  left join assessment_prices ap
    on tw.id = ap.test_window_id
    and ap.is_revoked = 0
  where tw.type_slug in (:TW_TYPE_SLUGS)
    and tw.is_active = 1
    and tw.is_archived = 0
  ${isAllowQaWindows ? '' : 'and tw.is_qa = 0' }
  order by tw.is_sa_signoff_required desc
         , tw.is_classroom_assessment asc
         , abs(UNIX_TIMESTAMP(date_start) - UNIX_TIMESTAMP(now())) asc
`

// todo: consider sd custom, will require a change in the query (and check downstream impacts)
// todo: we know that it is used in api--abed/src/services/public/school-admin/school/school.class.ts
export const SQL_TWTAR_BY_TW = ` -- SQL_TWTAR_BY_TW
  select twtar.test_window_id
       , twtar.type_slug as asmt_type_slug
       , twtar.slug as asmt_slug
       , twtar.assessment_def_id
       , twtar.is_field_test
       , twtar.lang
       , twtar.test_duration
       , twtar.test_date_start
  from test_window_td_alloc_rules twtar
  join test_windows tw
      on tw.id = twtar.test_window_id
  where twtar.test_window_id in (:test_windows_ids)
    and twtar.is_custom = 0
`

export const SQL_STA_BY_TW = ` -- SQL_STA_BY_TW
  select distinct tw.type_slug
  from school_test_access sta
  join test_windows tw on tw.id = sta.test_window_id
  where sta.school_group_id = :sch_group_id
    and sta.test_window_id in (:test_windows_ids)
    and sta.is_revoked != 1
`

export const SQL_SS_BY_TW = ` -- SQL_SS_BY_TW
  select id
      , foreign_scope_id
      , foreign_id
      , name
      , test_window_id 
  from school_semesters 
  where test_window_id in (:test_windows_ids)
`

export const SQL_SC_BY_S_GID_AND_SS = ` -- SQL_SC_BY_S_GID_AND_SS
  select sc.id
       , sc.group_id
       , sc.schl_group_id
       , sc.schl_dist_group_id
       , sc.name
       , sc.notes
       , sc.semester_id
       , ss.test_window_id
       , sc.group_type
       , sc.is_grouping
       , sc.access_code
       , sc.is_placeholder
       , sc.is_fi
       , sc.foreign_id
  from school_classes sc
  inner join school_semesters ss on ss.id = sc.semester_id
  where sc.schl_group_id = :sch_group_id
    and sc.is_active = 1
    and sc.semester_id in (:school_semesters_id)
`

export const SQL_SC_TEACHERS = ` -- SQL_SC_TEACHERS
  select distinct sc.group_id, urt.uid
  from school_classes sc
  join user_roles urt 
    on urt.group_id = sc.group_id 
    and urt.role_type = 'schl_teacher'
  where sc.schl_group_id = :sch_group_id
    and urt.is_revoked != 1 
    and sc.is_active = 1
`

export const SQL_TEACHER_DETAILS = ` -- SQL_TEACHER_DETAILS
  select u.id
       , u.first_name
       , u.last_name
       , case when au.id then u.contact_email
               else null
         end as contact_email
       , ui.created_on
       , ui.used_on
       , ui.expire_on
       , ui.invit_email
       , ui.id as invit_id
       , ui.secret_key
  from user_roles urt
  join users u on u.id = urt.uid
  left join u_invites ui 
    on ui.uid = u.id 
    and ui.is_revoked != 1
  left join auths au on au.uid = u.id
  where urt.group_id = :sch_group_id
    and urt.role_type = 'schl_teacher'
    and urt.is_revoked != 1
  group by u.id
`

export const SQL_SC_INVIGILATORS = ` -- SQL_SC_INVIGILATORS
  select ur.uid
       , u.contact_email
       , u.first_name
       , u.last_name
       , ur2.group_id
  from user_roles ur
  join users u 
    on u.id = ur.uid
  left join user_roles ur2 
    on ur2.uid = ur.uid 
    and ur2.is_revoked != 1 
    and ur2.role_type = 'schl_teacher_invig' 
    and ur2.group_id in (:classes_group_id)
  where ur.role_type ='schl_teacher'
    and ur.is_revoked != 1
    and ur.group_id = :sch_group_id
    group by ur.uid, ur2.group_id
    limit 3000
`

export const SQL_SC_STUDENTS = ` -- SQL_SC_STUDENTS
  select urt.group_id
       , urt.uid
  from user_roles urt
  where urt.role_type = 'schl_student'
    and urt.group_id in (:classes_group_id)
    and urt.is_revoked != 1;
`

export const SQL_SC_STUDENTS_GUESTS = ` -- SQL_SC_STUDENTS_GUESTS
  select scg.invig_sc_group_id group_id
       , urt.uid
       , scg.id scg_id
  from school_classes_guest scg
  join school_classes sc 
    on sc.group_id = scg.guest_sc_group_id 
    and sc.is_active = 1
  join user_roles urt 
    on urt.group_id = sc.group_id 
    and urt.role_type = 'schl_student' 
    and urt.is_revoked != 1
  where scg.invig_sc_group_id IN (:classes_group_id)
    and scg.is_revoked != 1
`

export const SQL_STU_ACCOMM_BY_UIDS = ` -- SQL_STU_ACCOMM
  SELECT acc.name
       , ua.uid
  FROM users_accommodations ua
  JOIN accommodations acc
    ON acc.id = ua.accommodation_id
    AND acc.is_revoked != 1
  WHERE ua.uid in (:classes_students_uids)
    AND ua.is_revoked != 1
    AND ua.accommodation_value = 1
`

export const SQL_STU_INFO_BY_UIDS = ` -- SQL_STU_INFO_BY_UIDS
  SELECT u.id id
      , u.is_PASI_student
      , u.first_name first_name
      , u.middle_name middle_name
      , u.last_name last_name
      , sap.id sap_id
      , tsp.id tsp_id
      , tsp.purchase_method_id purchase_method_id
      , (CASE
         WHEN (tsp.alternative_status IS not null)
           THEN tsp.alternative_status
         ELSE 0
       END
       ) altPaymentStatus
      , (CASE
         WHEN (tsp.alternative_status = 3)
           THEN 1
         ELSE 0
         END
       ) isPaid
      , sap2.id sap2
    FROM users u
    LEFT JOIN student_attempt_purchases sap
      ON  u.id = sap.uid
      AND sap.is_revoked != 1
      AND (sap.is_refunded != 1 OR sap.is_refunded IS null)
    LEFT JOIN test_session_purchases tsp
      ON  sap.ts_purchase_id = tsp.id
      AND tsp.is_revoked != 1
    LEFT JOIN student_attempt_purchases sap2
      ON  u.id = sap2.uid
      AND sap.id < sap2.id
      AND sap2.is_revoked != 1
      AND (sap2.is_refunded != 1 OR sap2.is_refunded IS null)
    WHERE u.id IN (:classes_students_uids) AND sap2.id is NULL
  `

export const SQL_STU_SUBMISSIONS = ` -- SQL_STU_SUBMISSIONS
  select ur.uid id
       , scts.slug
       , sr.is_data_insufficient
       , sr.overall
       , ta.id as test_attempt_id
       , ta.submitted_test_session_id
  from user_roles ur
  join student_reports sr
    on sr.uid = ur.uid
    and sr.is_isr = 1
    and sr.is_reporting = 1
    and sr.is_revoked = 0
    and sr.is_pending != 1
    and sr.is_withheld != 1
    and sr.is_data_insufficient != 1
    and sr.is_absent != 1
  join test_attempts ta on sr.attempt_id = ta.id
  join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
  where ur.role_type = 'schl_student'
    and ur.is_revoked != 1
    and ur.uid in (:classes_students_uids)
  group by ur.uid, scts.slug
`

export const SQL_STU_ATT = ` -- SQL_STU_ATT
  select ur.uid as uid
       , scts.slug
       , ta.id as test_attempt_id
       , ta.submitted_test_session_id
       , ta.is_submitted
       , ts.test_window_id
  from user_roles ur
  left join test_attempts ta
    on ta.uid = ur.uid
    and ta.twtdar_order = 0
    and submitted_test_session_id is not null
  join school_class_test_sessions scts
    on scts.test_session_id = ta.submitted_test_session_id
  join test_sessions ts
    on ts.id = scts.test_session_id
    and ts.is_cancelled = 0
  join school_classes sc
    on sc.id = scts.school_class_id
  where ur.role_type = 'schl_student'
    and ur.is_revoked != 1
    and ur.uid in (:classes_students_uids)
`

export const SQL_STU_META_BY_UIDS = ` -- SQL_STU_META_BY_UIDS
  select um.uid
       , um.key_namespace
       , um.key
       , um.value
       , um.id
  from user_metas um
  where um.uid in (:classes_students_uids)
`

export const SQL_STU_META_ALL_BY_UIDS = ` -- SQL_STU_META_ALL_BY_UIDS
  select um.uid
       , um.key_namespace
       , um.key
       , um.meta
       , um.value
  from tw_user_metas um
  where um.uid IN (:classes_students_uids)
`

export const SQL_SCHL_SESSIONS = (isStrictSessions:boolean) => ` -- SQL_SCHL_SESSIONS
  select 
        scts.school_class_id
      , ts.is_closed
      , ts.closed_on
      , scts.test_session_id
      , scts.slug
      , scts.caption
      , ts.date_time_start
      , sc.name
      , sc.group_id
      , sc.is_active sc_is_active
      , count(distinct usr.uid) as num_students
      , count(distinct ta.uid) as num_attempts
      , ut.first_name
      , ut.last_name
      , twtdar.test_design_id
      , twtdar.is_field_test
      , tw.type_slug as "tw_slug"
      , twtdar.resp_sheet_config
      , ts.is_cancelled
      , ts.cancelled_on
      , ts.name_custom
      , sc.access_code
  from school_class_test_sessions scts
  join test_sessions ts
    on ts.id = scts.test_session_id
  join test_windows tw
    on tw.id = ts.test_window_id
  join school_classes sc
    on sc.id = scts.school_class_id
  left join user_roles urt
    on urt.group_id = sc.group_id
    and urt.role_type = "schl_teacher"
    and urt.is_revoked = 0
  left join test_window_td_alloc_rules twtdar   
    on twtdar.test_window_id = ts.test_window_id 
    and twtdar.slug = scts.slug
    and twtdar.is_custom = 0
  left join user_roles usr
    on usr.group_id = sc.group_id
    and usr.role_type ='schl_student'
    ${isStrictSessions ? 'and usr.is_revoked != 1' : ''}
  left join test_attempts ta
    on ta.test_session_id = ts.id
    and ta.uid = usr.uid
    ${isStrictSessions ? 'and ta.id is not null' : ''}
  left join users ut
    on ut.id = urt.uid
  where ts.test_window_id in (:test_windows_ids)
    and sc.schl_group_id = :sch_group_id
    ${isStrictSessions ? '' : 'and not (usr.is_revoked = 1 and ta.id is null)'}
  group by test_session_id
`

export const SQL_S_TW_TA = ` -- SQL_S_TW_TA
  select max(ta.submitted_test_session_id) as submitted_test_session_id
       , count(ta.id) as num_attempts
       , sum(ta.is_submitted) as submissions
       , ta.twtdar_order as twtdar_order
  from school_class_test_sessions scts
  join test_sessions ts
    on ts.id = scts.test_session_id
    and ts.test_window_id in (:test_windows_ids)
  join school_classes sc
    on sc.id = scts.school_class_id
  left join test_attempts ta
  on ta.submitted_test_session_id = scts.test_session_id
    and ( (scts.slug in ("G9_SAMPLE","G9_OPERATIONAL", "OSSLT_SAMPLE", "OSSLT_OPERATIONAL") and ta.twtdar_order = 0)
    or  (scts.slug in ("PRIMARY_SAMPLE","PRIMARY_OPERATIONAL","JUNIOR_SAMPLE","JUNIOR_OPERATIONAL") and ta.twtdar_order in (0,1)) )
  where sc.schl_group_id = :sch_group_id
  group by ta.submitted_test_session_id, ta.twtdar_order
`

// {test_design_ids}
export const SQL_NUM_QUESTION_REQUIRING_SCAN = `
  -- SQL_NUM_QUESTION_REQUIRING_SCAN, < 0.5 sec
  SELECT tqr.test_design_id
  , count(distinct tqsi.item_id) as num_scans
  FROM test_question_register tqr
  LEFT JOIN test_question_scoring_info tqsi 
    ON tqr.tqsi_id = tqsi.id 
    AND tqsi.is_paper_response = 1 
  WHERE tqr.test_design_id in (:test_design_ids) 
  GROUP BY tqr.test_design_id;
`

// todo: does not incoroporate paper vs. online toggle 
export const SQL_TS_PENDING_SCANS = `
  -- SQL_TS_PENDING_SCANS
  SELECT ta.test_session_id
       , count(distinct ta.uid) n_students
       , count(distinct taqr.id) n_scans_expected
       , count(distinct tasr.id) n_scans_received
  FROM test_attempts ta 
  JOIN test_question_register tqr
  JOIN test_question_scoring_info tqsi 
    ON tqr.tqsi_id = tqsi.id 
    AND tqsi.is_paper_response = 1 
    AND tqsi.is_revoked = 0
  JOIN test_attempt_question_responses taqr 
    on taqr.test_question_id = tqr.question_id 
    and taqr.test_attempt_id = ta.id
    and taqr.is_invalid = 0
  LEFT JOIN test_attempt_scan_responses tasr
    on tasr.taqr_id = taqr.id
    and tasr.is_discarded != 1
  WHERE ta.test_session_id in (:test_session_ids)
  GROUP BY ta.test_session_id;
  `

// Map test_session_id to test_design_id for a list of sessions
// Same logic as query SQL_TS_TWTAR
// Note: This pattern is duplicated in several places in the codebase (see educator/session, student/session, etc.)
export const SQL_SESSION_TEST_DESIGN_IDS = ` -- SQL_SESSION_TEST_DESIGN_IDS
  select ts.id as test_session_id
       , twtar.test_design_id
  from test_sessions ts
  join school_class_test_sessions scts
    on scts.test_session_id = ts.id
<<<<<<< HEAD
  join test_window_td_alloc_rules twtar
    on twtar.type_slug = scts.slug
    and twtar.test_window_id = ts.test_window_id
  left join school_class_common_forms sccf
||||||| a3b376e2d0
  join test_window_td_alloc_rules twtar 
    on twtar.type_slug = scts.slug 
    and twtar.test_window_id = ts.test_window_id 
  left join school_class_common_forms sccf 
=======
  join test_window_td_alloc_rules twtar 
    on twtar.type_slug = scts.slug 
    and twtar.test_window_id = ts.test_window_id 
    and twtar.is_custom = 0
  left join school_class_common_forms sccf 
>>>>>>> release/abed
	  on sccf.school_class_id = scts.school_class_id
    and sccf.type_slug = scts.slug
    and sccf.twtdar_id = twtar.id
    and sccf.is_revoked != 1
  where ts.id in (:test_session_ids)
  and ((twtar.is_classroom_common_form = 0 and twtar.is_active = 1) or (sccf.id is not null))
  group by ts.id;
`;
