import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class SessionComplete implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params?.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {asmt_slug, schl_class_id} = params.query;

    const classMembers = await dbRawRead(this.app, [schl_class_id],
      `
        SELECT ur.uid
        FROM school_classes sc 
        JOIN user_roles ur 
        ON sc.group_id = ur.group_id
        WHERE sc.id = ?
        AND ur.is_revoked != 1
        AND ur.role_type = 'schl_student'
        GROUP BY ur.uid 
      `
      )
    const classUids = classMembers.map(m => m.uid);


    let incompleteTass = <any>[];
    if(classUids.length > 0) {
      incompleteTass = await dbRawRead(this.app, [schl_class_id, classUids, asmt_slug], `
      select tsss.order
      from users u
      left join school_class_test_sessions scts on scts.school_class_id = ?
      join test_attempt_sub_sessions tass on (tass.test_session_id = scts.test_session_id and tass.uid = u.id)
      join test_sessions ts on ts.id = scts.test_session_id
      join test_session_sub_sessions tsss on tsss.id = tass.sub_session_id
      join test_window_td_alloc_rules twtdar on twtdar.type_slug = scts.slug
      where tass.is_submitted != 1
        and tass.is_invalid != 1
        and twtdar.is_secured = 1
        and twtdar.is_custom = 0
        and u.id in (?)
        and scts.slug = ?
        and twtdar.test_window_id = ts.test_window_id
      group by tass.test_session_id,tsss.caption, tass.uid
      ;`);
    }

    if(!incompleteTass?.length) {
      return [false, false];
    }

    let isSessionCompleted = [true, true];

    for(let order = 0; order < isSessionCompleted.length; order++) {
      if(incompleteTass.find((tass: any) => (tass.order == null || tass.order === order))) {
        isSessionCompleted[order] = false;
      }
    }

    return isSessionCompleted;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
