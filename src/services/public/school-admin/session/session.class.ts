import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { Knex } from 'knex';
import { currentUid } from '../../../../util/uid';
import { EDeliveryOption } from '../../../db/schemas/test_sessions.schema';
import { dbRawRead, DbRawSeq, dbRawReadReporting } from '../../../../util/db-raw';
import { dbDateNow, dbDateOffsetHours, dbDateSetDateTime } from '../../../../util/db-dates';
import { DBD_U_GROUP_TYPES } from '../../../../constants/db-extracts';
import { generateAccessCode } from '../../../../util/secret-codes';
import e from 'compression';
import moment from 'moment';
import logger from '../../../../logger';
import { isArray } from 'lodash';
import { isABED } from '../../../../util/whiteLabelParser';
import { SQL_SCHL_SESSIONS, SQL_NUM_QUESTION_REQUIRING_SCAN, SQL_TS_PENDING_SCANS } from './sql';


const STANDARD_TIMEZONE = 'America/Edmonton';

export const isAssessmentABED = (assessment: string) => 
{
  return assessment.includes("ABED_");
};


interface Data { }

interface ISessionEdit{
  scheduled_time: string,
}
export interface ISession {
  school_class_id: number,
  slug: string,
  caption: string,
  scheduled_time:ISessionTime,
  isScheduled: boolean,
  isRemovePrev?: boolean;
  sessionName?:string
  is_fi_class?: boolean;
}
interface ISessionEdit {
  scheduled_time:string,
  slug:string,
}
export type ISessionTime = string[];
interface ServiceOptions { }

export class Session implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data[] | Paginated<Data> | Data> {
    if (!params || !params.query) {
      throw new Errors.BadRequest("REQ_PARAMS_MISSING")
    }
    const { schl_group_id } = (<any>params).query;

    const whiteLabel = this.app.get('whiteLabel');
    if(isABED(whiteLabel)){
      const { test_window_id } = params.query;

      const dbRawSeq = new DbRawSeq(this.app, 'FIND:public/school-admin/school');
      
      var school_sessions:any[]=[];
      school_sessions = await dbRawSeq.read(SQL_SCHL_SESSIONS(true), {test_window_id, schl_group_id});
      this.sanitizeSchoolSessions(school_sessions)
      await this.getIsSessionScan(school_sessions)
      
      const session_states = await this.app.service('public/school-admin/session').getSessionStates(school_sessions);
      const sessions = {school_sessions,session_states}

      return sessions

    } else {
      const classrooms = <any[]>await this.app
        .service('db/read/school-classes')
        .find({
          query: { schl_group_id, is_active: 1 },
          paginate: false
        });

      const classIds = classrooms.map(entry => entry.id)
      const classes_sessions = await this.getData([classIds], `
      select scts.school_class_id, scts.test_session_id, scts.slug, scts.caption, ts.date_time_start, sc.name, sc.group_id, count(us.id) as num_students, ut.first_name,ut.last_name
      from mpt_dev.school_class_test_sessions scts
      join mpt_dev.test_sessions ts on ts.id = scts.test_session_id
      join mpt_dev.school_classes sc on sc.id = scts.school_class_id
      join mpt_dev.user_roles usr on usr.group_id = sc.group_id
      join mpt_dev.user_roles urt on urt.group_id = sc.group_id
      join mpt_dev.users us on us.id = usr.uid
      join mpt_dev.users ut on ut.id = urt.uid
      where scts.school_class_id IN (?)
        and ts.is_cancelled = 0
        and ts.is_closed = 0
        and usr.role_type ='schl_student'
        and urt.role_type = "schl_teacher"
        and urt.is_revoked = 0
        and usr.is_revoked = 0
      group by test_session_id;
      ;`);
      const session_states: any[] = [];
      return Promise.all(
        classes_sessions.map(async (session) => {
          const session_state = await this.app.service('public/educator/session-sub').getTestSubSessions(session.test_session_id);
          session_states.push({ test_session_id: session.test_session_id, states: session_state });
        })
      )
        .then(res => {
          return [{
            classes_sessions,
            session_states
          }];
        })
      // return [{
      //   classes_sessions
      // }]
    }
  }

  async getSessionStates(classes_sessions:any) {
    const session_states: any[] = [];
    return Promise.all(
      classes_sessions.map(async (session:any) => {
        const session_state = await this.app.service('public/educator/session-sub').getTestSubSessions(session.test_session_id);
        session_states.push({ test_session_id: session.test_session_id, states: session_state });
      })
    )
      .then(res => {
        return session_states
      })
  }

  /** Determine if sessions are scan sessions based of scan questions in their test design */
  async getIsSessionScan(class_sessions: any[]){
    const test_design_ids = [...new Set(class_sessions.map(s => s.test_design_id))] // todo: sessions could have more than one test design id (not an issue for this purpose though)
    const test_session_ids = [...new Set(class_sessions.map(s => s.test_session_id))] // todo: sessions could have more than one test design id (not an issue for this purpose though)
    if (!test_design_ids.length) return class_sessions;

    const tdScanNums = await dbRawReadReporting(this.app,{test_design_ids},SQL_NUM_QUESTION_REQUIRING_SCAN);
    const scanProgress = await dbRawReadReporting(this.app,{test_session_ids}, SQL_TS_PENDING_SCANS);
    const hasScansRef = new Map()
    const scanProgressRef = new Map()
    for (let record of tdScanNums){
      if (record.num_scans > 0){
        hasScansRef.set(+record.test_design_id, true)
      }
    }
    for (let record of scanProgress){
      scanProgressRef.set(+record.test_session_id, record)
    }

    for (let s of class_sessions){
      const hasScans = !!hasScansRef.get(+s.test_design_id);
      const scanProgress = scanProgressRef.get(+s.test_session_id);
      if (hasScans){
        s.is_scan_session = true;
        s.n_students = scanProgress?.n_students
        s.n_scans_expected = scanProgress?.n_scans_expected
        s.n_scans_received = scanProgress?.n_scans_received
      }
    }

    return class_sessions;
  }

  sanitizeSchoolSessions(school_sessions:any[]){
    school_sessions.forEach(schoolSession => {
      if (schoolSession.num_attempts > schoolSession.num_students){
        schoolSession.num_students = schoolSession.num_attempts;
      }
      if (schoolSession.sc_is_active == 0 && schoolSession.is_closed == 0){
        schoolSession.closedBecauseInactive = true;
        schoolSession.is_closed = 1;
      }
    })
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(id: Id, params?: Params): Promise<Data> {
    const { school_class_group_id,school_class_id } = (<any>params).query;
    logger.silly({ school_class_id });
    // long polling
    if (params) {
      return this.app.service('public/educator/session').getSessionInfo(id, school_class_id, false);
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: ISession, params?: Params): Promise<Data> {
    const { school_class_group_id } = (<any>params).query;
    if (params) {
      const created_by_uid = await currentUid(this.app, params); // should be currentUid
      return this.createSession(created_by_uid, data);
    }
    return <any>{};
  }

  async getStudentRecords(school_class_id: any) {
    return await this.getData([school_class_id], `
    select sc.id, count(us.id) as num_students,ut.first_name, ut.last_name
    from mpt_dev.school_classes sc
    join mpt_dev.user_roles usr on usr.group_id = sc.group_id
    join mpt_dev.user_roles urt on urt.group_id = sc.group_id
    join mpt_dev.users us on us.id = usr.uid
    join mpt_dev.users ut on ut.id = urt.uid
    where sc.id = ?
      and usr.role_type ='schl_student'
      and urt.role_type = 'schl_teacher'
      and urt.is_revoked = 0
      and usr.is_revoked = 0
      group by id;
    ;`);

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // #2021-11-16-unclear-conflict
  // // eslint-disable-next-line @typescript-eslint/no-unused-vars
  // async patch(id: NullableId, data: ISessionEdit, params?: Params): Promise<Data> {
  //   const date_time_start = dbDateSetDateTime(this.app, 4, data.scheduled_time)
  //   return await this.app
  //     .service('db/write/test-sessions')
  //     .patch(id, { date_time_start });
  // }

  async createSession(created_by_uid: number, data: ISession) {
    //const test_ctrl_group_id = 6397; // hard coded to bc grad for now
    //const test_ctrl_group_id = 5403; // hard coded to g9 for now
    const delivery_format = EDeliveryOption.SCHOOL;
    const { school_class_id, slug, caption, isRemovePrev, isScheduled, is_fi_class, sessionName } = data;

    let date_time_start, date_time_end;
    if (isScheduled) {
      const scheduled_time = this.sanitizeScheduledTime(data.scheduled_time);
      if(slug === 'G9_SAMPLE' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_SAMPLE' || slug === 'OSSLT_OPERATIONAL') {
        const scheduledStandardTimeMoment = moment.tz(scheduled_time[0] || scheduled_time[1], STANDARD_TIMEZONE);
        const scheduledUTCTimeMoment = scheduledStandardTimeMoment.utc();
        const scheduledUTCTime = scheduledUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
        date_time_start = dbDateSetDateTime(this.app, 0, scheduledUTCTime);
      }
      else if(slug === 'PRIMARY_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_SAMPLE' || slug === 'JUNIOR_OPERATIONAL'){
        let startTimeMoment;
        // if Language scheduled only
        if(scheduled_time[0] && scheduled_time[1] && !scheduled_time[2] && !scheduled_time[3]){
          startTimeMoment = scheduled_time[0];
        }
        // if Math scheduled only
        if(!scheduled_time[0] && !scheduled_time[1] && scheduled_time[2] && scheduled_time[3]){
          startTimeMoment = (scheduled_time[0] <= scheduled_time[2]) ? scheduled_time[0] : scheduled_time[2];
        }
        // if Language and Math both scheduled
        if(scheduled_time[0] && scheduled_time[1] && scheduled_time[2] && scheduled_time[3]){
          startTimeMoment = (scheduled_time[0] <= scheduled_time[2]) ? scheduled_time[0] : scheduled_time[2];
        }
        const scheduledStandardStartTimeMoment = moment.tz(startTimeMoment, STANDARD_TIMEZONE);
        const scheduledUTCStartTimeMoment = scheduledStandardStartTimeMoment.utc();
        const scheduledUTCStartTime = scheduledUTCStartTimeMoment.format('YYYY-MM-DDTHH:mm:ss');
        date_time_start = dbDateSetDateTime(this.app, 0, scheduledUTCStartTime);
      }

      else 
      {
        // ABED logic here
        let dateTime = data.scheduled_time != null && !isArray(data.scheduled_time) ? data.scheduled_time
        : dbDateNow(this.app);
        const scheduledStandardStartTimeMoment = moment.tz(dateTime, STANDARD_TIMEZONE);
        const scheduledUTCStartTimeMoment = scheduledStandardStartTimeMoment.utc();
        const scheduledUTCStartTime = scheduledUTCStartTimeMoment.format('YYYY-MM-DDTHH:mm:ss');
        date_time_start = dbDateSetDateTime(this.app, 0, scheduledUTCStartTime);
      }
    }
    else {
      date_time_start = dbDateNow(this.app);
    }

    const schoolClassRecord = <any>await this.app
      .service('db/read/school-classes')
      .get(school_class_id);
    const schl_group_id = schoolClassRecord.schl_group_id;
    // await this.app.service('public/educator/session').ensureStudentLang(schl_group_id); // todo: this can be long running!

    const semester_id = schoolClassRecord.semester_id
    //get the class's semester
    const semesterRecord = <any>await this.app
      .service('db/read/school-semesters')
      .get(semester_id);

    const test_window_id = semesterRecord.test_window_id;
    // get active test window (take the first active one)
    /*
    const testWindowRecords = <Paginated<any>>await this.app
      .service('db/read/test-windows')
      .find({ query: { test_ctrl_group_id } });
    logger.silly(testWindowRecords)
    */
  const testWindowRecord = <Paginated<any>>await this.app
      .service('db/read/test-windows')
      .get(test_window_id);

    logger.silly(testWindowRecord)

    const testWindow = testWindowRecord;
    //const test_window_id = testWindow.id;

    const test_window_td_alloc_rules = await dbRawRead(this.app, [test_window_id, slug], `
      select twtar.*
      from test_window_td_alloc_rules twtar
      where twtar.test_window_id = ?
        and twtar.slug = ?
        and twtar.is_custom = 0
    ;`);
    const firstRule = test_window_td_alloc_rules[0];
    if (firstRule.is_secured) {
      await this.app.service('public/educator/session').validateTechnicalReadinessStatus(schl_group_id,slug);
    }

    const testSessionGroup = await this.app
      .service('db/write/u-groups')
      .create({
        group_type: DBD_U_GROUP_TYPES.mpt_test_session,
        created_by_uid,
      });
    logger.silly('hello', { testSessionGroup })
    const test_session_group_id = testSessionGroup.id;
    let access_code: string = '';
    for (let i = 0; i < 100; i++) {
      access_code = generateAccessCode(4);
      const previousCodeMatches = <Paginated<any>>await this.app
        .service('db/write/test-sessions')
        .find({
          query: {
            access_code,
            schl_group_id,
            is_cancelled: 0,
            is_closed: 0,
          }
        });
      if (previousCodeMatches.total === 0) {
        break;
      }
    }

    await this.app.service('public/educator/session').validateMultipleSessions(school_class_id, slug)

    const studentRecords = await this.getStudentRecords(school_class_id)
    const testSession = await this.app
      .service('db/write/test-sessions')
      .create({
        test_session_group_id,
        test_window_id,
        schl_group_id,
        delivery_format,
        date_time_start,
        access_code,
        name_custom: sessionName,
        is_access_code_enabled: true,
      })
    const test_session_id = testSession.id;
    await this.app
      .service('db/write/school-class-test-sessions')
      .create({
        school_class_id,
        test_session_id,
        slug,
        caption,
      });
      let subSessionRecords
      if(isScheduled){
        const scheduled_time = this.sanitizeScheduledTime(data.scheduled_time);
        subSessionRecords = await this.app.service('public/educator/session-sub').initSubSessionRecords(test_session_id, scheduled_time)
      }
      else{
        subSessionRecords = await this.app.service('public/educator/session-sub').initSubSessionRecords(test_session_id, undefined, is_fi_class)
      }

    //await this.app.service('public/educator/session').ensureSchool_class_common_forms(test_session_id, created_by_uid);

    await this.app.service('public/educator/session').ensureSchool_class_common_forms(test_session_id, created_by_uid);

    return <any>{
      ...subSessionRecords,
      test_session_id,
      date_time_start: testSession.date_time_start,
      school_class_id,
      slug,
      studentRecords,
      class_code: schoolClassRecord.name,
      caption,
      access_code,
      isFieldTest: firstRule.is_field_test
    };
  }

  private async getData(props: any[], query: string) {
    const db:Knex = this.app.get('knexClientRead');
    const res = await db.raw(query, props);
    return <any[]>res[0];
  }

  sanitizeScheduledTime(val:string | string[]){
    // temporary patch...
    if (typeof val === 'string'){
      return [
        val,
        val,
      ]
    }
    else{
      return val;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: ISessionEdit, params?: Params): Promise<Data> {
    const { slug } = data;
    let date_time_start, date_time_end, date_time_start2, date_time_end2, pj_date_time_start, pj_date_time_end;
    const scheduled_time = this.sanitizeScheduledTime(data.scheduled_time);
    
    // ABED Assessments
    if(isAssessmentABED(slug)) 
    {
      const scheduledStandardTimeMoment = moment.tz(scheduled_time[0], STANDARD_TIMEZONE);
      const scheduledUTCTimeMoment = scheduledStandardTimeMoment.utc()
      const scheduledUTCTime = scheduledUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
      date_time_start = dbDateSetDateTime(this.app, 0, scheduledUTCTime)

      logger.silly('SCHOOL_ADMIN_SESSION_PATCH_DATE', { scheduled_time: scheduled_time[0], scheduledUTCTime });

      const scheduledStandardTimeMoment2 = moment.tz(scheduled_time[1], STANDARD_TIMEZONE);
      const scheduledUTCTimeMoment2 = scheduledStandardTimeMoment2.utc()
      const scheduledUTCTime2 = scheduledUTCTimeMoment2.format('YYYY-MM-DDTHH:mm');
      date_time_start2 = dbDateSetDateTime(this.app, 0, scheduledUTCTime2)
    }
    // PJ
    if(slug === 'PRIMARY_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_SAMPLE' || slug === 'JUNIOR_OPERATIONAL') {
      const scheduledStandardStartTimeMoment = moment.tz(scheduled_time[0], STANDARD_TIMEZONE);
      const scheduledStandardEndTimeMoment = moment.tz(scheduled_time[1], STANDARD_TIMEZONE);
      const scheduledUTCStartTimeMoment = scheduledStandardStartTimeMoment.utc();
      const scheduledUTCEndTimeMoment = scheduledStandardEndTimeMoment.utc();
      const scheduledUTCStartTime = scheduledUTCStartTimeMoment.format('YYYY-MM-DDTHH:mm');
      const scheduledUTCEndTime = scheduledUTCEndTimeMoment.format('YYYY-MM-DDTHH:mm');
      date_time_start = dbDateSetDateTime(this.app, 0, scheduledUTCStartTime);
      date_time_end = dbDateSetDateTime(this.app, 0, scheduledUTCEndTime);
      const scheduledStandardStartTimeMoment2 = moment.tz(scheduled_time[2], STANDARD_TIMEZONE);
      const scheduledStandardEndTimeMoment2 = moment.tz(scheduled_time[3], STANDARD_TIMEZONE);
      const scheduledUTCStartTimeMoment2 = scheduledStandardStartTimeMoment2.utc();
      const scheduledUTCEndTimeMoment2= scheduledStandardEndTimeMoment2.utc();
      const scheduledUTCStartTime2 = scheduledUTCStartTimeMoment2.format('YYYY-MM-DDTHH:mm');
      const scheduledUTCEndTime2 = scheduledUTCEndTimeMoment2.format('YYYY-MM-DDTHH:mm');
      date_time_start2 = dbDateSetDateTime(this.app, 0, scheduledUTCStartTime2);
      date_time_end2 = dbDateSetDateTime(this.app, 0, scheduledUTCEndTime2);
      pj_date_time_start = date_time_start <= date_time_start2 ? date_time_start : date_time_start2;
      pj_date_time_end = date_time_end <= date_time_end2 ? date_time_end2 : date_time_end;
    }
    // G9 and OSSLT
    if(slug === 'G9_SAMPLE' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_SAMPLE' || slug === 'OSSLT_OPERATIONAL') {
      const scheduledStandardTimeMoment = moment.tz(scheduled_time[0], STANDARD_TIMEZONE);
      const scheduledUTCTimeMoment = scheduledStandardTimeMoment.utc()
      const scheduledUTCTime = scheduledUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
      date_time_start = dbDateSetDateTime(this.app, 0, scheduledUTCTime)

      logger.silly('SCHOOL_ADMIN_SESSION_PATCH_DATE', { scheduled_time: scheduled_time[0], scheduledUTCTime });

      const scheduledStandardTimeMoment2 = moment.tz(scheduled_time[1], STANDARD_TIMEZONE);
      const scheduledUTCTimeMoment2 = scheduledStandardTimeMoment2.utc()
      const scheduledUTCTime2 = scheduledUTCTimeMoment2.format('YYYY-MM-DDTHH:mm');
      date_time_start2 = dbDateSetDateTime(this.app, 0, scheduledUTCTime2)
    }

    const subSessions =  <any[]>await this.app.service('db/read/test-session-sub-sessions').find({ query: {test_session_id:id }, paginate: false });
    //const subSessions = subSessionsPage? <any[]>subSessionsPage:[]

    for(let subSession of subSessions)
    {
      // ABED Assessments
      if (isAssessmentABED(slug)) 
      {
        if(subSession.slug === 'session_a'){
          await this.app.service('db/write/test-session-sub-sessions').patch(subSession.id,{datetime_start:date_time_start})
        }
        if(subSession.slug === 'session_b'){
          await this.app.service('db/write/test-session-sub-sessions').patch(subSession.id,{datetime_start:date_time_start2})
        }
      }

      if(slug === 'G9_SAMPLE' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_SAMPLE' || slug === 'OSSLT_OPERATIONAL') {
        if(subSession.slug === 'session_a'){
          await this.app.service('db/write/test-session-sub-sessions').patch(subSession.id,{datetime_start:date_time_start})
        }
        if(subSession.slug === 'session_b'){
          await this.app.service('db/write/test-session-sub-sessions').patch(subSession.id,{datetime_start:date_time_start2})
        }
      }
      if(slug === 'PRIMARY_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_SAMPLE' || slug === 'JUNIOR_OPERATIONAL') {
        if(subSession.slug === 'lang_session_a' || subSession.slug === 'lang_session_b' || subSession.slug === 'lang_session_c' || subSession.slug === 'lang_session_d' ){
          await this.app.service('db/write/test-session-sub-sessions').patch(subSession.id,{ datetime_start: date_time_start, datetime_end: date_time_end});
        }
        if(subSession.slug === 'math_stage_1' || subSession.slug === 'math_stage_2' || subSession.slug === 'math_stage_3' || subSession.slug === 'math_stage_4'){
          await this.app.service('db/write/test-session-sub-sessions').patch(subSession.id,{ datetime_start: date_time_start2, datetime_end: date_time_end2 });
        }
      }
    }
    let patchQuery = {date_time_start}
    if(slug === 'PRIMARY_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_SAMPLE' || slug === 'JUNIOR_OPERATIONAL') {
      patchQuery = { date_time_start: pj_date_time_start };
    }
    return await this.app
      .service('db/write/test-sessions')
      .patch(id, patchQuery);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<Data> {
    if (id && params) {
      const uid = await currentUid(this.app, params);
      const session = <any>await this.app
        .service('db/read/test-sessions')
        .find({
          query: { id },
          paginate: false
        });
      if(session && session[0].date_time_start < new Date()) {
        throw new Errors.BadRequest('NO_CANCEL_FOR_ACTIVE_SESSION');
      }
      await this.app
      .service('db/write/test-sessions')
      .patch(id, {
        /*  is_closed: 1,
        closed_on: dbDateNow(this.app), */
        is_cancelled: 1,
        cancelled_on: dbDateNow(this.app),
      });
      return <any>{ id }
    }
    throw new Errors.BadRequest('MISSING_TEST_SESSION_ID');
  }
}
