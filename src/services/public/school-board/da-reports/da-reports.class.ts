import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { SQL_05A_ATTEMPTS_FROM_TW_ID } from '../../data-exporter/data-export-queries/queries/q05a_attempts/attempts-from-tw-id';
import { dbRawRead } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';

type TestDesignQuestionsReference = Record<string,Record<string, {
  weight: number,
  cutOff: number,
  sectionName: string,
}  
>>

export enum OutcomeValues {
  NOT_AT_RISK = "Not requiring additional supports",
  AT_RISK = "Requiring additional supports",
  NO_ATTEMPT = "N/A",
}

type ReportData = {
  [OutcomeValues.NO_ATTEMPT]: number,
  [OutcomeValues.AT_RISK]: number,
  [OutcomeValues.NOT_AT_RISK]: number,
}

interface Data {}

interface ServiceOptions {}

export class DaReports implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    const test_window_id = +id;
    if (!test_window_id || !params || !params.query) {
      throw new Errors.BadRequest("MISSING REQUIRED ID OR PARAMS")
    }
    const { schl_dist_id } = params.query;

    // Query for EYS twtar slugs and twtar ids and test design in test window
    const twtars = await this.getTwtars(test_window_id);
    // No EYS twtars means we are in the wrong test window
    if (twtars.length == 0) {
      return [];
    }

    // Prepare reference map of test questions to easily determine results
    const testDesignQuestionsReference:TestDesignQuestionsReference = {}
    const twtarReference: Record<string, {
      long_name: string,
      slug: string,
      type_slug: string,
    }> = {};
    for (let i = 0; i < twtars.length; i++) {
      twtarReference[twtars[i].twtar_id] = {
        long_name: twtars[i].long_name,
        slug: twtars[i].slug,
        type_slug: twtars[i].type_slug,
      }
      testDesignQuestionsReference[twtars[i].twtar_id] = {}
      const framework = JSON.parse(twtars[i].framework);
      const sections = framework.partitions
      
      sections.forEach(async (section: any) => {
        // Need to grab cutoff score from test_questions DB.
        const testQuestionId = framework.sectionItems[section.id].questions[0].id;
        const test_question = (await dbRawRead(this.app, [+testQuestionId], `
          SELECT * from test_questions tq where tq.id = ?
        `))[0];
        if (!test_question) {
          throw new Errors.BadRequest("MISSING_TEST_QUESTION_FOR_SCORE_ENTRY");
        }
        const test_question_config = JSON.parse(test_question.config);
        testDesignQuestionsReference[twtars[i].twtar_id][testQuestionId] = {
          weight: +test_question_config.content[0].scoreWeight,
          cutOff: +test_question_config.meta.Cut,
          sectionName: section.description,
        };
      });
    }

    const schools = await this.getSchools(schl_dist_id, test_window_id)
    
    for (let i = 0; i < schools.length; i++) {
      await this.prepareSchoolReportData(schools[i], test_window_id, testDesignQuestionsReference, twtarReference)
    }
    
    // Return Data
    return schools

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
  async prepareSchoolReportData(school:any, test_window_id: number, testDesignQuestionsReference: any, twtarReference: any) {
    school.assessmentType = {} as Record<string, Record<string, Record<string, ReportData>>>;
      const query = SQL_05A_ATTEMPTS_FROM_TW_ID.queryGen({
        tw_ids: [test_window_id],
        include_sample_assessments: false,
        s_id: school.id,
        eys_only: true,
      })
      const tw_ids = [test_window_id]
      const s_id = school.id
      const testAttemptsFromSchool = await dbRawRead(this.app, {tw_ids, s_id}, query);
      if (testAttemptsFromSchool.length == 0) {
        return;
      }
      const taIdsFromSchool = testAttemptsFromSchool.map(ta => ta.attempt_id)

      const taqrsFromSchool = await dbRawRead(this.app, [taIdsFromSchool], `
        select taqr.*
          , twtar.id
        from test_attempts ta  
        join test_window_td_alloc_rules twtar 
            on twtar.id = ta.twtdar_id 
            and twtar.is_custom = 0
        join test_attempt_question_responses taqr 
            on taqr.test_attempt_id = ta.id 
        where ta.id IN (?)
        and ta.is_invalid = 0
        and (ta.is_participating = 1 OR ta.is_participating IS NULL)

        `)

      const responsesByTaId = {} as Record<string, any>;
      
      for (let i = 0; i < taqrsFromSchool.length; i++) {
        const taId = taqrsFromSchool[i].test_attempt_id;
          if (!responsesByTaId[taId]) {
              responsesByTaId[taId] = {};
          }
          // Push the response into the array corresponding to the UID
          responsesByTaId[taId][taqrsFromSchool[i].test_question_id] = taqrsFromSchool[i]
      }
 
      for (let i = 0; i < testAttemptsFromSchool.length; i++) {
        const responsesPerQuestion = responsesByTaId[testAttemptsFromSchool[i].attempt_id];
        const { long_name, slug  } = twtarReference[testAttemptsFromSchool[i].twtar_id]
        const testQuestions = testDesignQuestionsReference[testAttemptsFromSchool[i].twtar_id]

        // Set up Assessment Type (Literacy or Numeracy) Object
        if (!school.assessmentType[slug]) {
          school.assessmentType[slug] = {}
        }
        // Set up specific grade assesssment object
        if (!school.assessmentType[slug][long_name]) {
          school.assessmentType[slug][long_name] = {}
        }
        // Set up Overall Result
        if (!school.assessmentType[slug][long_name]["Overall"]) {
          school.assessmentType[slug][long_name]["Overall"] = {
            "N/A": 0,
            "At Risk": 0,
            "Not At Risk": 0,
          }
        }
        const overAll = school.assessmentType[slug][long_name]["Overall"]
        let totalScore = 0;
        let totalCutOff = 0;
        let noAnswerQuestionPresent = false;


        for (const testQuestionId in testQuestions) {
          const testQuestion = testQuestions[testQuestionId];
          if (!school.assessmentType[slug][long_name][testQuestion.sectionName]) {
            school.assessmentType[slug][long_name][testQuestion.sectionName] = {
              [OutcomeValues.NO_ATTEMPT]: 0,
              [OutcomeValues.AT_RISK]: 0,
              [OutcomeValues.NOT_AT_RISK]: 0,
            } as ReportData
          }
          const taqr = responsesPerQuestion[testQuestionId];
          totalCutOff+= testQuestion.cutOff;
          if (!taqr) {
            school.assessmentType[slug][long_name][testQuestion.sectionName]["N/A"]++
            noAnswerQuestionPresent = true;
            continue;
          }
          const score = +taqr.score;
          if (score == null) {
            school.assessmentType[slug][long_name][testQuestion.sectionName]["N/A"]++
            noAnswerQuestionPresent = true;
          }
          else if (score >= testQuestion.cutOff) {
            school.assessmentType[slug][long_name][testQuestion.sectionName]["Not At Risk"]++
            totalScore += score
          }
          else {
            school.assessmentType[slug][long_name][testQuestion.sectionName]["At Risk"]++
            totalScore += score
          }
        }
        if (noAnswerQuestionPresent) {
          overAll['N/A']++
        }
        else if (totalScore >= totalCutOff) {
          overAll['Not At Risk']++
        }
        else {
          overAll['At Risk']++
        }
      }
  }

  async getTwtars(test_window_id:number):Promise<{
    type_slug: string,
    slug: string,
    long_name: string,
    twtar_id: number,
    framework: any,
  }[]> {
    const twtars = await dbRawRead(this.app, [test_window_id], `
      select twtar.type_slug, twtar.slug, twtar.long_name, twtar.id twtar_id, td.framework 
        from test_window_td_alloc_rules twtar
        join test_designs td on td.id = twtar.test_design_id
        where twtar.test_window_id = ? 
          and twtar.type_slug LIKE '%EYS%'
          and twtar.is_custom = 0
    `)
    return twtars;
  }

  async getSchools(schl_dist_id:number, test_window_id:number) {
    const schools = await dbRawRead(this.app, [schl_dist_id, test_window_id], `
      select s.id, s.foreign_id, s.name from schools s 
        join school_districts sd on sd.group_id = s.schl_dist_group_id 
        join school_classes sc on sc.schl_group_id = s.group_id 
        join school_semesters ss on ss.id = sc.semester_id 
        where sd.id = ? and ss.test_window_id = ?
        GROUP BY s.id
    `)

    return schools
  }
}

