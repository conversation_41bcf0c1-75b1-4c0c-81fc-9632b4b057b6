import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { AxiosInstance } from 'axios';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import { isABED } from '../../../../util/whiteLabelParser';

interface Data {}

interface ServiceOptions {}

export interface IScanAdjustments {
  crop: {
    top_ratio: number | null,
    right_ratio: number | null,
    bottom_ratio: number | null,
    left_ratio: number | null,
  },
  rotation: number | null
}

/* DEPRECATED; see config.json `scanningServiceConfig` */
// export const EQAO_SCANNING_API = 'http://localhost:5000';
// export const EQAO_SCANNING_API = 'http://eqao-pdf-scanning-1133926475.ca-central-1.elb.amazonaws.com:5000';

export class ResponseSheetCropAdjustment implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  scanningService: AxiosInstance;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.scanningService = this.app.get('scanningService');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async retrieveStudentData(taqr_id: number) {
    const student_data: any = {};

    const testSessionAndSchoolDetails = await dbRawRead(this.app, [taqr_id], `
      SELECT ts.id as test_session_id, ts.test_window_id, schl.is_sasn_login as is_sasn
      from test_attempt_question_responses taqr
      JOIN test_attempts ta on ta.id = taqr.test_attempt_id
      JOIN test_sessions ts on ts.id = ta.test_session_id
      JOIN school_class_test_sessions scts on scts.test_session_id = ts.id
      join school_classes sc on sc.id = scts.school_class_id
      join schools schl on schl.group_id = sc.schl_group_id
      where taqr.id = ?
    `)

    student_data.test_session_id = testSessionAndSchoolDetails[0].test_session_id;
    student_data.test_window_id = testSessionAndSchoolDetails[0].test_window_id;
    const isSasn = testSessionAndSchoolDetails[0].is_sasn;

    let studentIdentificationNumberKey;
    const whiteLabel = this.app.get('whiteLabel');
    if (isABED(whiteLabel)) {
      studentIdentificationNumberKey = "StudentIdentificationNumber";
    } else {
      studentIdentificationNumberKey = "StudentOEN";
    }

    // todo: why is the twtar not joined directly from the test attempt?
    const studentData = await dbRawRead(this.app, [taqr_id], `
      SELECT um.value as student_id
      , tqsi.batch_alloc_policy_item_slug as scan_slug 
      from test_attempt_question_responses taqr
      JOIN test_attempts ta on ta.id = taqr.test_attempt_id
      JOIN test_sessions ts on ts.id = ta.test_session_id
      JOIN user_metas um on ta.uid = um.uid and um.key = ${isSasn ? `'SASN'` : `'${studentIdentificationNumberKey}'`}
      JOIN school_class_test_sessions scts on scts.test_session_id = ts.id
      JOIN test_window_td_alloc_rules twtar 
        on twtar.test_window_id = ts.test_window_id 
        and twtar.type_slug = scts.slug
        and twtar.is_custom = 0
      JOIN test_question_register tqr on tqr.question_id = taqr.test_question_id and tqr.test_design_id = twtar.test_design_id
      JOIN test_question_scoring_info tqsi
		    on tqr.tqsi_id = tqsi.id
      where taqr.id = ?;
    `)

    student_data.student_id = studentData[0].student_id;
    student_data.scan_slug = studentData[0].scan_slug;
    return student_data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const uid = await currentUid(this.app, params);
    return await this.updateScanCrop(uid, <any> data)
  }

  async updateScanCrop(uid:number, config:{taqr_id?:number, tasr_id?:number, srb_id?:number, scanAdjusments:IScanAdjustments}){

    let {taqr_id, tasr_id, srb_id, scanAdjusments} = config;
    let student_data: any;


    let {top_ratio, bottom_ratio, right_ratio, left_ratio} = scanAdjusments.crop;

    if (!top_ratio) {
      top_ratio = null;
    }
    if (!bottom_ratio) {
      bottom_ratio = null;
    }
    if (!left_ratio) {
      left_ratio = null;
    }
    if (!right_ratio) {
      right_ratio = null;
    }
    let rotation_degree = +(<number> scanAdjusments.rotation) || null;

    let tasrRecords :any[];

    if (tasr_id){
      tasrRecords = await this.app
          .service('db/read/test-attempt-scan-responses')
          .db()
          .where('id', tasr_id)
    }
    else {
      tasrRecords = await this.app
          .service('db/read/test-attempt-scan-responses')
          .db()
          .where('taqr_id', taqr_id)
          .where('is_discarded', 0);
      if (tasrRecords.length < 1) {
        throw new Errors.BadRequest('MISSING_TASR_RECORD_WITH_TAQR_ID');
      }
    }

    const tasrRecord = tasrRecords[0];
    if (tasrRecord){
      taqr_id = <number> tasrRecord.taqr_id;
    }
    if (!taqr_id){
      throw new Errors.BadRequest(`ERR_NO_TAQR_FOR_TASR`);
    }

    try {
      student_data = await this.retrieveStudentData(taqr_id);
    } catch (e) {
      throw new Errors.BadRequest(`ERR_NO_TAQR_ID`);
    }

    const scanUrl = await generateS3DownloadUrl(tasrRecord.full_scan, 604800);
    try {
      // call scanning service endpoint to crop
      const crop_ratios = {
        top_ratio,
        bottom_ratio,
        left_ratio,
        right_ratio
      }
      const resCropScanPath: any = await this.scanningService.post(`/adjust-and-generate-crops`, { taqrId: taqr_id, scanUrl, student_data, crop_ratios, rotation_degree });

      if (resCropScanPath.data.length > 0) {
        const scan = resCropScanPath.data;

        if (scan === 'ERR_CROP_ADJUSTMENTS'){
          return {}
        }
        else {

          const crop_meta = JSON.stringify(scanAdjusments)

          await this.app
            .service('db/write/scan-review-edits')
            .create({
              srb_id,
              taqr_id,
              created_by_uid: uid,
              created_on: dbDateNow(this.app),
              path_post: scan,
              crop_meta_post: crop_meta,
              tasr_id: tasrRecord.id,
              path_pre: tasrRecord.scan,
              crop_meta_pre: tasrRecord.crop_meta,
            })

          await this.app
            .service('db/write/test-attempt-scan-responses')
            .patch(tasrRecord.id, { scan, crop_meta })

          const url = generateS3DownloadUrl(scan, 60*30);
          return { url, crop_meta };

        }
      }
    } catch (e) {
      console.log(e);
      return false;
    }
    return 0;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
