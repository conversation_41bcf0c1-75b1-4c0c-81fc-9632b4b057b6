import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead, dbRawReadCountReporting, dbRawReadReporting } from '../../../../util/db-raw';
import { SQL_SW_EXTRACT, SQL_SW_EXTRACT_COURSE_OVERRIDES, SQL_SW_EXTRACT_WITHHOLD_OVERRIDES, SQL_SW_QUICK_SUMMARY, SQL_TYPE_SLUG_TO_COURSE_CODE } from '../../../../sql/scoring';
import { Errors } from '../../../../errors/general';
import { IScoringExtractScaleMarkRecord } from './types';

interface Data {}

interface ServiceOptions {}

export class WindowStats implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {marking_window_id, options} = params.query;
      if(options == "scoring-extract" || !options){
        return this.getScoringExtract(marking_window_id);
      }
      if(options == "quick-summary"){
        return this.getQuickSummary(marking_window_id);
      }
        
    }
    return [];
  }

  async getScoringExtract(marking_window_id:number){
    let records:IScoringExtractScaleMarkRecord[] = await dbRawRead(this.app, {marking_window_id}, SQL_SW_EXTRACT);
    records = this.applyReadNumbersToRecords(records);
    if(records.length > 0){
      records = await this.applyAttemptWithholdings(records);
      records = await this.applyCourseOverrides(records);
    }
    return records;

    // let recordsTrimmed:any[] = records.filter(r => r.is_deleted === 0);
    // for (let r of recordsTrimmed){
    //   // delete r['mcbr_id']
    //   delete r['marker_uid']
    //   delete r['type_slug']
    //   delete r['test_window_id']
    // }
    // return recordsTrimmed;
  }

  async getQuickSummary(marking_window_id:number){
    const records = await dbRawRead(this.app, {marking_window_id}, SQL_SW_QUICK_SUMMARY);
    return records
  }

  extractTwAndTypeSlugs(records:IScoringExtractScaleMarkRecord[]){
    const testWindowIds:Set<number> = new Set(); 
    const typeSlugs:Set<string> = new Set(); 
    for (let record of records){
      testWindowIds.add(record.test_window_id)
      typeSlugs.add(record.type_slug)
    }
    const test_window_ids = [... testWindowIds]
    const twtar_type_slugs = [... typeSlugs]
    return {
      test_window_ids,
      twtar_type_slugs,
    }
  }

  async applyAttemptWithholdings(records:IScoringExtractScaleMarkRecord[]){
    const {
      test_window_ids,
      twtar_type_slugs,
    } = this.extractTwAndTypeSlugs(records);
    const overrideCases = await dbRawReadReporting(this.app, {test_window_ids, twtar_type_slugs}, SQL_SW_EXTRACT_WITHHOLD_OVERRIDES);
    const overrideKey = (uid:number, test_window_id:number, type_slug:string, attempt_id:number | string) => [uid, test_window_id, type_slug, attempt_id].join(':')
    const overrideCasesRef:Map<string, boolean> = new Map();
    for (let ovrdCase of overrideCases){
      const {uid, test_window_id, twtar_type_slug, test_attempt_id} = ovrdCase;
      try {
        const key = overrideKey(uid, test_window_id, twtar_type_slug, test_attempt_id || '*');
        overrideCasesRef.set(key, true)
      }
      catch(e){
        throw new Errors.GeneralError('INVALID_OVERRIDE_CONFIG'); // todo:side report these
      }
    }
    // apply remappings
    for (let record of records){
      const {stu_uid, test_window_id, type_slug, attempt_id} = record;
      const keys  = [
        overrideKey(stu_uid, test_window_id, type_slug, attempt_id),
        overrideKey(stu_uid, test_window_id, type_slug, '*')
      ];
      let isAnyWithheld = false;
      for (let key of keys){
        const isWithheld = overrideCasesRef.get(key);
        if (isWithheld){
          isAnyWithheld = true
        }
      }
      if (isAnyWithheld){
        record.is_attempt_withheld = 1
      }
      else {
        record.is_attempt_withheld = 0
      }
    }
    return records
  }

  async applyCourseOverrides(records:IScoringExtractScaleMarkRecord[]){
    // depends on `ac.course_code_foreign course_code` remapping in SQL_SW_EXTRACT
    const {
      test_window_ids,
      twtar_type_slugs,
    } = this.extractTwAndTypeSlugs(records);
    // get relevant information
    const overrideCases = await dbRawReadReporting(this.app, {test_window_ids, twtar_type_slugs}, SQL_SW_EXTRACT_COURSE_OVERRIDES);
    const overrideKey = (uid:number, test_window_id:number, type_slugs:string) => [uid, test_window_id, type_slugs].join(':')
    const overrideCasesRef:Map<string, string> = new Map();
    const newTypeSlugs:Set<string> = new Set()
    for (let ovrdCase of overrideCases){
      const {uid, test_window_id, twtar_type_slug, action_config} = ovrdCase;
      try {
        const {type_slug_new} = JSON.parse(action_config);
        newTypeSlugs.add(type_slug_new);
        const key = overrideKey(uid, test_window_id, twtar_type_slug);
        overrideCasesRef.set(key, type_slug_new)
      }
      catch(e){
        throw new Errors.GeneralError('INVALID_OVERRIDE_CONFIG'); // todo:side report these
      }
    }
    const courseCodeForeignMapByTypeSlugRef:Map<string, string> = new Map()
    if(newTypeSlugs.size > 0){
      // get course code replacements
      const courseCodeMappings = await dbRawReadReporting(this.app, {type_slugs: [...newTypeSlugs]}, SQL_TYPE_SLUG_TO_COURSE_CODE);
      for (let record of courseCodeMappings){
        const {type_slug, course_code_foreign} = record;
        courseCodeForeignMapByTypeSlugRef.set(type_slug, course_code_foreign);
      }
    }
    // apply remappings
    for (let record of records){
      const {stu_uid, test_window_id, type_slug} = record;
      const key = overrideKey(stu_uid, test_window_id, type_slug);
      const type_slug_new = overrideCasesRef.get(key);
      if (type_slug_new){
        const course_code_foreign = courseCodeForeignMapByTypeSlugRef.get(type_slug_new);
        if (course_code_foreign){
          record.type_slug = type_slug_new
          record.course_code = course_code_foreign
        }
        else {
          throw new Errors.GeneralError('MISMAPPED_COURSE_CODE'); // todo:side report these
        }
      }
    }
    return records;
  }

  applyReadNumbersToRecords(records:IScoringExtractScaleMarkRecord[]){
    // Step 1: Extract necessary columns and deduplicate
    const uniqueSet = new Set();  // To store unique string representations of records for deduplication
    const summarizedData:any[] = [];

    records.forEach(record => {
        const summary = {
            attempt_id: record.attempt_id,
            task_slug: record.task_slug,
            marker_read_id: record.marker_read_id,
            is_deleted: record.is_deleted
        };
        const key = JSON.stringify(summary); // Create a unique key based on the summary
        if (!uniqueSet.has(key)) {
            uniqueSet.add(key);
            summarizedData.push(summary);
        }
    });

    // Step 2: Sort the data
    const sortedData = summarizedData.sort((a, b) => {
      if (a.attempt_id !== b.attempt_id) return a.attempt_id - b.attempt_id;
      if (a.task_slug !== b.task_slug) return a.task_slug.localeCompare(b.task_slug);
      if (a.marker_read_id !== b.marker_read_id) return a.marker_read_id - b.marker_read_id;
      return a.is_deleted - b.is_deleted;
    });

    const filteredData = sortedData;

    // Step 3: Determine read_nbr value
    let read_nbr = 1;
    const readMapping = new Map();
    for (let i = 0; i < filteredData.length; i++) {
        if (i > 0 && filteredData[i].attempt_id === filteredData[i - 1].attempt_id && filteredData[i].task_slug === filteredData[i - 1].task_slug) {
            if (!filteredData[i - 1].is_deleted) {
                read_nbr++;
            }
        } else {
            read_nbr = 1;
        }
        const key = `${filteredData[i].attempt_id}|${filteredData[i].task_slug}|${filteredData[i].marker_read_id}`;
        readMapping.set(key, read_nbr);
    }

    // Step 4: Assign read_nbr to each record
    for(let record of records){
      const key = `${record.attempt_id}|${record.task_slug}|${record.marker_read_id}`;
      record.read_nbr = readMapping.get(key) || -1 // Default to -1 if not found
    };

    return records
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
