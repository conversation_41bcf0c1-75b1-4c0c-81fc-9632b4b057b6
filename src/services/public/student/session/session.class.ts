const _ = require('lodash');
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';
import { dbDateToMoment } from '../../../../hooks/_util';
import { Errors } from '../../../../errors/general';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import { generateSecretCode, hashValues } from '../../../../util/secret-codes';
import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { isDatePast, dbDateNow, isDateFuture } from '../../../../util/db-dates';
import { ITestSession, EDeliveryOption } from '../../../db/schemas/test_sessions.schema';
import { ITestWindow } from '../../../db/schemas/test_windows.schema';
import { randArrEntry } from '../../../../util/random';
import axios from 'axios';
import { Knex } from 'knex';
import { dbRawRead, dbRawReadReporting, dbRawReadSingle, dbRawWrite } from '../../../../util/db-raw';
import { ITestAttempt, ITestAttemptInfo } from '../../../db/schemas/test_attempts.schema';
import { ITestDesignPayload } from '../../test-taker/invigilation/test-attempt/test-design.data';
import { HEADER_SEB_REQ } from '../../../../constants/headers';
import { sanitizeFramework } from '../../anon/sample-test-design-form/util/sanitize-framework';
import { numAsBool } from '../../../../util/param-sanitization';
import { ISSConfig } from '../session-question/session-question.class';
import { AssessmentSettings } from '../../school-admin/school-profile/school-profile.class';
import Redis from 'ioredis';
import { GRADE12Class, examRegistrationCheck } from '../../../../login-abed-key.strategy';
import { ABED_IS_ALLOW_DIP_CROSS_WRITING, ENFORCE_TIME_CONTROLS_NON_SAMPLE } from '../../../../constants/site-flags';
import { SQL_ATTEMPT_TIME_CONTROLS, SQL_ATTEMPT_TS_TWTAR_TW_META, SQL_CLASS_TWTAR_AVAIL, SQL_TS_CLASS_LOCK, SQL_TS_CLASS_LOCK_TWTAR_FULL } from './model/sql';
import { StudentSessionTimeControl } from './util/time-control';
import { isRecentlyUnpaused } from '../../../../util/time-control'
import { EarlyYearSlugs, IAttemptTimeInfo } from './model/types';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';
import { FLAGS, isABED } from '../../../../util/whiteLabelParser';
import { SQL_TWTAR_DIRECT } from '../../educator/session/model/sql';


type ITestDsignInfo = {id:number, test_design_id:number, subsession_meta: any, is_classroom_common_form:number};


export const  IS_BCED_CONTEXT = false;
const IS_AUTO_INDENTITY_VERIFIED =  new Set([EDeliveryOption.SCHOOL, EDeliveryOption.SURVEY])
interface Data {}

interface ServiceOptions {}

export interface IGetTestAttemptOptions {
  isCreateNewIfEmpty:boolean,
  isTestTaker:boolean,
  isPresent: boolean
}
export interface ITestAttemptQuestionResponses {
  test_question_id:number,
  response_raw:string
}
export interface IAttemptPayload {
  uid: number,
  test_session_id: number,
  lang?: string,
  created_by_uid?: number,
  delivery_format?: EDeliveryOption
}
export interface ITestDesignInfo {
  test_design_id: Id,
  slug:string,
  source_item_set_id: Id,
  test_form_id: Id,
  alloc_id: Id,
  famework: string,
  delivery_format: EDeliveryOption,
  subsession_meta: string,
  test_form_linear: string
}
export interface IPopNonResConfig {
  cache:any, // todo: what is this exactly?
  resolvedAttempt:any,
}
interface IQueryConfig {
  test_session_id:number,
  lang:string
}

export class Session implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  private async findSchoolsByClassGroupId(schl_class_group_id: number, uid: number) {
    const school = await dbRawRead(this.app, [uid, schl_class_group_id], `
      select s.*
      from schools s
      join school_classes sc
        on s.group_id = sc.schl_group_id
      join user_roles ur
        on ur.group_id = s.group_id
        and uid = ?
        and ur.is_revoked != 1
      where sc.group_id = ?;
      ;
    `);

    //fetch guest_student_school
    if(school.length === 0){
      const guest_student_school = await dbRawRead(this.app, [uid, schl_class_group_id], `
        select s.*
          from schools s
          join school_classes sc on s.group_id = sc.schl_group_id
          join school_classes_guest scg on scg.invig_sc_group_id = sc.group_id and scg.is_revoked != 1
          join user_roles ur on ur.group_id = scg.guest_sc_group_id and uid = ? and ur.is_revoked != 1
         where sc.group_id = ?;
      ;`);
      guest_student_school.forEach(gsd => school.push(gsd))
    }

    return school;
  }

  processSessionPaymentStatus(session: any, school: any) {
    if(!session) {
      throw new Errors.BadRequest('MISSING_TEST_SESSION');
    }

    let paymentRequired = 0;
    if(session?.slug?.includes('G9')) {
      paymentRequired = school.payment_req_g9;
    } else if(session?.slug?.includes('OSSLT')) {
      paymentRequired = school.payment_req_osslt;
    } else if(session?.slug?.includes('JUNIOR') || session?.slug?.includes('PRIMARY')) {
      paymentRequired = school.payment_req_pj;
    }
    console.log('paymentRequired', `${session.slug} r${paymentRequired}? p${session.isPaid}?`)
    session.isPaid = paymentRequired ? session.isPaid : 1;
    console.log('paymentRequiredChecked', `${session.slug} r${paymentRequired}? p${session.isPaid}?`)
    return session
  }

  async verifyStudentPaidForTest(test_session_id: number, uid: number, schl_class_group_id: number) {
    const school = await this.findSchoolsByClassGroupId(schl_class_group_id, uid);
    if (school.length === 0) {
      throw new Errors.NotFound('NO_SCHOOL_ROLE_FOR_USER');
    }

    const test_session = await this.getData([test_session_id, uid], `
        select scts.slug,
        (CASE
          WHEN (tsp.alternative_status = 3)
            THEN 1
          WHEN scts.slug LIKE '%SAMPLE%'
            THEN 1
          ELSE 0
        END) isPaid
        from school_class_test_sessions scts
        join test_sessions ts on ts.id = scts.test_session_id
        join test_attempts ta on ta.test_session_id = scts.test_session_id
        LEFT JOIN student_attempt_purchases sap
          ON ta.uid = sap.uid
          AND sap.is_revoked != 1
          AND (sap.is_refunded != 1 OR sap.is_refunded IS null)
        LEFT JOIN test_session_purchases tsp
          ON sap.ts_purchase_id = tsp.id
          AND tsp.is_revoked != 1
        LEFT JOIN student_attempt_purchases sap2
          ON ta.uid = sap2.uid
          AND sap.id < sap2.id
          AND sap2.is_revoked != 1
          AND (sap2.is_refunded != 1 OR sap2.is_refunded IS null)
        where scts.test_session_id = (?)
          and ts.is_cancelled = 0
          and ts.is_closed = 0
          and ta.uid = ?
          and ts.date_time_start < now()
          AND sap2.id is NULL
        group by ts.id
      ;`);
    if(!(test_session.length > 0)){
      throw new Errors.Forbidden('NO_SESSION_FOUND');
    }
    const session = this.processSessionPaymentStatus(test_session[0], school[0]);

    if(session.isPaid === 0) {
      throw new Errors.Forbidden('STUDENT_NOT_PAID');
    }
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (!params){
      throw new Errors.BadRequest();
    }
    const {schl_class_group_id, allow_inactive_subsession, payCheck} = (<any>params).query;
    if(!schl_class_group_id) {
      throw new Errors.BadRequest('MISSING_REQ_PARAM');
    }
    const uid = await currentUid(this.app, params);

    let classrooms = await this.getData([uid, schl_class_group_id], `
      select sc.id
      from user_roles urt
      join school_classes sc
        on urt.group_id = sc.group_id
        and urt.role_type = 'schl_student'
        and urt.is_revoked != 1
        and sc.is_active = 1
      where urt.uid = ?
      AND sc.group_id = ?
    ;`);

    await this.confirmStudentRegistration(classrooms, uid);

    const inviClassRooms = await this.getData([uid, schl_class_group_id], `
        select sc.id
          from user_roles urt
          join school_classes_guest scg on scg.guest_sc_group_id = urt.group_id and scg.is_revoked != 1
          join school_classes sc on sc.group_id = scg.invig_sc_group_id and sc.is_active = 1
         where urt.role_type = 'schl_student'
           and urt.is_revoked != 1
           and urt.uid = ?
           and sc.group_id = ?
    ;`);
    classrooms = classrooms.concat(inviClassRooms)

    const classIds = classrooms.map(entry => entry.id)

    var classes_sessions:any[] = []
    var scheduled_sessions:any[] = []

    const subsessionRestriction = allow_inactive_subsession ? '' : 'and ta.active_sub_session_id IS NOT NULL'

    if(classIds.length > 0){
      classes_sessions = await this.getData([classIds, uid], `
        select scts.school_class_id, scts.test_session_id, scts.slug, scts.caption, ts.date_time_start,
        (CASE
          WHEN (tsp.alternative_status = 3)
            THEN 1
          WHEN scts.slug LIKE '%SAMPLE%'
            THEN 1
          ELSE 0
        END) isPaid
        from school_class_test_sessions scts
        join test_sessions ts on ts.id = scts.test_session_id
        join test_attempts ta on ta.test_session_id = scts.test_session_id
        LEFT JOIN student_attempt_purchases sap
          ON ta.uid = sap.uid
          AND sap.is_revoked != 1
          AND (sap.is_refunded != 1 OR sap.is_refunded IS null)
        LEFT JOIN test_session_purchases tsp
          ON sap.ts_purchase_id = tsp.id
          AND tsp.is_revoked != 1
        LEFT JOIN student_attempt_purchases sap2
          ON ta.uid = sap2.uid
          AND sap.id < sap2.id
          AND sap2.is_revoked != 1
          AND (sap2.is_refunded != 1 OR sap2.is_refunded IS null)
        where scts.school_class_id IN (?)
          and ts.is_cancelled = 0
          and ts.is_closed = 0
          and ta.uid = ?
          ${subsessionRestriction}
          and ts.date_time_start < now()
          AND sap2.id is NULL
        group by ts.id
      ;`);

      scheduled_sessions = await this.getData([classIds], `
        select scts.school_class_id, scts.test_session_id, scts.slug, scts.caption, ts.date_time_start
        from school_class_test_sessions scts
        join test_sessions ts on ts.id = scts.test_session_id
        where scts.school_class_id IN (?)
          and ts.is_cancelled = 0
          and ts.is_closed = 0
          and ts.date_time_start > now()
      ;`);
    }

    const school = await this.findSchoolsByClassGroupId(schl_class_group_id, uid)

    if (school.length === 0) {
      throw new Errors.NotFound('NO_SCHOOL_ROLE_FOR_USER');
    }

    classes_sessions.forEach(session => this.processSessionPaymentStatus(session, school[0]));
    scheduled_sessions.forEach(session => this.processSessionPaymentStatus(session, school[0]));

    return [{
      classrooms,
      scheduled_sessions,
      classes_sessions,
    }];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    // throw new Errors.GeneralError('WINDOWS_CLOSED');
    if (params && id){
      const {schl_class_group_id, lang} = (<any>params).query;
      const test_session_id = <number> id;
      const uid = await currentUid(this.app, params);
      const ip_addr = params.ip;

      await this.verifyStudentPaidForTest(test_session_id, uid, schl_class_group_id);
      
      await this.app.service('public/student/lock-down').validateSecurity(params, uid, test_session_id);

      // to do: validate access of this user to this test session.
      const attempts = await this.findAttemptPayload({uid, test_session_id}, params);
      const attempt = attempts[0];
      if (attempt){
        await this.validateAttemptId(uid, attempt.attemptId, false, undefined, true);
      }
      return attempts

    }
    throw new Errors.BadRequest();
  }

  private async getAsmtSettings(schoolRecord: any, slug: string): Promise<AssessmentSettings> {
    const findParams = {
      query: {
        school_info: [{
          id: schoolRecord.id, 
          type_slug: schoolRecord.type_slug
        }]
      }
    }
    const school_assessments = <any[]> await this.app.service('public/school-admin/school-profile').find(findParams);
    const asmt_settings: AssessmentSettings = school_assessments[0].find(
      (asmt: AssessmentSettings) => slug.includes(asmt.settings_type_slug)
    );
    return asmt_settings;
  }

  public async allowInsecureSchool(test_session_id:number){
    let allowInsecure = false;
    let testWindowRecord;
    let slug = '';
    const testSessionRecord = await this.app.service('db/read/test-sessions').get(test_session_id);
    if(testSessionRecord && testSessionRecord.test_window_id) {
      testWindowRecord = await this.app.service('db/read/test-windows').get(testSessionRecord.test_window_id);
    }
    if(testWindowRecord) {
      slug = testWindowRecord.type_slug;
    }
    const schoolRecords = <any[]> await this.app
      .service('db/read/schools')
      .find({
        query: {
          group_id: testSessionRecord.schl_group_id,
        },
        paginate:false
      });
    const schoolRecord = schoolRecords[0];
    const asmt_settings = await this.getAsmtSettings(schoolRecord, slug);

    if (asmt_settings?.is_insecure) {
      allowInsecure = true;
    }
    if(schoolRecord.is_insecure == 1) {
      allowInsecure = true;
    }
    // if((slug === 'EQAO_G3P' || slug === 'EQAO_G6J') && schoolRecord.is_insecure_pj == 1){
    //   allowInsecure = true;
    // }
    // if(slug === 'EQAO_G9M' && schoolRecord.is_insecure_g9 == 1){
    //   allowInsecure = true;
    // }
    // if(slug === 'EQAO_G10L' && schoolRecord.is_insecure_g10 == 1){
    //   allowInsecure = true;
    // }
    return allowInsecure;
  }

  private async loadStudentMetas(uid:number){
    const student_metas = await this.getData([uid], `
      select key_namespace, \`key\`, \`value\`
      from user_metas
      where uid = ?
    ;`);
    const studentMetaMap = new Map();
    student_metas.forEach((meta:any) => {
      studentMetaMap.set(meta.key_namespace+'.'+meta.key, meta.value);
    });
    return studentMetaMap;
  }

  public async loadNewStudentTestDesign(uid:number, test_session_id:Id, twtdar_order:number = 0, delivery_format?: EDeliveryOption, lang?:string | null) : Promise<ITestDesignInfo>{

    const testSession = await dbRawReadSingle(this.app, {test_session_id}, `
      select ts.*
           , scts.slug as session_slug
           , sd.fi_option
           , sd.is_sample 
      from test_sessions ts 
      join school_class_test_sessions scts 
        on scts.test_session_id = ts.id
      join school_classes sc 
        on sc.id = scts.school_class_id 
      join school_districts sd 
        on sd.group_id = sc.schl_dist_group_id 
      where ts.id = :test_session_id;
    `);

    const schl_dist_fi_otion = testSession.fi_option
    const session_slug = testSession.session_slug
    const test_window_id = testSession.test_window_id
    const custom_twtar_id = testSession.twtdar_id;
    const isSampleSchool = (testSession.is_sample==1)
    const isScoreEntry = session_slug.includes(EarlyYearSlugs.SUFFIX)
    let slug = session_slug; // For students

    let testFormSelection
    let testDesignMatch:{test_design_id:Id, id:Id, subsession_meta:any, is_classroom_common_form:number};
    
    const studentMetaMap = await this.loadStudentMetas(uid); // todo:this is s not still necessary

    if (custom_twtar_id){
      const twtarRecord = await dbRawReadSingle(this.app, {twtar_id: custom_twtar_id}, SQL_TWTAR_DIRECT) || {};
      testDesignMatch = {
        test_design_id: twtarRecord.test_design_id,
        id: twtarRecord.id,
        is_classroom_common_form: twtarRecord.is_classroom_common_form,
        subsession_meta: twtarRecord.subsession_meta,
      }
    }
    else {
      // For teacher admin questionnaire
      if(delivery_format === EDeliveryOption.SURVEY){
        const questionnaireTestSession = <Paginated<any>> await this.app
          .service('db/write/questionnaire-test-sessions')
          .find({ query: { test_session_id }})
        const qtsRecord = questionnaireTestSession.data[0]
        slug = qtsRecord.slug
      }
      
      const formLockRecords = await dbRawReadReporting(this.app, {test_session_id, slug}, SQL_TS_CLASS_LOCK);
      const locked_twtdar_ids = formLockRecords.map(r => r.twtdar_id)
      const focus_on_custom_assessments = false; // todo: need a clean way to set this, not assuming its going to come cleanly from the query parameters, have to be careful
  
      const test_window_td_alloc_rules = await dbRawReadReporting(
        this.app, 
        { 
          test_window_id, 
          slug, 
          twtdar_order,
          locked_twtdar_ids
        }, 
        SQL_CLASS_TWTAR_AVAIL({
          isScoreEntry,
          isSampleSchool,
          hasSpecificIds: locked_twtdar_ids.length>0,
          focus_on_custom_assessments
        })
      )
      if (test_window_td_alloc_rules.length === 0) {
        let isClosing = false;
        if (testSession.is_closed == 0){
          isClosing = true;
          await this.app.service('db/write/test-sessions').patch(test_session_id, {
            is_closed: 1, 
            closed_on: dbDateNow(this.app),
          })
        }
        throw new Errors.BadGateway('NO_TEST_DESIGN_RULE_MATCH', {session_slug, isSampleSchool, isClosing});
      }
      const whiteLabel = this.app.get('whiteLabel');
  
      // let isTestDesignMatchFound:boolean = false; //add test_design_id to be an array
      const testDesignMatches: ITestDsignInfo[] = [];
      testDesignMatch = {id:-1, test_design_id:-1, subsession_meta: undefined, is_classroom_common_form:0};
        // If twtdar is common form, find common twtar for this school class
      if(test_window_td_alloc_rules[0].is_classroom_common_form === 1 && test_session_id){
        const commonTwtdar = await dbRawReadSingle(this.app, {test_session_id, slug}, SQL_TS_CLASS_LOCK_TWTAR_FULL);
        // Filter user metas
        const filters:{[key:string]:Array<string|number>} = JSON.parse(commonTwtdar.user_metas_filter || '{}');
        let isAllRulesPassed = true;
          Object.keys(filters).forEach(key => {
            const possibleValues = filters[key].map(val => ''+val);
            // For questionnair used lang filter for now
            // #TODO: Maybe insert teacher/student record in user_metas with `lang` as key
            const val = (delivery_format === EDeliveryOption.SURVEY && lang) ? lang : ''+studentMetaMap.get(key);
            if (possibleValues.length > 0){
              if (possibleValues.indexOf(val) === -1){
                isAllRulesPassed = false;
              }
            }
            else {
              if (val && val !== '#'){
                isAllRulesPassed = false;
              }
            }
          });
          if (!isAllRulesPassed){
            // If not pass, throw error
            throw new Errors.BadGateway('COMMON_FORM_TEST_DESIGN_RULE_NOT_MATCH');
          }
          testDesignMatch = commonTwtdar;
      }
      else{
        test_window_td_alloc_rules.forEach(testDesignCandidate => {
          const filters:{[key:string]:Array<string|number>} = JSON.parse(testDesignCandidate.user_metas_filter || '{}');
          let isAllRulesPassed = true;
          Object.keys(filters).forEach(key => {
            const possibleValues = filters[key].map(val => ''+val);
            // For questionnair used lang filter for now
            // #TODO: Maybe insert teacher/student record in user_metas with `lang` as key
            const val = (delivery_format === EDeliveryOption.SURVEY && lang) ? lang : ''+studentMetaMap.get(key);
            if (possibleValues.length > 0){
              if (possibleValues.indexOf(val) === -1){
                isAllRulesPassed = false;
              }
            }
            else {
              if (val && val !== '#'){
                isAllRulesPassed = false;
              }
            }
          });
          if (isAllRulesPassed){
            testDesignMatches.push(testDesignCandidate);
            // isTestDesignMatchFound = true;
            // testDesignMatch = testDesignCandidate
          }
        });
        if (testDesignMatches.length > 0){
          testDesignMatch = randArrEntry(testDesignMatches);
        }else{
          throw new Errors.BadGateway('NO_TEST_DESIGN_RULE_MATCH');
        }
      }
      
    }

    const testDesignRecord = await this.app .service('db/read/test-designs').get(testDesignMatch.test_design_id);
    if (!testDesignRecord){
      throw new Errors.NotFound();
    }
    const testFormRefs = <Paginated<any>> await this.app
      .service('db/read/test-forms')
      .find({
        query: {
          $select: ['id', 'lang'],
          test_design_id: testDesignMatch.test_design_id,
          is_revoked: 0,
          $limit: 1000,
        }
      });

    testFormSelection = randArrEntry(testFormRefs.data);


    return {
      test_design_id: testDesignMatch.test_design_id,
      slug,
      source_item_set_id: testDesignRecord.source_item_set_id,
      test_form_id: testFormSelection.id,
      alloc_id: testDesignMatch.id,
      famework: testDesignRecord.framework,
      delivery_format: testSession.delivery_format,
      subsession_meta: testDesignMatch.subsession_meta,
      test_form_linear: studentMetaMap.get('eqao_dyn.Linear')
    }
  }

  public async loadTestDesignForm(test_design_id:number){
    return {
      test_design_id,
      // testFormData,
      // framework: testDesignRecord.framework,
    };
  }

  public async getTestDesign(file_path:string, testFormId?:number, sourceFormId?:number){
    const testDesign = await this.getTestDesignPromise(file_path);
    return <any> {
      testFormId,
      sourceFormId,
      ... testDesign.data
    };
  }

  public getTestDesignPromise(file_path: string) {
    const url = generateS3DownloadUrl(file_path, 60);
    return axios.get(url, {});
  }

  private async ensureAttemptMarkedStarted(currentAttempt:ITestAttempt){
    if (!currentAttempt.started_on){
      const dbNow = dbDateNow(this.app);
      await this.app
        .service('db/write/test-attempts')
        .patch(currentAttempt.id, {
          started_on: dbNow,
          last_touch_on: dbNow,
        });
    }
  }

  private async getAttemptQuestionResponses(test_attempt_id:number){
    // await this.app.service('public/student/session-question').removeDuplicateQuestionAttemptRecords(test_attempt_id); // this is not necessary because we are anyway pulling the records in order of id
    const questionResponseStates = <ITestAttemptQuestionResponses[]> await this.app
      .service('db/read/test-attempt-question-responses')
      .find({query:{
        test_attempt_id,
        is_invalid: 0,
      }, paginate: false});
    const questionStates:{[key:string]: string} = {};
    questionResponseStates.forEach(q => {
      questionStates[q.test_question_id] = JSON.parse(q.response_raw)
    });
    const redis:Redis = this.app.get('redis');
    if (redis) {
      const submData = await redis.hgetall(`submData:${test_attempt_id}`);
      for (let questId in submData) {
        const questSubm = JSON.parse(submData[questId]);
        if (typeof questSubm.response_raw === 'string') {
          questionStates[questId] = JSON.parse(questSubm.response_raw);
        } else {
          questionStates[questId] = questSubm.response_raw;
        }
      }
    }
    return questionStates;
  }

  public async getAttemptTimeInfo(attempt_id:Id){
    const attemptTimeInfo:IAttemptTimeInfo = await dbRawReadSingle(this.app, {attempt_id}, SQL_ATTEMPT_TIME_CONTROLS);
    const studentSessionTimeControl = new StudentSessionTimeControl(attemptTimeInfo)
    return studentSessionTimeControl.evaluate();
  }

  public async loadAttemptTestFormData(currentAttempt:ITestAttempt){
    let test_form_cache = {};
    if (currentAttempt.test_form_cache){
      try {
        test_form_cache = JSON.parse(currentAttempt.test_form_cache);
      }
      catch(e){}
    }
    const testFormData = await this.loadTestFormById(currentAttempt.test_form_id);
    return {
      test_form_id: currentAttempt.test_form_id,
      ... testFormData,
      ... test_form_cache, // because this one comes second, it will override any properties that have the same name in the pristine static test form payload (which is loaded from S3)
    }
  }

  private async loadFrameworkByTestformId(test_form_id:number){
    const records = await dbRawRead(this.app, [test_form_id], `
      select td.framework
      from test_forms tf
      join test_designs td on td.id = tf.test_design_id
      where tf.id = ?
    ;`);
    return sanitizeFramework(records[0].framework)
  }

  async loadStyleProfileByTestDesign(test_form_id:number){
    const db:Knex = await this.app.get('knexClientRead');

    const tdStyleProfileRecs = <any[]>await db('test_forms as tf')
    .join('test_designs as td', 'tf.test_design_id', 'td.id')
    .join('style_profile_versions as spv', 'spv.id', 'td.style_profile_version_id')
    .join('style_profiles as sp', 'sp.id', 'spv.style_profile_id')
    .where('tf.id', test_form_id)
    .select('td.profile_path', 'td.style_profile_version_id', 'sp.id as style_profile_id', 'sp.slug')

    if(tdStyleProfileRecs.length){
      // always load packed style profile from S3 - this will be important when cardinality is implemented
      const tdStyleProfileRec = tdStyleProfileRecs[0];
      const s3styleProfile = await this.getTestDesignPromise(tdStyleProfileRec.profile_path);

      return {
        id: tdStyleProfileRec.style_profile_id,
        slug: tdStyleProfileRec.slug, 
        config: s3styleProfile.data,
        version_id: tdStyleProfileRec.style_profile_version_id,
      }

    }
    return undefined;    
  }

  private async getDeliveryFormat(test_session_id:number){
    const deliveryFormatInfo = await this.app.service('db/read/test-sessions')
      .db()
      .select('delivery_format')
      .where('id', test_session_id)
    return deliveryFormatInfo[0].delivery_format;
  }

  private async getDictionaryAvailability(twtdar_id:number){
    const dictionaryAvailabilityInfo = await dbRawRead(this.app, [twtdar_id], `
    select twtdar.is_dictionary_enabled
    from test_window_td_alloc_rules twtdar
    where twtdar.id = ?
    ;`);
    return dictionaryAvailabilityInfo[0].is_dictionary_enabled;
  }

  public async findAttemptPayload(data: IAttemptPayload,params: Params){
    const { uid, test_session_id, lang} = data;
    const created_by_uid = data.created_by_uid || uid;
    const isOnBeHalf = (created_by_uid !== uid);
    const delivery_format = await this.getDeliveryFormat(test_session_id);
    const accommodations = await this.app.service('public/educator/student-accommodations').getStudentAccommodationsMap(uid)
    const isCreateNewIfEmpty = delivery_format === EDeliveryOption.SURVEY ? true : false
    const currentAttempt:ITestAttempt = await this.getCurrentAttempt(uid, test_session_id, {isCreateNewIfEmpty, isTestTaker:!isOnBeHalf, isPresent: true}, delivery_format, lang);
    const isPreviouslyOpened = !!(currentAttempt.started_on)
    // await this.identifyPreAssessmentStep(currentAttempt, true); // if this does not throw an error, it means there are no more pre-setup steps left to be done
    await this.ensureAttemptMarkedStarted(currentAttempt);
    const testDesign:ITestDesignPayload = await this.loadAttemptTestFormData(currentAttempt);
    const questionStates = await this.getAttemptQuestionResponses(currentAttempt.id)
    const framework = await this.loadFrameworkByTestformId(currentAttempt.test_form_id)
    const styleProfileConfig = await this.loadStyleProfileByTestDesign(currentAttempt.test_form_id);
    const is_issue_reporting_enabled =  0; // todo: move this into the test window configuration
    const {isDurationEnforced, time_ext_m, duration_m, ends_on, isNotStarted, isSessionEnded} = await this.getAttemptTimeInfo(currentAttempt.id);
    const subSessionData = await this.validateSubSessionAttempt(currentAttempt.id, currentAttempt, delivery_format);
    const isDictionaryEnabled = currentAttempt.twtdar_id !== undefined ?  await this.getDictionaryAvailability(currentAttempt.twtdar_id) : 0;
    const testSessionInfo = await dbRawReadSingle(this.app ,{test_session_id}, `
      select scts.slug
           , sc.group_type
           , ts.is_soft_lock_enabled
           , ts.is_soft_lock_disabled
      from test_sessions ts
      join school_class_test_sessions scts
        on scts.test_session_id = ts.id
      join school_classes sc 
        on sc.id = scts.school_class_id
      where ts.id = :test_session_id
    `)
    let asmt_slug;
    let group_type;
    if(testSessionInfo) {
      asmt_slug = testSessionInfo.slug;
      group_type = testSessionInfo.group_type;
    }
    const assistiveTech = await this.verifyAssistedTechAccomodation(uid, group_type, asmt_slug)
    const linear = await this.getLinear(uid, group_type);
    const IsCrScanDefault = await this.getUserMetaProp(uid, group_type, 'IsCrScanDefault');

    currentAttempt.section_index = currentAttempt.section_index || 0;
    currentAttempt.question_index = currentAttempt.question_index || 0;
    let subsession_index = undefined;
    let sections_allowed;
    if (subSessionData){
      subsession_index = subSessionData.order;
      if (subSessionData.activeSubSessionAttempt){
        const tass = subSessionData.activeSubSessionAttempt;
        if(tass.sections_allowed) {
          sections_allowed = JSON.parse(tass.sections_allowed);
        }
        if (!tass.started_on){
          await this.app
            .service('db/write/test-attempt-sub-sessions')
            .patch(tass.id, {
              started_on: dbDateNow(this.app),
              last_touch_on: dbDateNow(this.app),
            });
        }
      }

      if(!sections_allowed) {
        sections_allowed = subSessionData.sections_allowed; //Used temporarily for transitioning to new logic - now should use tass.
      }
      if (sections_allowed && currentAttempt.section_index < sections_allowed[0]){
        currentAttempt.section_index = sections_allowed[0];
        currentAttempt.question_index = 0;
        currentAttempt.module_id = undefined;
      }
    }

    const isSebMode = this.isLockdownSoftlock(params);

    const sessionTabHash = generateSecretCode(16)

    return [
      {
        lang: currentAttempt.lang,
        question_index: currentAttempt.question_index,
        section_index: currentAttempt.section_index,
        module_id: currentAttempt.module_id,
        attempt_key: currentAttempt.attempt_key,
        attemptId: currentAttempt.id,
        isPreviouslyOpened,
        sessionTabHash,
        assistiveTech,
        linear,
        isSebMode,
        is_soft_lock_enabled: testSessionInfo.is_soft_lock_enabled,
        is_soft_lock_disabled: testSessionInfo.is_soft_lock_disabled,
        attempt_twtdar_order: currentAttempt.twtdar_order,
        framework,
        accommodations,
        testDesign,
        styleProfileConfig,
        is_issue_reporting_enabled,
        questionStates,
        sections_allowed,
        subsession_index,
        asmt_slug,
        IsCrScanDefault,
        isDurationEnforced, 
        time_ext_m,
        duration_m, 
        ends_on, 
        isNotStarted, 
        isSessionEnded,
        isDictionaryEnabled,
      }
    ];
  }

  private async identifyPreAssessmentStep(currentAttempt:ITestAttempt, markAsPresent:boolean){
    // ensure tt is marked as present
    if (markAsPresent && currentAttempt.is_present === 0){
      await this.app
        .service('db/write/test-attempts')
        .patch(currentAttempt.id, {
          is_present: 1,
          is_absent: 0,
        });
    }
    // ensure tt has accepted attestation (to do: this should only apply if the test session requires attestation)
    if (!currentAttempt.is_attested){
      throw new Errors.Forbidden('ACCEPT_ATTEST');
    }
    // ensure tt has identified their language (to do: this might be fine to leave as is when language is pre-selected for tt, as long as it is indicated in the test-attempt entry by the system before this request is made)
    if (!currentAttempt.lang){
      throw new Errors.Forbidden('SELECT_LANG');
    }
  }

  getNamespace(group_type: string) {
    switch(group_type) {
      case 'EQAO_G3':
        return 'eqao_sdc_g3';
      case 'EQAO_G6':
        return 'eqao_sdc_g6';
      case 'EQAO_G9':
        return 'eqao_sdc';
      case 'EQAO_G10':
        return 'eqao_sdc_g10';
    }
    return 'eqao_sdc';
  }

  async getUserMetaProp(uid: number, group_type: string, key:string, asNum?: boolean) {
    const namespace = this.getNamespace(group_type);
    const values = await this.app.service('db/read/user-metas').db().where('uid', uid).where('key', key).where('key_namespace', namespace).pluck('value');

    if(!values?.length && asNum) {
      return asNum ? 0 : null;
    }

    let val = values[0];

    if(!asNum) {
      return val;
    }

    if(!val || val === '#') {
      val = 0;
    } else {
      val = +val;
    }
    return val;
  }


  async getLinear(uid: number, group_type: string) {
    return this.getUserMetaProp(uid, group_type, 'Linear', true)
  }

  async verifyAssistedTechAccomodation(uid:number, group_type: string, asmt_slug?: string){
    const namespace = this.getNamespace(group_type);
    let keyToSearch = ['AccAssistiveTech'];
    if (asmt_slug) {
      if (asmt_slug === 'PRIMARY_OPERATIONAL' || asmt_slug === 'JUNIOR_OPERATIONAL') {
        keyToSearch = [];
        keyToSearch.push('AccAssistiveTechMath', 'AccAssistiveTechRead', 'AccAssistiveTechWrite');
      }
    }
    // todo:DB_DATA_MODEL why 1 and 2?
    const studentRecord = await dbRawRead(this.app, {uid, keyToSearch, namespace}, `
      select um.*
      from user_metas um
      where um.uid = :uid
        and um.key in (:keyToSearch)
        and um.value IN ('1','2')
        and um.key_namespace = :namespace
    `);
    return !!studentRecord.length;
  }

  private async validateSessionTiming(attemptTimeInfo: IAttemptTimeInfo){
    const sessionTimeCtrl = new StudentSessionTimeControl(attemptTimeInfo);
    sessionTimeCtrl.validate();
  }

  private async validateAttemptWindowAndTiming(attemptRecord:ITestAttemptInfo, testSession:ITestSession){
    const test_session_time_ext_m = attemptRecord.session_time_ext_m || 0;
    const user_time_ext_m = attemptRecord.time_ext_m || 0;
    let momentTimeStart = dbDateToMoment(attemptRecord.date_time_start);
    const testWindow =  <ITestWindow> await this.app.service('db/read/test-windows').get(testSession.test_window_id);
    const isActive = testWindow.is_active;
    const isArchived = testWindow.is_archived;
    if (!isActive){
      throw new Errors.Forbidden('TEST_WINDOW_INACTIVE');
    }
    if (isArchived){
      throw new Errors.Forbidden('TEST_WINDOW_ARCHIVED');
    }
    const test_window_time = testWindow.duration_m || 0;
    const timeClose = momentTimeStart.add(test_session_time_ext_m+user_time_ext_m+test_window_time, 'minutes');
    if (isDatePast(timeClose.format())){
      throw new Errors.Forbidden('TIME_OUT');
    }
  }

  public async validateSubSessionAttempt(attempt_id:number, attemptRecord?:ITestAttempt, delivery_format?: EDeliveryOption, subsession_config?: ISSConfig){
    // todo: check if in a sub session structure test session
    if(delivery_format === EDeliveryOption.SURVEY) return null;

    if (!attemptRecord){
      attemptRecord = <ITestAttempt> await this.app.service('db/read/test-attempts').get(attempt_id);
    }

    let subsession_slug: any = '';
    let subsession_order: any = '';

    if (subsession_config) {
      subsession_slug = subsession_config.subsession_slug;
      subsession_order = subsession_config.subsession_order;
    }

    let tsssId = null;
    if (subsession_slug && subsession_slug.length > 0) {
      let tsssSlug = subsession_slug;
      // let tsssRecord;

      const tsssRec = await this.app.service('db/read/test-session-sub-sessions')
        .db()
        .where('test_session_id', attemptRecord.test_session_id)
        .where('slug', tsssSlug)
        .where('order', subsession_order);
      if(tsssRec[0]){
        tsssId = tsssRec[0].id;
      }
    }

    const active_sub_session_id = tsssId ? tsssId : attemptRecord.active_sub_session_id;
    if (active_sub_session_id === null){
      throw new Errors.Forbidden('SESSION_CLOSED');
    }
    const subSession = <any> await this.app.service('db/read/test-session-sub-sessions').get(<number> active_sub_session_id);
    let sections_allowed;

    if(subSession.sections_allowed) {
      sections_allowed = JSON.parse(subSession.sections_allowed);
    }
    const is_last:number = subSession.is_last;
    const is_last_bool = is_last === 1 ? true : false;
    const subSessionAttempts = <any> await this.app.service('db/read/test-attempt-sub-sessions').find({query:{test_attempt_id:attempt_id, sub_session_id:active_sub_session_id, is_invalid: {$ne: 1}}, paginate: false});
    const activeSubSessionAttempt = subSessionAttempts[0];
    return {activeSubSessionAttempt, sections_allowed, is_last: is_last_bool, order: subSession.order};
  }

  public async validateAttemptId(uid:number, attempt_id:number, isOnBehalf:boolean=false, subsession_config?: ISSConfig, isAttemptEntry:boolean=false){
    
    const attemptRecord:ITestAttemptInfo = await dbRawReadSingle(this.app, {attempt_id}, SQL_ATTEMPT_TS_TWTAR_TW_META);

    if (!attemptRecord){
      throw new Errors.Forbidden('NOT_BOOKED_APPL'); 
    }
    if (!attemptRecord.is_identity_verified && !isOnBehalf){ // mpt only
      throw new Errors.Forbidden('NOT_VERIFIED');
    }
    if (attemptRecord.is_closed){
      throw new Errors.Forbidden('ATTEMPT_CLOSED');
    }
    if (attemptRecord.is_identity_missing && !isOnBehalf){ // mpt only
      throw new Errors.Forbidden('MARKED_NO_ID');
    }
    if (attemptRecord.is_absent && !isOnBehalf){
      throw new Errors.Forbidden('MARKED_ABSENT');
    }
    if (attemptRecord.is_session_closed){
      throw new Errors.Forbidden('SESSION_CLOSED');
    }
    if (attemptRecord.is_session_paused == 1){
      // throw new Errors.Forbidden('NOT_VERIFIED');
      throw new Errors.Forbidden('ATTEMPT_PAUSED');
    }
    if (isAttemptEntry) {
      if (attemptRecord.is_paused == 1 && !isRecentlyUnpaused(attemptRecord.unpaused_on)) {
        throw new Errors.Forbidden('ATTEMPT_PAUSED');
      }
    } else {
      if (attemptRecord.is_paused == 1) {
        throw new Errors.Forbidden('ATTEMPT_PAUSED');
      }
    }
    if (attemptRecord.is_session_cancelled == 1){
      throw new Errors.Forbidden('SESSION_CANCELLED');
    }
    if (attemptRecord.is_tw_closed || attemptRecord.is_hard_closed){
      throw new Errors.Forbidden('SESSION_CLOSED');
    }

    await this.validateSessionTiming({
      ta_started_on: ''+(attemptRecord.started_on || ''),
      ts_started_on: ''+attemptRecord.date_time_start,
      ta_time_ext_m: attemptRecord.time_ext_m || 0,
      ts_time_ext_m: attemptRecord.session_time_ext_m || 0,
      twtar_duration_m: attemptRecord.test_duration || 0,
      ts_duration_m: attemptRecord.ts_duration_m || 0,
      twtar_is_sample: attemptRecord.is_sample || 0,
      twtar_test_date_end: attemptRecord.test_date_end,
      is_sample_time_enforced: attemptRecord.is_sample_time_enforced || 0,
      hardstop_offset_h: attemptRecord.hardstop_offset_h || 0,
    });

    // if (attemptRecord.delivery_format !== EDeliveryOption.SCHOOL && attemptRecord.delivery_format !== EDeliveryOption.SURVEY){
    //   await this.validateAttemptWindowAndTiming(attemptRecord, testSession);
    // }

    let subSessionData:{activeSubSessionAttempt:any, is_last: boolean, order: number} | null = null;
    if (attemptRecord.delivery_format === EDeliveryOption.SCHOOL && subsession_config){
      subSessionData = await this.validateSubSessionAttempt(attempt_id, undefined, undefined, subsession_config);
    }

    // this would indicate that it is the first time that this student is accessing this attempt
    return {attemptRecord, subSessionData};
  }


  // likely needs to be somewhat re-written
  async getRandomTestFormId(test_session_id:number){
    const availableTestForms = <Paginated<any>> await this.app.service('db/read/test-session-test-forms').find({query: {test_session_id, $limit:1000} });
    const i = Math.floor(availableTestForms.data.length * Math.random());
    const test_form_id = availableTestForms.data[i].test_form_id;
    return test_form_id;
  }

  private async loadTestFormById(test_form_id:Id){
    const testFormRecord = await this.app.service('db/read/test-forms').get(test_form_id);
    return this.getTestDesign(testFormRecord.file_path, testFormRecord.id, testFormRecord.source_tf_id);
  }

  public async generateTestFormCache(testDesign:ITestDesignInfo) : Promise<string>{
    let cache:{[key:string]:any} = {};
    try {
      // temp: these are temporary... just need to cycle out the forms after completing this ticket: https://bubo.vretta.com/vea/platform/vea-web-client/-/issues/2246
      const framework = JSON.parse(testDesign.famework);
      cache.testFormType = framework.testFormType;
      cache.isTimerDisabled = framework.isTimerDisabled;
      cache.referenceDocumentPages = framework.referenceDocumentPages;
      cache.helpPageId = framework.helpPageId;
    }catch(e){}
    const testFormData = await this.loadTestFormById(testDesign.test_form_id);
    try {
      if (testFormData.panelModules){
        testFormData.panelModules.forEach((panelModule:{questions:number[]}) => {
          panelModule.questions = _.shuffle(panelModule.questions);
        })
        cache.panelModules = testFormData.panelModules
      }
    }catch(e){}
    return JSON.stringify(cache);
  }


  private async createAttempt(uid:number, isTestTaker:boolean, test_session_id:number, lang:string | null, is_present:number = 1, twtdar_order = 0, delivery_format?:EDeliveryOption){
    // const test_form_id = await this.getRandomTestFormId(test_session_id);
    const testDesign = await this.loadNewStudentTestDesign(uid, test_session_id, twtdar_order, delivery_format, lang);
    const test_form_id = <number> testDesign.test_form_id;
    const test_form_cache = await this.generateTestFormCache(testDesign);
    const createFields:Partial<ITestAttempt> = {
      uid,
      test_session_id,
      lang: <any>lang,
      section_index: 0,
      question_index: 0,
      test_form_id,
      test_form_cache,
      attempt_key: generateSecretCode(5),
      is_present,
      is_absent: 0,
      twtdar_order,
      twtdar_id: <number>testDesign.alloc_id,
      test_form_linear: testDesign.test_form_linear
    }
    if (IS_AUTO_INDENTITY_VERIFIED.has(testDesign.delivery_format)){
      createFields.is_identity_verified = 1;
    }
    if (isTestTaker){
      createFields.started_on = dbDateNow(this.app);
    }
    return this.app
    .service('db/write/test-attempts')
    .create(createFields).then((value) => {
      return {
        ...value,
        subsession_meta: testDesign.subsession_meta
      }
    })
  }

  public isRecentlyUnpaused(unpausedOn: any): boolean {
    if (unpausedOn === null) {
        return false;
    }

    const now = Date.now();
    const timeDifference = now - unpausedOn.getTime();

    const MINUTE_TO_MS = 60000;// This can be defined somewhere more global
    return timeDifference >= 0 && timeDifference <= MINUTE_TO_MS;
  }

  public validateSessionKey(uid:number, test_session_id:number, timestamp_start:number, sessionHash:string){
    return (sessionHash === this.generateSessionKey(uid, test_session_id, timestamp_start));
  }

  public  generateSessionKey(uid:number, test_session_id:number, timestamp_start:number){
    const sessionHash = hashValues([uid, test_session_id, timestamp_start])
    return sessionHash
  }

  public async createAttemptByTeacher(uid:number, test_session_id:number, twtdar_order:number = 0)  {
    return this.createAttempt(uid, false, test_session_id, null, 0, twtdar_order );
  }

  private async getCurrentAttempt(uid:number, test_session_id:number, options:IGetTestAttemptOptions,  delivery_format?: EDeliveryOption, lang?:string | null)  {

    const numSubSessionsRows = await this.app.service('db/read/test-session-sub-sessions')
    .db()
    .where('test_session_id', test_session_id)
    .count()

    let numSubSessions = 0;
    if(numSubSessionsRows.length > 0) {
      // numSubSessions = numSubSessionsRows[0].count as number;
      numSubSessions = (numSubSessionsRows[0]["count(*)"] as number) || (numSubSessionsRows[0].count as number);
    }

    let query:any = {uid, test_session_id, $limit:1};

    if(numSubSessions > 0) {
      query["active_sub_session_id"] = {$ne: null};
      options.isCreateNewIfEmpty = false;
    }

    const attempts = <ITestAttempt[]> await this.app
      .service('db/read/test-attempts')
      .find({query: {
        uid,
        test_session_id,
        $limit:1,
        active_sub_session_id: numSubSessions > 0 ? {$ne: null} : null,
        is_invalid: {$ne: 1}
      }, paginate: false })
    if (attempts.length > 0){
      if(delivery_format === EDeliveryOption.SURVEY){
        await this.validateAttemptId(attempts[0].uid, attempts[0].id);
      }
      return attempts[0];
    }
    if (options.isCreateNewIfEmpty){
      const present_bool_int = options.isPresent ? 1 : 0;
      if(delivery_format === EDeliveryOption.SURVEY && lang){
        return await this.createAttempt(uid, options.isTestTaker, test_session_id, lang, present_bool_int, 0, delivery_format);
      }
      return await this.createAttempt(uid, options.isTestTaker, test_session_id, null, present_bool_int);
    }

    throw new Errors.Forbidden("SESSION_CLOSED");
  }


  public async closeAttempt (test_attempt_id:number, closedByUid:number, forceClose:boolean = false, cache:any = {}, testSessionId?:number, tassConfig?: {id:number, closesTestAttempt: boolean, twtdar_order: number, test_session_id: number, attemptUid: number, ssOrder: number, autoOpenNext: boolean}, forceKeepActiveSubsession:boolean=false ) {

    const is_submitted = forceClose ? undefined : 1;
    // const attempt:ITestAttempt = await this.app.service('db/read/test-attempts').get(test_attempt_id);

    if(tassConfig !== undefined && tassConfig.id !== undefined) {
      await this.app.service('db/write/test-attempt-sub-sessions').patch(tassConfig.id, {
        is_submitted,
        last_locked_on: dbDateNow(this.app),
      })
    }

    let patchRecord: any = {
      active_sub_session_id: null,
      last_updated_by_uid: closedByUid
    }

    if(tassConfig === undefined || tassConfig.closesTestAttempt) {
      patchRecord["is_closed"] = 1;
      patchRecord["is_submitted"] = is_submitted;
      patchRecord["submitted_test_session_id"] = testSessionId;
      patchRecord["closed_on"] = dbDateNow(this.app);
    }

    if (forceKeepActiveSubsession) {
      delete patchRecord.active_sub_session_id
    }
    try {
      await this.app.service('db/write/test-attempts').patch(test_attempt_id, patchRecord);
    } catch (e) {
      // TODO what if attempt is already closed?
    }

    if(tassConfig?.closesTestAttempt && tassConfig?.autoOpenNext) {
      const nextTwtdarOrder = tassConfig.twtdar_order + 1;
      const nextSsOrder = tassConfig.ssOrder + 1;

      //Attempt to auto-open the next test_attempt if it exists
      const nextTestAttemptRow =  await this.app.service('db/read/test-attempts').db()
      .where('test_session_id', tassConfig.test_session_id)
      .where('twtdar_order', nextTwtdarOrder)
      .where('uid', tassConfig.attemptUid)
      .whereNot('is_invalid', 1)
      .limit(1);

      if(nextTestAttemptRow && nextTestAttemptRow.length > 0) {
        const nextTestAttempt = nextTestAttemptRow[0];

        const nextSubSessionRow = await this.app.service('db/read/test-session-sub-sessions').db()
        .where('test_session_id', tassConfig.test_session_id)
        .where('twtdar_order', nextTwtdarOrder)
        .where('order', nextSsOrder)
        .limit(1)

        if(nextSubSessionRow && nextSubSessionRow.length > 0) {
          const nextSubSession = nextSubSessionRow[0];
          await this.app.service('db/write/test-attempts')
          .patch(nextTestAttempt.id, {active_sub_session_id: nextSubSession.id} );
        }
      }
    }



    // todo: lively remove
    // this.populateNonResponses({
    //   cache,
    //   resolvedAttempt,
    // });

  }

  // todo: likely remove
  private async populateNonResponses(config:IPopNonResConfig){
    throw new Errors.NotImplemented();
    // const {
    //   cache,
    //   resolvedAttempt,
    // } = config;

    // if (!cache.testSessions) {
    //   cache.testSessions = {};
    // }
    // if (!cache.testSessions[resolvedAttempt.test_session_id]) {
    //   cache.testSessions[resolvedAttempt.test_session_id] = await this.app.service('db/read/test-sessions').get(resolvedAttempt.test_session_id);
    // }
    // const testSession = cache.testSessions[resolvedAttempt.test_session_id];

    // if (!cache.testWindow) {
    //   cache.testWindow = await this.app.service('db/read/test-windows').get(testSession.test_window_id);
    // }
    // const testWindow = cache.testWindow;

    // if (!cache.testDesignFramework) {
    //   cache.testDesignFramework = await this.getTestDesignFramework(testWindow.test_design_id);
    //   if (_.isString(cache.testDesignFramework)) {
    //     cache.testDesignFramework = JSON.parse(cache.testDesignFramework);
    //   }
    //   if (!cache.testDesignFramework.testlets) {
    //     cache.testDesignFramework.testlets = [];
    //   }
    // }
    // const testDesignFramework = cache.testDesignFramework;

    // const testDesign = await this.getTestDesign(''+resolvedAttempt.test_form_id);
    // const itemDataMap = this.getItemDataMap(testDesign, testDesignFramework);
    // const existingResps = <any[]> await this.app.service('db/write/test-attempt-question-responses').find({
    //   query: {
    //     test_attempt_id: resolvedAttempt.id
    //   },
    //   paginate: false
    // });
    // await Bluebird.mapSeries(existingResps, response => {
    //   const testletData = itemDataMap[response.test_question_id];
    //   if (!testletData) {
    //     return;
    //   }
    //   return this.app.service('db/write/test-attempt-question-responses').patch(response.id, testletData)
    // })
    // const skippedQuestionIds = _(testDesign.questionDb)
    //   .keys()
    //   .differenceWith(existingResps, (idFromTestDesign:number, response:{test_question_id:number}) => {
    //     return idFromTestDesign == response.test_question_id
    //   })
    //   .value()
    // await Bluebird.mapSeries(skippedQuestionIds, (qId:number) => {
    //   return this.app.service('db/write/test-attempt-question-responses').create(
    //     _.assign(
    //       {
    //         test_attempt_id: resolvedAttempt.id,
    //         test_question_id: qId,
    //         test_question_version_id: qId,
    //         response_raw: null,
    //         response: null,
    //         updated_on: dbDateNow(this.app),
    //         is_not_seen: true
    //       },
    //       itemDataMap[qId]
    //     )
    //   )
    // });
  }

  // todo: likely remove
  private getItemDataMap (testDesign:any, testDesignFramework:any) {
    throw new Errors.NotImplemented();
    // const testlets = _.filter(testDesignFramework.testlets, (testlet:any) => _.includes(testDesign.testletIds, testlet.id));
    // const itemDataMap:any = {};
    // _.each(testlets, (testlet:any) => {
    //   _.each(testlet.questions, (question:any) => {
    //     const section = _.find(testDesign.sections, (section:any) => _.includes(section.questions, question.id));
    //     itemDataMap[question.label] = {
    //       section_id: testlet.section,
    //       section_form_order: section.questions.indexOf(question.id),
    //       testlet_id: testlet.id,
    //       quadrant_id: testlet.quadrant
    //     }
    //   })
    // });
    // return itemDataMap;
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async patch (id: NullableId, data: {test_attempt_id: number}, params?: Params): Promise<Data> {
    const {schl_class_group_id} = (<any>params).query;
    const {test_attempt_id} = data;
    this.app.service('db/write/test-attempts').patch(test_attempt_id, {last_touch_on: dbDateNow(this.app)});
    return {};
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  private async getData(props:any[], query:string){
    const db:Knex = this.app.get('knexClientRead');
    const res = await db.raw(query, props);
    return <any[]> res[0];
  }

  async getAssessmentType(uid:any, class_id:any){
    const studentRecord = await dbRawRead(this.app, [uid, class_id], `
      select scts.slug
        from test_attempts ta
        join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
        join test_sessions ts on ts.id = scts.test_session_id and ts.is_cancelled = 0 and ts.is_closed = 0
        join test_windows tw on tw.id = ts.test_window_id
       where ta.uid = ?
         and ta.is_invalid = 0
         and ta.active_sub_session_id is not null
         and tw.is_active = 1
         and scts.school_class_id = ?
    ;`);
    return studentRecord;
  }

  /**
   * Using the school class's id and the student uid. This method checks if this class is hosting a diploma. 
   * If the class does host a diploma exam then the student enrollments will be checked to see if 
   *  they are registered to any diploma the class hosts.
   * If the class does not host any diploma exams then the regular logic flow continues.
   * 
   * @param sc_ids an array of string ids of the school classes to be checked for diploma exams
   * @param uid the student uid 
   * @throws an 'abed_missing_registration' erorr which gets translated on WC and displays: 
   *  "You are not registered for a diploma this class is hosting."
   */
  private async confirmStudentRegistration(sc_ids: any[], uid: number){
    const mappedSCIDs = sc_ids.map(sc => sc.id);
    if(!mappedSCIDs.length){
      return;
    }
    const class_diplomas = await dbRawReadReporting(this.app, {id: mappedSCIDs}, SQL_DIP_SC_BY_SC_ID());
    await examRegistrationCheck(this.app, class_diplomas, uid);
  }
  
  /**
   * Determines if lockdown/softlock mode should be activated based on headers and query parameters
   *  - Chrome 136 is excluded from lockdown/softlock mode temporarily
   * @param params - Request parameters containing headers and query
   * @returns boolean - true if lockdown mode should be activated
   */
  private isLockdownSoftlock(params: Params): boolean {
    // SEB (SafeExamBrowser)
    if(params.headers && params.headers[HEADER_SEB_REQ]) {
      return true;
    }

    // LDB (Respondus LockDown Browser)
    if(params?.query?.rldbarv) {
      // Don't activate for Chrome 136 in Respondus environments
      const userAgent = params.headers?.['user-agent'];
      if (userAgent && userAgent.includes('Chrome/136')) {
        return false;
      }

      return true;
    }

    return false;
  }
}

// this should not be hardcoded
const SQL_DIP_SC_BY_SC_ID = () => ` -- SQL_DIP_SC_BY_SC_ID
  SELECT 
    ac.course_code_foreign as asmt_type_slug 
    , tw.pasi_exam_period 
  FROM school_classes sc
  JOIN school_class_test_sessions scts 
    on scts.school_class_id = sc.id 
  JOIN test_window_td_alloc_rules twtdar 
    on twtdar.slug = scts.slug
    and twtdar.is_custom = 0
  JOIN test_sessions ts 
    on ts.id = scts.test_session_id
  JOIN test_window_td_types twtdt 
    on twtdt.type_slug = twtdar.slug
    and twtdt.test_window_id is null
    and twtdt.is_revoked = 0
  JOIN test_windows tw 
    on tw.id = twtdt.test_window_id 
  JOIN assessment_courses ac 
    on ac.course_code = twtdt.course_code
  WHERE sc.id in (:id)
    AND sc.group_type = '${GRADE12Class}'
    AND sc.is_active = 1
    AND ts.is_closed = 0
;
`