import { ENFORCE_TIME_CONTROLS_NON_SAMPLE } from "../../../../../constants/site-flags";
import { Errors } from "../../../../../errors/general";
import { dbDateToMoment } from "../../../../../hooks/_util";
import { dateAsUtcDbString, dateEarliest, dateLatest, isDateFuture, isDatePast } from "../../../../../util/db-dates";
import { IAttemptTimeInfo } from "../model/types";

export interface IAttemptTimeEval {
    isDurationEnforced: boolean;
    started_on: string;
    ends_on: string;
    time_ext_m: number;
    duration_m: number;
    isNotStarted:boolean;
    isSessionEnded:boolean;
}

export class StudentSessionTimeControl {

    constructor(
        public attemptTimeInfo: IAttemptTimeInfo
    ) {}

    public evaluate(isDurationEnforced?:boolean){
        const info = this.attemptTimeInfo;
        let isNotStarted = false;
        let isSessionEnded = false;
        const duration_m = info.ts_duration_m || info.twtar_duration_m || 0;
        const time_ext_m = (info.ta_time_ext_m || 0) + (info.ts_time_ext_m || 0)
        const started_on = info.ta_started_on || info.ts_started_on
        const momentSessionStart = dbDateToMoment(info.ts_started_on); 
        const momentTimeStart = dbDateToMoment(info.ta_started_on || info.ts_started_on);
        const impliedTimeStart:any = dateLatest(momentSessionStart, momentTimeStart || momentSessionStart);

        let momentTimeEnd;
        if (isDateFuture(momentSessionStart)){
            isNotStarted = true;
        }
        let timeClose, timeHardStop;
        if (duration_m){
            timeClose = impliedTimeStart.add(duration_m+time_ext_m, 'minutes');
            if (isDatePast(timeClose.format())){
                isSessionEnded = true
            }
        }
        if (info.hardstop_offset_h && info.twtar_is_sample==0){
            timeHardStop = impliedTimeStart.add(info.hardstop_offset_h, 'hours');
            if (isDatePast(timeHardStop.format())){
                isSessionEnded = true
            }
        }
        momentTimeEnd = dateEarliest(timeClose, timeHardStop);

        if(info.twtar_test_date_end) {
            const momentTestEnd = dbDateToMoment(info.twtar_test_date_end);
            momentTimeEnd = dateEarliest(momentTimeEnd, momentTestEnd);
            if (isDatePast(momentTestEnd.format())){
                isSessionEnded = true
            }
        }

        const ends_on = dateAsUtcDbString(momentTimeEnd)
        if (!isDurationEnforced){ // confirm if not passed in
            isDurationEnforced = this.checkIsEnforced();
        }
        const status:IAttemptTimeEval = {
            isDurationEnforced,
            started_on,
            ends_on,
            time_ext_m,
            duration_m,
            isNotStarted,
            isSessionEnded,
        }
        return status;
    }

    private checkIsEnforced(){
        const info = this.attemptTimeInfo;
        if (ENFORCE_TIME_CONTROLS_NON_SAMPLE){
            const twtar_is_sample = (info.twtar_is_sample == 0)
            const is_sample_time_enforced = (info.is_sample_time_enforced == 1)
            if (twtar_is_sample || is_sample_time_enforced || info.ts_duration_m){
                return true;
            }
        }
        return false
    }

    public validate(){
        let isEnforced = this.checkIsEnforced();
        if (isEnforced){
            const status = this.evaluate(isEnforced);
            if (status.isNotStarted){
                throw new Errors.Forbidden('ATTEMPT_PAUSED');
            }
            if (status.isSessionEnded){
                throw new Errors.Forbidden('SESSION_ENDED');
            }
        }
    }
}
