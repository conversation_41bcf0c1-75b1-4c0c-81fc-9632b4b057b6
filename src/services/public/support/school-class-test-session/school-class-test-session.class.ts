import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { ISession } from '../../school-admin/session/session.class';

interface Data {
  sc_id: number,
  isOperational?: boolean,
}

interface ServiceOptions {}

export class SchoolClassTestSession implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Error()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Error()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    const {sc_id, isOperational} = data;

    if (!params){
      throw new Errors.BadRequest()
    }

    if (!isOperational){
      throw new Error('This script does not yet support non operational session creation.')
    }

    const isForced = true

    const created_by_uid = await currentUid(this.app, params)

    const focus_on_custom_assessments = false; // todo: need a clean way to set this, not assuming its going to come cleanly from the query parameters, have to be careful

    // get operational type
    const twtarSlugRecords = await dbRawRead(this.app, [sc_id], `
      select twtar.type_slug twtar_type_slug
           , sc.group_id sc_group_id
      from school_classes sc  
      join school_semesters ss 
        on ss.id = sc.semester_id 
      join test_windows tw 
        on tw.id = ss.test_window_id 
      join test_window_td_alloc_rules twtar 
        on twtar.test_window_id = tw.id 
        and twtar.is_secured = 1 -- todo - should this be hard coded
        and twtar.is_custom = ${ focus_on_custom_assessments ? '1' : '0' }
      where sc.id = ?
      group by twtar.type_slug
    `);
    if (twtarSlugRecords.length !== 1){
      throw new Errors.GeneralError('Invalid number of operational assessments in twtar')
    }
    const {twtar_type_slug, sc_group_id} = twtarSlugRecords[0]

    // check for existing session
    const testSessions = await dbRawRead(this.app, [sc_id, twtar_type_slug], `
      select ts.id, ts.is_closed, ts.created_on, ts.date_time_start 
      from school_classes sc  
      join school_class_test_sessions scts 
        on scts.school_class_id  = sc.id 
      join test_sessions ts 
        on ts.id = scts.test_session_id 
        and ts.is_cancelled = 0
      where sc.id = ?
        and scts.slug = ?
      order by ts.is_closed asc 
    `);

    let ts_id;
    if (testSessions.length === 0){
      // create session
      const newSessionConfig = {
        caption: 'auto',
        school_class_id: sc_id,
        slug: twtar_type_slug,
        isScheduled:true,
        scheduled_time:["2022-08-21","2022-08-31","2022-08-21","2022-08-31"], // to do: make dynamic
        is_fi: false // to do: make dynamic
      }
      const newSession = await this.app.service('public/educator/session').createSessionForEducator(created_by_uid, newSessionConfig, sc_group_id, isForced);
      ts_id = newSession.test_session_id
    }
    else {
      ts_id = testSessions[0].id
    }
    // ensure session is inited
    
    await this.app.service('public/educator/session').ensureSessionInit(ts_id, sc_id, created_by_uid, isForced);

    return <any> {ts_id};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Error()
  }
}
