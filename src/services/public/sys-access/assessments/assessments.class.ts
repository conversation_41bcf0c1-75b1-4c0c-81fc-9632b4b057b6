import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { SQL_ASSESSMENT_LOOKUP } from '../../../../sql/feature-access';

interface Data {
  [key: string]: any,
}
interface ServiceOptions {}

export class Assessments implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    const query = params?.query || {};
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    
    if (!userInfo || !userInfo.uid) {
      throw new Errors.NotAuthenticated('User not authenticated');
    }

    // Check if user has sys_access role
    const hasSysAccess = await this.checkUserSysAccess(userInfo.uid);
    if (!hasSysAccess) {
      throw new Errors.Forbidden('Insufficient permissions');
    }

    const { school_foreign_id } = query;
    
    if (!school_foreign_id) {
      throw new Errors.BadRequest('school_foreign_id is required');
    }

    // Query assessments for the school
    const result = await dbRawRead(this.app, [school_foreign_id], SQL_ASSESSMENT_LOOKUP);

    return {
      data: result,
      total: result.length,
      limit: 100,
      skip: 0
    };
  }

  async get(id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed('Get method not allowed');
  }

  async create(data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed('Create method not allowed');
  }

  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed('Patch method not allowed');
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed('Remove method not allowed');
  }

  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed('Update method not allowed');
  }

  private async checkUserSysAccess(uid: number): Promise<boolean> {
    try {
      const userRoles = await this.app.service('db/read/user-roles').find({
        query: {
          uid,
          role_type: 'sys_access',
          is_revoked: 0,
          $limit: 1
        }
      });

      return (userRoles as Paginated<any>).total > 0;
    } catch (error) {
      return false;
    }
  }
} 