// Simple schema for assessments service
export interface Assessments {
  id: number;
  assessment_name: string;
  course_code: string;
  school_foreign_id: string;
  school_name: string;
  mapping_created_on: string;
}

export interface AssessmentsData {
  assessment_name: string;
  course_code: string;
  school_foreign_id: string;
}

export interface AssessmentsPatch {
  assessment_name?: string;
  course_code?: string;
  school_foreign_id?: string;
}

export interface AssessmentsQuery {
  school_foreign_id?: string;
} 