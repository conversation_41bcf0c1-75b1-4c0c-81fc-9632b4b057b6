import { Application } from '../../../../declarations';
import { Assessments } from './assessments.class';
import { assessmentsPath, assessmentsMethods } from './assessments.shared';

export { Assessments } from './assessments.class';

export const assessments = (app: Application): void => {
  const options = {
    // A list of all methods this service exposes externally
    methods: assessmentsMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  };

  // Register our service on the Feathers application
  app.use(assessmentsPath, new Assessments(options, app));
};

export default assessments; 