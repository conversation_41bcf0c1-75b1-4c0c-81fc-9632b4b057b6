import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawReadReporting, dbRawWrite } from '../../../../util/db-raw';
import { SQL_FEATURE_FLAGS_GROUPS_LIST, SQL_FEATURE_ACCESS_GROUP_MEMBERS, SQL_FEATURE_ACCESS_ADD, SQL_FEATURE_ACCESS_REVOKE } from '../../../../sql/feature-access';
import { currentUid } from '../../../../util/uid';

interface Data {
  id?: number;
  group_id: number;
  group_name: string;
  group_type: string;
  feature_flags: string[];
  member_count: number;
  created_on: string;
  updated_on: string;
}

interface CreateData {
  feature_slug: string;
  group_id: number;
  expires_on?: string | null;
  notes?: string;
}

interface ServiceOptions {}

export class FeatureFlags implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    return dbRawRead(this.app, [], SQL_FEATURE_FLAGS_GROUPS_LIST);
  }

  async get(id: Id, params?: Params): Promise<Data> {
    const group_id = +id;
    const users = await dbRawReadReporting(this.app, {group_id}, SQL_FEATURE_ACCESS_GROUP_MEMBERS);
    return {users} as any; // Cast to any since the SQL returns member data, not group data
  }

  async create(data: CreateData, params?: Params): Promise<Data> {
    const uid = await currentUid(this.app, params || {});
    if (!uid) {
      throw new Errors.NotAuthenticated('User not authenticated');
    }

    const { feature_slug, group_id, expires_on, notes } = data;
    
    if (!feature_slug || !group_id) {
      throw new Errors.BadRequest('Feature slug and group ID are required');
    }

    try {
      await dbRawWrite(this.app, [feature_slug, group_id, uid, expires_on || null, notes || ''], SQL_FEATURE_ACCESS_ADD);
      
      // Return the created record
      const result = await dbRawRead(this.app, [], SQL_FEATURE_FLAGS_GROUPS_LIST);
      const createdRecord = result.find((item: any) => item.group_id === group_id && item.feature_slug === feature_slug);
      return createdRecord || data as any;
    } catch (error) {
      throw new Errors.GeneralError('Failed to create feature access', error);
    }
  }

  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error('Method not implemented');
  }

  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error('Method not implemented');
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    const uid = await currentUid(this.app, params || {});
    if (!uid) {
      throw new Errors.NotAuthenticated('User not authenticated');
    }

    const groupId = id;
    if (!groupId) {
      throw new Errors.BadRequest('Group ID is required');
    }

    // For now, we'll revoke the access instead of deleting
    // You can modify this to actually delete if needed
    try {
      await dbRawWrite(this.app, [uid, 'CUSTOM_CLASSROOM_ASSESSMENTS', groupId], SQL_FEATURE_ACCESS_REVOKE);
      return { id: groupId } as Data;
    } catch (error) {
      throw new Errors.GeneralError('Failed to remove feature access', error);
    }
  }

} 