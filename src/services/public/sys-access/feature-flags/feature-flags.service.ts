// Initializes the `public/sys-access/feature-flags` service on path `/public/sys-access/feature-flags`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { FeatureFlags } from './feature-flags.class';
import hooks from './feature-flags.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/sys-access/feature-flags': FeatureFlags & ServiceAddons<any>;
  }
}

export default function (app: Application) {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/sys-access/feature-flags', new FeatureFlags(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/sys-access/feature-flags');

  service.hooks(hooks);
} 