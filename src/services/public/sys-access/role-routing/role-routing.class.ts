import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawReadReporting, dbRawWrite } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { SQL_ACCOUNT_TYPE_ROLE_ROUTES, SQL_ACCOUNT_TYPE_ROLE_ROUTES_UPDATE } from '../../../../sql/feature-access';

interface Data {
  [key: string]: any,
}
interface ServiceOptions {}

export class RoleRouting implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    // const uid = await currentUid(this.app, params);
    // Query all role routing mappings
    return dbRawReadReporting(this.app, [], SQL_ACCOUNT_TYPE_ROLE_ROUTES);

  }

  async get(id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed('Get method not allowed');
  }

  async create(data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed('Create method not allowed');
  }

  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (!id) {
      throw new Errors.BadRequest('ID is required for patch operation');
    }

    if (!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const uid = await currentUid(this.app, params);
    
    // Validate required fields
    const requiredFields = ['role_type', 'group_type', 'route_template', 'route_join', 'caption', 'color', 'display_order'];
    for (const field of requiredFields) {
      if (data[field] === undefined || data[field] === null) {
        throw new Errors.BadRequest(`Missing required field: ${field}`);
      }
    }

    // Execute update query
    const updateParams = {
      role_type: data.role_type,
      group_type: data.group_type,
      account_type: data.account_type || null,
      route_template: data.route_template,
      route_join: data.route_join,
      caption: data.caption,
      color: data.color,
      display_order: data.display_order,
      id: id
    };

    return dbRawWrite(this.app, updateParams, SQL_ACCOUNT_TYPE_ROLE_ROUTES_UPDATE);
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed('Remove method not allowed');
  }

  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed('Update method not allowed');
  }

  private async checkUserSysAccess(uid: number): Promise<boolean> {
    try {
      const userRoles = await this.app.service('db/read/user-roles').find({
        query: {
          uid,
          role_type: 'sys_access',
          is_revoked: 0,
          $limit: 1
        }
      });

      return (userRoles as Paginated<any>).total > 0;
    } catch (error) {
      return false;
    }
  }
} 