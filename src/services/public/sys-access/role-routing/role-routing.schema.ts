// Simple schema for role-routing service
export interface RoleRouting {
  id: number;
  role_type: string;
  group_type: string;
  account_type: string;
  route_template: string;
  route_join: string;
  caption: string;
  color: string;
  display_order: number;
  created_on: string;
  updated_on: string;
}

export interface RoleRoutingData {
  role_type: string;
  group_type: string;
  account_type: string;
  route_template: string;
  route_join: string;
  caption: string;
  color: string;
  display_order: number;
}

export interface RoleRoutingPatch {
  role_type?: string;
  group_type?: string;
  account_type?: string;
  route_template?: string;
  route_join?: string;
  caption?: string;
  color?: string;
  display_order?: number;
}

export interface RoleRoutingQuery {
  role_type?: string;
  group_type?: string;
  account_type?: string;
} 