import { Application } from '../../../../declarations';
import { RoleRouting } from './role-routing.class';
import { roleRoutingPath, roleRoutingMethods } from './role-routing.shared';

export { RoleRouting } from './role-routing.class';

export const roleRouting = (app: Application): void => {
  const options = {
    // A list of all methods this service exposes externally
    methods: roleRoutingMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  };

  // Register our service on the Feathers application
  app.use(roleRoutingPath, new RoleRouting(options, app));
};

export default roleRouting; 