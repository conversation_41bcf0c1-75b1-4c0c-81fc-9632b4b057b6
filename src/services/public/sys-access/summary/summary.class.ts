import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead, dbRawReadReporting } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { Errors } from '../../../../errors/general';
import { SQL_TEST_WINDOW_CTRL_GROUPS } from '../../../../sql/feature-access';
import { DBD_U_GROUP_TYPES } from '../../../../constants/db-extracts';

interface Data {}

interface ServiceOptions {}

interface TestWindowGroup {
  test_ctrl_group_id: number;
  group_type: string;
  description: string;
}

export class Summary implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    // Check if user has sys_access role
    if (!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const uid = await currentUid(this.app, params);
    // Use the proper user role actions service to check for sys_access role
    const systemAccess = await this.app.service('auth/user-role-actions').getSystemRoles(uid, DBD_U_GROUP_TYPES.mpt_sys);
    if (!systemAccess.length) {
      throw new Errors.Forbidden('NO_SYS_ACCESS');
    }
    return dbRawReadReporting(this.app, [], SQL_TEST_WINDOW_CTRL_GROUPS);
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Error('Method not allowed');
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Error('Method not allowed');
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error('Method not allowed');
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error('Method not allowed');
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Error('Method not allowed');
  }
} 