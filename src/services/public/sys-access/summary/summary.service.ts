// Initializes the `public/sys-access/summary` service on path `/public/sys-access/summary`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Summary } from './summary.class';
import hooks from './summary.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/sys-access/summary': Summary & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/sys-access/summary', new Summary(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/sys-access/summary');

  service.hooks(hooks);
}
