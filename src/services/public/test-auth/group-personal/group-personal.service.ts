// Initializes the `public/test-auth/group-personal` service on path `/public/test-auth/group-personal`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { GroupPersonal } from './group-personal.class';
import hooks from './group-personal.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/group-personal': GroupPersonal & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/group-personal', new GroupPersonal(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/group-personal');

  service.hooks(hooks);
}
