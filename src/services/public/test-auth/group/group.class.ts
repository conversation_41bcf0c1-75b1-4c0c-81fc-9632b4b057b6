import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import {Errors} from "../../../../errors/general";
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class Group implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const created_by_uid = userInfo.uid;

    if (params){
      const created_by_uid = await currentUid(this.app, params);
        
      const {
        description,
      } = <any> data;

      return this.createAuthoringGroup(description, created_by_uid);
    }

    throw new Errors.BadRequest('No_PARAMS');
  }

  async createAuthoringGroup(description: string, created_by_uid: number){

    const groupData = {
      group_type: 'questionbank',
      description,
      created_by_uid
    }
    const group = await this.app.service('db/write/u-groups').create(groupData);

    // now create user roles for current user in this group.
    const group_id = group.id;
    const roleData = {
      uid: created_by_uid,
      group_id,
      created_by_uid
    };

    await this.app.service('db/write/user-roles').create({
      ... roleData,
      role_type: 'test_item_author',
    });
    await this.app.service('db/write/user-roles').create({
      ... roleData,
      role_type: 'test_item_author_super',
    });

    return group;

  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
