import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { hashGivenString } from '../../../../util/secret-codes';
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { dbDateNow } from '../../../../util/db-dates';
import { currentUid } from '../../../../util/uid';
import { NotifType } from '../notifications/notifications.class';
import { Knex } from 'knex';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { ensureLockedDomain } from '../../../../util/domain-lock';
import { RequestBreakdown } from '../../../../util/logger';
import { SQL_BATCH_ALLOC_ALLOWED_TWATR_SLUGS, SQL_BATCH_ALLOC_DETAIL, SQL_HISTORICAL_PSYCH_STATS, SQL_HISTORICAL_PSYCH_STATS_SUMMARY, SQL_TWATR_SLUGS } from './model/item-set-sql';

interface Data {
  group_id?: number,
  slug?: string,
  name?: string,
  description?: string,
  hash?: string,
  compositeAsmtTemplateQsId?: number[],
  test_design_question_sets?: number[],
  is_test_design?: number,
  error?: unknown
}
interface PaginatedData {
  total: number;
  limit: number;
  skip: number;
  data: Data[];
}

interface ServiceOptions {}

export class ItemSet implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }




  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);

    if(!userInfo.uid) {
      throw new Errors.BadRequest('MISSING_UID');
    }
    const {getArchived} = params?.query || {getArchived: false}
    if(+getArchived){
      return await fetchQuestionSets(this.app, userInfo.uid, 0, 1);
    } else{
      return await fetchQuestionSets(this.app, userInfo.uid, 0, 0);
    }
  }

  async checkUserSingularGroupRoles(groupIds:(number | string)[], uid: number, targetRoles:DBD_U_ROLE_TYPES[]){
    const groupIdsNumeric = groupIds.filter(id => id).map(groupId => parseInt(''+groupId)) ;
    const userGroupRoleTypes = await this.app.service('auth/user-role-actions').getUserRoleRecords(groupIdsNumeric, uid);
    let isMatch = false;
    let isSingle = false;
    let authRoles: string[] = [];
    targetRoles.forEach(targetRole => {
      const userRole = userGroupRoleTypes.data.find((role) => role.role_type == targetRole)
      if(userRole) {
        isMatch = true;
        isSingle = userRole.group_id == groupIds[1];
        authRoles.push(userRole.role_type)
      }
    })
    return {isMatch, isSingle, authRoles};
  }



  async checkItemSetAccess(itemSet: any, uid: number) {
    const hasAccess = await this.checkUserSingularGroupRoles([itemSet.group_id, itemSet.single_group_id], uid, [DBD_U_ROLE_TYPES.test_item_author, DBD_U_ROLE_TYPES.test_item_author_rev, DBD_U_ROLE_TYPES.bc_auth_base, DBD_U_ROLE_TYPES.eqao_auth_read_only])
    if (!hasAccess.isMatch){
      const info = await dbRawReadSingle(this.app, {group_id: itemSet.group_id}, `select id, description from u_groups ug where id = :group_id`)
      throw new Errors.Forbidden('GROUP_ROLE_REQ', info)
    }

    return hasAccess
  }

  async getItemSetQuestionIds(question_set_id: number){
    const itemSetMeta = await dbRawReadSingle(this.app, {question_set_id}, `
      select test_design_question_sets
      from temp_question_set
      where id = :question_set_id
    `)
    const parentSetIds = [
      question_set_id,
      ...JSON.parse(itemSetMeta.test_design_question_sets || '[]')
    ]
    const items = await dbRawRead(this.app, {parentSetIds}, `
      select id
      from test_questions
      where question_set_id IN (:parentSetIds)
        and is_archived = 0
    `)
    return items.map(q => q.id);
  }

  async get (id: Id, params?: Params): Promise<Data> {
    const question_set_id = <number> id;
    if (params && params.headers && params.query){
      
      const reqLog = new RequestBreakdown()

      const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
      const uid = <number> userInfo.uid;
      const snapshotFilter = params.query.snapshotFilter
      reqLog.stamp('user-info');

      const payload = await this.app
        .service('db/read/temp-question-set')
        .get(id);
      reqLog.stamp('db/read/temp-question-set');
      
      const isScoreEntry = payload.is_score_entry;
      const groupId = payload.group_id;

      // domain sanitization
      const lockedDomain = payload.locked_domain;
      ensureLockedDomain(params, lockedDomain);
      const hasAccess = await this.checkItemSetAccess(payload, uid);
      reqLog.stamp('checkItemSetAccess');

      let questions:any[] = [];
      let childItemBanksInfo:any[] = [];

      if (payload.is_test_design && !isScoreEntry){
        const childQuestionSets:number[] = JSON.parse(payload.test_design_question_sets);
        childItemBanksInfo = await this.app
        .service('db/read/temp-question-set')
        .db()
        .select(['id','group_id','slug','name','description'])
        .whereIn('id', childQuestionSets)
        .andWhere('is_archived', 0);
        const nonArchivedQuestionSets = childItemBanksInfo.map((IBank)=>IBank.id);
        for(let i=0; i<nonArchivedQuestionSets.length; i++){
          const childQuestionSetId = nonArchivedQuestionSets[i];
          const childQuestions = await this.app
            .service('public/test-auth/questions')
            .loadQuestionsByQuestionSet(childQuestionSetId, false, {snapshotFilter});
          reqLog.stamp('loadQuestionsByQuestionSet');
          questions = questions.concat(childQuestions)
        }

        childItemBanksInfo = await this.app
          .service('db/read/temp-question-set')
          .db()
          .select(['id','group_id','slug','name','description'])
          .whereIn('id', childQuestionSets)
        reqLog.stamp('db/read/temp-question-set');
      }
      else{
        const retrieveArchived = !!params.query?.getArchived;
        questions = await this.app
          .service('public/test-auth/questions')
          .loadQuestionsByQuestionSet(question_set_id, false, undefined, retrieveArchived);
        reqLog.stamp('loadQuestionsByQuestionSet::archived');
      }
      const qIds = questions.map( q => q.id);

      const res = await this.app.service('db/read/user-metas').db()
        .where('uid', uid)
        .where('key', 'show_comments')
        .where('key_namespace', 'test_auth')
        .select('value');
      let showComments;
      if(res && res.length) {
        showComments = res[0].value;
      }
      reqLog.stamp('showComments');

      // const availableTags = await this.app.service('db/read/item-tags').db()
      // .where('group_id', groupId)
      // .where('is_deleted', 0)
      // reqLog.stamp('db/read/item-tags');\

      const historicalItemRegisterQuery = (isSummary:boolean=false) => `
        select twtar.test_window_id
            , twtar.slug  twtar_slug
              ${ isSummary ? `
                , tqr.test_design_id
                , tw.title tw_title
                , tw.date_start 
                , tw.date_end 
              ` : '' }
              ${ !isSummary ? `
                , tqr.*
              ` : '' }
        from test_windows tw 
        join test_window_td_alloc_rules twtar
          on twtar.test_window_id = tw.id 
          and twtar.is_questionnaire = 0 
          and twtar.is_sample = 0 
          and twtar.is_custom = 0
        join test_question_register tqr
          on tqr.test_design_id = IFNULL(twtar.tqr_ovrd_td_id, twtar.test_design_id) 
        where tw.is_qa = 0 
          and tw.is_bg = 0
          and tqr.question_id IN (:questionIdsLookup)
        ${ !isSummary ? `group by tw.id, tqr.test_design_id, tqr.question_id` : ''}
        ${ isSummary ? `group by tw.id, tqr.test_design_id` : ''}
        order by twtar.test_window_id desc, tqr.test_design_id desc
      `

      const questionIdsLookup = [-1].concat(qIds)
      const historicalItemRegisterSummary = await dbRawRead(this.app, {questionIdsLookup}, historicalItemRegisterQuery(true))
      const historicalItemRegister = await dbRawRead(this.app, {questionIdsLookup}, historicalItemRegisterQuery())
      historicalItemRegister.forEach(record=> {
        delete record.id // no need to store the tqr.id
        delete record.test_form_id // no need to store the tqr.test_form_id
        delete record.item_bank_id // no need to store the tqr.item_bank_id
      })

      const historicalPsychStatsSummary = await dbRawRead(this.app, {questionIdsLookup}, SQL_HISTORICAL_PSYCH_STATS_SUMMARY);
      const historicalPsychStats = await dbRawRead(this.app, {questionIdsLookup}, SQL_HISTORICAL_PSYCH_STATS);


      const db:Knex = this.app.get('knexClientRead');

      // const tagLinks = await db('item_tag_link as l')
      // .join('item_tags as t', 't.id', 'l.tag_id')
      // .whereIn('l.item_id', qIds)
      // .where('l.is_valid', 1)
      // .where('t.is_deleted', 0)
      // .select('l.tag_id', 'l.item_id')
      // reqLog.stamp('tagLinks');

      const scoringInfo = <any[]> await this.app
        .service('public/test-auth/test-question-scoring-info')
        .find({query: { question_ids: qIds} , paginate: false})
      reqLog.stamp('scoringInfo');

      // const availableScoringCodes = <any[]> await this.app
      //   .service('public/test-auth/test-question-scoring-codes')
      //   .find();
      // reqLog.stamp('availableScoringCodes');

      // Get the current stage and assignees of the questions
      const stageInfoByQuestion = await this.app
      .service('public/test-auth/question-workflow-stages')
      .getStageInfoByQIds(qIds);
      reqLog.stamp('stageInfoByQuestion');

      // Get # of pending graphic requests of the questions
      const pendingGraphicReqCountByQuestion = await this.app
      .service('public/test-auth/question-graphic-requests')
      .getPendingCountByQIds(qIds);
      reqLog.stamp('pendingGraphicReqCountByQuestion');

      // get batch allocation policies
      const batchAllocPolicies = await this.getBatchAllocPolicies(question_set_id)
      reqLog.stamp('batchAllocPolicies');

      if(hasAccess.isSingle && hasAccess.authRoles.includes(DBD_U_ROLE_TYPES.test_item_author_rev)) {
        questions = [];
      }

      return  {
        ... payload,
        questions,
        batchAllocPolicies,
        scoringInfo,
        availableScoringCodes:[], // PERMANENTLY DISABLED
        childItemBanksInfo,
        showComments,
        availableTags:[], // TEMPORARILY DISABLED
        tagLinks:[], // TEMPORARILY DISABLED
        stageInfoByQuestion,
        pendingGraphicReqCountByQuestion,
        isSingle: hasAccess.isSingle,
        __requestLog: reqLog.getLog(),
        historicalItemRegisterSummary,
        historicalItemRegister,
        historicalPsychStatsSummary,
        historicalPsychStats
      }
    }
    throw new Errors.BadRequest();
  }

  async getBatchAllocPolicies(item_set_id:number) {
    const typeSlugRecords = await dbRawRead(this.app, {item_set_id}, SQL_TWATR_SLUGS);
    const assocTwtarTypeSlugs = typeSlugRecords.map(r => r.type_slug);
    // pull all batch alloc policies and find the ones that fit the constraint
    const batchAllocTypeSlugFilters = await dbRawRead(this.app, {}, SQL_BATCH_ALLOC_ALLOWED_TWATR_SLUGS);
    const batchAllocIds:Set<number> = new Set();
    for (let batchAllocFilterRecord of batchAllocTypeSlugFilters){
      try {
        const allowedTwtarTypeSlugs = JSON.parse(batchAllocFilterRecord.allowed_twtar_type_slugs);
        for (let twtarTypeSlug of assocTwtarTypeSlugs){
          if (allowedTwtarTypeSlugs.includes(twtarTypeSlug)){
            batchAllocIds.add(batchAllocFilterRecord.id)
          }
        }
      }
      catch(e){}
    }
    if (batchAllocIds.size > 0){
      const mbap_ids = [... batchAllocIds];
      const batchAllocs = await dbRawRead(this.app, {mbap_ids}, SQL_BATCH_ALLOC_DETAIL);
      return batchAllocs;
    }
    else {
      return [];
    }
  }

  async create (data: Data, params?: Params): Promise<Data> {

    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {
      group_id,
      slug,
      name,
      description,
      is_test_design,
      compositeAsmtTemplateQsId,
    } = data;

    let test_design_question_sets;
    if (!!is_test_design){
      test_design_question_sets = JSON.stringify([]);
    }

    const payload:any = {
      test_design_question_sets
    };

    if (compositeAsmtTemplateQsId){
      const qsTemplate = await this.app
        .service('db/write/temp-question-set')
        .get(+compositeAsmtTemplateQsId);
      Object.keys(qsTemplate).forEach(fieldName => {
        payload[fieldName] = qsTemplate[fieldName]
      });
      delete payload['id'];
      delete payload['single_group_id'];
      if (qsTemplate.is_test_design == 0){
        payload['test_design_question_sets'] = JSON.stringify([qsTemplate.id]);
      }
    }

    const newRecord = await this.app
      .service('db/write/temp-question-set')
      .create({
        ... payload, // params fed in after override whatever is in the payload
        group_id,
        slug,
        name,
        description,
        is_test_design,
      });

    const uid = await currentUid(this.app, params);

    this.app.service('public/test-auth/notifications').createNotif({
      config: {
        notifType: NotifType.CREATED_ASSESSMENT,
        itemId: newRecord.id,
        uid
      },
      forDevCoords: true
    }, params)

    return newRecord;

  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const {oldHash, content, name, description, languages, style_profile, override} = <any> data;
    if (id && params){
      const uid = await currentUid(this.app, params);
      const lastSavedHashRecord = await this.app
        .service('db/read/temp-question-set')
        .db()
        .where('id', id)
        .select('hash')
      const lastSavedHash = lastSavedHashRecord[0].hash;
      if ((oldHash !== lastSavedHash) && (!override)){
        throw new Errors.NotAcceptable('REQ_OVERRIDE')
      }
      const hashBasis = JSON.stringify(data);
      const hash = hashGivenString(hashBasis);
      const patchRecord:any = { hash}
      if (name){ patchRecord.name = name };
      if (description){ patchRecord.description = description };
      if (languages){ patchRecord.languages = languages };
      if (style_profile) {patchRecord.style_profile = style_profile};

      const record = await this.app
        .service('db/write/temp-question-set')
        .patch(id, patchRecord)
      const question_set_id = record.id
      await this.app
        .service('db/write/temp-question-set-log')
        .create({ question_set_id, content, hash, updated_by_uid:uid })
      return {hash}
    }
    throw new Errors.GeneralError()
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    try{  
      if(!params) {
        throw new Errors.BadRequest('MISSING_PARAMS');
      }
      const is_archived = params?.query?.isRecovering? 0 : 1; 
      const payload = {
        is_archived: is_archived,
        archived_on: dbDateNow(this.app),
      }
      const res = await this.app
      .service('db/write/temp-question-set')
      .patch(id, payload);

      const uid = await currentUid(this.app, params);
      this.app.service('public/test-auth/notifications').createNotif({
        config: {
          notifType: NotifType.ARCHIVED_ASSESSMENT,
          itemId: res.id,
          uid: uid
        },
        forDevCoords: true
      }, params)
      return {}
    } catch(e){
      console.error(e);
      return {error:e}
    }


  }
}
// getting item banks in a group that user has access to 
const item_set_query_by_group_member = ` 
  SELECT 
      qs.id AS id,
      qs.single_group_id AS single_group_id,
      qs.group_id AS group_id,
      qs.slug AS slug,
      qs.name AS name,
      qs.description AS description,
      qs.is_test_design AS is_test_design,
      qs.test_design_question_sets AS test_design_question_sets,
      qsit.num_items AS num_items,
      qgm.uid AS uid
  FROM 
      questionset_group_members qgm
  JOIN 
      temp_question_set qs 
      ON (qs.is_archived = :isArchived AND qs.group_id = qgm.group_id AND qs.is_test_design = :isTestDesign)
  LEFT JOIN 
      question_set_item_tally qsit 
      ON qsit.id = qs.id
  WHERE 
      qgm.uid = :userID
  GROUP BY 
      qs.id, qgm.uid`

// getting individual access (i.e. they have access to an item bank but not it's group)
const item_set_query_by_single_member =
  `SELECT 
        qs2.id AS id,
        qs2.single_group_id AS single_group_id,
        qs2.group_id AS group_id,
        qs2.slug AS slug,
        qs2.name AS name,
        qs2.description AS description,
        qs2.is_test_design AS is_test_design,
        qs2.test_design_question_sets AS test_design_question_sets,
        qsit2.num_items AS num_items,
        qsm.uid AS uid
    FROM 
        question_set_single_members qsm
    JOIN 
        temp_question_set qs2 
        ON (qs2.is_archived = :isArchived AND qs2.single_group_id = qsm.single_group_id AND qs2.is_test_design = :isTestDesign)
    LEFT JOIN 
        question_set_item_tally qsit2 
        ON qsit2.id = qs2.id
    WHERE 
        qsm.uid = :userID
    GROUP BY 
        qs2.id, qsm.uid

    ORDER BY 
        id;
  `;
// Usage with dbRawRead function
export const fetchQuestionSets = async (app: any, userID: number, isTestDesign: number, isArchived: number) => {
  const props = {
    isArchived,
    isTestDesign,
    userID
  }
  
  const results_by_group = await dbRawRead(app, props, item_set_query_by_group_member); 
  const results_by_single = await dbRawRead(app, props, item_set_query_by_single_member);
  const results = [
    ...new Map(
      [...results_by_group, ...results_by_single].map(item => [item.id, item])
    ).values()
  ];
  return results;
};
