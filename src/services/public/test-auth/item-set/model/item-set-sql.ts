

export const SQL_TWATR_SLUGS = `-- SQL_TWATR_SLUGS 
    select distinct type_slug 
    from test_window_td_alloc_rules twtar 
    where item_set_id = :item_set_id
;`

export const SQL_BATCH_ALLOC_ALLOWED_TWATR_SLUGS = `-- SQL_BATCH_ALLOC_ALLOWED_TWATR_SLUGS 
    select mbap.id
         , mbap.slug
         , mbap.allowed_twtar_type_slugs  
    from marking_batch_alloc_policies mbap 
    where mbap.is_revoked = 0
      and mbap.is_classroom = 0
;`

export const SQL_BATCH_ALLOC_DETAIL = `-- SQL_BATCH_ALLOC_DETAIL 
    select mbap.id
         , mbap.slug
         , mbap.caption
         , mbap.read_rules 
    from marking_batch_alloc_policies mbap 
    where mbap.id in (:mbap_ids)
;`

export const SQL_ITEM_BATCH_ALLOC_INFO = `-- SQL_ITEM_BATCH_ALLOC_INFO 
    select tqsi.id 
        , tqsi.is_human_scored 
        , tqsi.batch_alloc_policy_id 
        , tqsi.batch_alloc_policy_item_slug 
        , tqsi.stu_title_entry_id 
        , tqsi.is_paper_response
        , tqsi.resp_sheet_config
    from test_question_scoring_info tqsi 
    where tqsi.item_id IN (:item_id)
        and lang = :lang
    and tqsi.is_revoked = 0
;`

export const SQL_ITEM_BATCH_ALLOC_INFO_PREV = `-- SQL_ITEM_BATCH_ALLOC_INFO_PREV 
    select tqsi.id 
    from test_question_scoring_info tqsi 
    where tqsi.item_id IN (:item_id)
      and tqsi.lang = :lang
      and tqsi.is_revoked = 0
;`
export const SQL_REVOKE_PREV_ITEM_BATCH_ALLOC_INFO = `-- SQL_ITEM_BATCH_ALLOC_INFO_PREV 
    UPDATE test_question_scoring_info
    SET is_revoked = 1
      , revoked_on = now()
      , revoked_by_uid = :revoked_by_uid
    WHERE id IN (:prev_tqsi_ids)
;`

export const SQL_HISTORICAL_PSYCH_STATS_SUMMARY  = `
    select 
	  twtar.slug twtar_slug
	    , tw.title 
	    , tw.id test_window_id
        , pis.test_design_id 
 	    , tw.title tw_title
        , tw.date_start 
        , tw.date_end 
    from psych_item_stats pis 
    join test_windows tw 
        on tw.id = pis.test_window_id
    join test_window_td_alloc_rules twtar 
        on twtar.test_window_id = pis.test_window_id 
        and twtar.is_questionnaire = 0 
        and twtar.is_sample = 0 
        and twtar.is_custom = 0
        and( twtar.test_design_id = pis.test_design_id or twtar.tqr_ovrd_td_id= pis.test_design_id)
    where item_id IN (:questionIdsLookup)
    group by pis.test_window_id, pis.test_design_id;
`

export const SQL_HISTORICAL_PSYCH_STATS = `
    select 
	   twtar.slug twtar_slug
     , pis.test_window_id 
     , pis.item_id 
     , pis.lang
     , pis.NR
     , pis.NF
     , pis.omit
     , pis.p
     , pis.rpb 
     , pis.crpb
     , pis.form_code 
     , pis.95p_lower
	 , pis.95p_upper
	 , pis.p_high
	 , pis.p_mid
	 , pis.p_low
	 , pis.total
	 , pis.high
	 , pis.mid
	 , pis.low
	 , pis.iri
	 , pis.rbis
	 , pis.crbis
     , pis.test_design_id 
     , pis.imported_on 
     , pis.export_id 
    from psych_item_stats pis 
    join test_windows tw 
        on tw.id = pis.test_window_id
    join test_window_td_alloc_rules twtar 
        on twtar.test_window_id = pis.test_window_id 
        and twtar.is_questionnaire = 0 
        and twtar.is_sample = 0 
        and twtar.is_custom = 0
        and( twtar.test_design_id = pis.test_design_id or twtar.tqr_ovrd_td_id= pis.test_design_id)
    where pis.item_id IN (:questionIdsLookup)
    group by pis.test_window_id, pis.test_design_id, pis.item_id ;
`