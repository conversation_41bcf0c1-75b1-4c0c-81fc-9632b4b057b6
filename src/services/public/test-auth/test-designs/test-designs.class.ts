import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';
import { Errors } from '../../../../errors/general';
import { storeInS3 } from '../../../upload/upload.listener';
import { dbRawRead } from '../../../../util/db-raw';
import { arrToMap } from '../../../../util/param-sanitization';
import { ILanguage } from '../../test-question-register/tqr-publish/types';
import logger from '../../../../logger';
import { IStyleProfileDb } from '../../../db/schemas/style-profile.schema';

export interface IQuestionWeightMapping {questionId: number, weight: number | null}
interface Data {source_item_set_id:number, name:string, forms:string[], framework:string, lang:string, test_design_id?:number, question_weight_mapping?: IQuestionWeightMapping[], item_version_mapping?: any, expected_answer_mapping?: any}

interface ServiceOptions {}

export class TestDesigns implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {source_item_set_id} = params.query;
      const testDesignRecords = await dbRawRead(this.app, [source_item_set_id], `
        select td.id
          , td.name
          , td.created_on
          , td.is_public
          , count(tf.id) num_forms
          , tdso.id tdso_id
          , tdso.is_approved
          , tdso.compared_to_td_id 
          , tdso.approved_on
          , tdso.created_on started_on
          , CONCAT(approved_by_u.first_name, ' ', approved_by_u.last_name) approved_by
          , CONCAT(created_by_u.first_name, ' ', created_by_u.last_name) started_by
        from test_designs td
        join test_forms tf
          on tf.test_design_id = td.id
        left join test_design_sign_off tdso
        	on tdso.test_design_id = td.id
        	and tdso.is_revoked = 0
        left join users approved_by_u 
        	on approved_by_u.id = tdso.approved_by_uid
        left join users created_by_u
        	on created_by_u.id = tdso.created_by_uid 
        where td.source_item_set_id = ?
         and td.is_revoked = 0
        group by td.id 
        order by td.created_on desc
        limit 50;
      `);
      const testDesignIds = testDesignRecords.map(td => td.id);
      if (testDesignIds.length == 0){
        return [];
      }
      const testDesignRef = arrToMap(testDesignRecords, 'id');
      const twtdarRecords = await dbRawRead(this.app, [testDesignIds.concat(-1)], `
        select twtar.test_design_id td_id
             , tw.id tw_id
             , tw.title
             , tw.is_qa 
             , tw.date_start
             , tw.date_end
             , twtar.id twtar_id
             , twtar.type_slug 
             , twtar.slug
             , twtar.order
             , twtar.user_metas_filter
             , twtar.cut_score_def_id
             , twtar.test_date_start twtar_date_start
             , twtar.long_name twtar_name
        from test_window_td_alloc_rules twtar 
        join test_windows tw on tw.id = twtar.test_window_id 
        where twtar.test_design_id in (?)
          and twtar.is_custom = 0
      `);
      for (let twtdar of twtdarRecords){
        const td = testDesignRef.get(twtdar.td_id);
        if (td){
          td.twtdars = td.twtdars || [];
          td.twtdars.push(twtdar);
        }
      }
      return testDesignRecords;
    }
    throw new Errors.BadRequest();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async create (data: Data, params?: Params): Promise<Data> {
    const {source_item_set_id, framework, name, forms, question_weight_mapping, item_version_mapping, expected_answer_mapping} = data;
    if (params){
      // who?
      const created_by_uid = await currentUid(this.app, params);
      // create test design
      let test_design_id:number | undefined = data.test_design_id;
      let created_on = '';
      let styleProfileDbRec: IStyleProfileDb | undefined = undefined;

      // fetch style profile & version
      const queSetRec = await this.app.service('db/read/temp-question-set').get(source_item_set_id)
      if(!queSetRec.style_profile) logger.silly(`STYLE_PROFILE_MISSING: framework does not have associated style profile for item set id: ${source_item_set_id}.`) // silent err 
      if(queSetRec.style_profile){
        styleProfileDbRec = await this.app
        .service('db/read/style-profiles')
        .db()
        .where('slug', queSetRec.style_profile)
        .first();
      }

      if (!test_design_id){
        const testDesignRecord = await this.app
          .service('db/write/test-designs')
          .create({
            created_by_uid,
            name,
            framework,
            source_item_set_id,
            author_group_id: null,
            style_profile_version_id: styleProfileDbRec?.version_id,
            assessment_structure_id: queSetRec?.assessment_structure_id
          })
        test_design_id = testDesignRecord.id;
        created_on = testDesignRecord.created_on;
      }
      if (test_design_id){
        // Test form path for test design:
        const testDesignFormsPath = `test_forms/pregen/8/test_design/${test_design_id}`;
        
        // upload style-profile
        if(styleProfileDbRec){
          const { version_id, slug } = styleProfileDbRec
          const styleProfileSlug = slug?.split('.')?.[0] || slug;
          const styleProfilePath = `${testDesignFormsPath}/style_profiles/${styleProfileSlug}-v${version_id}-${(new Date()).valueOf()}.json`;
          await storeInS3(styleProfilePath, <string>(styleProfileDbRec.config));
          await this.app.service('db/write/test-designs').patch(test_design_id, { profile_path: styleProfilePath });
        } 


        // create test forms
        const formConfigs:any[] = forms.map(form => {
          const formObj = JSON.parse(form);
          return {
            formObj,
            form
          }
        });
        // const recentlyCreatedTQR = new Map();
        // for (let formConfig of formConfigs){
        //   const {formObj} = formConfig;
        //   const {lang} = formObj;
        //   await this.processFormTQR(+<number>test_design_id, lang, formObj, recentlyCreatedTQR);
        // }

        const formIdMap: Map<number,any> = new Map();
        let testLang = '';
        await Promise.all(formConfigs.map(async (formConfig:any) => {
          const {form, formObj} = formConfig;
          const {lang, sourceFormId} = formObj;
          if(!testLang) testLang = lang
          const newTestFormRecord = await this.app
            .service('db/write/test-forms')
            .create({
              test_design_id,
              created_by_uid,
              source_tf_id: sourceFormId || null, // only defined for pre-assembled panels
              lang,
            })
          const testFormId = newTestFormRecord.id;
          formIdMap.set(testFormId, formObj);
          const timestamp = (new Date()).valueOf();
          const file_path = `${testDesignFormsPath}/test_forms/${testFormId}/${testFormId}-${timestamp}.json`;
          await storeInS3(file_path, form);
          await this.app
            .service('db/write/test-forms')
            .patch( testFormId, {file_path});
          return newTestFormRecord.id
        }))

        const mappings = {
          question_weight_mapping: question_weight_mapping || null, 
          item_version_mapping: item_version_mapping || null, 
          expected_answer_mapping: expected_answer_mapping || null
        }
        // publish TQR 
        const publishedTqr = await this.app
        .service('public/test-question-register/tqr-publish')
        .populateTestQuestionRegisterEQAO(+<number>test_design_id, formIdMap, <ILanguage>testLang, mappings);


        return <any> {
          id: test_design_id,
          created_on,
        };
      }
    }
    throw new Errors.BadRequest();
  }

  async processFormTQR(test_design_id:number, lang:string, formObj:any, recentlyCreatedTQR:Map<number, boolean>){
    // extract list of questions
    const qIds:number[] = [];
    Object.keys(formObj.questionDb).map((qId:string | number) => qIds.push(+qId));
    // check which questions are already indicated in the tqr
    const existingRecords = await dbRawRead(this.app, [test_design_id, lang, qIds], `
      select tqr.question_id, tqr.id 
      from test_question_register tqr 
      where tqr.test_design_id = ?
      and tqr.lang = ?
      and tqr.question_id in (?)
    `);
    const tqrRef = arrToMap(existingRecords, 'question_id');
    const qIdsToInsert:number[] = [];
    qIds.forEach(qId => {
      if (!tqrRef.get(qId) && !recentlyCreatedTQR.get(qId)){
        qIdsToInsert.push(qId);
      }
    });
    if (qIdsToInsert.length === 0){
      return;
    }
    const paramMapRules:{item_bank_code:string, tqr_col:string, options:any}[] = await dbRawRead(this.app, [], `
      select item_bank_code
           , tqr_col
           , options
      from test_question_register_param_map tqrpm
    ;`);
    paramMapRules.forEach(paramMapRule => {
      if (paramMapRule.options){
        paramMapRule.options = JSON.parse(paramMapRule.options);
      }
      else {
        paramMapRule.options = {};
      }
    })

    const questionConfigs = await dbRawRead(this.app, [qIdsToInsert], `
      select tq.id question_id
           , tq.question_label question_label
           , tq.config 
      from test_questions tq 
      where tq.id in (?)
    `);
    await Promise.all(
      questionConfigs.map(async (record:any) => {
        const {question_id, question_label, config} = record;
        const questionConfig = JSON.parse(config);
        const meta = questionConfig.meta || {};
        const tqrPayload:any = {
          question_id, 
          question_label,
          test_design_id, 
          lang,
        }
        paramMapRules.forEach(paramMapRule => {
          let val = meta[paramMapRule.item_bank_code] || null;
          // to do: can handle more complicated rules with options
          tqrPayload[paramMapRule.tqr_col] = val;
        });
        await this.app.service('db/write/test-question-register').create(tqrPayload);
        recentlyCreatedTQR.set(question_id, true);
      })
    );
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
