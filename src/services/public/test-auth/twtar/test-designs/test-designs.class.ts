import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { dbRawReadReporting, dbRawWrite } from '../../../../../util/db-raw';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class TestDesigns implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    
    if (params){
      const uid = await currentUid(this.app, params)

      const records = await dbRawReadReporting(this.app, {tw_id: id}, `
        select twtar.id 
            -- name 
            , twtar.type_slug 
            , twtar.long_name 
            , twtar.lang
            , twtar.order 
            , twtar.form_code
            , twtar.test_duration
            , twtar.selection_order
            , tw.test_ctrl_group_id tc_group_id
            , twtar.test_window_id
            -- test design
            , ifnull(twtar.item_set_id, td.source_item_set_id) display_item_set_id 
            , tqs.name display_item_set_name 
            , twtar.item_set_id 
            , td.source_item_set_id
            , twtar.test_design_id 
            , twtar.tqr_ovrd_td_id
            , td.created_on td_created_on
            , td.created_by_uid td_created_by_uid
            , td_u.first_name td_created_by_first_name
            , td_u.last_name td_created_by_last_name
            , td_u.contact_email td_created_by_contact_email
            -- marking setup needs
            , twtar.is_marking_req 
            -- active status
            , twtar.is_active_for_auth
            , twtar.is_active 
            , twtar.is_active_for_scheduled_access
            , twtar.is_active_for_qa
            -- target use
            , twtar.is_secured
            , twtar.is_field_test
            , twtar.is_sample 
            , twtar.is_school_allowed_strict 
            , twtar.req_sd_lang 
            , twtar.req_sd_lang_not 
            , twtar.is_classroom_common_form
            -- timing and scheduling
            , twtar.test_duration 
            , twtar.is_scheduled 
            , twtar.is_date_restricted
            , twtar.test_date_start
            , twtar.is_sample_time_enforced 
            , twtar.is_schedule_range 
            , twtar.hard_close_on
            -- accommodations and tools
            , twtar.is_dictionary_enabled 
            , twtar.is_alternative
            -- cover page overrides
            , twtar.cover_page_configs
            , twtar.styling_configs
            -- print version
            , twtar.is_print
            , twtar.print_configs
        from test_window_td_alloc_rules twtar 
        join test_windows tw 
          on tw.id = twtar.test_window_id
        left join test_designs td 
          on td.id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
        left join temp_question_set tqs 
          on tqs.id = ifnull(twtar.item_set_id, td.source_item_set_id)
        left join users td_u
          on td_u.id = td.created_by_uid
        where twtar.test_window_id = :tw_id
          and twtar.is_custom = 0
          and (
                twtar.is_active = 1 
              or twtar.is_active_previously = 1
              or twtar.is_active_for_auth = 1
          )
      `);
      const groupMappings = await this.getGroupMappings()
      const isEditPermission = await this.checkEditRoleLevel(id, uid)
      return {
        records,
        groupMappings,
        isEditPermission,
      }
    }

    throw new Errors.BadRequest();

  }

  async checkEditRoleLevel(tw_id:Id, uid:number){
    const records = await dbRawReadReporting(this.app, {tw_id, uid}, `
      select ur.id 
      from test_windows tw 
      join user_roles ur 
        on ur.group_id = tw.test_ctrl_group_id
        and ur.role_type = 'test_item_author_admin'
        and ur.is_revoked = 0
        and ur.uid = :uid
      where tw.id = :tw_id
    `)
    return records.length > 0 
  }

  async getGroupMappings(){
    return dbRawReadReporting(this.app, {}, `
      select group_id, name, type_slug, tags  
      from authoring_groups
      order by type_slug, name 
    `)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    const {
      test_window_id,
      type_slug,
      item_set_id,
      long_name,
      form_code,
      lang,
    } = <any>data;

    if (params && test_window_id){
      const uid = await currentUid(this.app, params);
      const isEditPermission = await this.checkEditRoleLevel(test_window_id, uid);
      if (!isEditPermission){
        throw new Errors.Forbidden()
      }

      this.app.service('db/write/test-window-td-alloc-rules').create({
        test_window_id,
        type_slug,
        slug: type_slug,
        item_set_id,
        long_name,
        form_code,
        lang,
        is_active: 0,
        is_active_for_auth: 1, 
      })

      return data;
    }
    throw new Errors.BadRequest()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async isTwtarAuthEditable(id:NullableId){
    let isRevokable = false;

    const editableFieldsNeverDirect = [
      'type_slug',
      'source_item_set_id',
      'test_design_id',
      'is_active',
    ]
    const editableFieldsAlways = [
      'long_name',
      'lang',
      'order ',
      'selection_order',
      'tqr_ovrd_td_id',
      'is_active_for_auth',
      'is_active_for_qa',
    ]
    const editableFieldsPreAdmin = [
      'item_set_id',
      'form_code',
      'is_marking_req',
      'is_active_for_scheduled_access',
      'is_secured',
      'is_field_test',
      'is_sample',
      'is_school_allowed_strict',
      'req_sd_lang',
      'req_sd_lang_not',
      'is_classroom_common_form',
      'test_duration',
      'is_scheduled',
      'is_date_restricted',
      'test_date_start',
      'test_date_end',
      'perusal_type',
      'perusal_offset_hours',
      'perusal_duration_hours',
      'perusal_date_start',
      'perusal_date_end',
      'is_sample_time_enforced ',
      'is_schedule_range ',
      'hard_close_on',
      'is_dictionary_enabled',
      'is_alternative',
      'cover_page_configs',
      'styling_configs',
      'is_print',
      'print_configs',
    ]

    const editableFields = [... editableFieldsAlways]

    const [twtar] = await dbRawReadReporting(this.app, {id: id}, `
      select twtar.id 
           , twtar.is_active
           , twtar.is_active_for_auth
      from test_window_td_alloc_rules twtar 
      where twtar.id = :id
        and twtar.is_custom = 0
    `);
    if (twtar.is_active == 0 && twtar.is_active_for_auth == 1){
      isRevokable = true;
      editableFields.push(... editableFieldsPreAdmin)
    }
    return {
      isRevokable,
      editableFields,
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (params && id){
      const uid = await currentUid(this.app, params);
      const isEditPermission = await this.checkEditRoleLevel(id, uid);
      if (!isEditPermission){
        throw new Errors.Forbidden()
      }
      const {editableFields} = await this.isTwtarAuthEditable(id);
      const payload:any = {};
      return data;
    }
    throw new Errors.BadRequest()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if (params && id){
      const uid = await currentUid(this.app, params);
      const isEditPermission = await this.checkEditRoleLevel(id, uid);
      if (!isEditPermission){
        throw new Errors.Forbidden()
      }
      else {
        // const {isRevokable} = await this.isTwtarAuthEditable(id);
        const isRevokable = true
        if (isRevokable){
          await dbRawWrite(this.app, {id, uid}, `
            UPDATE test_window_td_alloc_rules
            SET is_revoked=1
              , is_active_for_auth=0
              , is_active=0
              , revoked_by_uid=:uid
              , revoked_on=now()
            WHERE id=:id;
    
          `)
          return {}
        }
      }
    }
    throw new Errors.BadRequest()
  }
}
