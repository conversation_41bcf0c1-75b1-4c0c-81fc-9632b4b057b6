import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { dbRawRead } from '../../../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class Sessions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // todo: this should not be hardcoded, is this being used anywhere?
  async expandTestDesigns (projectId:number, transform:(record:any, responseRecords:any[])=>any) {
    const records = await dbRawRead(this.app, [], `
      select  td.id as test_design_version_id
            , td.source_item_set_id
            , td.framework
            , UNIX_TIMESTAMP(td.created_on) as created_on
            , td.created_by_uid
      from test_window_td_alloc_rules twtar
      join test_designs td 
        on td.id = twtar.test_design_id
      where test_window_id = 6
      and twtar.is_custom = 0
      and test_design_id != 0
    ;`);
    const responseRecords:any[] = [];
    records.forEach((record:any) => transform(record, responseRecords) );
    return responseRecords
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const projectId = +params.query.projectId
      return this.app
        .service('public/test-ctrl/schools/data-dl/asmt-frmk/sessions')
        .expandTestDesigns(projectId, (record:any, responseRecords:any[]) => {
          const {
            test_design_version_id,
            source_item_set_id,
            framework
          } = record;
          const {sub_sessions} = this.app
            .service('public/test-ctrl/schools/data-dl/asmt-frmk/test-design-versions')
            .parseFrameworkInfo(framework, source_item_set_id);
          sub_sessions.forEach((sub_session, i) => {
            responseRecords.push({
              id: test_design_version_id*100+i,
              test_design_version_id,
              session_number: i + 1,
              description: sub_session.description,
              num_questions: sub_session.num_questions,
              duration_m: sub_session.duration_m,
            })
          })
        });
    }
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
