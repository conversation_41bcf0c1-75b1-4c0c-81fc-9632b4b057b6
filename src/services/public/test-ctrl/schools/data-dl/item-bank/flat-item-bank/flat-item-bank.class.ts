import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { dbRawRead } from '../../../../../../../util/db-raw';
import { Errors } from '../../../../../../../errors/general';
import * as _ from 'lodash';

interface Data {}

interface ServiceOptions {}

export class FlatItemBank implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const projectId = +params.query.projectId
      const records = await this.app
        .service('public/test-ctrl/schools/data-dl/item-bank/flat-item-bank')
        .getQuestionData();
      const columns = 'id	component_id	question_label	lang	expected_answer	expectation	source	P-ID	EQAOID	diff	rpb	b	a	c'.split('\t')
      return records.map(record => {
        const row:{[key:string]:string} = {};
        columns.forEach(prop => {
          row[prop] = record[prop];
        })
        return row;
      })
    }
    throw new Errors.BadRequest();
  }

  async getQuestionData(){
    const records = await dbRawRead(this.app, [], `
      select q.id
          , qs.id as item_bank_id
            , q.question_label as question_label
            , qs.languages as languages
            , q.config
      from test_window_td_alloc_rules twtar
      join test_designs td
        on td.id = twtar.test_design_id
        and twtar.is_custom = 0
      join temp_question_set qs
        on qs.id = td.source_item_set_id
      join test_questions q
        on q.question_set_id = qs.id
        and q.is_archived != 1
      group by q.id
    ;`);
    const recordsClean:any[] = []
    records.forEach(question => {
      try {
        const languages = JSON.parse(question.languages || '["en"]');
        languages.forEach((lang:string, i:number) => {
          let config:any = {};
          try { config = JSON.parse(question.config) } catch(e){
            console.log(e)
          }
          if (!config.meta){config.meta = {};}
          const meta:any = {};

          let EQAOID = config.meta['DIB_ID'] || question.question_label || '';
          if (typeof EQAOID.split === 'function') {
            EQAOID = EQAOID.split('-')[0]
            EQAOID = EQAOID.split('_')[0]
            EQAOID = EQAOID.split('/')[0]
          }
          if (!Number.isInteger(parseInt(EQAOID, 10))){
            EQAOID = 0;
          }

          recordsClean.push({
            'id': question.id,
            lang,
            'question_label': question.question_label,
            'component_id': question.item_bank_id,
            'item_bank_id': question.item_bank_id,
            'label': question.question_label,
            'expected_answer': this.parseExpectedAnswer(config.meta['EA']),
            'expectation': config.meta['OE'],
            'skill_category': config.meta['SC'],
            EQAOID,
            'diff': config.meta['diff'],
            'rpb': config.meta['pbis'],
            'b': config.meta['irt_b'],
            'a': config.meta['irt_a'],
            'c': 0,
          })
        })
      } catch(e){
        console.log(e)
      }
    });
    return recordsClean;
  }

  parseExpectedAnswer(ans:string){
    switch(ans){
      case 'A': return 0;
      case 'B': return 1;
      case 'C': return 2;
      case 'D': return 3;
      case 'E': return 4;
      case 'F': return 5;
      case 'G': return 6;
      default: return ans;
    }
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
