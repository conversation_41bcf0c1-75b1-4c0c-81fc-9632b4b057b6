import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { dbRawRead } from '../../../../../../../util/db-raw';
import { Errors } from '../../../../../../../errors/general';

interface Data {}

interface ServiceOptions {}

export class Questionnaire implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const projectId = +params.query.projectId
      const {questions} = await this.app
        .service('public/test-ctrl/schools/data-dl/item-bank/questionnaire')
        .getQuestionnaireQuestions();
      const columns = 'id	test_design_version_id	label	lang	question_text'.split('\t')
      return questions.map(record => {
        const row:{[key:string]:string} = {};
        columns.forEach(prop => {
          row[prop] = record[prop];
        })
        return row;
      })
    }
    throw new Errors.BadRequest();
  }


  async getQuestionnaireQuestions(isResponseLevel:boolean=true){
    const records = await dbRawRead(this.app, [], `
      select q.id
            , q.question_label as label
            , qs.languages as languages
            , td.id as test_design_version_id
            , q.config
      from test_window_td_alloc_rules twtar
      join test_designs td
        on td.id = twtar.test_design_id
      join temp_question_set qs
        on qs.id = td.source_item_set_id
      join test_questions q
        on q.question_set_id = qs.id
        and q.is_archived != 1
      where twtar.slug IN ('g9_quest_en', 'g9_quest_fr')
        and twtar.is_custom = 0
      group by q.id
    ;`);
    const questions:any[] = []
    const responses:any[] = []
    records.forEach(question => {
      const {
        id,
        label,
        test_design_version_id,
      } = question;
      try {
        const languages = JSON.parse(question.languages || '["en"]');
        languages.forEach((lang:string, i:number) => {
          let config:any = {};
          try { config = JSON.parse(question.config) } catch(e){}
          let question_text = '';
          let content:any[] = config.content;
          if (lang === 'fr'){
            content = config.langLink.content;
          }
          const responsesContent: {response_code:number,response_text:string}[] = []
          content.forEach(el => {
            if (el.elementType == 'text'){
              // to do, this is not capturing more advanced forms of text, I think we do this in one of the print modes for the question (or script gen)
              if (question_text){ question_text = question_text +'\n'; }
              question_text += el.caption;
            }
            if (el.elementType == 'mcq'){
              // not handling all forms of option types (see script gen algo for pieces of more robust approach)
              el.options.forEach((option:any, i:number) => {
                responsesContent.push({
                  response_code: i,
                  response_text: option.content,
                })
              })
            }
          });


          questions.push({
            id: id,
            lang,
            label,
            test_design_version_id,
            question_text,
          });

          responsesContent.forEach((responseContent, i) => {
            responses.push({
              id: id*100+i,
              test_design_version_id,
              question_id: id,
              lang,
              label,
              response_code: responseContent.response_code,
              response_text: responseContent.response_text,
            })
          });

        })
      } catch(e){}
    });
    return {questions, responses};
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
