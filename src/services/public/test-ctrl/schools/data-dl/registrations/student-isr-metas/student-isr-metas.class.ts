import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { dbRawRead } from '../../../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class StudentIsrMetas implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    // this is a get request with a large input parameter
    const {ta_ids} = <any> data;
    return await dbRawRead(this.app, [ta_ids], `
      select
          ta.id test_attempt_id
        , twtar.test_window_id
        , twtar.type_slug as assessment_slug
        , tw.academic_year as school_year
        , schl.foreign_id as school_mident
        , sd.foreign_id as board_mident
        , um.value as student_oen
        , schl.lang as lang
      from test_attempts ta 
      join test_window_td_alloc_rules twtar on twtar.id = ta.twtdar_id and twtar.is_custom = 0
      join test_windows tw on tw.id = twtar.test_window_id 
      join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
      join school_classes sc on sc.id = scts.school_class_id 
      join schools schl on schl.group_id = sc.schl_group_id
      join school_districts sd on sd.group_id =  sc.schl_dist_group_id
      join user_metas um on key_namespace = 'eqao_sdc' and um.key = 'StudentOEN' and um.uid = ta.uid
      where ta.id in (?)
    ;`);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
