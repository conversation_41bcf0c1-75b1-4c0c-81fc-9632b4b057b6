import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { dbRawRead } from '../../../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class TestSessions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const projectId = +params.query.projectId
      const records = await dbRawRead(this.app, [], `
        select 
          ts.id
        , ts.test_window_id
        , s.foreign_id as school_id
        , sc.name as class_code
        , UNIX_TIMESTAMP(ts.date_time_start) as start_time
        , scts.slug as name
        , '' as class_room
        , '' as capacity
        , '' as bookings
        , 0 as present
        , ts.is_closed
        , '' as reported_issues
        , 0 as is_practice
        from test_window_td_alloc_rules twtar
        join test_sessions ts
          on ts.test_window_id = twtar.test_window_id
        join school_class_test_sessions scts 
          on scts.test_session_id = ts.id
        join school_classes sc 
          on sc.id = scts.school_class_id
        join schools s
          on s.group_id = sc.schl_group_id
        where twtar.is_custom = 0
        group by ts.id
      ;`);

      const testSessionIds = records.map(r => r.id);
      const testSessionRecords = await dbRawRead(this.app, [testSessionIds], `
        select 
          t.test_session_id, 
          count(0) as num_students 
        from (
          select ta.test_session_id, ta.id as test_attempt_id
          from test_attempts ta
          join test_attempt_question_responses taqr
            on taqr.test_attempt_id = ta.id
          where ta.test_session_id IN (?)
          group by ta.id
        ) t
        group by t.test_session_id
      ;`);
      const testSessionCountRef = new Map()
      testSessionRecords.forEach(ts => testSessionCountRef.set(+ts.test_session_id, ts.num_students) );

      return records.map(record => {
        const {
          id,
          test_window_id,
          school_id,
          class_code,
          start_time,
          name,
          class_room,
          capacity,
          bookings,
          is_closed,
          reported_issues,
        } = record;

        const is_practice = (name == 'G9_OPERATIONAL') ? 0 : 1;
        const present = testSessionCountRef.get(+id) || 0;

        return {
          id,
          test_window_id,
          school_id,
          class_code,
          start_time,
          name,
          class_room,
          capacity,
          bookings,
          present,
          is_closed,
          reported_issues,
          is_practice,
        }
      })
    }
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
