import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { dbRawRead } from '../../../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class TestWindowDesignAlloc implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const projectId = +params.query.projectId
      return dbRawRead(this.app, [], `
        select 
          twtar.id
        , twtar.test_window_id
        , twtar.slug as short_name
        , twtar.long_name
        , td.source_item_set_id as test_design_id
        , twtar.test_design_id as test_design_version_id
        , 1 as is_avail_for_assign
        from test_window_td_alloc_rules twtar
        join test_designs td
          on td.id = twtar.test_design_id
        where twtar.test_window_id > 0
          and twtar.test_design_id != 0
          and twtar.is_custom = 0
      ;`);
    }
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
