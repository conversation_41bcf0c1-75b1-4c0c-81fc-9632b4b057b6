import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { dbRawRead } from '../../../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class TestWindows implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const projectId = +params.query.projectId
      return dbRawRead(this.app, [], `
        select 
          tw.id
        , tw.title as description
        , UNIX_TIMESTAMP(tw.date_start) as date_start
        , UNIX_TIMESTAMP(tw.date_end) as date_end
        , UNIX_TIMESTAMP(tw.created_on) as created_on
        , tw.create_by_uid as created_by_uid
        , tw.duration_m as duration_m
        , UNIX_TIMESTAMP(tw.last_activated_on) as last_activated_on
        , tw.last_activated_by_uid as last_activated_by_uid
        , tw.is_allow_new_ts as is_allow_new_ts
        , tw.is_allow_new_bookings as is_allow_new_bookings
        , tw.is_allow_results_tt as is_allow_results_tt
        , tw.is_multi_attempt as is_multi_attempt
        , tw.num_attempts as num_attempts
        , 0 as attempts_intvl -- tw.attempts_intvl
        , tw.is_invig_unsubmit as is_invig_unsubmit
        , tw.is_invig_taketest as is_invig_taketest
        , tw.is_invig_mark as is_invig_mark
        , tw.is_archived as is_archived
        , tw.is_allow_test_centre as is_allow_test_centre
        , tw.is_allow_classroom as is_allow_classroom
        , tw.is_allow_remote as is_allow_remote
        , tw.is_allow_mobile_tether as is_allow_mobile_tether
        , tw.is_allow_video_conference as is_allow_video_conference
        from test_window_td_alloc_rules twtar
        join test_windows tw
          on tw.id = twtar.test_window_id
          and twtar.is_custom = 0
        group by tw.id
      ;`);
    }
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
