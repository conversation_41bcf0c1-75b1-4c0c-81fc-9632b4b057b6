import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../../util/db-raw';
import { IReadRules } from '../../../scor-lead/process-scoring-third-reads/process-scoring-third-reads';
import { dbDateNow } from '../../../../../util/db-dates';
import { currentUid } from '../../../../../util/uid';

interface Data {
  batch_alloc_policy_ids: number[],
  name: string,
  start_on: Date,
  end_on: Date,
  test_window_id: number,
  lang: string,
  is_sample?: number,
  test_design_ids: number[]
}

interface ServiceOptions {}

export interface IMarkingBatchAllocPolicies {
  twtar_id: number,
  long_name: string,
  is_active: number, 
  test_design_id: number, 
  lang: string,
  item_ids: string, 
  batch_alloc_policy_id: number, 
  batch_alloc_policy_item_slug: string, 
  marking_window_id: number,
  read_rules: string,
  is_sample: number,
  marking_window_name: string,
  twtar_form_code: string,
  type_slug: string,
  order: number,
}

export interface IScoringWindowBluePrint {
  read_rules: IReadRules,
  marking_window_items: {
    assessment_slug: string,
    caption: string,
    lang: string,
    is_multiple_form?: string | boolean,
    score_profile_groups: {
      item_slug: string,
      score_profile_group_id: number,
      score_profile_ids: number[],
    }[]
  },
  auto_pool_flags: any[],
  is_response_sheet_enabled?: number
}

const DEFAULT_INVITE_EMAIL_MESSAGE = JSON.stringify(
  {
    "new_user_content": {
        "en": "Hello {{SCORER_NAME}},\n\nTo participate fully in the online scoring, you will need to create an account in the e-assessment scoring system.\n\nYour invitation code is {{INVITATION_CODE}}\n\nPlease click the link below and use your invitation code to create your account:\n\n{{DOMAIN}}#/{{LANG_CODE}}/test-admin/accept-invitation/{{EMAIL_ENCODED}}/{{INVITATION_CODE}}",
        "fr": "Hello {{SCORER_NAME}},\n\nTo participate fully in the online scoring, you will need to create an account in the e-assessment scoring system.\n\nYour invitation code is {{INVITATION_CODE}}\n\nPlease click the link below and use your invitation code to create your account:\n\n{{DOMAIN}}#/{{LANG_CODE}}/test-admin/accept-invitation/{{EMAIL_ENCODED}}/{{INVITATION_CODE}}"
    },
    "existing_user_content": {
        "en": "Hello {{SCORER_NAME}},\n\nTo participate fully in the online scoring, you will need to sign in to the e-assessment scoring system.\n\nPlease click the link below and use your email address and password to log in:\n\n{{DOMAIN}}",
        "fr": "Hello {{SCORER_NAME}},\n\nTo participate fully in the online scoring, you will need to sign in to the e-assessment scoring system.\n\nPlease click the link below and use your email address and password to log in:\n\n{{DOMAIN}}"
    }
  });
export class ScoringWindow implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any[]> {
    if(!params || !params.query || !params.query.tw_id) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const { tw_id, is_sample } = params.query;
    if(tw_id == undefined || is_sample == undefined){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    return await dbRawRead(this.app, { tw_id, is_sample }, `
      select twtar.id twtar_id
          , twtar.long_name
          , twtar.is_active twtar_is_active
          , tqr.test_design_id
          , GROUP_CONCAT(distinct tqr.question_id) item_ids
          , tqsi.batch_alloc_policy_id
          , GROUP_CONCAT(distinct tqsi.batch_alloc_policy_item_slug) batch_alloc_policy_item_slug
          , min(IFNULL(mwtw.is_sample, 0)) is_sample
          , mw.id marking_window_id
          , mw.name marking_window_name
          , mw.is_rescore
          , mw.is_active mw_is_active
          , mw.is_archived 
      from test_windows tw 
      join test_window_td_alloc_rules twtar 
        on twtar.test_window_id = tw.id 
        and twtar.is_questionnaire = 0 
        and twtar.is_custom = 0
      join test_question_register tqr
        on tqr.test_design_id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
        -- todo: there may be other fields we should use to filter on tqr (but will not get into that now)
      join test_question_scoring_info tqsi 
        on tqsi.id = tqr.tqsi_id
        and tqsi.lang = tqr.lang
        and tqsi.is_human_scored = 1
      left join marking_window_items mwi 
        on mwi.item_id = tqsi.item_id  
        and mwi.id = mwi.sync_batches_to_wmi_id
        and mwi.batch_alloc_policy_id = tqsi.batch_alloc_policy_id 
      left join marking_window_test_window mwtw 
        on mwtw.test_window_id  = tw.id 
        and mwtw.marking_window_id = mwi.marking_window_id
        and mwtw.is_removed = 0
        and mwtw.is_sample = :is_sample
      left join marking_windows mw
      on mw.id = mwi.marking_window_id
        and mwtw.marking_window_id = mw.id
      where tw.id = :tw_id -- 106 or 117
      group by twtar.id, tqsi.batch_alloc_policy_id, mw.id
      order by twtar.long_name, mwi.marking_window_id
      ;
    `)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<any> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS')
    }
    if(
        !data.batch_alloc_policy_ids || 
        !data.end_on || 
        !data.start_on || 
        !data.test_window_id || 
        !data.name || 
        !data.lang ||
        !data.test_design_ids
    ) {
      throw new Errors.BadRequest('MISSING_DATA');
    }

    // One scoring window can have multiple assessments (batch allocation policies)
    const {batch_alloc_policy_ids, name, start_on, end_on, test_window_id, is_sample, lang, test_design_ids} = data;
    const UID = await currentUid(this.app, params);
    const batchPolicy: IMarkingBatchAllocPolicies[] = await dbRawRead(this.app, {batch_alloc_policy_ids, test_design_ids, test_window_id}, `
      select twtar.id twtar_id
          , twtar.long_name
          , twtar.is_active
          , twtar.form_code twtar_form_code
          , tw.type_slug 
          , tqr.test_design_id
          , tqr.lang
          , GROUP_CONCAT(distinct tqr.question_id) item_ids
          , tqsi.batch_alloc_policy_id
          , GROUP_CONCAT(distinct tqsi.batch_alloc_policy_item_slug) batch_alloc_policy_item_slug
          , mwi.marking_window_id
          , mw.name marking_window_name
          , mw.is_rescore
          , min(IFNULL(mwtw.is_sample, 0)) is_sample
          , mbap.read_rules
          , mbap.order
      from test_windows tw 
      join test_window_td_alloc_rules twtar 
        on twtar.test_window_id = tw.id 
        and twtar.is_sample = 0
        and twtar.is_questionnaire = 0 
        and twtar.is_custom = 0
      join test_question_register tqr
        on tqr.test_design_id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
        -- todo: there may be other fields we should use to filter on tqr (but will not get into that now)
      join test_question_scoring_info tqsi 
        on tqsi.id = tqr.tqsi_id
        and tqsi.lang = tqr.lang
        and tqsi.is_human_scored = 1
      join marking_batch_alloc_policies mbap
        on mbap.id = tqsi.batch_alloc_policy_id
      left join marking_window_items mwi 
        on mwi.item_id = tqsi.item_id  
        and mwi.id = mwi.sync_batches_to_wmi_id
        and mwi.batch_alloc_policy_id = tqsi.batch_alloc_policy_id 
      left join marking_window_test_window mwtw 
        on mwtw.test_window_id  = tw.id 
        and mwtw.marking_window_id = mwi.marking_window_id
        and mwtw.is_removed = 0
      left join marking_windows mw
        on mw.id = mwi.marking_window_id
      where tw.id = :test_window_id -- 106 or 117
        and tqsi.batch_alloc_policy_id in (:batch_alloc_policy_ids)
        and tqr.test_design_id in (:test_design_ids)
      group by twtar.id, tqsi.batch_alloc_policy_id
      order by twtar.long_name, mwi.marking_window_id
      ;
    `);

    if(!batchPolicy) {
      throw new Errors.BadRequest('INVALID_ID');
    }

    // Create u_group record
    const markingWindowGroup = await this.app.service('db/write/u-groups').create({
      group_type: 'marking_window',
      description: name
    });

    const langs = new Set(batchPolicy.map(policy => policy.lang));
    const isMultipleLanguage = langs.size > 1;

    const groupId = markingWindowGroup.id;
    const isTestCentre = batchPolicy.filter(policy=>policy.type_slug == "ABED_CAEC");
    const twtarIds = [...new Set(batchPolicy.map(policy=> policy.twtar_id))];

    // Create marking_windows record
    const markingWindow = await this.app.service('db/write/marking-windows').create({
      group_id: groupId,
      name,
      start_on,
      end_on,
      created_on: dbDateNow(this.app),
      created_by_uid: UID,
      lang,
      is_active: 1,
      is_scoring_disabled: 0,
      invite_email_message: DEFAULT_INVITE_EMAIL_MESSAGE,
      is_test_centre: isTestCentre.length > 0?1:0,
      twtdar_ids : JSON.stringify({
        twtdar_ids: twtarIds
      }) 
    });

    const markingWindowId = markingWindow.id;
    // Create marking_window_test_window record
    const mwtw = await this.app.service('db/write/marking-window-test-window').create({
      marking_window_id: markingWindowId,
      test_window_id,
      created_on: dbDateNow(this.app),
      created_by_uid: UID,
      is_sample: is_sample != undefined && is_sample != null ? is_sample : 0,
      is_material: 0,
    })
    
    // Create marking_window_items
    const markingWindowItems: any[] = [];
    const testDesignBatchAllocMap = await this.getQuestionIdBatchAllocMap(batch_alloc_policy_ids, test_window_id, test_design_ids);

    // For each assessment (each batch policy is a separate assessment)
    try{
      for(let policy of batchPolicy) {
        const batchAllocAssmtMap = testDesignBatchAllocMap.get(policy.test_design_id)!;
        const scoringWindowBlueprint: IScoringWindowBluePrint = JSON.parse(policy.read_rules);
        const {assessment_slug, caption, score_profile_groups, is_multiple_form} = scoringWindowBlueprint.marking_window_items;
        let captionFormSuffix = ''; // if false
        let slugFromSuffix = '';
        if(is_multiple_form && policy.twtar_form_code){
          captionFormSuffix = ` (Form ${policy.twtar_form_code})` // if true
          slugFromSuffix = `-FORM-${policy.twtar_form_code}`
        }
        if(isMultipleLanguage && lang != policy.lang){
          captionFormSuffix = captionFormSuffix + ` (${policy.lang.toUpperCase()})` // if true
          slugFromSuffix = slugFromSuffix + `-${policy.lang.toUpperCase()}`
        }
        const auto_pool_flags = scoringWindowBlueprint.auto_pool_flags ? JSON.stringify(scoringWindowBlueprint.auto_pool_flags) : '[]';
        const batchAllocPolicyId = policy.batch_alloc_policy_id;
        const questionAssmtMap = batchAllocAssmtMap.get(policy.batch_alloc_policy_id);
        if(!questionAssmtMap) {
          throw new Errors.GeneralError('MISSING_BATCH_POLICY_ASMT_MAPPING');
        }

        let group_to_mwi_id = -1; // Each assessment/batch policy will have its own group_to_mwi_id

        // For each assignment in the assessment
        for(let i=0; i<score_profile_groups.length; i++) {
          const assignment = score_profile_groups[i];
          const questionId: number | undefined = questionAssmtMap.get(assignment.item_slug)?.item_id;
          if(!questionId) {
            throw new Errors.GeneralError('MISSING_ASMT_QUESTION_MAPPING');
          }
          const scoreProfileIds = assignment.score_profile_ids;

          let sync_batches_to_wmi_id = -1; // Each assessment/batch policy will have its own group_to_mwi_id

          // For each scale/score profile in the assessment, create the mwi based on the blueprint
          for(let k = 0; k<scoreProfileIds.length; k++) {
            let mwi = await this.app.service('db/write/marking-window-items').create({
              marking_window_id: markingWindowId,
              group_id: groupId,
              item_id: questionId,
              score_profile_id: scoreProfileIds[k],
              score_profile_group_id: assignment.score_profile_group_id,
              slug: assessment_slug + slugFromSuffix,
              batch_alloc_policy_id: batchAllocPolicyId,
              caption: caption + captionFormSuffix,
              created_on: dbDateNow(this.app),
              created_by_uid: UID,
              group_to_mwi_id,
              sync_batches_to_wmi_id,
              lang: policy.lang
            })

            if(i==0 && k==0) { // If first scale in the assessment, set as group_to_mwi_id
              group_to_mwi_id = mwi.id;
              mwi = await this.app.service('db/write/marking-window-items').patch(group_to_mwi_id, {
                group_to_mwi_id
              });
            }
            if(k==0) { // If first scale in the assignment, set as sync_batches_to_wmi_id
              sync_batches_to_wmi_id = mwi.id;
              mwi = await this.app.service('db/write/marking-window-items').patch(sync_batches_to_wmi_id, {
                sync_batches_to_wmi_id
              });
            }

            markingWindowItems.push(mwi);
          }
        }

        const questionMapArray = Array.from(questionAssmtMap.values());
        const transformedEntryIds = questionMapArray.reduce((acc: any, item) => {
          acc[item.item_id] = item.stu_title_entry_id;
          return acc;
        }, {});

        // map for mwtd
        const itemIds = JSON.stringify({item_ids: questionMapArray.map((item) => item.item_id), item_id_stu_title_entry_id: transformedEntryIds});

        await this.app.service('db/write/marking-window-task-definitions').create({
          marking_window_id: markingWindowId,
          batch_alloc_policy_id: batchAllocPolicyId,
          item_id_map: itemIds,
          cached__group_to_mwi_id: group_to_mwi_id,
          cahed__slug: assessment_slug + slugFromSuffix,
          auto_pool_flags: auto_pool_flags,
          created_on: dbDateNow(this.app),
          order: policy.order
        })
      }
    } catch (err:any) {
      await this.cleanUpOnError(markingWindow, mwtw, UID);
      throw new Errors.GeneralError(err.message);
    }

    return { markingWindow, markingWindowGroup, mwtw, markingWindowItems };
  }

  async cleanUpOnError(markingWindow: any, mwtw: any, uid: number) {
    await this.app.service('db/write/marking-windows').patch(markingWindow.id, {
      is_scoring_disabled: 1,
      is_active: 0,
      is_hidden_for_scorers: 1
    })

    await this.app.service('db/write/marking-window-test-window').patch(mwtw.id, {
      is_removed: 1,
      removed_by_uid: uid,
      removed_on: dbDateNow(this.app),
    })
  }

  async getQuestionIdBatchAllocMap(batch_alloc_policy_ids: number[], test_window_id: number, test_design_ids: number[]) {
    type IAssmtQuestionMap = Map<string, {item_id: number, stu_title_entry_id: number}>;
    type IBatchAllocAssmtMap = Map<number, IAssmtQuestionMap>;
    const testDesignBatchAllocMap = new Map<number, IBatchAllocAssmtMap>();
    const questions = await dbRawRead(this.app, {batch_alloc_policy_ids, test_window_id, test_design_ids}, `
      select tqsi.batch_alloc_policy_id
           , tqsi.item_id
           , tqsi.stu_title_entry_id
           , tqsi.batch_alloc_policy_item_slug 
           , tqr.test_design_id
      from test_question_scoring_info tqsi
      join test_question_register tqr
        on tqsi.id = tqr.tqsi_id
        and tqr.lang = tqsi.lang
      join test_window_td_alloc_rules twtar 
        on tqr.test_design_id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
        and twtar.is_sample = 0
        and twtar.is_questionnaire = 0 
        and twtar.is_custom = 0
      join test_windows tw
      on twtar.test_window_id = tw.id 
      where tqsi.batch_alloc_policy_id in (:batch_alloc_policy_ids)
        and tw.id = :test_window_id
        and tqr.test_design_id in (:test_design_ids)
      group by tqr.test_design_id, tqsi.item_id, tqsi.batch_alloc_policy_id;
    `);

    questions.forEach((question) => {
      const batchAllocId = question.batch_alloc_policy_id;
      const testDesignId = question.test_design_id
      if(!testDesignBatchAllocMap.has(testDesignId)) {
        const batchAllocAssmtMap = new Map<number, IAssmtQuestionMap>();
        testDesignBatchAllocMap.set(testDesignId, batchAllocAssmtMap)
      }
      const batchAllocAssmtMap = testDesignBatchAllocMap.get(testDesignId)!;
      if(!batchAllocAssmtMap.has(batchAllocId)) {
        const assmtQuestionMap: IAssmtQuestionMap = new Map<string, {item_id: number, stu_title_entry_id: number}>();
        batchAllocAssmtMap.set(batchAllocId, assmtQuestionMap);
      }
      const assmtQuestionMap = batchAllocAssmtMap.get(batchAllocId)!;
      if(!assmtQuestionMap.has(question.batch_alloc_policy_item_slug)) {
        assmtQuestionMap.set(question.batch_alloc_policy_item_slug, {item_id: question.item_id, stu_title_entry_id: question.stu_title_entry_id});
      }
    })

    return testDesignBatchAllocMap;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<any> {
    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const { is_removing, markingWindowIds, testWindowId } = params.query;
    if(is_removing == undefined || markingWindowIds == undefined || testWindowId == undefined){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const uid = await currentUid(this.app, params);

    for (const markingWindowId of markingWindowIds){
      await this.app.service('db/write/marking-windows').patch(markingWindowId, {
        is_scoring_disabled: 1,
        is_active: 0,
        is_hidden_for_scorers: 1,
        is_archived: 1,
      });
    }

    if(is_removing == 1){
      const marking_window_test_window = await dbRawRead(this.app, { markingWindowIds, testWindowId }, `
        SELECT mwtw.id 
        FROM marking_window_test_window mwtw 
        WHERE marking_window_id in (:markingWindowIds)
          AND mwtw.test_window_id = :testWindowId
          AND mwtw.is_removed  = 0
        ;`)
      const mwtwIds = marking_window_test_window.map(mwtw => mwtw.id);
      for (const mwtwId of mwtwIds){
        await this.app.service('db/write/marking-window-test-window').patch(mwtwId, {
          is_removed : 1,
          removed_on: dbDateNow(this.app),
          removed_by_uid: uid
        })
      }
    }
    
    return { success: true };
  }
}
