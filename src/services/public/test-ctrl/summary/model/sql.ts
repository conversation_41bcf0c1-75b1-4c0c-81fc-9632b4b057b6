export const SQL_TEST_CTRL_STUDENT_INVALID_ATTEMPT = (queryParams:any) => `
select s.name s_name
     , s.foreign_id s_code
     , sc.name sc_name
     , scts.slug assessment_code
     , ta.uid student_uid
     , ta.id attempt_id
     , um_sin.value student_gov_id
     , u.first_name student_fname
     , u.last_name student_lname
     , ta.started_on
from school_class_test_sessions scts  
join school_classes sc 
  on sc.id = scts.school_class_id 
join test_sessions ts 
  on ts.id = scts.test_session_id 
  and ts.is_cancelled = 0 
join test_attempts ta  
  on ta.test_session_id = ts.id  
  and ta.is_invalid = 1
left join user_metas um_sin 
  on um_sin.uid = ta.uid 
  and um_sin.key = 'StudentIdentificationNumber' -- todo:WHITELABEL
join test_window_td_alloc_rules twtar 
  on twtar.id = ta.twtdar_id 
  and twtar.test_window_id = :test_window_id
  and twtar.is_custom = 0
join schools s 
  on s.group_id  = sc.schl_group_id 
  and s.is_sandbox = 0
  and s.is_not_reported = 0
join users u on u.id = ta.uid 
join school_districts sd 
  on sd.group_id = s.schl_dist_group_id
where sd.is_sample = 0  
  ${ queryParams.includeAsmtSample ? '' : `and twtar.is_sample = 0`}
  ${ queryParams.focusAsmtSwapRisk ? `and twtar.is_swap_risk = 1` : ''}
group by ta.id
order by ta.started_on desc 
limit 50000
`