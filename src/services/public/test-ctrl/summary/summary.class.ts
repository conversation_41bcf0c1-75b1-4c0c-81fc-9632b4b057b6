import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application, ServiceTypes } from '../../../../declarations';
import { ITestWindow } from '../../../db/schemas/test_windows.schema';
import { Knex } from 'knex';
import { dbDateNow, dbDateOffsetDays } from '../../../../util/db-dates';
import { DBD_U_GROUP_TYPES, DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { Errors } from '../../../../errors/general';
import { MPT_TEST_CTRL_GROUP_ID } from '../../../../constants/test-ctrl-constant';
import { dbRawRead, dbRawReadReporting, dbRawWrite } from '../../../../util/db-raw';
import { SQL_TEST_CTRL_STUDENT_INVALID_ATTEMPT } from './model/sql';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import axios from 'axios';
import { COLUMN_NAMES } from './model/config'
//import { MPT_TEST_CTRL_GROUP_ID } from '../../test-admin/test-session-setup/test-windows/test-windows.class';

interface Data {}

const QUICK_TZ_SHIFT = -4; // change away from constant

interface ServiceOptions {}

type IRecord = any;
type IRecords = IRecord[];
interface ISimpleRetrLog {
  slug?:string,
  timestamp?:number,
  diff?:number,
  started_on?:string,
  completed_on?:string,
}

interface IAttemptsCache {
  started_on: string;
  completed_on: string;
  records: any[] | string;
  retrievalLog?: {started_on:string}[];
  isFileMissing?: boolean
}

interface IDataFrame {
  records: IRecords | string,
  title: string,
  slug: string,
  retrievalLog?: ISimpleRetrLog[],
  note?: string,
  gridOptions: {
    columnDefs: IColumnDef[];
    defaultColDef: IDefaultColDef;
  }
}
interface IColumnDef {
  headerName: string;
  field: string;
  width?: number;
  checkboxSelection?: boolean;
}
interface IDefaultColDef {
  filter: boolean;
  sortable: boolean;
  resizable: boolean;
}
const renderColDefs = (fieldNames:string[], overrides:{[fieldName:string]:any}) : IColumnDef[] => {
  overrides = overrides || {}
  return fieldNames.map(field => {
    return {
      field,
      headerName: COLUMN_NAMES[field] ?? field,
      width:120,
      ... (overrides[field] || {})
    }
  })
}
const defaultColDef = () => {
  return {
    filter: true,
    sortable: true,
    resizable: true,
  }
}

const valMin = (a:number | string , b:number | string) => {
  if (a < b){
    return a;
  }
  return b
}
const valMax = (a:number | string , b:number | string) => {
  if (a > b){
    return a;
  }
  return b
}

interface ITestWindowSessionsCreated {
  test_window_id: number,
  num_sessions_created: number,
}

interface ITallyRecord {
  tally: number,
}

interface ISummary {
  test_windows: ITestWindowSummary[],
  accounts: {
    num_test_applicants: IAccountTally,
    num_session_bookings: IAccountTally,
    num_session_waitlisters: IAccountTally,
    num_accomm_pending: IAccountTally,
    num_institutions: IAccountTally,
    num_inst_mngrs: IAccountTally,
    num_accomm_coords: IAccountTally,
    num_invigs: IAccountTally,
    num_cert_bodys: IAccountTally,
    num_test_ctrls: IAccountTally,
  }
}
interface IAccountTally {
  r: number,
  l30: number,
  p?: number,
}
interface ITestWindowSummary {
  test_window_id: number,
  test_window_name: IInlineTranslation,
  test_window_date_start: string,
  test_window_date_end: string,
  num_sessions_created: number,
  num_sessions_administered: number,
  num_results_pending_release: number,
  is_allow_new_bookings: boolean,
}

interface IInlineTranslation {
  [index:string] : string,
}

export class Summary implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const MAX_WINDOWS_TO_LIST:number = 30;

    const testWindows:ITestWindowSummary[] = [];
    const testWindowRef:Map<number, ITestWindowSummary> = new Map();
    const tw_records = <ITestWindow[]> await this.app
      .service('db/read/test-windows')
      .db()
      .where('is_archived', 0)
      .where('test_ctrl_group_id', MPT_TEST_CTRL_GROUP_ID)
      .limit(MAX_WINDOWS_TO_LIST);
    tw_records.forEach(twr => {
      const test_window_id = <number> twr.id
      const testWindowSummary:ITestWindowSummary = {
        test_window_id,
        test_window_name: twr.title ? JSON.parse(twr.title) : null,
        test_window_date_start: <string> twr.date_start,
        test_window_date_end: <string> twr.date_end,
        is_allow_new_bookings: twr.is_allow_new_bookings ? true: false,
        num_sessions_created: 0,
        num_sessions_administered: 0,
        num_results_pending_release: 0,
      }
      testWindowRef.set(test_window_id, testWindowSummary);
      testWindows.push(testWindowSummary);
    })

    const tw_sessions_created = <ITestWindowSessionsCreated[]> await this.app
      .service('db/read/test-window-sessions-created')
      .db()
      .where('test_window_test_ctrl_group_id', MPT_TEST_CTRL_GROUP_ID)
      .limit(MAX_WINDOWS_TO_LIST);
    tw_sessions_created.forEach(tw => {
      if (testWindowRef.has(tw.test_window_id)){
        const testWindowSummary = <ITestWindowSummary> testWindowRef.get(tw.test_window_id);
        testWindowSummary.num_sessions_created = tw.num_sessions_created;
      }
    })

    const getTally = async (query:Knex.QueryBuilder) : Promise<number> => {
      const tallyRecords = <ITallyRecord[]> await query.limit(1);
      if (tallyRecords && tallyRecords.length){
        return tallyRecords[0].tally;
      }
      return 0;
    }

    const getTableTally = async (query:Knex.QueryBuilder, isLast30:boolean=false) : Promise<IAccountTally> => {
      query = query.count('id as tally');
      const query_r = query.clone();
      const query_l30 = query.clone().where('created_on', '>', dbDateOffsetDays(this.app, -30));
      return {
        r: await getTally(query_r),
        l30: await getTally(query_l30),
      }
    }
    const getRoleTallySummary = async (groupType:DBD_U_GROUP_TYPES, role_type:DBD_U_ROLE_TYPES) : Promise<IAccountTally> => {
      return {
        r: await getRoleTally(groupType, role_type, true, false),
        l30: await getRoleTally(groupType, role_type, true, true),
        p: await getRoleTally(groupType, role_type, false, false),
      }
    }
    const getRoleTally = (groupType:DBD_U_GROUP_TYPES, role_type:DBD_U_ROLE_TYPES, isClaimed:boolean=true, isLast30:boolean=false) => {
      let query = this.app
        .service('db/read/user-role-active-w-group-type')
        .db()
        .where('group_type', groupType)
        .where('role_type', role_type)
        .where('is_revoked', 0)
        .where('is_claimed', isClaimed ? 1 : 0);
      if (isLast30){
        query = query.where('created_on', '>', dbDateOffsetDays(this.app, -30));
      }
      query = query.count('id as tally');
      return getTally( query );
    }

    const g_inst = DBD_U_GROUP_TYPES.mpt_institution;
    const g_sys = DBD_U_GROUP_TYPES.mpt_sys;
    const g_tc = DBD_U_GROUP_TYPES.mpt_test_controller;
    const g_ts = DBD_U_GROUP_TYPES.mpt_test_session;

    const num_institutions = getTableTally(
      this.app
        .service('db/read/institutions')
        .db()
        .where('is_active', 1)
        .where('is_shown', 1)
    );
    const num_inst_mngrs          = getRoleTallySummary(g_inst, DBD_U_ROLE_TYPES.mpt_test_admin_inst_mngr);
    const num_accomm_coords       = getRoleTallySummary(g_inst, DBD_U_ROLE_TYPES.mpt_test_admin_accomm_coord);
    const num_invigs              = getRoleTallySummary(g_inst, DBD_U_ROLE_TYPES.mpt_test_admin_invig);
    const num_test_applicants     = getRoleTallySummary(g_sys, DBD_U_ROLE_TYPES.mpt_applicant);
    const num_session_bookings    = getRoleTallySummary(g_ts, DBD_U_ROLE_TYPES.mpt_booked_applicant);
    const num_session_waitlisters = getRoleTallySummary(g_ts, DBD_U_ROLE_TYPES.mpt_waiting_list_applicant);
    const num_accomm_applicants   = getRoleTallySummary(g_inst, DBD_U_ROLE_TYPES.mpt_accomm_applicant);
    const num_test_ctrls          = getRoleTallySummary(g_tc, DBD_U_ROLE_TYPES.test_ctrl_lias_test_admin);

    const res:ISummary = {
      test_windows: testWindows,
      accounts: {
        num_test_applicants: await num_test_applicants,
        num_session_bookings: await num_session_bookings,
        num_session_waitlisters: await num_session_waitlisters,
        num_accomm_pending:  await num_accomm_applicants,
        num_institutions: await num_institutions,
        num_inst_mngrs: await num_inst_mngrs,
        num_accomm_coords: await num_accomm_coords,
        num_invigs: await num_invigs,
        num_cert_bodys: { r: 0, l30: 0, p: 0, },
        num_test_ctrls: await num_test_ctrls,
      }
    }
    return [res];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    const test_window_id = +id;
    const queryParams = params?.query || {};
    const dagJobName = await this.app.service('public/data-exporter/data-export').confirmTwDefaultJob(test_window_id, 'tc-attempts');
    switch(dagJobName){
      case 'tc-attempts-school':
        return this.getSummaryTcSchool(test_window_id);
      case 'tc-attempts-testcentre':
      default:
        return this.getSummaryTcTestCentre(test_window_id);
    }
  }

  async getSummaryTcTestCentre(test_window_id:number){
    const dfs:IDataFrame[] = [
      await this.dfTestCentreAttempts(test_window_id),
      // Removing item responses from API response since it is not currently present in the export job
      // await this.dfTestCentreItemResponses(test_window_id),
      await this.dfItemScaleStats(test_window_id),
      await this.dfItemResponseValueStats(test_window_id),
      await this.dfTestCentreIndividualResults(test_window_id),
      await this.dfTestCentreResultsDistribution(test_window_id),
    ]
    return dfs
  }



  /** Start */
  async getSummaryTcSchool(test_window_id:number){

    const dfs:IDataFrame[] = [
      // Pulling this data first as it is a raw query
      await this.dfRecentStudentActivityRaw(test_window_id, 10),
      await this.dfRecentStudentActivityRaw(test_window_id, 60*8),

      await this.dfSchoolAdmins(test_window_id),
      await this.dfTeachers(test_window_id),
      await this.dfScheduledSessions(test_window_id),
      await this.dfSchoolFormDist(test_window_id),
      await this.dfStudentAttempts(test_window_id),
      await this.dfStudentAttemptBySchool(test_window_id),
      await this.dfStudentAttemptBySchoolAuthority(test_window_id),
      await this.dfStudentAttemptBySubject(test_window_id),
      await this.dfStudentAttemptByDateSubj(test_window_id),
      await this.dfStudentAttemptByDate(test_window_id)
      // await this.dfLocalMarkingProgress(test_window_id, queryParams),
      // await this.dfSupercededForms(test_window_id, queryParams),
      // await this.dfInvalidatedStudentAttempts(test_window_id, queryParams),
      // await this.dfItems(test_window_id),
      // await this.dfTwStatementDetails(test_window_id),
      // await this.dfTwStatementNote(test_window_id),
    ];
    return dfs;
  }

  private rowAggDate(srow:IRecord, row:IRecord){
    srow.min_started_on    = valMin(srow.min_started_on, row.min_started_on);
    srow.max_started_on    = valMax(srow.max_started_on, row.max_started_on);
    srow.min_last_touch_on = valMin(srow.min_last_touch_on, row.min_last_touch_on);
    srow.max_last_touch_on = valMax(srow.max_last_touch_on, row.max_last_touch_on);
  }

  private async dfStudentAttemptBySchoolClass(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'trfm_attempts_by_sc')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Attempts (by class)',
      slug: 'trfm_attempts_by_sc',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: this.genBasicGridOptions([
          'assessment_code',
          's_code',
          'sc_name',
          ////////
          'sd_code',
          'sd_name',
          's_name',
          'n_students',
          'min_started_on',
          'max_started_on',
          'min_last_touch_on',
          'max_last_touch_on',
      ])
    }
  }

  private async dfStudentAttemptBySchool(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'trfm_attempts_by_s')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Attempts (by School)',
      slug: 'trfm_attempts_by_s',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          'assessment_code',
          's_code',
          ////////
          'sd_code',
          'sd_name',
          's_name',
          'n_classes',
          'n_students',
          'min_started_on',
          'max_started_on',
          'min_last_touch_on',
          'max_last_touch_on',
        ], {
        }),
        defaultColDef: defaultColDef()
      }
    }
  }

  private async dfStudentAttemptBySchoolAuthority(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'trfm_attempts_by_sd')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Attempts (by School Authority)',
      slug: 'trfm_attempts_by_sd',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          'assessment_code',
          'sd_code',
          ////////
          'sd_name',
          'n_schools',
          'n_classes',
          'n_students',
          'min_started_on',
          'max_started_on',
          'min_last_touch_on',
          'max_last_touch_on',
        ], {
        }),
        defaultColDef: defaultColDef()
      }
    }
  }

  private async dfStudentAttemptBySubject(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'trfm_attempts_by_subject')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Attempts (by Subject)',
      slug: 'trfm_attempts_by_subject',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          'assessment_code',
          ////////
          'n_school_districts',
          'n_schools',
          'n_classes',
          'n_students',
          'min_started_on',
          'max_started_on',
          'min_last_touch_on',
          'max_last_touch_on',
        ], {
        }),
        defaultColDef: defaultColDef()
      }
    }
  }

  private async dfStudentAttemptByDateSubj(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'trfm_attempts_by_date_subj')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Attempts (by Date and Subject)',
      slug: 'trfm_attempts_by_date_subj',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          'assessment_code',
          'started_on_date',
          'n_students',
        ], {
        }),
        defaultColDef: defaultColDef()
      }
    }
  }

  private async dfScansByStudent(test_window_id:number): Promise<IDataFrame> {
    const data = await this.loadTaggedCache(test_window_id, 'tc-scan-detail', 'scan_detail_by_student')
    const {records, started_on, completed_on} = data;

    return {
      title: 'Scans (by Students)',
      slug: 'scan_detail_by_student',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: this.genBasicGridOptions([
        "student_gov_id",
        "student_fname",
        "student_lname",
        "teacher_name",
        "sc_name",
        "s_name",
        "assessment_code",
        "is_started",
        "is_submitted",
        "closed_on_date",
        "num_scans_required",
        "num_scans_uploaded",
        "num_scans_missing",
        "first_scan_uploaded_on",
        "last_scan_uploaded_on"
        ]
      )
    }
  }

  private async dfScansBySchool(test_window_id:number): Promise<IDataFrame> {
    const data = await this.loadTaggedCache(test_window_id, 'tc-scan-detail', 'scan_detail_by_school')
    const {records, started_on, completed_on} = data;
    return {
      title: 'Scans (by School)',
      slug: 'scan_detail_by_school',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: this.genBasicGridOptions([
        'assessment_code',
        's_code',
        's_name',
        'sd_code',
        'sd_name',
        'num_sessions',
        'num_sessions_any_uploaded',
        'num_students',
        'num_students_any_uploaded',
        "num_scans_uploaded",
        "first_scan_uploaded_on",
        "last_scan_uploaded_on"
      ])
    }
  }

  private async dfScansByAssessment(test_window_id:number): Promise<IDataFrame> {
    const data = await this.loadTaggedCache(test_window_id, 'tc-scan-detail', 'scan_detail_by_assessment')
    const {records, started_on, completed_on} = data;
    return {
      title: 'Scans (by Assessment)',
      slug: 'scan_detail_by_assessment',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: this.genBasicGridOptions([
        'assessment_code',
        'num_districts',
        'num_schools',
        'num_classes',
        'num_students',
        "num_scans_uploaded",
        "first_scan_uploaded_on",
        "last_scan_uploaded_on"
      ])
    }
  }


  private async dfScansBySession(test_window_id:number): Promise<IDataFrame> {
    const data = await this.loadTaggedCache(test_window_id, 'tc-scan-detail', 'scan_detail_by_session')
    const {records, started_on, completed_on} = data;
    return {
      title: 'Scans (by Session)',
      slug: 'scan_detail_by_session',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: this.genBasicGridOptions([
        'ts_id',
        'ts_name',
        's_code',
        's_name',
        'sc_name',
        'assessment_code',
        'ts_start',
        'ts_is_closed',
        'num_students',
        'num_students_started',
        'num_students_submitted',
        'num_students_uploaded',
        "num_scans_required",
        "num_scans_uploaded",
        "num_scans_missing",
        "teacher_uid",
        "teacher_email"
      ])
    }
  }

  private async dfScanCountByDate(test_window_id:number): Promise<IDataFrame> {
    const data = await this.loadTaggedCache(test_window_id, 'tc-scan-detail', 'scan_count_by_date')
    const {records, started_on, completed_on} = data;

    return {
      title: 'Scans (by Date)',
      slug: 'scan_count_by_date',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: this.genBasicGridOptions(['upload_date', 'n_scans'])
    }
  }


  private async dfStudentAttemptByDate(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'trfm_attempts_by_date')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Attempts (by Date)',
      slug: 'trfm_attempts_by_date',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          'started_on_date',
          'n_students',
        ], {
        }),
        defaultColDef: defaultColDef()
      }
    }
  }




  private async dfTwStatementDetails(test_window_id:number): Promise<IDataFrame> {
    return {
      title: 'Principal Statement Details',
      slug: 'tw_statement_details',
      records: await this.app.service('public/school-admin/tw-statement').getTwStatementAllDetails(test_window_id),
      gridOptions: {
        columnDefs: renderColDefs([
          'uid',
          'first_name',
          'last_name',
          'lang',
          's_code',
          'sd_code',
          'dob',
          'student_gov_id',
          'ABED_ELA_30_2_A', // todo: make dynamic
          'ABED_ELA_30_1_A',
          'ABED_SS_30_1_A',
          'ABED_SS_30_2_A',
          'ABED_ES_30_1_A',
          'ABED_ES_30_2_A',
        ], {

        }),
        defaultColDef: defaultColDef()
      }
    }
  }

  private async dfTwStatementNote(test_window_id:number): Promise<IDataFrame> {
    return {
      title: 'Principal Statement Summary',
      slug: 'tw_statement_summary',
      records: await this.app.service('public/school-admin/tw-statement').loadTwStatementSummary(test_window_id),
      gridOptions: {
        columnDefs: renderColDefs([
          's_id',
          's_name',
          's_code',
          'sd_code',
          'suspected_contravention',
          'securely_administered',
          'notes',
        ], {

        }),
        defaultColDef: defaultColDef()
      }
    }
  }

  private async dfItems(test_window_id:number): Promise<IDataFrame> {
    return {
      title: 'Items',
      slug: 'items',
      records: await dbRawReadReporting(this.app, {test_window_id}, `
        select lang
              , item_id
              , item_label
              , question_set_id
              , qs_name
              , count(distinct taqr_id) n
              -- turned off stats until we have proper allocation of questionnaire items
          --      , min(taqr_score) max_score
          --      , avg(taqr_score) avg_score
          --      , max(taqr_score) min_score
          --      , max(taqr_weight) weight
          from (
            select s.id s_id
                  , tqr.lang
                  , twtar.type_slug
                  , tqr.question_id item_id
                  , tq.question_label item_label
                  , tq.question_set_id
                  , tqs.name qs_name
                  , taqr.id taqr_id
                  , taqr.score taqr_score
                  , taqr.weight taqr_weight
            from test_window_td_alloc_rules twtar
            join test_question_register tqr
              on tqr.test_design_id = twtar.test_design_id
            join test_questions tq
              on tq.id = tqr.question_id
            join temp_question_set tqs
              on tqs.id = tq.question_set_id
            left join test_attempts ta
              on twtar.id = ta.twtdar_id
            left join school_class_test_sessions scts
              on ta.test_session_id = scts.test_session_id
            left join school_classes sc
              on sc.id = scts.school_class_id
            left join test_attempt_question_responses taqr
              on taqr.test_attempt_id = ta.id
              and taqr.test_question_id = tqr.question_id
            left join schools s
              on s.group_id  = sc.schl_group_id
              and s.is_sandbox = 0
              and s.is_not_reported = 0
            where twtar.test_window_id = :test_window_id
              and twtar.is_custom = 0
            group by tqr.id, taqr.id
          ) t
          where (s_id is not null or taqr_id is null)
          group by lang, item_id
          order by type_slug, question_set_id
      `),
      gridOptions: {
        columnDefs: renderColDefs([
          'lang',
          'question_set_id',
          'item_id',
          'item_label',
          'n',
          'qs_name',
        ], {

        }),
        defaultColDef: defaultColDef()
      }
    }
  }

  private async dfSchoolAdmins(test_window_id:number, queryParams?:any): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'load_school_admins')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'School Administrators',
      slug: 'load_school_admins',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          's_name',
          's_code',
          'first_name',
          'last_name',
          'contact_email',
          'is_claimed',
          'created_on',
        ], {
        }),
        defaultColDef: defaultColDef()
      }
    }
  }

  private async dfTeachers(test_window_id:number, queryParams?:any): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'load_school_teachers')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Teachers',
      slug: 'load_school_teachers',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          's_name',
          's_code',
          'first_name',
          'last_name',
          'contact_email',
          'is_claimed',
          'created_on',
        ], {
        }),
        defaultColDef: defaultColDef()
      }
    }
  }

  private async dfTestCentreAttempts(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'load_ta_from_tw__testcentres')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Test Taker Attempts',
      slug: 'test_taker_attempts',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          'admin_window_id',
          'admin_window_name',
          'TestingJID',
          'jurisdiction_id',
          'jurisdiction_name',
          'test_center_id',
          'test_center_name',
          'test_session_id',
          'SrlNbr',
          'CAECUID',
          'first_name',
          'lang',
          'asmt_code',
          'form_code',
          'is_print',
          'is_attempt_invalidated',
          'is_session_cancelled',
          'is_session_closed',
          'started_on',
          'results_computed_on',
          'results_released_on',
        ], {

        }),
        defaultColDef: defaultColDef()
      }
    }
  }

  private async dfTestCentreItemResponses(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'trfm_item_responses')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Item Responses',
      slug: 'trfm_item_responses',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          'response_id',
          'ta_id',
          'item_id',
          'response_type',
          'formatted_response',
          'score',
          'is_nr',
          'weight',
        ], {

        }),
        defaultColDef: defaultColDef()
      }
    }
  }



  private async dfSchoolFormDist(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'trfm_attempts_by_form_code')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Form Distribution',
      slug: 'trfm_attempts_by_form_code',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          'assessment_code',
          'form_code',
          'n_students',
          'started_on_date',
        ], {
        }),
        defaultColDef: defaultColDef()
      }
    }
  }





  private async dfItemScaleStats(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'trfm_item_scale_stats')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Item Stats',
      slug: 'trfm_item_scale_stats',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          'type_slug',
          'form_code',
          'td_id',
          'item_id',
          'p', // mean score
          'n', // todo
          'n_nr', // todo
        ], {

        }),
        defaultColDef: defaultColDef()
      }
    }
  }
  private async dfItemResponseValueStats(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'trfm_item_response_value_stats')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Item Response Distribution',
      slug: 'trfm_item_response_value_stats',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          'type_slug',
          'form_code',
          'item_id',
          'formatted_response',
          'score',
          'n',
        ], {

        }),
        defaultColDef: defaultColDef()
      }
    }
  }
  private async dfTestCentreResultsDistribution(test_window_id:number): Promise<IDataFrame> {
    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'trfm_result_distribution')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Results Distribution',
      slug: 'trfm_result_distribution',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          'type_slug', //todo
          'form_code',
          'result_code',
          'count',
        ], {

        }),
        defaultColDef: defaultColDef()
      }
    }
  }
  private async dfTestCentreIndividualResults(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'load_test_reports')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Individual Results',
      slug: 'test_taker_results',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: {
        columnDefs: renderColDefs([
          'uid',
          'test_attempt_id',
          'is_successful',
          'is_no_show',
          'institution_name',
          'num_questions_answered',
          'num_questions_total',
          'created_on',
          'test_session_date_time_start',
          'is_invalid',
          'slug',
          'result_code',
          'results_released_on',
          'raw_score',
        ], {

        }),
        defaultColDef: defaultColDef()
      }
    }
  }


  private async dfScheduledSessions(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'load_ts_detail')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Test Sessions',
      slug: 'test_sessions',
      records,
      gridOptions: {
        columnDefs: renderColDefs([
          'ts_id',
          's_name',
          's_code',
          'sc_name',
          'assessment_code',
          'date_time_start',
          'is_closed',
          'num_students',
          'num_students_started',
          'num_students_submitted',
          'teacher_uid',
          'teacher_email',
        ], {

        }),
        defaultColDef: defaultColDef()
      }
    }
  }

  private dfSummarize(config:{df:IDataFrame, slug:string, title:string, columns:string[], tKey:string[], tRowInit:Partial<IRecord>, tAgg: (srow:IRecord, row:IRecord) => void,  }){
    const {slug, title} = config
    let gridOptions = this.genBasicGridOptions(config.columns)
    // data.gridOptions;
    const resultMap = new Map<string, IRecord>();
    (config.df.records as IRecords).forEach((row: IRecord) => {
      const key = config.tKey.map(prop => row[prop]).join('-');
      if (!resultMap.has(key)) {
        resultMap.set(key, {
          ... row,
          ... config.tRowInit,
        });
      }
      const srow = resultMap.get(key);
      config.tAgg(srow, row)
    })
    const records = Array.from(resultMap.values());
    // config.transformRecords(config.df.records);
    const df = {
      ... config.df,
      slug,
      title,
      records,
      gridOptions,
    }
    return df
  }

  private async dfRecentStudentActivity(test_window_id:number, minutes:number, attemptsCache:IAttemptsCache): Promise<IDataFrame> {

    let records:any[] = [];
    if (attemptsCache.records && attemptsCache.started_on){
      const {started_on} = attemptsCache;
      const referenceTimestamp = new Date(started_on).valueOf()
      const duration = minutes * 60 * 1000;
      if (attemptsCache.records){
        const aggByTypeSlug:{[type_slug:string] : number} = {}
        for (let record of attemptsCache.records){
          const timestamp = new Date(record.last_touch_on).valueOf()
          const isWithinTargetTimeRange = (timestamp + duration >= referenceTimestamp)
          if (isWithinTargetTimeRange){ // within tar
            const {assessment_code} = record
            aggByTypeSlug[assessment_code] = (aggByTypeSlug[assessment_code] || 0) + 1
          }
        }
        Object.keys(aggByTypeSlug).forEach(asmt_code => {
          records.push({
            asmt_code,
            n: aggByTypeSlug[asmt_code],
            measured_at: started_on,
            measured_back_by: minutes
          })
        })
      }
    }

    return {
      title: `Student Activity (last ${minutes} min.)`,
      slug: `stu_activity_${minutes}`,
      records,
      gridOptions: this.genBasicGridOptions([
        'asmt_code',
        'n',
        'measured_at',
        'measured_back_by'
      ])
    }
  }

  private async dfRecentStudentActivityRaw(test_window_id:number,minutes:number, queryParams?:any): Promise<IDataFrame> {
    return {
      title: `Student Activity (last ${minutes} min.)`,
      slug: `stu_activity_${minutes}`,
      records: await dbRawReadReporting(this.app, {test_window_id}, `
        select twtar.type_slug asmt_code
             , count( distinct taqr.test_attempt_id) n
             , now() measured_at
             , ${minutes} measured_back_by
             , twtar.is_secured
             , twtar.is_field_test
        from test_attempt_question_responses taqr
        join test_attempts ta
          on ta.id = taqr.test_attempt_id
        join test_window_td_alloc_rules twtar
          on twtar.id = ta.twtdar_id
          and twtar.is_custom = 0
        join school_class_test_sessions scts
          on scts.test_session_id = ta.test_session_id
        join school_classes sc
          on sc.id = scts.school_class_id
        join schools s
          on s.group_id  = sc.schl_group_id
        join school_districts sd
          on sd.group_id = s.schl_dist_group_id
          and sd.is_sample  = 0
        where taqr.updated_on > date_add(now(), interval -${minutes} minute)
          and twtar.test_window_id = :test_window_id
        group by twtar.type_slug
      `),
      gridOptions: this.genBasicGridOptions([
        'asmt_code',
        'n',
        'measured_at',
        'measured_back_by'
      ])
    }
  }

  private async dfLocalMarkingProgress(test_window_id:number, queryParams:any): Promise<IDataFrame> {
    return {
      title: 'Local Marking',
      slug: 'local_marking',
      records: await dbRawReadReporting(this.app, {test_window_id}, `
        select twtar.type_slug asmt_code
            , s.foreign_id school_code
            , count(ta.uid) n_students_written
            , count(tls.id) n_students_marked
        from test_window_td_alloc_rules twtar
        join test_window_td_types twtt
          on twtt.type_slug = twtar.type_slug
          and twtt.test_window_id is null
          and twtt.is_revoked = 0
        join test_attempts ta on ta.twtdar_id = twtar.id and ta.started_on
        join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
        join school_classes sc on sc.id = scts.school_class_id
        join schools s on s.group_id = sc.schl_group_id
        join school_districts sd on sd.group_id = s.schl_dist_group_id and sd.is_sample = 0
        join test_sessions ts on ts.id = scts.test_session_id
        left join temp_local_scoring tls on tls.attempt_id = ta.id
        where twtar.test_window_id  = :test_window_id
          and twtar.is_secured = 1
          and twtt.is_local_score = 1
          and twtar.is_custom = 0
        group by ta.test_session_id
        order by n_students_marked desc
      `),
      gridOptions: this.genBasicGridOptions([
        'asmt_code',
        'school_code',
        'n_students_written',
        'n_students_marked',
      ])
    }
  }

  private async dfSupercededForms(test_window_id:number, queryParams:any): Promise<IDataFrame> {
    return {
      title: 'Superceded Forms',
      slug: 'superceded_forms',
      records: await dbRawReadReporting(this.app, {test_window_id}, `
        select type_slug asmt_code
              , count(0) n
              , td_created_on
              , max(ta_created_on) max_ta_created_on
              , (twtar_test_date_start < td_created_on) admin_sched_before_td_update
        from  (
          select ta.id as ta_id
              , ta.uid
              , ta.test_session_id
              , ta.created_on  ta_created_on
              , td.created_on td_created_on
              , twtar.type_slug
              , twtar.test_date_start twtar_test_date_start
          from test_window_td_alloc_rules twtar
          join test_attempts ta
            on twtar.id = ta.twtdar_id
            and ta.started_on is null
            and ta.is_invalid = 0 -- to avoid double processing
            and ta.uid > 0 -- should not be required
          join test_forms tf
            on tf.id = ta.test_form_id
          join test_designs td
            on td.id = twtar.test_design_id
          where twtar.test_window_id = :test_window_id
            and twtar.is_secured = 1
            and twtar.is_custom = 0
            and tf.test_design_id < twtar.test_design_id
        ) t
        group by type_slug
      `),
      gridOptions: this.genBasicGridOptions([
          'asmt_code',
          'n ',
          'td_created_on',
          'max_ta_created_on',
          'admin_sched_before_td_update',
      ])
    }
  }

  private async getTwTaLite(test_window_id:number, isAsmtSample:boolean, isFocusAsmtSwapRisk:boolean, pre_ta_ids:number[]){
    type R = {attempt_id:number, is_closed:number}[]
    let taRecords:R = await dbRawReadReporting(this.app, {test_window_id}, `
      select /*+ MAX_EXECUTION_TIME(300000) */
            ta.id attempt_id
          , ta.is_closed
      from school_class_test_sessions scts
      join school_classes sc
        on sc.id = scts.school_class_id
      join test_sessions ts
        on ts.id = scts.test_session_id
        and ts.is_cancelled = 0
      join test_attempts ta
        on ta.test_session_id = ts.id
        and ta.is_invalid = 0
      join test_window_td_alloc_rules twtar
        on twtar.id = ta.twtdar_id
        and twtar.is_custom = 0
      join test_attempt_question_responses taqr
        on taqr.test_attempt_id = ta.id
      join schools s
        on s.group_id  = sc.schl_group_id
        and s.is_sandbox = 0
        and s.is_not_reported = 0
      join school_districts sd
        on sd.group_id = s.schl_dist_group_id
      where sd.is_sample = 0
        and twtar.test_window_id in (:test_window_id)
        ${ !isAsmtSample ? `and twtar.is_sample = 0` : ''}
        ${ isFocusAsmtSwapRisk ? `and twtar.is_swap_risk = 1` : ''}
      group by ta.id
      limit 500000
    `)
    // taRecords = taRecords.filter(r => !pre_ta_ids.includes(+r.attempt_id))
    // const ta_open = taRecords.filter(r => !(r.is_closed==1));
    // const ta_closed = taRecords.filter(r => r.is_closed==1);
    return taRecords
  }

  private async loadTaggedCache(test_window_id:number, tagSlug:any, fileRef:string){
    const cacheRecords = await dbRawReadReporting(this.app, {test_window_id, tagSlug}, `
      select dej.urls
           , dej.started_on
           , dej.completed_on
      from data_export_job_tags dejt
      join data_export_jobs dej
        on dej.id = dejt.export_id
        and dej.status = 'COMPLETE'
      where dejt.is_revoked = 0
        and dejt.test_window_id = :test_window_id
        and dejt.slug = :tagSlug
      limit 10 -- really 1, but want to see if there is extra
    `)
    if (cacheRecords.length == 0){
      return {
        started_on: '',
        completed_on: '',
        records:[]
      }
    }
    const record = cacheRecords[0]
    const urls = JSON.parse(record.urls)
    const filepath = urls[fileRef]
    try {
      let fileUrl;
      if (filepath) {
      // Signed link expiration time is now set to 5 mins
        fileUrl = generateS3DownloadUrl(filepath, 60*5); 
      } else { 
        // If asset file does not exist, filepath will be empty. In that case, we return []
        fileUrl = [];
      }
      return {
        started_on: record.started_on,
        completed_on: record.completed_on,
        records: fileUrl,
      }
    }
    catch(e){
        return {
          started_on: '',
          completed_on: '',
          records:[],
          isFileMissing: true,
        }
    }
  }

  private async dfStudentResponses(test_window_id:number): Promise<IDataFrame> {

    const attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'load_taqr')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Student Responses',
      slug: 'stu_item_resp',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: this.genBasicGridOptions([
        'ta_id',
        'item_id',
        'score',
        'weight',
      ])
    }
  }

  private async dfStudentAttempts(test_window_id:number): Promise<IDataFrame> {

    const  attemptsCache = await this.loadTaggedCache(test_window_id, 'tc-attempts', 'load_ta_detail')
    const {records, started_on, completed_on} = attemptsCache;

    return {
      title: 'Student Attempts',
      slug: 'attempts',
      records,
      retrievalLog: [{started_on, completed_on}],
      gridOptions: this.genBasicGridOptions([
          'attempt_id',
          's_name',
          's_code',
          'sc_name',
          'assessment_code',
          'form_code',
          'student_gov_id',
          'student_uid',
          'student_fname',
          'student_lname',
          'time_spent_min',
          'num_screens_accessed',
          'machine_score',
          'machine_weight',
          'last_touch_on',
          'started_on',
          'started_on_date',
      ])
    }
  }

  private genBasicGridOptions(columnList:string[]){
    return {
      columnDefs: renderColDefs(columnList, { }),
      defaultColDef: defaultColDef()
    }
  }

  private async dfInvalidatedStudentAttempts(test_window_id:number, queryParams:any): Promise<IDataFrame> {
    return {
      title: 'Invalidated Student Attempts',
      slug: 'invalid_attempts',
      records: await dbRawReadReporting(this.app, {test_window_id}, SQL_TEST_CTRL_STUDENT_INVALID_ATTEMPT(queryParams)),
      gridOptions: {
        columnDefs: renderColDefs([
          'attempt_id',
          's_name',
          's_code',
          'sc_name',
          'assessment_code',
          'student_gov_id',
          'student_uid',
          'student_fname',
          'student_lname',
          'last_touch_on',
          'started_on',
        ], {

        }),
        defaultColDef: defaultColDef()
      }
    }
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    const update = await dbRawWrite(this.app, {test_window_id: id}, `
        UPDATE tw_ta_blob_cache
        SET is_revoked = 1
          and revoked_on = now()
        where tw_id = :test_window_id
          and is_revoked = 0
    `)
    return {id, update}
  }
}
