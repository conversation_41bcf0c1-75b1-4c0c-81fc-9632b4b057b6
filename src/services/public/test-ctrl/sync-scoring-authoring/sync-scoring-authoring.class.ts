import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawWrite, dbRawWriteMulti } from '../../../../util/db-raw';

interface Data {
  [key: string]: any,
}

interface ServiceOptions {}

export class SyncScoringAuthoring implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const test_window_id = (<any>params).query.test_window_id;
    const activeMarkingWindows = await dbRawRead(this.app, [test_window_id], `
      select * 
      from marking_windows mw 
      join marking_window_test_window mwtw 
        on mw.id = mwtw.marking_window_id 
      where mwtw.test_window_id = ?
        -- and mw.is_active = 1
    ;`);
    return activeMarkingWindows;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if (params && params.query && params.query.marking_window_id) {
      const { updatedFrom, updatedTo } = await this.getScoreProfilesPreview(params.query.marking_window_id);
      return {
        updatedFrom,
        updatedTo
      }
    }
    return {};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }
    if (data && data.marking_window_id) {
      const { updatedTo } = await this.getScoreProfilesPreview(data.marking_window_id);

      let updates:Promise<any>[] = [];

      for (let obj of updatedTo) {
        const { score_profile_id, marking_window_id, item_id, skill_code } = obj;
        updates.push(
          dbRawWrite(this.app, [score_profile_id, marking_window_id, item_id], `
            UPDATE marking_window_items 
            SET score_profile_id = ?
            WHERE marking_window_id = ? 
              AND item_id = ?
              ${skill_code ? `AND skill_code = ${skill_code}` : ''}
          ;`)
        )  
      }

      await Promise.all(updates);

      return data;
    }
    throw new Errors.Forbidden('MARKING_WINDOW_ID_NOT_PROVIDED');
  }

  async getScoreProfilesPreview(marking_window_id: number) {
    const items = await dbRawRead(this.app, [marking_window_id], `
      select distinct marking_window_id, item_id, score_profile_id, skill_code, caption
      from marking_window_items mwi 
      where marking_window_id = ?
    ;`);
    const itemIds = items.map(function(item) {
      return item['item_id'];
    });
    const scoreProfileIdsFromAuthoring = await dbRawRead(this.app, [itemIds], `
      select distinct tqsr.test_question_id item_id, tqsr.score_profile_id
      from marking_windows mw
      join marking_window_test_window mwtw 
        on mw.id = mwtw.marking_window_id 
      join test_windows tw 
        on tw.id = mwtw.test_window_id 
      join test_window_td_alloc_rules twtar 
        on twtar.test_window_id = tw.id
        and twtar.is_custom = 0
      join test_question_scales_register tqsr
        on twtar.test_design_id = tqsr.test_design_id
        and tqsr.is_human_scored = 1
      join test_questions tq 
        on tq.id = tqsr.test_question_id
      left join marking_window_items mwi 
        on mwi.item_id = tq.id
        and mwi.score_profile_id = tqsr.score_profile_id
      where tqsr.test_question_id in (?)
    ;`);
    const availableScoreProfileIds = await dbRawRead(this.app, [], `
      select *
      from marking_score_profiles msp 
      where is_active = 1;
    ;`);

    let updatedFrom = items;
    let updatedTo = [];
    for (let obj of scoreProfileIdsFromAuthoring) {
      const {item_id, score_profile_id} = obj;

      const skill_code = availableScoreProfileIds.find(e => e.id == score_profile_id).skill_code;
      updatedTo.push({
        marking_window_id: +marking_window_id,
        item_id,
        score_profile_id,
        skill_code
      })
    }

    return {
      updatedFrom,
      updatedTo
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
