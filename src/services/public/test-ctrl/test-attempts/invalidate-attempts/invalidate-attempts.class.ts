import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import {Errors} from "../../../../../errors/general";
import { dbRawRead } from '../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class InvalidateAttempts implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    const testAttemptsToInvalidate = await dbRawRead(this.app, [id], `
    select ta.id as ta_id
		 , ta.test_session_id as ts_id
     , twtar.type_slug 
     , sd.is_sample is_sample_school
     , s.name school_name
     , tf.test_design_id
     , twtar.test_design_id
    from test_window_td_alloc_rules twtar 
    join test_attempts ta 
      on twtar.id = ta.twtdar_id
      and ta.started_on is null 
      and ta.is_invalid = 0 -- to avoid double processing
      and ta.uid > 0 -- should not be required 
    join test_forms tf 
      on tf.id = ta.test_form_id 
    join test_designs td 
      on td.id = twtar.test_design_id
    join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id 
    join school_classes sc on sc.id = scts.school_class_id 
    join schools s on s.group_id  = sc.schl_group_id 
    join school_districts sd on sd.group_id  = s.schl_dist_group_id 
    where twtar.test_window_id = ?
      and twtar.is_secured = 1
      and twtar.is_custom = 0
      and tf.test_design_id < twtar.test_design_id
    order by twtar.type_slug , s.name
    `)
    if(testAttemptsToInvalidate.length > 0){
      const ta_ids = testAttemptsToInvalidate.map(testAttempt => testAttempt.ta_id)
      if(ta_ids.length > 0){
        await Promise.all(ta_ids.map(async (ta_id)=>{
          return await this.app.service('db/write/test-attempts').patch(ta_id,{
            is_invalid: 1
          })
        }));
      }
    }
    return testAttemptsToInvalidate;
  }
}
