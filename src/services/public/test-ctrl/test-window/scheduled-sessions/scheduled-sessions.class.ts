import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead } from '../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class ScheduledSessions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query || !params.query.test_window_id) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    const {test_window_id, isExcludeFtConstr} = params.query;
    const isFtConstr = isExcludeFtConstr != 1;

    const records = await dbRawRead(this.app, {test_window_id}, `
      SELECT -- 4s on tw108
        t.*,
        sccf.test_form_id,
        twtdar_l.form_code locked_form_code,
        td_l.id locked_td_id
      FROM (
        SELECT ts.id ts_id
          , twtdar.is_classroom_common_form
          , twtdar.type_slug
          , ts.date_time_start
          , ts.date_time_start sched_start_on
          , min(ta.started_on) actual_start_on
          , ts.name_custom AS custom_session_name
          , ts.capacity
          -- , COUNT(DISTINCT ta.uid) AS capacity
          , COUNT(DISTINCT CASE WHEN ta.started_on IS NOT NULL THEN ta.uid END) AS num_students
          , ts.is_cancelled
          , ts.is_closed
          , sc.name AS class_name
          , s.id AS s_id
          , s.name AS s_name
          , s.foreign_id AS s_code 
          , sd.name sa_name
          , sd.foreign_id sa_code
          , sd.is_sample is_sample_school
          , scts.school_class_id as scts_sc_id
          , scts.slug as scts_slug
        FROM test_window_td_alloc_rules twtdar
        JOIN school_class_test_sessions scts 
          ON scts.slug = twtdar.slug
        JOIN school_classes sc 
          ON sc.id = scts.school_class_id
        JOIN schools s 
          ON s.group_id = sc.schl_group_id
        JOIN school_districts sd 
          ON sd.group_id = s.schl_dist_group_id
        JOIN test_sessions ts 
          ON ts.id = scts.test_session_id 
          AND ts.test_window_id = twtdar.test_window_id
        LEFT JOIN test_attempts ta
          ON ta.test_session_id = ts.id 
        WHERE twtdar.test_window_id = :test_window_id
          and twtdar.is_custom = 0
          ${isFtConstr ? `and twtdar.is_field_test = 1` : ''}
        group by ts.id
      ) t
      LEFT JOIN school_class_common_forms sccf 
        ON sccf.school_class_id = t.scts_sc_id
        AND sccf.type_slug = t.scts_slug
        and sccf.is_revoked = 0
      LEFT JOIN test_window_td_alloc_rules twtdar_l
        ON twtdar_l.id = sccf.twtdar_id
        and twtdar_l.is_custom = 0
      LEFT JOIN test_forms tf 
        ON tf.id = sccf.test_form_id
      LEFT JOIN test_designs td_l
        ON td_l.id = tf.test_design_id
      GROUP BY t.ts_id
    ;`);

    return records
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}