import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawR<PERSON>, dbRawReadSingle } from '../../../../../util/db-raw';
import { currentUid } from '../../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class ScoringWindowItems implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query && params.query.marking_window_id) {
      const batchPolicyList  = await dbRawRead(this.app, [], `
        select * from marking_batch_alloc_policies mbap
      ;`)
      const newMarkingWindowItems = await this.getNewMarkingWindowItems(params.query.marking_window_id);

      return [{
        newMarkingWindowItems: newMarkingWindowItems,
        batchPolicyList: batchPolicyList
      }]
    }
    throw new Errors.BadRequest;

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (data && params) {
      const {
        newMarkingWindowItems
      } = <any> data;
  
      const currentUID = await currentUid(this.app, params);
  
      let primaryMarkingWindowItemId;
      for (let i = 0; i < newMarkingWindowItems.length; i++) {
        const markingWindowItemCreateFields = {
          ...newMarkingWindowItems[i],
          cached_taqr_tally: 0,
          created_by_uid: currentUID
        }
        const markingWindow = await this.app
          .service('db/write/marking-window-items')
          .create(markingWindowItemCreateFields);

        if (i == 0) {
          primaryMarkingWindowItemId = markingWindow.id
        }
        
        const markingWindowItemPatchFields = {
          sync_batches_to_wmi_id: primaryMarkingWindowItemId,
        }
  
        await this.app
          .service('db/write/marking-window-items')
          .patch(markingWindow.id, markingWindowItemPatchFields);
      }
      return {};
    }
    return new Errors.BadRequest('NO_PARAMS');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }

  async getNewMarkingWindowItems(marking_window_id: number) {
    const markingWindow = await dbRawReadSingle(this.app, [marking_window_id], `
      select mw.id, mw.group_id
      from marking_windows mw
      where mw.id = ?      
    ;`);

    const itemsToAdd  = await dbRawRead(this.app, [marking_window_id], `
      select distinct test_question_id,
          score_profile_id,
          mwi_id,
          question_label
      from (
        select tqsr.test_question_id, tqsr.score_profile_id, mwi.id mwi_id, tq.question_label
        from marking_windows mw
        join marking_window_test_window mwtw 
          on mw.id = mwtw.marking_window_id 
        join test_windows tw 
          on tw.id = mwtw.test_window_id 
        join test_window_td_alloc_rules twtar 
          on twtar.test_window_id = tw.id
          and twtar.is_custom = 0
        join test_question_scales_register tqsr
          on twtar.test_design_id = tqsr.test_design_id
          and tqsr.is_human_scored = 1
        join test_questions tq 
          on tq.id = tqsr.test_question_id
        left join marking_window_items mwi 
          on mwi.item_id = tq.id
          and mwi.score_profile_id = tqsr.score_profile_id
        where mw.id = ?
      ) t
      where t.mwi_id is null
    ;`)

    const newMarkingWindowItems = itemsToAdd.map(item => {
      return {
        marking_window_id,
        group_id: markingWindow.group_id,
        item_id: item.test_question_id,
        score_profile_id: item.score_profile_id,
        slug: item.question_label,
      }
    })
    return newMarkingWindowItems
  }
}
