import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { ISectionPerformance } from './model/types';
import { REPORT_MODEL } from './model/report-model';
import { QUADRANT_TESTLET_COUNTS} from './model/quadrant-testlet-counts';
import { QUESTION_LABEL_TO_QUADRANT } from './model/question-quadrant-maps';
import { ITestAttempt } from '../../../../db/schemas/test_attempts.schema';
import { ITestSession } from '../../../../db/schemas/test_sessions.schema';
import { IInstitution } from '../../../../db/schemas/institutions.schema';
import { Knex } from 'knex';
import { getSysConstNumeric } from '../../../../../util/sys-const-numeric';
import { dbDateNow } from '../../../../../util/db-dates';
import { dbRawRead, dbRawReadSingle } from '../../../../../util/db-raw';

const MPT_LINEAR = 'MPT_LINEAR'
interface ITestReport {
  uid: number,
  is_successful: number,
  is_no_show: number,
  oct_id: string,
  institution_name: string,
  test_session_date_time_start: string,
  room: string,
  campus_building: string,
  attempt_key: string,
  num_questions_answered: number,
  num_questions_total: number,
  component_performances: string | any,
  is_viewed?: number,
  first_viewed_on?: string | Knex.Raw,
}

interface Data {}

interface ServiceOptions {}

export class Results implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params: Params): Promise<Data> {

    const test_attempt_id = (<number>id)*1;
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid;
    let getReport;
    const getreport = params!.query;
    if(params && JSON.stringify(params.query) !== '{}'){
      getReport = getreport;
      const report = await this.ensureReport(test_attempt_id, uid, false);
      if (uid !== report.uid){
        throw new Errors.Forbidden('WRONG_USER')
      }
      if(getreport){ return report}
    } else{
      const report = await this.ensureReport(test_attempt_id, uid, false);
      if (uid !== report.uid){
        throw new Errors.Forbidden('WRONG_USER')
      }

      // check sys_constants_numeric.SHOW_APPLICANT_RESULTS to make sure that results are available to be pulled
      const SHOW_APPLICANT_RESULTS = await getSysConstNumeric(this.app, 'SHOW_APPLICANT_RESULTS')
      if (!SHOW_APPLICANT_RESULTS){
        throw new Errors.Forbidden('NOT_YET_RELEASED')
      }



    // for (let i=test_attempt_id+1; i<test_attempt_id+500; i++){
    //   console.log('trying to generate report for', i)
    //   this.ensureReport(i, uid).catch(e=> {
    //     console.log('could not generate attempt', i)
    //   })
    // }
    return report;
    }
    return [];
  }

  async ensureReport(test_attempt_id:number, uid:number, isAuto:boolean){
    let report:ITestReport = await this.app
      .service('db/read/test-reports')
      .get(test_attempt_id)
      .catch(e =>{
        // console.log(e)
        // dont throw an error if no results found
      })
    console.log(report)
      if (report){
        if (!isAuto && !report.is_viewed){
          await this.app
            .service('db/write/test-reports')
            .patch(test_attempt_id, {
              is_viewed: 1,
              first_viewed_on: dbDateNow(this.app),
            })
        }
        return {
          ... report,
          component_performances: JSON.parse(report.component_performances),
        };
      }
    // otherwise recompue and save
    return this.computeReport(test_attempt_id, uid, isAuto);
  }

  async computeReport(test_attempt_id:number, uid:number, isAuto:boolean){

    let component_performances:ISectionPerformance[] = [];
    let num_questions_answered = 0;
    let num_questions_total = 0;
    let isOverallSuccessful = false;

    const testAttempt:ITestAttempt = await this.app
      .service('db/read/test-attempts')
      .get(test_attempt_id);

    const testFormId = testAttempt.test_form_id
    const testType = await dbRawRead(this.app, [testFormId],`
     select twtdar.type_slug as type from mpt_dev.test_forms tf
     join mpt_dev.test_window_td_alloc_rules twtdar 
      on twtdar.test_design_id  = tf.test_design_id
      and twtdar.is_custom = 0
     and tf.id = ?;`);
     let isLinear = false;
     if (testType && testType.length > 0){
       if(testType[0].type === MPT_LINEAR){
         isLinear = true;
       }
     }


    if (!isAuto){
      if (uid !== testAttempt.uid){
        throw new Errors.Forbidden('WRONG_USER')
      }
    }

    const attempt_key = testAttempt.attempt_key;
    const test_session_id = testAttempt.test_session_id;
    const applicantUid = testAttempt.uid;
    let isNoShow:boolean = (testAttempt.is_absent == 1 || testAttempt.lang === null);

    const octIdRecords = <Paginated<any>> await this.app
      .service('db/read/oct-applicant-ids')
      .find({query: {uid:testAttempt.uid}});

    const octId = octIdRecords.data[0].oct_id;

    const testSession:ITestSession = await this.app
      .service('db/read/test-sessions')
      .get(test_session_id);

    const institutionRecords = <Paginated<IInstitution>> await this.app
      .service('db/read/institutions')
      .find({query: {group_id: testSession.instit_group_id}});
    const institution = institutionRecords.data[0];

    if (!isNoShow){

      const db:Knex = this.app.get('knexClientRead');

      // .service('db/read/test-attempt-qr-correct')
      const correctAnswers:{question_label:string}[] = await dbRawRead(this.app, [test_attempt_id], `
        SELECT r.*
        FROM (
          SELECT
            r.id AS id,
            r.test_attempt_id AS test_attempt_id,
            a.lang AS lang,
            q.component AS component,
            q.question_label AS question_label
          FROM test_question_register q
          JOIN test_attempt_question_responses r
            ON r.test_question_id = CONVERT( q.question_id USING UTF8)
            AND r.response = CONVERT( q.expected_answer USING UTF8)
          JOIN test_attempts a
            ON a.id = r.test_attempt_id
           -- AND a.lang = CONVERT( q.lang USING UTF8)
          where r.test_attempt_id = ?
            AND q.subcomponent != 'field test'
          group by r.test_question_id
        ) r
        GROUP BY r.test_attempt_id , r.lang , r.question_label
        ;
      `)
      //console.log(correctAnswers.length);
      type TQ = {test_attempt_id:number, total_questions:number}
      const totalQuestions:TQ  = await dbRawReadSingle(this.app, [test_attempt_id],`
        select COUNT(0) AS total_questions
        FROM (
          SELECT taqr.id
            , COUNT(taqr.test_question_id) AS total_questions
          FROM test_attempt_question_responses taqr
            JOIN test_question_register tqr
              ON taqr.test_question_id = tqr.question_id
              AND tqr.component != 'questionnaire'
              AND tqr.subcomponent != 'field test'
          AND taqr.test_attempt_id = ?
          group by taqr.test_question_id
        ) t
      `)
      num_questions_total = totalQuestions.total_questions
      //console.log(num_questions_total);
      type RT = {id:number, tally:number}
      const taResponseTally:RT = await dbRawReadSingle(this.app, [test_attempt_id], `
        SELECT taqr.test_attempt_id AS id, COUNT(0) AS tally
        FROM (
          SELECT taqr.test_attempt_id AS test_attempt_id
              , ta.lang AS lang
              , taqr.test_question_id
          FROM mpt_dev.test_attempts ta
          JOIN mpt_dev.test_attempt_question_responses taqr
            ON ta.id = taqr.test_attempt_id
              AND taqr.response <> ''
            AND taqr.response IS NOT NULL
          JOIN mpt_dev.test_question_register q
            ON q.question_id = taqr.test_question_id
            -- AND ta.lang = CONVERT( q.lang USING UTF8)
            AND q.component IN ('math' , 'pedagogy')
            AND q.subcomponent != 'field test'
          WHERE ta.id = ?
          GROUP BY taqr.test_question_id
        ) taqr
        GROUP BY taqr.test_attempt_id
      `);

      //num_questions_answered = taResponseTally.tally || 0;
      num_questions_answered = taResponseTally? taResponseTally.tally:0;

      const quadrantNumCorrect = new Map();
      const quadrantDeno = new Map();
      const quadrantScore = new Map();
      let numQuestionsTotal = 0;
      QUADRANT_TESTLET_COUNTS.forEach(q =>{
        quadrantNumCorrect.set(q.quadrant_id, 0);
        quadrantDeno.set(q.quadrant_id, q.count);
        numQuestionsTotal += q.count;
      });
      correctAnswers.forEach(response =>{
        //console.log(response)
        const quadrant_id = QUESTION_LABEL_TO_QUADRANT[response.question_label];
        if(!quadrant_id){console.log(response.question_label)}
        if(quadrant_id){
          let tally = quadrantNumCorrect.get(quadrant_id)
          quadrantNumCorrect.set(quadrant_id, tally + 1)
        }
      });
      /* console.log(quadrantNumCorrect)
      console.log(quadrantDeno)
 */
      // QUADRANT_TESTLET_COUNTS.forEach(quadrantTestlet =>{
      //   const quadrant_id = quadrantTestlet.quadrant_id;
      //   const numCorrect = quadrantNumCorrect.get(quadrant_id);
      //   const score = numCorrect / quadrantTestlet.count;
      //   quadrantScore.set(quadrant_id, score);
      // })

      const cutScore = 0.7;
      const computeScore = (count:number, max:number) => (max === 0) ? 0 : count/max;
      isOverallSuccessful = true;

      component_performances = REPORT_MODEL.map(componentDef =>{
        let componentNume = 0;
        let componentDeno = 0;
        const subComponentPerformance:any = componentDef.sub.map(subComponentDef => {
          let subComponentNume = 0;
          let subComponentDeno = 0;
          let feedbackMsgs;
          if(subComponentDef.quadrants){
            subComponentDef.quadrants.forEach(quadrant_id =>{
              subComponentNume += quadrantNumCorrect.get(quadrant_id);
              subComponentDeno += quadrantDeno.get(quadrant_id);
            })
          }
          if(subComponentDef.sub){
            feedbackMsgs = subComponentDef.sub.map(dimDef =>{
              let nume = 0;
              let deno = 0;
              if (dimDef.quadrants){
                dimDef.quadrants.forEach(quadrant_id => {
                  nume += quadrantNumCorrect.get(quadrant_id);
                  deno += quadrantDeno.get(quadrant_id);
                })
              }
              let isPassed = computeScore(nume, deno) > cutScore;
              subComponentNume += nume;
              subComponentDeno += deno;
              return isPassed ? dimDef.pass : dimDef.fail;
            });
          }
          const score = computeScore(subComponentNume, subComponentDeno);
          componentNume += subComponentNume;
          componentDeno += subComponentDeno;
          return {
            name: subComponentDef.label,
            feedbackMsgs,
            isSuccessful: score > cutScore,
          }
        })
        //console.log(componentNume, componentDeno)
        const score = computeScore(componentNume, componentDeno);
        const isSuccessful = score >= cutScore;
        if (!isSuccessful){ isOverallSuccessful = false; }
        const componentPerformance:ISectionPerformance = {
          name: componentDef.label,
          score,
          isSuccessful,
          subComponents: subComponentPerformance
        }
        return componentPerformance;
      });
    }

    let is_successful = isOverallSuccessful ? 1 : 0;
    let is_no_show = isNoShow ? 1 : 0;
    if (isNoShow){
      is_successful = 0;
      num_questions_answered = 0;
    }

    const creditsEnabled: any = await this.app
      .service('public/credits/credit-system')
      .find();
    
    if (creditsEnabled.isEnabled) {
      // if not successful, receive credit refund
      if (!is_successful) {
        await this.receiveFreeCreditTokenIfUnsuccessful(applicantUid)
      }
    }
    const payload:ITestReport = {
      uid: testAttempt.uid,
      is_successful,
      is_no_show,
      oct_id: octId,
      institution_name: <string> institution.name,
      test_session_date_time_start: <string> testSession.date_time_start,
      room: testSession.room,
      campus_building: testSession.campus_building,
      attempt_key,
      num_questions_answered,
      num_questions_total, // should be computed dynamically, but need to exclude questionnaire
      // num_questions_total: 71, // should be computed dynamically, but need to exclude questionnaire
      component_performances,
    };


    if (!isAuto){
      payload.is_viewed = 1;
      payload.first_viewed_on = dbDateNow(this.app);
    }

    const result=await this.app
      .service('db/write/test-reports')
      .create({
        ... payload,
        component_performances: JSON.stringify(payload.component_performances),
        created_by_uid: uid,
        id: test_attempt_id,
      });

    return {...result ,
      component_performances:payload.component_performances,
      created_by_uid: uid,
      id: test_attempt_id,
    } ;
  }

  async receiveFreeCreditTokenIfUnsuccessful(uid: number): Promise<void> {

    const currentDate = new Date();
    const freeCreditReason = 'mpt status eligible'
    const mptStatus = await this.app
      .service('db/read/user-metas')
      .db()
      .where('uid', uid)
      .where('key', 'mpt_status_code');

    const createdCredit = await this.app
      .service('credits/credits')
      .createCredit(uid, 1, freeCreditReason, currentDate, mptStatus.length > 0 ? mptStatus[0].value : null);

    await this.app
      .service('credits/credit-transactions')
      .createAcquiredTransaction(createdCredit.id, currentDate, uid, freeCreditReason);

  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
