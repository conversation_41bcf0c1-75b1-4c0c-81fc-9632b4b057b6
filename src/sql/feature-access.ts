/* Feature Access Management SQL Queries */

export const SQL_FEATURE_ACCESS_GROUPS_LIST = `/*SQL_FEATURE_ACCESS_GROUPS_LIST*/
  SELECT 
    fag.id,
    fag.feature_slug,
    fag.group_id,
    fag.created_on,
    fag.created_by_uid,
    fag.expires_on,
    fag.is_revoked,
    fag.revoked_on,
    fag.revoked_by_uid,
    fag.notes,
    g.name as group_name,
    g.type as group_type,
    creator.first_name as creator_first_name,
    creator.last_name as creator_last_name,
    revoker.first_name as revoked_by_first_name,
    revoker.last_name as revoked_by_last_name,
    CASE 
      WHEN fag.is_revoked = 1 THEN 'revoked'
      WHEN fag.expires_on IS NOT NULL AND fag.expires_on < NOW() THEN 'expired'
      ELSE 'active'
    END as status
  FROM feature_access_groups fag
  LEFT JOIN groups g ON fag.group_id = g.id
  LEFT JOIN users creator ON fag.created_by_uid = creator.uid
  LEFT JOIN users revoker ON fag.revoked_by_uid = revoker.uid
  WHERE fag.feature_slug = ?
  ORDER BY fag.created_on DESC
`;

export const SQL_FEATURE_ACCESS_GROUPS_BY_GROUP = `/*SQL_FEATURE_ACCESS_GROUPS_BY_GROUP*/
  SELECT 
    fag.id,
    fag.feature_slug,
    fag.group_id,
    fag.created_on,
    fag.created_by_uid,
    fag.expires_on,
    fag.is_revoked,
    fag.revoked_on,
    fag.revoked_by_uid,
    fag.notes,
    g.name as group_name,
    g.type as group_type,
    creator.first_name as creator_first_name,
    creator.last_name as creator_last_name,
    revoker.first_name as revoked_by_first_name,
    revoker.last_name as revoked_by_last_name,
    CASE 
      WHEN fag.is_revoked = 1 THEN 'revoked'
      WHEN fag.expires_on IS NOT NULL AND fag.expires_on < NOW() THEN 'expired'
      ELSE 'active'
    END as status
  FROM feature_access_groups fag
  LEFT JOIN groups g ON fag.group_id = g.id
  LEFT JOIN users creator ON fag.created_by_uid = creator.uid
  LEFT JOIN users revoker ON fag.revoked_by_uid = revoker.uid
  WHERE fag.group_id = ?
  ORDER BY fag.created_on DESC
`;

export const SQL_FEATURE_ACCESS_CHECK = ` /*SQL_FEATURE_ACCESS_CHECK*/
  SELECT 
    fag.id,
    fag.feature_slug,
    fag.group_id,
    fag.created_on,
    fag.expires_on,
    fag.is_revoked,
    CASE 
      WHEN fag.is_revoked = 1 THEN 'revoked'
      WHEN fag.expires_on IS NOT NULL AND fag.expires_on < NOW() THEN 'expired'
      ELSE 'active'
    END as status
  FROM feature_access_groups fag
  WHERE fag.feature_slug = ? 
    AND fag.group_id = ?
    AND fag.is_revoked = 0
    AND (fag.expires_on IS NULL OR fag.expires_on > NOW())
  LIMIT 1
`;

export const SQL_FEATURE_ACCESS_CHECK_UID = ` /*SQL_FEATURE_ACCESS_CHECK_UID*/
  SELECT 
      fag.id fag_id
    , ur.id ur_id
  FROM feature_access_groups fag
  JOIN user_roles ur 
  	on ur.group_id = fag.group_id
  	and ur.is_revoked = 0
  WHERE fag.feature_slug = :feature_slug 
    AND ur.uid = :uid
    AND fag.is_revoked = 0
    AND (fag.expires_on IS NULL OR fag.expires_on > NOW())
  LIMIT 1
`;

export const SQL_FEATURE_ACCESS_GROUP_MEMBERS = ` /*SQL_FEATURE_ACCESS_GROUP_MEMBERS*/
  SELECT 
      u.id uid
    , u.first_name
    , u.last_name
    , u.contact_email
    , ur.role_type
    , ur.created_on as role_created_on
    , ur.expires_on as role_expires_on
  FROM users u
  JOIN user_roles ur 
    ON u.id = ur.uid
  WHERE ur.group_id = :group_id
    AND ur.is_revoked = 0
    AND (ur.expires_on IS NULL OR ur.expires_on > NOW())
  ORDER BY u.first_name, u.last_name
`;

export const SQL_FEATURE_ACCESS_STATS = ` /*SQL_FEATURE_ACCESS_STATS*/
  SELECT 
    feature_slug,
    COUNT(*) as total_access,
    COUNT(CASE WHEN is_revoked = 0 AND (expires_on IS NULL OR expires_on > NOW()) THEN 1 END) as active_access,
    COUNT(CASE WHEN is_revoked = 1 THEN 1 END) as revoked_access,
    COUNT(CASE WHEN expires_on IS NOT NULL AND expires_on < NOW() THEN 1 END) as expired_access
  FROM feature_access_groups
  GROUP BY feature_slug
  ORDER BY feature_slug
`;

export const SQL_FEATURE_ACCESS_ADD = ` /*SQL_FEATURE_ACCESS_ADD*/
INSERT INTO feature_access_groups (
  feature_slug, 
  group_id, 
  created_by_uid, 
  expires_on, 
  notes
) VALUES (?, ?, ?, ?, ?)
`;

export const SQL_FEATURE_ACCESS_REVOKE = ` /*SQL_FEATURE_ACCESS_REVOKE*/
  UPDATE feature_access_groups 
  SET is_revoked = 1, 
      revoked_on = NOW(), 
      revoked_by_uid = ?
  WHERE feature_slug = ? 
    AND group_id = ?
    AND is_revoked = 0
`; 

export const SQL_TEST_WINDOW_CTRL_GROUPS = ` /*SQL_TEST_WINDOW_CTRL_GROUPS*/
  SELECT tw.test_ctrl_group_id
      , g.group_type 
      , g.description
  FROM test_windows tw 
  JOIN u_groups g 
    ON g.id = tw.test_ctrl_group_id
  GROUP BY g.id 
  ORDER BY g.description
`;

export const SQL_ASSESSMENT_LOOKUP = ` /*SQL_ASSESSMENT_LOOKUP*/
  SELECT 
    ad.assessment_name,
    ad.course_code,
    s.foreign_id as school_foreign_id,
    s.name as school_name,
    sam.created_on as mapping_created_on
  FROM assessment_def ad
  JOIN school_assessments_map sam ON ad.id = sam.assessment_def_id
  JOIN schools s ON sam.school_id = s.id
  WHERE s.foreign_id = ?
  ORDER BY ad.assessment_name, ad.course_code
`;

export const SQL_ACCOUNT_TYPE_ROLE_ROUTES = ` /*SQL_ACCOUNT_TYPE_ROLE_ROUTES*/
  SELECT 
      urgat.id
    , urgat.role_type
    , urgat.group_type
    , urgat.account_type
    , urgat.route_template
    , urgat.route_join
    , urgat.caption
    , urgat.color
    , urgat.display_order
    , urgat.created_on
  FROM user_role_group_account_types urgat
  ORDER BY urgat.display_order
          , urgat.role_type
          , urgat.group_type
`

export const SQL_FEATURE_FLAGS_GROUPS_LIST = ` /*SQL_FEATURE_FLAGS_GROUPS_LIST*/
  SELECT 
        fag.id 
      , ug.id as   group_id
      , fag.feature_slug
      , ug.description as group_name
      , ug.group_type
      , fag.created_on
      , COUNT(DISTINCT ur.uid) as n_users
  FROM feature_access_groups fag 
  JOIN u_groups ug 
    ON ug.id = fag.group_id 
  JOIN user_roles ur 
    ON ur.group_id = fag.group_id
    AND ur.is_revoked = 0
  GROUP BY fag.id 
`;

export const SQL_ACCOUNT_TYPE_ROLE_ROUTES_UPDATE = ` /*SQL_ACCOUNT_TYPE_ROLE_ROUTES_UPDATE*/
  UPDATE user_role_group_account_types 
  SET role_type = :role_type
    , group_type = :group_type
    , account_type = :account_type
    , route_template = :route_template
    , route_join = :route_join
    , caption = :caption
    , color = :color
    , display_order = :display_order
  WHERE id = :id
`;