export const LOCAL_SCORING_FRAMEWORK = ` -- LOCAL_SCORING_FRAMEWORK
select twtar.long_name
    , twtar.is_active
    , tqr.test_design_id
    , GROUP_CONCAT(distinct tqr.question_id) item_ids
    , tqsi.batch_alloc_policy_id
    , GROUP_CONCAT(distinct tqsi.batch_alloc_policy_item_slug) batch_alloc_policy_item_slug
    , mbap.read_rules
    , mbap.id mbap_id
    , GROUP_CONCAT(distinct tqsi.id) tqsi_ids
from test_windows tw 
left join test_window_td_alloc_rules twtar 
    on twtar.test_window_id = tw.id 
    and twtar.is_questionnaire = 0 
    and twtar.is_custom = 0
left join test_question_register tqr
    on tqr.test_design_id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
-- todo: there may be other fields we should use to filter on tqr (but will not get into that now)
left join test_question_scoring_info tqsi 
    on tqsi.id = tqr.tqsi_id
    and tqsi.lang = tqr.lang
    and tqsi.is_human_scored = 1
left join marking_batch_alloc_policies mbap 
    on mbap.id = tqsi.batch_alloc_policy_id
where tw.id = :tw_id -- 106 or 117
    and twtar.slug = :slug
group by twtar.id, tqsi.batch_alloc_policy_id
having tqsi.batch_alloc_policy_id is not null
order by twtar.long_name
;`

export const LOCAL_SCORING_TW = `
select ss.test_window_id 
from school_class_test_sessions scts 
join school_classes sc 
    on sc.id = scts.school_class_id 
join school_semesters ss 
    on sc.semester_id = ss.id 
where test_session_id = :ts_id
`

export const LOCAL_SCORING_PROFILE_FLAGS = `
select 
    mso.slug
    , mso.caption 
from marking_score_profile_flags mspf
join marking_score_options mso 
    on mspf.score_option_id = mso.id
where mspf.score_profile_id in (:score_profile_ids) 
    and mso.is_local_score_flag = 1
    and mspf.is_revoked = 0
order by mspf.order
`

export const LOCAL_SCORING_PROFILE_GROUP = `
select * 
from marking_score_profile_groups mspg 
where id = :score_profile_group_id
`

export const LOCAL_SCORING_SCALES = `
select 
    mspo.score_profile_id msp_id
    , msp.skill_code scaleCode
    , msp.short_name caption
    , mso.slug scoreCode
    , mso.value scoreValue
    , msp.order msp_order
    , msp.guide1_url
    , msp.guide2_url
    , mspo.order mspo_order
from marking_score_profile_options mspo 
join marking_score_options mso 
    on mspo.score_option_id = mso.id
join marking_score_profiles msp 
    on msp.id = mspo.score_profile_id 
where mspo.score_profile_id in (:score_profile_ids)
    and mso.is_non_scored_scale = 0
    and mspo.is_revoked = 0
    order by msp.order, mspo.order
`

export const LOCAL_SCORING_TQSI_MAP = `
select 
    tqsi.item_id
    , tqsi.batch_alloc_policy_item_slug 
from test_question_scoring_info tqsi 
where tqsi.item_id in (:mbapItemIds)
and tqsi.id in (:tqsi_ids)
and tqsi.batch_alloc_policy_id is not null;
`

export const LOCAL_SCORING_ACTIVE_MARKING_WINDOWS = `
select mw.id
    , mw.is_rescore
    , tw.is_closed as is_test_window_closed 
from marking_windows mw
left join marking_window_test_window mwtw
    on mwtw.marking_window_id = mw.id
left join test_windows tw 
    on tw.id = mwtw.test_window_id
    and mwtw.is_material = 0
where mw.is_active = 1
    and mw.id in (:markingWindowIds)
group by mw.id
`

export const LOCAL_SCORING_MTC_SCORES = `
select tls.id
    , tls.data
    , tls.updated_on
    , tls.updated_by_uid
    , tls.attempt_id
    , tls.item_id
    , responses.teacher_uid
    , responses.taqr_id
    , responses.schl_group_id
    , responses.test_attempt_id mtc_ta_id
    , responses.student_accommodation_cache 
from temp_local_scoring tls 
right join
(select 
    ta.id test_attempt_id
    , taqr.test_question_id test_question_id
    , ur.uid teacher_uid
    , r.taqr_id
    , r.student_accommodation_cache 
    , sc.schl_group_id schl_group_id
from (
    select mtc.taqr_id
    , mtc.schl_group_id
    , mtc.mwi_id
    , max(mmcbr.id) as resp_claim_id
    , mtc.marking_window_id
    , mtc.student_accommodation_cache 
    from marking_taqr_cache mtc
    left join (
    -- select * from (
    select mcbr.id
            , mcbr.taqr_id
            , mcb.marking_window_item_id
            , min(mcbr.is_rescore_indic) is_rescore_indic
    from marking_claimed_batches mcb
    join marking_claimed_batch_responses mcbr
        on mcb.id = mcbr.claimed_batch_id
        and mcb.is_training = 0
        and (
                (mcbr.is_revoked = 0
                and mcbr.is_rescore_indic = 0
                and (mcbr.is_invalid is null OR mcbr.is_invalid = 0)) 
            or 
                mcbr.tls_id is not null
            )
    join marking_window_items mwi
        on mwi.id = mcbr.window_item_id
    where mwi.marking_window_id = :markingWindowId
    group by mcbr.taqr_id
            , mcb.marking_window_item_id
    -- ) mmcbr_inner
    ) mmcbr
    on mmcbr.taqr_id = mtc.taqr_id
    and mmcbr.marking_window_item_id = mtc.mwi_id
    where mtc.marking_window_id = :markingWindowId
    and mtc.is_material = 0
    group by mtc.taqr_id
    ) r
join test_attempt_question_responses taqr
    on r.taqr_id = taqr.id
join test_attempts ta
    on ta.id = taqr.test_attempt_id
join schools s
    on s.group_id = r.schl_group_id
join school_class_test_sessions scts
    on ta.test_session_id = scts.test_session_id
join school_classes sc
    on sc.id = scts.school_class_id
left join user_roles ur 
    on ur.group_id = sc.group_id
    and ur.role_type = 'schl_teacher'
    and ur.is_revoked = 0
join marking_windows mw
    on mw.id = r.marking_window_id
join marking_window_items mwi
    on mwi.id = r.mwi_id
where r.resp_claim_id is null
group by taqr.test_attempt_id, taqr.test_question_id
order by ta.id
) responses
on responses.test_attempt_id = tls.attempt_id
and responses.test_question_id = item_id 
order by tls.updated_on desc
;`

export const LOCAL_SCORING_BATCH_ALLOCATION_POLICIES = `
select tqsi.item_id
    , mbap.id
    , mbap.read_rules
    , mbap.created_on 
from marking_batch_alloc_policies mbap
join marking_window_items mwi 
    on mwi.batch_alloc_policy_id = mbap.id
join test_question_scoring_info tqsi 
    on tqsi.batch_alloc_policy_id = mbap.id
where mwi.marking_window_id = :markingWindowId
    and mbap.is_revoked = 0
    and tqsi.item_id > 0
group by tqsi.item_id 
`

export const LOCAL_SCORING_MARKING_WINDOW_SCORE_OPTIONS = `
select mwi.id mwi_id
    , mwi.group_to_mwi_id 
    , mwi.sync_batches_to_wmi_id 
    , mwi.item_id 
    , msp.skill_code
    , mso.slug
    , mso.value  
    , mspo.id mspo_id
    , mwi.batch_alloc_policy_id
    , msp.is_non_scored_profile 
from marking_score_profile_options mspo 
join marking_score_options mso 
    on mspo.score_option_id = mso.id
join marking_score_profiles msp 
    on msp.id = mspo.score_profile_id 
join marking_window_items mwi
    on mwi.score_profile_id	= msp.id 
    and mwi.marking_window_id = :markingWindowId
`
export const LOCAL_SCORING_MARKING_WINDOW_FLAG_OPTIONS = `
select mwi.id mwi_id
    , mwi.group_to_mwi_id 
    , mwi.sync_batches_to_wmi_id 
    , mwi.item_id 
    , msp.skill_code
    , mso.slug
    , mso.value 
    , mspf.id mspf_id
    , mwi.batch_alloc_policy_id
    , msp.is_non_scored_profile 
from marking_score_profile_flags mspf
join marking_score_options mso 
    on mspf.score_option_id = mso.id
join marking_score_profiles msp 
    on msp.id = mspf.score_profile_id 
join marking_window_items mwi
    on mwi.score_profile_id	= msp.id 
    and mwi.marking_window_id = :markingWindowId
`

export const LOCAL_SCORING_MARKING_WINDOW_ITEM_MAP = `
select mtc.taqr_id
    , mtc.ta_id
    , mtc.mwi_id 
    , mwi.item_id 
from marking_taqr_cache mtc
join marking_window_items mwi 
    on mtc.mwi_id = mwi.id 
where mtc.marking_window_id = :markingWindowId
`
export const LOCAL_SCORING_MARKER_CREATION_META = `
select mwi.id mwi_id 
    , mwi.lang lang
    , mw.group_id 
from marking_window_items mwi 
join marking_windows mw
    on mwi.marking_window_id = mw.id
where mwi.marking_window_id = :markingWindowId
`

export const LOCAL_SCORING_EXISTING_MARKER_TASK = `
select mimt.marking_window_item_id
    , mimt.uid
from marking_item_marker_tasks mimt 
where mimt.marking_window_item_id in (:markingWindowItemIds)
    and uid in (:uids )
    and status = "ACTIVE" 
    and ctrl_group_id = :group_id
    -- and is_revoked = 0 no matter blocked or not
    and is_removed = 0;
`
export const LOCAL_SCORING_STATS_BATCH_POLICY = `
select mbap.*
from marking_batch_alloc_policies mbap
join marking_window_items mwi 
    ON mwi.batch_alloc_policy_id = mbap.id
where mwi.marking_window_id = :mw_id
    AND mbap.is_revoked = 0
group by mbap.id
;`

export const LOCAL_SCORING_STATS_BATCH_GROUPS = `
    select distinct(mcbr.claimed_batch_group_id) as mcbgIds
    from marking_claimed_batch_responses mcbr 
    join marking_window_items mwi 
        on mwi.id = mcbr.window_item_id 
        and mwi.marking_window_id = :mw_id
    join marking_claimed_batches mcb 
        on mcb.id = mcbr.claimed_batch_id 
    join marking_windows mw
        on mw.id = mwi.marking_window_id 
    left join user_roles ur 
        on ur.uid = mcb.uid 
        and ur.role_type = 'mrkg_ctrl' 
        and ur.group_id = mw.group_id 
    where mcbr.tls_id is not null 
        and (mcbr.is_invalid = 0 or mcbr.is_invalid is null)
        and mcbr.is_revoked = 0
    and ur.role_type is null;
`
export const SQL_GET_READ_INFO_LOCAL_SCORING_STATS = `
SELECT 
  mcbr.claimed_batch_group_id
  , mcbr.marker_read_id
  , mcbr.id mcbr_id
  , mwi.id mwi_id
  , read_table.read_id
  , read_table.marker_uid
  , mcbr.is_scale_supressed
  , msp.is_non_scored_profile
  , mso.slug score_slug
  , mso.id score_option_id
  , mso.raw_score_value
  , msf.slug flag_slug
  , mwi.score_profile_id
  , mwi.batch_alloc_policy_id
  , mcbr.read_rule_type
  , mcbr.from_read_rule 
  , mcbr.is_prorated
  , mwi.sync_batches_to_wmi_id 
  , mwi.group_to_mwi_id 
  , mcbr.tls_id
  , msp.supress_condition
  , msp.skill_code
  , msp.short_name score_profile_name
  FROM (
  SELECT mwi.id mwi_id, mcbr.marker_read_id, mcbr.claimed_batch_group_id, mcb.uid marker_uid
  , ROW_NUMBER() OVER (PARTITION BY mcbr.claimed_batch_group_id ORDER BY mcbr.marker_read_id) AS read_id
    FROM marking_window_items mwi
    JOIN marking_window_task_definitions mwtd
      ON mwtd.cached__group_to_mwi_id = mwi.group_to_mwi_id
    JOIN marking_claimed_batch_responses mcbr 
      ON mcbr.window_item_id = mwi.id
    JOIN marking_claimed_batches mcb
      ON mcb.id = mcbr.claimed_batch_id
    LEFT JOIN marking_response_scores mrs
        ON mrs.batch_response_id = mcbr.id
    WHERE mcbr.claimed_batch_group_id IS NOT NULL
      AND mcbr.claimed_batch_group_id in (:mcbgIds)
      AND mcb.is_marked = 1
      AND (mcbr.is_invalid IS NULL OR mcbr.is_invalid = 0)
      AND (mcbr.is_revoked IS NULL OR mcbr.is_revoked = 0)
    GROUP BY mcbr.claimed_batch_group_id, mcbr.marker_read_id
    ORDER BY mcbr.claimed_batch_group_id, mcbr.marker_read_id
  ) read_table
  JOIN marking_claimed_batch_responses mcbr
    ON mcbr.marker_read_id = read_table.marker_read_id
  JOIN marking_window_items mwi
    ON mwi.id = mcbr.window_item_id
  LEFT JOIN marking_response_scores mrs
    ON mrs.batch_response_id = mcbr.id
  LEFT JOIN marking_score_profile_options mspo
    ON mspo.id = mrs.score_option_id
  LEFT JOIN marking_score_options mso
    ON mso.id = mspo.score_option_id
  LEFT JOIN marking_score_profiles msp
    ON msp.id = mwi.score_profile_id
  LEFT JOIN marking_score_profile_flags mspf
    ON mspf.id = mrs.score_flag_id
  LEFT JOIN marking_score_options msf
    ON msf.id = mspf.score_option_id
  ORDER BY read_table.claimed_batch_group_id, read_table.read_id
  ;
`

export const LOCAL_SCORING_STATS_STUDENT_META = `
    select mcbr.claimed_batch_group_id
    , ta.uid
    , u.first_name
    , u.last_name 
    , sc.id sc_id
    , s.name school_name
    , s.foreign_id school_code
    from marking_claimed_batch_responses mcbr 
        join marking_window_items mwi 
        on mcbr.window_item_id = mwi.id
        and mwi.marking_window_id = :mw_id
    join marking_taqr_cache mtc 
        on mcbr.taqr_id = mtc.taqr_id 
        and mwi.marking_window_id = mtc.marking_window_id 
    join test_attempts ta 
        on ta.id = mtc.ta_id
    join users u 
        on u.id = ta.uid
    join test_sessions ts 
    	on ta.test_session_id = ts.id 
	join school_class_test_sessions scts 
		on scts.test_session_id = ts.id 
	join school_classes sc 
		on sc.id = scts.school_class_id 
    join schools s 
		on sc.schl_group_id = s.group_id 
    where mcbr.claimed_batch_group_id in (:mcbgIds)
        group by mcbr.claimed_batch_group_id;
`

export const LOCAL_SCORING_STATS_MARKER_META = `
    select mcb.uid
    , mcbr.claimed_batch_group_id
    , mcbr.marker_read_id
    , mcbr.tls_id
    , mwum.value marker_number
    , CASE WHEN (ur.role_type IS NULL and tls_id IS NOT NULL) THEN 1 ELSE 0 END is_local_score
    , mwi.marking_window_id 
    from marking_claimed_batch_responses mcbr 
    join marking_window_items mwi 
    	on mcbr.window_item_id = mwi.id
    	and mwi.marking_window_id = :mw_id
    join marking_claimed_batches mcb 
    	on mcbr.claimed_batch_id = mcb.id 
	join marking_window_user_meta mwum 
		on mwum.marker_uid  = mcb.uid 
		and mwum.marking_window_id = mwi.marking_window_id 
		and mwum.meta_key = 'Marker_Number'
    join marking_windows mw
        on mw.id = mwi.marking_window_id 
    left join user_roles ur 
        on ur.uid = mcb.uid 
        and ur.group_id = mw.group_id 
        and ur.role_type = 'mrkg_ctrl'
    where mcbr.claimed_batch_group_id in (:mcbgIds)
	group by mcbr.claimed_batch_group_id, mcbr.marker_read_id;
`

export const LOCAL_SCORING_STATS_LAST_EXPORT = `
    select dej.id 
        , dej.completed_on 
        , dej.urls 
    from data_export_jobs dej
    join marking_local_scoring_exports mlse 
        on mlse.data_export_job_id = dej.id
        and mlse.marking_window_id = dej.marking_window_id 
    where dej.marking_window_id = :mw_id
        and dej.status = 'COMPLETED'
    group by dej.id
    order by dej.id desc;
`

export const LOCAL_SCORING_STATS_MW_LOCAL_SCOR_AVAIL = `
    select MAX(twtt.is_local_score) is_local_score_available
        , mw.name 
    from marking_windows mw 
    join marking_window_test_window mwtw 
        on mwtw.marking_window_id = mw.id 
        and mwtw.is_removed = 0
    join test_windows tw 
        on mwtw.test_window_id = tw.id
        and tw.is_allow_local_scoring_report = 1
    join test_window_td_alloc_rules twtar 
        on twtar.test_window_id = tw.id 
        and twtar.is_custom = 0
    join test_window_td_types twtt 
        on twtt.type_slug = twtar.type_slug
        and twtt.test_window_id is null
        and twtt.is_revoked = 0
    where mw.id = :mw_id
    group by mw.id;
`

export const LOCAL_SCORING_STATS_MW_NUM_LOCAL_SCORE = `
    select COUNT(DISTINCT mcbr.claimed_batch_group_id) ls_num
    from marking_claimed_batch_responses mcbr 
    join marking_window_items mwi 
        on mcbr.window_item_id = mwi.id 
    where mwi.marking_window_id = :mw_id
        and (mcbr.is_invalid = 0 OR mcbr.is_invalid IS NULL)
        and mcbr.is_revoked = 0
        and mcbr.tls_id is not null;
`