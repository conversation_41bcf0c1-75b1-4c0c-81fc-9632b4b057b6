// const taqr_nr_clause = `taqr.is_nr is null`; //

export const SQL_SCAN_TAQR_REVIEWED = `
    -- SQL_SCAN_TAQR_REVIEWED
select *
from (
    select mw_id
         , taqr_id
         , sum(case when srbr_n >= 1 then 1 else 0 end) review_1
         , sum(case when srbr_n >= 2 then 1 else 0 end) review_2
    from (
        select srtp.mw_id, srtp.taqr_id, count(srbr.id) srbr_n, ifnull(max(srbr.is_flagged), 0) is_flagged
        from scan_review_taqr_pool srtp
        left join scan_review_batch_responses srbr
            on srbr.taqr_id = srtp.taqr_id
            and srbr.is_complete = 1
            and srbr.is_revoked = 0
        where srtp.is_locked = 0
        and srtp.mw_id = ?
        group by srtp.mw_id, srtp.taqr_id
    ) t
    where is_flagged = 0
    group by taqr_id
) t2
where review_2 = 1
;
`;

// [marking_window_id]
export const SQL_SCAN_REVIEW_TAQR_POOL = (include_recent_reuploads: boolean, include_sample_schools: boolean, days: number, include_not_closed_sessions: boolean = false) => {
  return `
  -- SQL_SCAN_REVIEW_TAQR_POOL
  select /*+ MAX_EXECUTION_TIME(1440000000)*/
        test_question_id
      , taqr_id
      , tw_id
      , mw_id
      , cached_tasr_id
      , scan_uploaded_on
      , pooled_on
      , srtp_id
  from (
      select taqr.test_question_id
          , taqr.id taqr_id
          , mwtw.test_window_id tw_id
          , mwtw.marking_window_id mw_id
          , srtp.id srtp_id
          , tasr.id cached_tasr_id
          , tasr.uploaded_on as scan_uploaded_on
          , srtp.created_on as pooled_on
      from marking_window_items mwi
      join marking_window_test_window mwtw
        on mwtw.marking_window_id = mwi.marking_window_id
      join test_sessions ts
        on ts.test_window_id = mwtw.test_window_id
        ${!include_not_closed_sessions ? 'and ts.is_closed = 1' : ''}
      join school_class_test_sessions scts
        on scts.test_session_id = ts.id
      join school_classes sc
        on sc.id = scts.school_class_id
      join schools s
        on s.group_id = sc.schl_group_id
      join school_districts sd
        on sd.group_id = s.schl_dist_group_id
        ${include_sample_schools ? `` : `and sd.is_sample = 0`}
      join test_attempts ta
        on ts.id = ta.test_session_id
        and ta.started_on is not null
      join test_attempt_question_responses taqr
        on mwi.item_id = taqr.test_question_id
        and ta.id = taqr.test_attempt_id
      join test_attempt_scan_responses tasr
        on tasr.taqr_id = taqr.id
        and tasr.is_discarded = 0
        and tasr.is_pooled = 0
        and tasr.is_allow_review_bypass = 0
      left join scan_review_taqr_pool srtp
          on srtp.taqr_id  = tasr.taqr_id
      where mwi.marking_window_id in (?)
      group by tasr.taqr_id
  ) t
  where srtp_id is null
  ${include_recent_reuploads ? `or (pooled_on < scan_uploaded_on && scan_uploaded_on > NOW() - INTERVAL ${days} DAY)` : ``}
  ;
  `
};

export const SQL_TEST_ATTEMPT_TO_POOL_BY_TA_ID = (include_scan_records: boolean) => {
  return `
    select /*+ MAX_EXECUTION_TIME(1440000000)*/ * from (
      select /*+ MAX_EXECUTION_TIME(1440000000)*/ ta.id ta_id
          , max(mtc.id) mtc_id
          , max(tasr.id) tasr_id
      from marking_window_items mwi
      join marking_window_test_window mwtw
        on mwtw.marking_window_id = mwi.marking_window_id
      join test_attempts ta
        on ta.id in (:ta_ids)
      join test_attempt_question_responses taqr
        on mwi.item_id = taqr.test_question_id
        and ta.id = taqr.test_attempt_id
        and taqr.is_invalid = 0
      join test_window_td_alloc_rules twtar
        on ta.twtdar_id = twtar.id
        and twtar.is_custom = 0
      join test_question_register tqr
        on tqr.test_design_id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
        and tqr.lang = mwi.lang
      left join marking_taqr_cache mtc 
        on mtc.taqr_id = taqr.id
          and mtc.mwi_id = mwi.id
      left join test_attempt_scan_responses tasr 
 		on taqr.id  = tasr.taqr_id 
      where mwi.marking_window_id in (:marking_window_id)
      group by ta.id
    ) t
    where mtc_id is null 
    ${include_scan_records?"":"and tasr_id is null"}
    ;
  `
}


// {marking_window_id, withholdCategory, twtarIds}
export const SQL_TEST_ATTEMPT_FOR_MW = (include_not_closed_sessions: boolean = false, include_sample_schools: boolean = false, filter_by_twtar_ids: boolean =false) => {
  return `
  select /*+ MAX_EXECUTION_TIME(1440000000)*/ ta.id ta_id
    from marking_window_items mwi
    join marking_window_test_window mwtw
      on mwtw.marking_window_id = mwi.marking_window_id
    join test_sessions ts
      on ts.test_window_id = mwtw.test_window_id
      ${!include_not_closed_sessions ? 'and ts.is_closed = 1' : ''}
      ${!include_not_closed_sessions ? 'and ts.closed_on < DATE_ADD(NOW(), INTERVAL -1 DAY)' : ''}
    join school_class_test_sessions scts
      on scts.test_session_id = ts.id
    join school_classes sc
      on sc.id = scts.school_class_id
    join schools s
      on s.group_id = sc.schl_group_id
    join school_districts sd
      on sd.group_id = s.schl_dist_group_id
      ${include_sample_schools ? '' : 'and sd.is_sample = 0'}
    join test_attempts ta
      on ts.id = ta.test_session_id
      and ta.started_on is not null
      and ta.is_invalid = 0
    join test_window_td_alloc_rules twtar
      on ta.twtdar_id = twtar.id
      and twtar.is_custom = 0
      ${filter_by_twtar_ids?`and ta.twtdar_id in (:twtarIds)`:``}
    left join tw_exceptions_students tes 
      on tes.uid = ta.uid
      and tes.test_window_id = twtar.test_window_id
      and (tes.test_attempt_id = ta.id or tes.test_attempt_id is null)
      and tes.category = :withholdCategory
      and tes.is_revoked = 0
    where mwi.marking_window_id in (:marking_window_id) and tes.id is null
    group by ta.id
`
}

// {marking_window_id, withholdCategory, twtarIds}
export const SQL_TEST_ATTEMPT_FOR_MW_TC = (include_not_closed_sessions: boolean = false, include_sample_schools: boolean = false, filter_by_twtar_ids: boolean =false) => {
  return `
 select /*+ MAX_EXECUTION_TIME(1440000000)*/ ta.id ta_id
    from marking_window_items mwi
    join marking_window_test_window mwtw
      on mwtw.marking_window_id = mwi.marking_window_id
    join test_sessions ts
      on ts.test_window_id = mwtw.test_window_id
      ${!include_not_closed_sessions ? 'and ts.is_closed = 1' : ''}
    join institutions i -- like a school
    on i.group_id  = ts.instit_group_id 
    join certification_bodies cb -- like school district
	    on cb.id = i.jurisdiction_id 
      ${include_sample_schools ? '' : 'and cb.is_sample = 0'}
      ${include_sample_schools ? '' : 'and i.jurisdiction_id not in (:sample_jd_ids)'}
    join test_attempts ta 
	    on ts.id = ta.test_session_id 
	   	and ta.is_closed = 1
	    and ta.is_invalid = 0
	    and ta.started_on IS NOT null
	join test_window_td_alloc_rules twtar 
	    on ta.twtdar_id = twtar.id 
      and twtar.is_custom = 0
	join test_attempt_question_responses taqr 
	    on mwi.item_id = taqr.test_question_id
	    and taqr.test_attempt_id = ta.id 
       ${filter_by_twtar_ids?`and ta.twtdar_id in (:twtarIds)`:``}
  left join tw_exceptions_students tes 
    on tes.uid = ta.uid
    and tes.test_window_id = twtar.test_window_id
    and (tes.test_attempt_id = ta.id or tes.test_attempt_id is null)
    and tes.category = :withholdCategory
    and tes.is_revoked = 0
  where mwi.marking_window_id in (:marking_window_id) and tes.id is null
  group by ta.id
`
}


// Which TAQRs get pooled to scoring
// [marking_window_id]
export const SQL_MARKING_WINDOW_TAQR_RAW = (include_not_closed_sessions: boolean = false, include_sample_schools: boolean = false, bypass_scan_review: boolean = false) => {
  return `
  -- SQL_MARKING_WINDOW_TAQR_RAW
  select taqr.id
     , taqr.marking_window_id
     , taqr.mwi_id
     , taqr.test_attempt_id
     , taqr.taqr_id
     , taqr.schl_group_id
     , taqr.is_material
     , taqr.test_question_id
     , srtp_id
     , is_scan_confirmed
     , taqr.item_id_map 
     , taqr.response_raw
from (
  select taqr.*
        , mtc.id mtc_id
      , srtp.id srtp_id
      , srtp.is_locked is_scan_confirmed
      , mwtd.item_id_map
      , tasr.is_allow_review_bypass
      , tasr.id tasr_id
  from (
    select taqr.id id
          , taqr.test_attempt_id
          , taqr.response_raw
          , taqr.test_question_id
          , taqr.is_paper_format
          , mwi.marking_window_id
          , mwi.id mwi_id
          , mwi.batch_alloc_policy_id
          , mwi.group_to_mwi_id
          , taqr.id taqr_id
          , sc.schl_group_id
          , ifNull(mwtw.is_material, 0) is_material
    from marking_window_items mwi
    join marking_window_test_window mwtw
      on mwtw.marking_window_id = mwi.marking_window_id
    join test_sessions ts
      on ts.test_window_id = mwtw.test_window_id
      ${!include_not_closed_sessions ? 'and ts.is_closed = 1' : ''}
      ${!include_not_closed_sessions ? 'and ts.closed_on < DATE_ADD(NOW(), INTERVAL -1 DAY)' : ''}
    join school_class_test_sessions scts
      on scts.test_session_id = ts.id
    join school_classes sc
      on sc.id = scts.school_class_id
    join schools s
      on s.group_id = sc.schl_group_id
    join school_districts sd
      on sd.group_id = s.schl_dist_group_id
      ${include_sample_schools ? '' : 'and sd.is_sample = 0'}
    join test_attempts ta
      on ts.id = ta.test_session_id
      and ta.started_on is not null
      and ta.id in (:test_attempt_ids)
    join test_attempt_question_responses taqr
      on mwi.item_id = taqr.test_question_id
      and ta.id = taqr.test_attempt_id
      and taqr.is_invalid = 0
    join test_window_td_alloc_rules twtar
      on ta.twtdar_id = twtar.id
      and twtar.is_custom = 0
    join test_question_register tqr
      on tqr.test_design_id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
      and tqr.lang = mwi.lang
    where mwi.marking_window_id in (:marking_window_id)
      and taqr.is_invalid = 0
      -- and taqr.is_nr = 0
  ) taqr
  left join marking_taqr_cache mtc on mtc.taqr_id = taqr.id
    and mtc.mwi_id = taqr.mwi_id
    and mtc.marking_window_id in (:marking_window_id)
  left join marking_window_task_definitions mwtd
    on mwtd.marking_window_id = taqr.marking_window_id
    and mwtd.batch_alloc_policy_id = taqr.batch_alloc_policy_id
    and mwtd.cached__group_to_mwi_id = taqr.group_to_mwi_id
  left join scan_review_taqr_pool srtp on srtp.taqr_id = taqr.taqr_id
  left join test_attempt_scan_responses tasr on tasr.taqr_id = taqr.taqr_id 
  group by taqr.id, taqr.mwi_id
) taqr
where mtc_id is null
and  (
  (taqr.is_paper_format = 0) or (
    taqr.is_paper_format = 1 
    and tasr_id is not null 
    ${ bypass_scan_review ? '' : 'and (is_scan_confirmed = 1 or is_allow_review_bypass = 1)'}
  )
)
`;
}

export const SQL_MARKING_WINDOW_TAQR_RAW_TC = (include_not_closed_sessions: boolean = false, include_sample_schools: boolean = false, bypass_scan_review: boolean = false) => {
  return `
  -- SQL_MARKING_WINDOW_TAQR_RAW
  select taqr.id
     , taqr.marking_window_id
     , taqr.mwi_id
     , taqr.test_attempt_id
     , taqr.taqr_id
     , taqr.schl_group_id
     , taqr.is_material
     , taqr.test_question_id
     , srtp_id
     , is_scan_confirmed
     , taqr.item_id_map 
     , taqr.response_raw
from (
  select taqr.*
        , mtc.id mtc_id
      , srtp.id srtp_id
      , srtp.is_locked is_scan_confirmed
      , mwtd.item_id_map
      , tasr.is_allow_review_bypass
      , tasr.id tasr_id
  from (
    select taqr.id id
          , taqr.test_attempt_id
          , taqr.response_raw
          , taqr.test_question_id
          , taqr.is_paper_format
          , mwi.marking_window_id
          , mwi.id mwi_id
          , mwi.batch_alloc_policy_id
          , mwi.group_to_mwi_id
          , taqr.id taqr_id
          , i.group_id schl_group_id -- to be fix later
          , ifNull(mwtw.is_material, 0) is_material
    from marking_window_items mwi
    join marking_window_test_window mwtw
      on mwtw.marking_window_id = mwi.marking_window_id
    join test_sessions ts
      on ts.test_window_id = mwtw.test_window_id
      ${!include_not_closed_sessions ? 'and ts.is_closed = 1' : ''}
    join institutions i -- like a school
    	on i.group_id  = ts.instit_group_id 
    join certification_bodies cb -- like school district
    	on cb.id = i.jurisdiction_id 
      ${include_sample_schools ? '' : 'and cb.is_sample = 0'}
      ${include_sample_schools ? '' : 'and i.jurisdiction_id not in (:sample_jd_ids)'}
    join test_attempts ta
      on ts.id = ta.test_session_id
      and ta.started_on is not null
      and ta.id in (:test_attempt_ids)
    join test_attempt_question_responses taqr
      on mwi.item_id = taqr.test_question_id
      and ta.id = taqr.test_attempt_id
      and taqr.is_invalid = 0
    join test_window_td_alloc_rules twtar
      on ta.twtdar_id = twtar.id
      and twtar.is_custom = 0
    join test_question_register tqr
      on tqr.test_design_id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
      and tqr.lang = mwi.lang
    where mwi.marking_window_id in (:marking_window_id)
      and taqr.is_invalid = 0
      -- and taqr.is_nr = 0
  ) taqr
  left join marking_taqr_cache mtc on mtc.taqr_id = taqr.id
    and mtc.mwi_id = taqr.mwi_id
    and mtc.marking_window_id in (:marking_window_id)
  left join marking_window_task_definitions mwtd
    on mwtd.marking_window_id = taqr.marking_window_id
    and mwtd.batch_alloc_policy_id = taqr.batch_alloc_policy_id
    and mwtd.cached__group_to_mwi_id = taqr.group_to_mwi_id
  left join scan_review_taqr_pool srtp on srtp.taqr_id = taqr.taqr_id
  left join test_attempt_scan_responses tasr on tasr.taqr_id = taqr.taqr_id 
  group by taqr.id, taqr.mwi_id
) taqr
where mtc_id is null
and  (
  (taqr.is_paper_format = 0) or (
    taqr.is_paper_format = 1 
    and tasr_id is not null 
    ${ bypass_scan_review ? '' : 'and (is_scan_confirmed = 1 or is_allow_review_bypass = 1)'}
  )
);
`;
}

// inner query
// select taqr.id id
//      , mwi.marking_window_id
//      , mwi.id mwi_id
//      , taqr.id taqr_id
//      , sc.schl_group_id
//      , ifNull(mwtw.is_material, 0) is_material
// from marking_window_items mwi
// join marking_window_test_window mwtw
//   on mwtw.marking_window_id = mwi.marking_window_id
// join test_sessions ts
//   on ts.test_window_id = mwtw.test_window_id
// join school_class_test_sessions scts
//   on scts.test_session_id = ts.id
// join school_classes sc
//   on sc.id = scts.school_class_id
// join test_attempts ta
//   on ts.id = ta.test_session_id
//   and ta.is_closed = 1
//   and ta.started_on is not null
// join test_attempt_question_responses taqr
//   on mwi.item_id = taqr.test_question_id
//   and ta.id = taqr.test_attempt_id
// where mwi.marking_window_id in (7,8)
//   and taqr.is_invalid = 0
//   and taqr.is_nr = 0
// `


`
select taqr.id
	 , taqr.marking_window_id
     , taqr.mwi_id
	 , taqr.taqr_id
	 , taqr.schl_group_id
	 , taqr.is_material
from (
	select taqr.*
	     , mtc.id mtc_id
	from (
		select taqr.id id
		     , mwi.marking_window_id
		     , mwi.id mwi_id
		     , taqr.id taqr_id
		     , sc.schl_group_id
		     , ifNull(mwtw.is_material, 0) is_material
		from marking_window_items mwi
		join marking_window_test_window mwtw
		  on mwtw.marking_window_id = mwi.marking_window_id
		join test_sessions ts
		  on ts.test_window_id = mwtw.test_window_id
		join school_class_test_sessions scts
		  on scts.test_session_id = ts.id
		join school_classes sc
		  on sc.id = scts.school_class_id
		join schools s
		  on s.group_id = sc.schl_group_id
		join school_districts sd
		  on sd.group_id = s.schl_dist_group_id
		  and sd.is_sample  = 0
		join test_attempts ta
		  on ts.id = ta.test_session_id
		  and ta.is_closed = 1
		  and ta.started_on is not null
		join test_attempt_question_responses taqr
		  on mwi.item_id = taqr.test_question_id
		  and ta.id = taqr.test_attempt_id
		where mwi.marking_window_id in (8)
		  and taqr.is_invalid = 0
		  and taqr.is_nr = 0
	) taqr
	left join marking_taqr_cache mtc on mtc.taqr_id = taqr.id and mtc.mwi_id = taqr.mwi_id
) taqr
where mtc_id is null
;
`
export const SQL_STUDENT_ACCOMMODATION = `
  select ua.uid
    , acc.accommodation_slug 
    , ua.extra_notes_value 
    , acc.id 
    , acc.name 
    , acc.system_slug 
    , acc.accommodation_type accommodation_type 
    , ua.accommodation_value
    , ta.id ta_id
  from test_attempts ta
  join marking_windows mw
  	on mw.id in (:marking_window_id)
  join marking_window_test_window mwtw 
    on mw.id = mwtw.marking_window_id 
    and mwtw.is_removed = 0
  join test_windows tw 
    on mwtw.test_window_id = tw.id 
  join users_accommodations ua 
    on ua.uid = ta.uid
    and ua.is_revoked = 0
    and ua.accommodation_value = 1
  join accommodations acc
    on acc.id = ua.accommodation_id 
 	and tw.type_slug = acc.system_slug 
  where ta.id in (:test_attempt_ids);
`
export const SQL_MISSING_TAQRS_FROM_TEST_ATTEMPTS = `
  -- SQL_MISSING_TAQRS_FROM_TEST_ATTEMPTS
  SELECT * FROM (
    SELECT taqr.test_attempt_id, count(0) taqr_count, correct_taqr_counts.taqr_count correct_taqr_count
    FROM marking_window_items mwi
    JOIN test_attempt_question_responses taqr
        on taqr.test_question_id = mwi.item_id
      and taqr.test_attempt_id IN (:test_attempt_ids)
      and taqr.is_invalid = 0
    JOIN test_attempts ta 
      ON taqr.test_attempt_id = ta.id
    JOIN test_window_td_alloc_rules twtar 
      ON ta.twtdar_id = twtar.id 
      AND twtar.lang = mwi.lang
      and twtar.is_custom = 0
    JOIN marking_score_profile_groups mspg 
      on mspg.id = mwi.score_profile_group_id
    JOIN (
      SELECT assessment_slug, count(0) taqr_count FROM marking_score_profile_groups mspg 
      GROUP by assessment_slug
    ) correct_taqr_counts
      on correct_taqr_counts.assessment_slug = mspg.assessment_slug
    WHERE mwi.marking_window_id = :marking_window_id
      AND mwi.id = mwi.sync_batches_to_wmi_id
    group by taqr.test_attempt_id
  ) taqr_counts
  WHERE correct_taqr_count != taqr_count
  ;
`;

export const SQL_MISSING_TAQR_ALIGNMENT_INFO = `
SELECT taqr.id, taqr.test_question_id, ta.id test_attempt_id, tqr.question_id req_question_id 
FROM test_attempts ta 
  JOIN test_window_td_alloc_rules twtar 
    ON twtar.id = ta.twtdar_id 
    and twtar.is_custom = 0
  JOIN test_question_register tqr 
    ON tqr.lang = twtar.lang
    AND tqr.test_design_id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
  LEFT JOIN test_attempt_question_responses taqr 
    ON taqr.test_question_id = tqr.question_id 
    AND taqr.test_attempt_id = ta.id
    AND taqr.is_invalid = 0 -- when checking missing taqr, same filter was applied
  WHERE ta.id in (:test_attempt_ids)
	  AND taqr.id IS NULL
order by ta.id;
`

export const SQL_MARKING_WINDOW_SUPPLEMENT_TAQR_RAW = (include_not_closed_sessions: boolean = false, include_sample_schools: boolean = false) => {
  return `
    -- SQL_MARKING_WINDOW_SUPPLEMENT_TAQR_RAW
    select taqr.id
          , taqr.marking_window_id
          , taqr.mwi_id
          , taqr.taqr_id
          , taqr.schl_group_id
    from (
      select taqr.*
          , mstc.id mstc_id
      from (
        select taqr.id id
              , mwi.marking_window_id
              , mwi.id mwi_id
              , taqr.id taqr_id
              , sc.schl_group_id
        from marking_window_items mwi
        join marking_window_test_window mwtw
          on mwtw.marking_window_id = mwi.marking_window_id
        join test_sessions ts
          on ts.test_window_id = mwtw.test_window_id
          ${!include_not_closed_sessions ? 'and ts.is_closed = 1' : ''}
          ${!include_not_closed_sessions ? 'and ts.closed_on < DATE_ADD(NOW(), INTERVAL -1 DAY)' : ''}
        join school_class_test_sessions scts
          on scts.test_session_id = ts.id
        join school_classes sc
          on sc.id = scts.school_class_id
        join schools s
          on s.group_id = sc.schl_group_id
        join school_districts sd
          on sd.group_id = s.schl_dist_group_id
          ${include_sample_schools ? '' : 'and sd.is_sample = 0'}
        join test_attempts ta
          on ts.id = ta.test_session_id
          and ta.started_on is not null
        join marking_window_item_supplements mwis
          on mwi.id = mwis.mwi_id
        join test_attempt_question_responses taqr
          on mwis.item_id = taqr.test_question_id
          and ta.id = taqr.test_attempt_id
        where mwi.marking_window_id in (?)
          and taqr.is_invalid = 0
          -- and taqr.is_nr = 0
      ) taqr
      left join marking_supplement_taqr_cache mstc on mstc.taqr_id = taqr.id
      group by taqr.id, taqr.mwi_id
    ) taqr
    where mstc_id is null
  ;`
}
const marking_window_taqr_pre = (isMaterialsIncl : boolean = true) => `
  from marking_taqr_cache mtc
`;
// and ${taqr_nr_clause} and ${taqr_invalid_clause}
// and mtc.is_material = 0
const marking_window_taqr_post = `
    where mtc.mwi_id = ? -- TO BE FED IN BY API
`;

// [uid, marking_window_item_id]
export const SQL_NUM_TQAR_BY_SCH = `
  -- SQL_NUM_TQAR_BY_SCH
  select mtc.schl_group_id
        , count(0) as num_responses
        , sum(mmcbr.counter) - sum(mmcbr.rescore_indic_counter) as num_assigned
  from marking_taqr_cache mtc
  left join (
    -- select * from ( -- to force temp table
      select mcbr.id
            , mcbr.taqr_id
            , mcbr.is_marked
            , mcb.marking_window_item_id
            , 1 as counter
            , min(mcbr.is_rescore_indic) rescore_indic_counter
      from marking_claimed_batches mcb
      join marking_claimed_batch_responses mcbr
        on mcb.id = mcbr.claimed_batch_id
        and mcbr.is_revoked = 0
        and mcbr.is_invalid is null
        and ((mcbr.is_rescore_indic =0) or (mcbr.is_rescore_indic =1 and uid != :uid))
        and mcbr.is_validity is null
      where mcb.marking_window_item_id = :mwi_id
        and mcb.is_training = 0
      group by mcb.marking_window_item_id, mcbr.taqr_id
    -- ) mmcbr_inner
  ) mmcbr
    on mmcbr.taqr_id = mtc.taqr_id
    and mmcbr.marking_window_item_id = mtc.mwi_id
    where mtc.mwi_id = :mwi_id
    and mtc.is_material = 0
  group by mtc.schl_group_id
;`;

// [marking_window_item_id, uid]
export const SQL_NUM_TQAR_SCORED_ = `
    -- SQL_NUM_TQAR_SCORED_

`;

// [marking_window_item_id, uid]
export const SQL_NUM_TQAR_SCORED_BY_SCH_AND_UID = `
    -- SQL_NUM_TQAR_SCORED_BY_SCH_AND_UID
    select mcbr.schl_group_id, count(0) as num_responses
    from marking_claimed_batches mcb
    join marking_claimed_batch_responses mcbr
      on mcb.id = mcbr.claimed_batch_id
      and mcbr.is_revoked = 0
    where mcb.marking_window_item_id = ?
      and mcb.uid = ?
      and mcb.is_training = 0
    group by mcbr.schl_group_id
;
`;

// [marking_window_item_id, marking_window_item_id, excluded_schl_group_id]
// deprecated, this query is not ued anywhere
export const SQL_ITEM_TQAR_AVAIL = `
    -- SQL_ITEM_TQAR_AVAIL
    select r.taqr_id, r.schl_group_id
    from (
        select mtc.taqr_id as taqr_id
             , mtc.schl_group_id
             , mmcbr.id as resp_claim_id
        ${marking_window_taqr_pre(false)}
        left join (
            select mcbr.id, mcbr.taqr_id, mcb.marking_window_item_id
            from marking_claimed_batches mcb
            join marking_claimed_batch_responses mcbr
              on mcb.id = mcbr.claimed_batch_id
              and mcbr.is_revoked = 0
              and mcbr.is_invalid is null
            where mcb.marking_window_item_id = ?
              and mcb.is_training = 0
        ) mmcbr
            on mmcbr.taqr_id = mtc.taqr_id
            and mmcbr.marking_window_item_id = mtc.mwi_id
        ${marking_window_taqr_post}
          and mtc.is_material = 0
          and mtc.schl_group_id not in (?)
    ) r
    where r.resp_claim_id is null
`;

// [marking_window_item_id]
export const SQL_ITEM_BATCH_POLICIES = `
  -- SQL_ITEM_BATCH_POLICIES
  select mbap.*
  from marking_window_items mwi
  join marking_batch_alloc_policies mbap
    on mbap.id = mwi.batch_alloc_policy_id
  where mwi.id = ?
`

export const SQL_ITEMS_BATCH_POLICIES = `
  -- SQL_ITEMS_BATCH_POLICIES
  select mwi.id mwi_id
       , mbap.id scoring_policy_id
       , read_rules
  from marking_window_items mwi
  join marking_batch_alloc_policies mbap
    on mbap.id = mwi.batch_alloc_policy_id
  where mwi.id IN (:mwi_ids)
`

// [claimed_batch_id]

export const SQL_BATCH_RESP_RESPONSES = `
  -- SQL_BATCH_RESP_RESPONSES
select mcbr.id
  , mcbr.taqr_id
  , mtc.ta_id
  , mcbr.is_marked
  , mtc.student_defined_label
  , mcbr.marked_on
  , mcbr.is_scale_supressed
  , mcbr.supervisor_note
  , is_validity
  , mcbr.claimed_batch_group_id
  , msp.is_non_scored_profile 
from marking_claimed_batches mcb
join marking_claimed_batch_responses mcbr
  on mcbr.claimed_batch_id = mcb.id
join marking_taqr_cache mtc
  on mtc.mwi_id = mcb.marking_window_item_id
  and mtc.taqr_id = mcbr.taqr_id
join marking_window_items mwi on
 mcbr.window_item_id = mwi.id
 join marking_score_profiles msp 
 on mwi.score_profile_id = msp.id 
where mcb.id  = ?
group by mcbr.id
order by mcbr.id asc
;`

// [claimed_batch_id]
export const SQL_BATCH_RESP_SCORES = `
  -- SQL_BATCH_RESP_SCORES
  SELECT mrs.*
  FROM marking_claimed_batch_responses mcbr
  join marking_response_scores mrs
    on mrs.batch_response_id = mcbr.id
  where mcbr.claimed_batch_id = ?
;`

// [claimed_batch_id]
export const SQL_BATCH_RESP_SCORES_PAIRED_DRAFT = `
  -- SQL_BATCH_RESP_SCORES_PAIRED_DRAFT, < 0.1 sec
  select mrspd.* , mcbrpd.batch_response_id 
  from marking_response_scores_paired_draft mrspd 
  join marking_claimed_batch_responses_paired_draft mcbrpd 
    on mrspd.batch_response_paired_draft_id = mcbrpd.id 
    and mcbrpd.is_revoked = 0
  join marking_claimed_batches_paired_draft mcbpd 
    on mcbrpd.claimed_batch_paired_draft_id = mcbpd.id 
    and mcbpd.is_revoked = 0
  where mcbpd.marking_claimed_batch_id = ?
    and mcbpd.uid = ?
;`

export const SQL_BATCH_RESP_SCORES_PAIRED_DRAFT_ALL_STAGE_1 = `
  -- SQL_BATCH_RESP_SCORES_PAIRED_DRAFT, < 0.2 sec
  select mrspd.* 
  , mcbrpd.batch_response_id 
  , u.id scorer_uid
  , concat(u.first_name, " ", u.last_name) scorer_name
  from marking_response_scores_paired_draft mrspd 
  join marking_claimed_batch_responses_paired_draft mcbrpd 
    on mrspd.batch_response_paired_draft_id = mcbrpd.id 
    and mcbrpd.is_revoked = 0
  join marking_claimed_batches_paired_draft mcbpd 
    on mcbrpd.claimed_batch_paired_draft_id = mcbpd.id 
    and mcbpd.is_revoked = 0
  left join users u
    on u.id = mcbpd.uid
  where mcbpd.marking_claimed_batch_id = ?
;`

// [window_item_id]
export const SQL_ITEM_RULES = `
  -- SQL_ITEM_RULES
  select mwi.id marking_window_item_id, mwir.* from marking_window_item_rules mwir
  join marking_score_profile_options mspo
    on mspo.id = mwir.score_option_id
  join marking_window_items mwi 
    on mwi.score_profile_id = mspo.score_profile_id 
    and mwi.id in (?)
  where mwir.is_revoked = 0
  ;
`

// [window_item_id]
export const SQL_ITEM_SCORE_OPTIONS = `
  -- SQL_ITEM_SCORE_OPTIONS
  select mwi.id as mwi_id, mspo.id, mspo.order, mspo.is_offset, mso.slug, mso.value, mso.caption, mso.color, mspo.rubric_text, msp.is_non_scored_profile 
  from marking_window_items mwi
  join marking_score_profiles msp 
  on msp.id = mwi.score_profile_id
  join marking_score_profile_options mspo
    on mspo.score_profile_id = mwi.score_profile_id
    and mspo.is_revoked = 0
  join marking_score_options mso
    on mso.id = mspo.score_option_id
  where mwi.id in (?)
  order by mspo.score_profile_id, mspo.order
;`


// [window_item_id]
export const SQL_ITEM_SCORE_FLAGS = (isFilterToSensitive:boolean=false) => `
  -- SQL_ITEM_SCORE_FLAGS
  select mwi.id as mwi_id, mspf.id, mspf.order, mso.slug, mso.caption, mso.report_msg, mso.is_top_flag, mso.is_comment_req, mso.is_score_req, mso.is_score_allowed, mso.is_pre_score_flag, mso.slug_code
  from marking_window_items mwi
  join marking_score_profile_flags mspf
    on mspf.score_profile_id = mwi.score_profile_id
    and mspf.is_revoked = 0
  join marking_score_options mso
    on mso.id = mspf.score_option_id
  where mwi.id in (?)
  ${isFilterToSensitive? `and mso.is_sensitive = 1` : ''}
  order by mspf.order
;`

// [uid, uid, mw_id, SCAN_CHECK_COUNT, claimPoolSize]
export const SQL_SCAN_BATCH_RESP_EDIT = `
  -- SQL_SCAN_BATCH_RESP_EDIT

`

// [is_editing, uid, uid, mw_id, SCAN_CHECK_COUNT, claimPoolSize]
// [uid, SCAN_CHECK_COUNT, claimPoolSize]
export const SQL_SCAN_BATCH_RESP_REVIEW = (mw_id:number, isEditing:boolean) => `
  -- SQL_SCAN_BATCH_RESP_REVIEW
  select * from (
    select mwi.id mwi_id
        , srtp.taqr_id
        , srtp.cached_tasr_id tasr_id
        , ifnull(max(rev_other.prev), 0) rev_other_prev
        , ifnull(max(rev_self.prev), 0) rev_self_prev
        , rev_other.srb_id
    --
    -- all available responses
    from marking_window_items mwi
    join marking_window_test_window mwtw
      on mwtw.marking_window_id = mwi.marking_window_id
    join scan_review_taqr_pool srtp
        on srtp.tw_id = mwtw.test_window_id
        and srtp.test_question_id = mwi.item_id
        and srtp.is_locked = 0
    ${ isEditing ? `
      join scan_review_batch_responses srbr_preedit
      on srbr_preedit.taqr_id = srtp.taqr_id
      and srbr_preedit.is_flagged = 1
      and srbr_preedit.is_revoked = 0
    ` : ''}
    --
    -- taqrs marked by others  (in target editing mode)
    left join (
      select srtp.taqr_id
            , count(distinct srbr.id) prev
            , srb.id srb_id
        from marking_window_items mwi
        join marking_window_test_window mwtw
          on mwtw.marking_window_id = mwi.marking_window_id
        join scan_review_taqr_pool srtp
          on srtp.tw_id = mwtw.test_window_id
          and srtp.test_question_id = mwi.item_id
          and srtp.is_locked = 0
        join scan_review_batch_responses srbr
          on srbr.taqr_id = srtp.taqr_id
          and srbr.is_revoked = 0
        join scan_review_batches srb
          on srbr.srb_id = srb.id
          and srb.is_editing = ${ isEditing ? 1 : 0 }
        where mwi.marking_window_id = ${+mw_id}
        group by srtp.taqr_id
    ) rev_other on rev_other.taqr_id = srtp.taqr_id
    --
    -- taqrs marked by self  (in target editing mode)
    left join (
      select srtp.taqr_id
            , count(distinct srbr.id) prev
        from marking_window_items mwi
        join marking_window_test_window mwtw
          on mwtw.marking_window_id = mwi.marking_window_id
        join scan_review_taqr_pool srtp
          on srtp.tw_id = mwtw.test_window_id
          and srtp.test_question_id = mwi.item_id
          and srtp.is_locked = 0
        join scan_review_batch_responses srbr
          on srbr.taqr_id = srtp.taqr_id
          and srbr.is_revoked = 0
        join scan_review_batches srb
          on srbr.srb_id = srb.id
          and srb.is_editing = ${ isEditing ? 1 : 0 }
          and srb.uid = ?
        where mwi.marking_window_id = ${+mw_id}
        group by srtp.taqr_id
    ) rev_self on rev_self.taqr_id = srtp.taqr_id
    where mwi.marking_window_id = ${+mw_id}
    group by srtp.taqr_id
  ) t
  where rev_other_prev < ?
    and rev_self_prev = 0
  limit ?
`

// [window_item_id, uid, cutScore]
export const SQL_ITEM_FAILED_QT = `
  -- SQL_ITEM_FAILED_QT
  select mcb.id
  from marking_claimed_batches mcb
  join  marking_claimed_batch_responses mcbr
    on mcb.id  = mcbr.claimed_batch_id
  where mcb.marking_window_item_id = ?
    and mcb.component_type = 'QUALIFYING'
    and mcb.uid = ?
    and mcb.is_training = 1
    and mcbr.is_marked = 1
  group by mcb.id
  having count(case when
              mcbr.is_training_correct = 1
              then 1 end) / count(*) < ?
  order by mcb.created_on desc
;`

// [window_item_id]
export const SQL_ITEM_DISPLAY_CONFIG = `
  -- SQL_ITEM_DISPLAY_CONFIG
  select tq.config, mwi.lang
  from marking_window_items mwi
  join test_questions tq
    on mwi.item_id = tq.id
  where mwi.id = ?
;`

// {window_item_id}
export const SQL_MWI_ITEM_DISPLAY_MAP = ` /*SQL_MWI_ITEM_DISPLAY_MAP*/
  select mwi_sync.id mwi_id
       , mwi_sync.item_id 
  from marking_window_items mwi 
  join marking_window_items mwi_sync 
    on mwi_sync.group_to_mwi_id  = mwi.id
    and mwi_sync.sync_batches_to_wmi_id = mwi_sync.id 
  where mwi.id = :window_item_id
;`

// [window_item_id]
export const SQL_SCORING_ITEM_SUPPLEMENT_W_RESPONSE = `
  -- SQL_SCORING_ITEM_SUPPLEMENT_W_RESPONSE
  SELECT taqr2.id, mwis.item_id, tq.config, taqr2.response_raw
  FROM marking_claimed_batch_responses mcbr
  JOIN marking_window_item_supplements mwis
    on mwis.mwi_id = mcbr.window_item_id
  JOIN test_attempt_question_responses taqr
    ON taqr.id = mcbr.taqr_id
  JOIN test_attempt_question_responses taqr2
    ON taqr.test_attempt_id  = taqr2.test_attempt_id
    AND taqr2.test_question_id = mwis.item_id
  JOIN test_questions tq
    ON tq.id = mwis.item_id
  WHERE mcbr.id = :mcbr_id
  GROUP BY taqr2.id
;`

// [window_item_id]
export const SQL_ITEM_SCOR_MATERIALS = `
  -- SQL_ITEM_SCOR_MATERIALS
  select mwi.id
       , mwi.caption as mwi_name
       , mwi.guide_files_url as mwi_guides
       , mwi.rubric_url as mwi_rubric_url
       , mwi.anchors_url as mwi_anchors_url
       , mwi.reading_item_id
       , msp.description as msp_name
       , msp.guide1_url as msp_guide
       , msp.guide2_url as msp_video
  from marking_window_items mwi
  join marking_score_profiles msp
    on msp.id = mwi.score_profile_id
  where mwi.id = ?
`
// [window_id]
export const SQL_ITEM_LEAD_MATERIALS = `
  -- SQL_ITEM_LEAD_MATERIALS
select mwi.id
		   , mwi.group_to_mwi_id
       , mwi.slug, mwi.caption
       , mwi.guide_files_url
       , mwi.guide_files_updated_on
       , mwi.guide_files_updated_by_uid
       , mwi.rubric_url
       , mwi.rubric_updated_on
       , mwi.rubric_updated_by_uid
       , mwi.anchors_url
       , mwi.anchors_updated_on
       , mwi.anchors_updated_by_uid
       , mwi.reading_item_id
  from marking_window_items mwi
  where mwi.marking_window_id = ?
  and id = group_to_mwi_id
  order by id;
`

// [window_item_id]
export const SQL_ITEM_RUBRIC_OPTIONS = `
  -- SQL_ITEM_RUBRIC_OPTIONS
`

// [batch_response_id]
export const SQL_RESPONSE_STATE = ` /*SQL_RESPONSE_STATE*/
select taqr.response_raw
     , taqr.id taqr_id
     , taqr.test_attempt_id
     , mtc.student_accommodation_cache
     , mbap.is_accomm_displayed
from marking_claimed_batch_responses mcbr
join marking_claimed_batches mcb 
	on mcb.id = mcbr.claimed_batch_id 
join test_attempt_question_responses taqr
  on mcbr.taqr_id = taqr.id
join marking_taqr_cache mtc 
  on taqr.id = mtc.taqr_id 
  and mtc.mwi_id = mcb.marking_window_item_id 
join marking_window_items mwi 
  on mwi.id = mcbr.window_item_id 
join marking_batch_alloc_policies mbap 
  on mwi.batch_alloc_policy_id = mbap.id
where mcbr.id = ?
;`

// [taqr_id]
export const SQL_RAW_RESPONSE_STATE = `
-- SQL_RAW_RESPONSE_STATE
select taqr.response_raw, taqr.test_attempt_id, taqr.test_question_id
from test_attempt_question_responses taqr
where taqr.id = ?
;`

// [taqr_id]
export const SQL_IS_TAQR_PAPER_FORMAT = `
  -- SQL_IS_TAQR_PAPER_FORMAT, < 0.1 sec
  select 
  taqr.id
  , tqsi.is_paper_response
  from 
  test_attempt_question_responses taqr
  join test_attempts ta
    on ta.id = taqr.test_attempt_id
  join test_window_td_alloc_rules twtdar
    on twtdar.id = ta.twtdar_id
    and twtdar.is_custom = 0
  left join test_question_scoring_info tqsi
    on tqsi.item_id = taqr.test_question_id
      and tqsi.lang = twtdar.lang
      and tqsi.is_revoked = 0
  where taqr.id = ?
`

// [window_item_id, taqr_id]
export const SQL_RESP_SELECTION = `
-- SQL_RESP_SELECTION
select mrsel.taqr_id as id
     , mrsel.id as mrsel_id
     , mrsel.tag1
     , mrsel.tag2
     , mrsel.score_option_id
     , mrsel.rationale
     , mrsel.rationale_hash
     , mrset.response_set_num
     , mrset.response_set_order
     , mrs.is_revoked
from marking_response_selections mrsel
RIGHT join marking_response_set_selections mrset
  on mrset.selection_id = mrsel.id
  and mrset.window_item_id = mrsel.window_item_id
right join marking_response_set mrs
  on mrs.id = mrset.set_id
where mrsel.window_item_id = ?
  and mrsel.taqr_id = ?

  UNION

select mrsel.taqr_id as id
     , mrsel.id as mrsel_id
     , mrsel.tag1
     , mrsel.tag2
     , mrsel.score_option_id
     , mrsel.rationale
     , mrsel.rationale_hash
     , mrset.response_set_num
     , mrset.response_set_order
     , mrs.is_revoked
from marking_response_selections mrsel
LEFT join marking_response_set_selections mrset
  on mrset.selection_id = mrsel.id
  and mrset.window_item_id = mrsel.window_item_id
left join marking_response_set mrs
  on mrs.id = mrset.set_id
where mrsel.window_item_id = ?
  and mrsel.taqr_id = ?
;`

// [batch_response_id]
export const SQL_BATCH_RESP_SCORE_ENTRY_BY_ID= `
  -- SQL_BATCH_RESP_SCORE_ENTRY_BY_ID
  select id, score_option_id, score_flag_id, meta
  from marking_response_scores
  where batch_response_id = ?
;`

// [batch_response_id]
export const SQL_BATCH_RESP_SCORE_ENTRY_BY_ID_PAIRED_DRAFT= `
  -- SQL_BATCH_RESP_SCORE_ENTRY_BY_ID_PAIRED_DRAFT, < 0.1 sec
  select mrspd.id
  , mrspd.score_option_id
  , mrspd.score_flag_id
  , mrspd.meta
    from marking_response_scores_paired_draft mrspd 
  join marking_claimed_batch_responses_paired_draft mcbrpd 
    on mrspd.batch_response_paired_draft_id = mcbrpd.id 
    and mcbrpd.is_revoked = 0
    and mcbrpd.batch_response_id = ?
  join marking_claimed_batches_paired_draft mcbpd 
      on mcbrpd.claimed_batch_paired_draft_id = mcbpd.id 
      and mcbpd.uid = ?
;`

// [batch_response_id]
export const SQL_BATCH_RESP_SCORE_BOTTOM_FLAG_BY_ID= `
  -- SQL_BATCH_RESP_SCORE_BOTTOM_FLAG_BY_ID
   	select mrs.id
	, mrs.score_option_id
	, mrs.score_flag_id
	, mrs.meta 
	from marking_claimed_batch_responses mcbr
 	join (
	 select mcbr.claimed_batch_group_id 
	 from marking_claimed_batch_responses mcbr 
	 where mcbr.id = ?
 	)t
 	on mcbr.claimed_batch_group_id = t.claimed_batch_group_id 
 	join marking_response_scores mrs 
 	on mcbr.id = mrs.batch_response_id
 	join marking_score_profile_flags mspf
 	on mrs.score_flag_id = mspf.id 
 	and mspf.is_revoked = 0
 	join marking_score_options mso 
 	on mso.id = mspf.score_option_id
  and mso.is_revoked = 0  
;`

export const SQL_BATCH_RESP_SCORE_BOTTOM_FLAG_BY_ID_PAIRED_DRAFT= `
  -- SQL_BATCH_RESP_SCORE_BOTTOM_FLAG_BY_ID, < 0.2 sec
   	select mrs.id
	, mrs.score_option_id
	, mrs.score_flag_id
	, mrs.meta 
	from marking_claimed_batch_responses mcbr
 	join (
	 select mcbr.claimed_batch_group_id 
	 from marking_claimed_batch_responses mcbr 
	 where mcbr.id = ?
 	)t
 	on mcbr.claimed_batch_group_id = t.claimed_batch_group_id 
 	join marking_response_scores mrs 
 	on mcbr.id = mrs.batch_response_id
 	join marking_score_profile_flags mspf
 	on mrs.score_flag_id = mspf.id 
 	and mspf.is_revoked = 0
 	join marking_score_options mso 
 	on mso.id = mspf.score_option_id
  and mso.is_revoked = 0  
;`

// [claimed_batch_id]
export const SQL_RESPONSE_SCORES = `
  -- SQL_RESPONSE_SCORES
  select
  from marking_claimed_batch_responses mcbr
  join marking_response_scores mrs
    on mrs.batch_response_id = mcbr.id
    and mrs.uid = ?
  where mcbr.claimed_batch_id = ?
;`

export const SQL_RESCORE_RESPONSE = `
  -- SQL_RESCORE_RESPONSE
  SELECT mcbr.id
  , mcbr.claimed_batch_group_id
  , mcbr.is_scale_rescore_supressed
  , max(mcbr.read_id) read_id
  FROM marking_claimed_batch_responses mcbr
  WHERE mcbr.taqr_id = ?
  AND mcbr.window_item_id = ?
  AND mcbr.claimed_batch_group_id IS NOT NULL
  AND is_rescore_indic = 1
  AND (mcbr.is_invalid = 0 OR mcbr.is_invalid is null)
  group by mcbr.claimed_batch_group_id
  limit 1
  ;
`

export const SQL_EXISTING_BATCH_GROUP = (includeInvalidRead: boolean = true) => { 
  return `
-- SQL_EXISTING_BATCH_GROUP
SELECT mcbr.id
, mcbr.window_item_id
, mcbr.claimed_batch_group_id
, mcbr.is_scale_rescore_supressed
, mcbr.read_rule_type
, mcbr.read_id
FROM marking_claimed_batch_responses mcbr
JOIN (
  SELECT MAX(mcbr.marker_read_id) max_read_id 
  FROM marking_claimed_batch_responses mcbr
  JOIN (  SELECT mcbr.id
    , mcbr.claimed_batch_group_id
    FROM marking_claimed_batch_responses mcbr
    JOIN marking_window_items mwi 
    ON mwi.id = mcbr.window_item_id 
    WHERE mcbr.taqr_id = :taqr_id
    AND mwi.group_to_mwi_id in (:mwi_ids) -- was mcbr.window_item_id
    AND mcbr.claimed_batch_group_id IS NOT NULL
    AND mcbr.is_revoked = 0
    group by mcbr.claimed_batch_group_id
    order by mcbr.claimed_batch_group_id
    limit 1) batch_group_match
  ON mcbr.claimed_batch_group_id = batch_group_match.claimed_batch_group_id
  AND mcbr.is_revoked = 0
  ${includeInvalidRead?``:`AND (mcbr.is_invalid = 0 OR mcbr.is_invalid IS NULL)`}
) mcbr2
ON mcbr.marker_read_id = mcbr2.max_read_id
;
`}

export const SQL_EXISTING_BATCH_GROUP_FROM_GROUP_ID = `
  -- SQL_EXISTING_BATCH_GROUP_FROM_GROUP_ID
  SELECT mcbr.id
  , mcbr.window_item_id
  , mcbr.claimed_batch_group_id
  , mcbr.is_scale_rescore_supressed
  , mcbr.read_id
  FROM marking_claimed_batch_responses mcbr
  JOIN (
    SELECT MAX(mcbr.marker_read_id) max_read_id FROM marking_claimed_batch_responses mcbr
    JOIN (  SELECT mcbr.id
      , mcbr.claimed_batch_group_id
      FROM marking_claimed_batch_responses mcbr
      WHERE mcbr.claimed_batch_group_id = :claimedBatchgroupId
      AND mcbr.claimed_batch_group_id IS NOT NULL
      AND mcbr.is_revoked = 0
      group by mcbr.claimed_batch_group_id
      order by mcbr.claimed_batch_group_id
      limit 1) batch_group_match
    ON mcbr.claimed_batch_group_id = batch_group_match.claimed_batch_group_id
    AND mcbr.is_revoked = 0
    AND (mcbr.is_invalid = 0 OR mcbr.is_invalid IS NULL)
  ) mcbr2
  ON mcbr.marker_read_id = mcbr2.max_read_id
  ;
`

// {mw_id}
export const SQL_ITEM_SUMMARY = `
  -- SQL_ITEM_SUMMARY
  -- < 0.1 sec
  select mwtd.id
       , mw.domain_lock
       , mw.is_test_centre
       , mwtd.cahed__slug assignment_item_code
       , mbap.caption
       , mwtd.batch_alloc_policy_id
       , 1 n_items
       , mwtd.max_read_process
       , mwtd.auto_pool_flags
       -- , count(distinct mwi.id) n_scales
       -- , count(distinct mtc.taqr_id) n_responses
       -- , count(distinct mtc.ta_id) n_students
       , CASE WHEN (mbap.is_paired_marking and mw.is_paired_marking_allowed) THEN 1 ELSE 0 END as is_paired_marking
       , mbap.paired_marking_target_group_size
       , mbap.paired_marking_backup_group_size
  from marking_windows mw
  join marking_window_task_definitions mwtd
      on mwtd.marking_window_id = mw.id
  join marking_batch_alloc_policies mbap
    on mbap.id = mwtd.batch_alloc_policy_id
  join marking_window_items mwi
    on mwi.group_to_mwi_id = mwtd.cached__group_to_mwi_id
  left join marking_taqr_cache mtc
    on mtc.mwi_id = mwi.id
  where mw.id = :mw_id
  group by mwtd.id
  order by mwtd.order
  ;
`
// {mw_id}
export const SQL_ITEM_COUNTS_SCORER_BY_TASK = `
  -- SQL_ITEM_COUNTS_SCORER_BY_TASK
  select mwtd.id mwtd_id, COUNT(DISTINCT mimt.uid) n_scorers
  from marking_item_marker_tasks mimt
  join marking_window_items mwi
  	on mimt.marking_window_item_id = mwi.id
  join marking_window_task_definitions mwtd
  	on mwi.group_to_mwi_id = mwtd.cached__group_to_mwi_id
  JOIN marking_windows mw 
  	on mw.id = mwi.marking_window_id 
  JOIN user_roles ur  
  	on ur.group_id = mw.group_id
  	and ur.uid = mimt.uid 
  	and ur.is_revoked = 0
  where mwtd.marking_window_id= :mw_id 
    and mimt.is_revoked = 0 
    and mimt.uid > 0
    and mimt.is_removed = 0
  group by mwtd.id
;
`
// {mw_id}
export const SQL_ITEM_COUNTS_STUDENT_BY_TASK = `
-- SQL_ITEM_COUNTS_STUDENT_BY_TASK
select mwtd.id mwtd_id, count(distinct mtc.ta_id) n_students 
  from marking_window_task_definitions mwtd
  left join marking_window_items mwi
    on mwi.group_to_mwi_id = mwtd.cached__group_to_mwi_id
  left join marking_taqr_cache mtc
    on mtc.mwi_id = mwi.id
  where mwtd.marking_window_id = :mw_id
  group by mwtd.id
`

// {mw_id}
export const SQL_ITEM_COUNTS_CLAIMED_RESPONSES = `
 -- SQL_ITEM_COUNTS_CLAIMED_RESPONSES
   select mwtd_id
      , marking_window_id
      , count(distinct mcbr_id) n_claimed
  from (
      select mwtd.id mwtd_id
          , mwi.marking_window_id
          , mwi.id mwi_id
          , mcbr.taqr_id
          , mcbr.id mcbr_id
          , mwi.sync_batches_to_wmi_id
          , mwi.group_to_mwi_id
          , mwi.id
      from marking_window_task_definitions mwtd
      join marking_batch_alloc_policies mbap
        on mbap.id = mwtd.batch_alloc_policy_id
      join marking_window_items mwi
        on mwi.id = mwtd.cached__group_to_mwi_id
      left join marking_claimed_batches mcb
          on mcb.marking_window_item_id  = mwi.id
      left join marking_claimed_batch_responses mcbr
          on mcbr.claimed_batch_id = mcb.id
          -- and mcbr.claimed_batch_group_id is not null -- batch group does not get assigned until score is given...
          -- and mcbr.is_marked = 0 -- only care if the batch has been submitted
          -- and mcbr.is_revoked = 0 -- only care if the batch has been submitted
          and mcb.is_marked = 0
          and mcb.is_revoked = 0
          and mcbr.claimed_batch_group_id is not null
          and mcbr.marker_read_id is not null
      where mwtd.marking_window_id in (:mw_id)
      group by mwtd_id, mcbr.taqr_id
    ) t
  group by mwtd_id
`

// {mw_id}
export const SQL_ITEM_COUNTS_BY_READ = `
-- SQL_ITEM_COUNTS_BY_READ
SELECT mwtd_id
  , read_id
  , COUNT(DISTINCT t.marker_read_id) n_marks
  , SUM(case when (score_flag_id is not null or second_assignment_flag_id is not null) then 1 else 0 end) n_flags
  , (COUNT(DISTINCT t.marker_read_id) - SUM(case when (score_flag_id is not null or second_assignment_flag_id is not null) then 1 else 0 end)) n_marks_no_flags
FROM (
  SELECT mwtd.id mwtd_id, max(mrs.score_flag_id) score_flag_id, MAX(second_assignment_flag.score_flag_id) second_assignment_flag_id, mcbr.marker_read_id, mcbr.claimed_batch_group_id, ROW_NUMBER() OVER (PARTITION BY mcbr.claimed_batch_group_id ORDER BY mcbr.marker_read_id) AS read_id
  FROM marking_window_items mwi
  JOIN marking_window_task_definitions mwtd
  ON mwtd.cached__group_to_mwi_id = mwi.id
  JOIN marking_claimed_batch_responses mcbr 
    ON mcbr.window_item_id = mwi.id
    AND mcbr.claimed_batch_group_id IS NOT NULL
    AND (mcbr.is_invalid IS NULL OR mcbr.is_invalid = 0)
  JOIN marking_claimed_batches mcb
    ON mcb.id = mcbr.claimed_batch_id
    AND mcb.is_marked = 1
  LEFT JOIN marking_response_scores mrs
      ON mrs.batch_response_id = mcbr.id
  LEFT JOIN 
  (
  	SELECT  
  	mcbr.marker_read_id
  	, mrs.score_flag_id
	from marking_window_items mwi 
	join marking_claimed_batch_responses mcbr 
		ON mcbr.window_item_id = mwi.id
		AND mcbr.claimed_batch_group_id IS NOT NULL
		AND (mcbr.is_invalid IS NULL OR mcbr.is_invalid = 0)
	JOIN marking_claimed_batches mcb
    	ON mcb.id = mcbr.claimed_batch_id
	    AND mcb.is_marked = 1
	JOIN marking_response_scores mrs
	    ON mrs.batch_response_id = mcbr.id
	WHERE mwi.marking_window_id  = :mw_id
	AND mwi.sync_batches_to_wmi_id != mwi.group_to_mwi_id 
	AND mwi.marking_window_id  = :mw_id
	AND mrs.score_flag_id is not null
	group by mcbr.claimed_batch_group_id, mcbr.marker_read_id
  ) second_assignment_flag
  ON second_assignment_flag.marker_read_id = mcbr.marker_read_id
  WHERE mwi.marking_window_id IN (:mw_id)
  GROUP BY mcbr.claimed_batch_group_id, mcbr.marker_read_id
  ORDER BY mcbr.claimed_batch_group_id, mcbr.marker_read_id
) t
WHERE read_id IS NOT NULL
GROUP BY mwtd_id, read_id;
`

export const SQL_READ_COUNTS_BY_ASSESSMENT = `
  -- SQL_READ_COUNTS_BY_ASSESSMENT
  SELECT mwtd_id
      , marking_window_id
      , read_id
      , slug
      , SUM(case when score_flag_id is not null  or n_scored_flags > 0 then 1 else 0 end) n_flags
      , (COUNT(DISTINCT t.marker_read_id) - SUM(case when score_flag_id is not null or n_scored_flags > 0 then 1 else 0 end)) n_marks_no_flags
      , SUM(case when is_rescore_indic = 0 and is_read_rules_processed = 0 and is_expert_score = 0 and score_flag_id is null and n_scored_flags = 0 then 1 else 0 end) n_needs_rescore
      , SUM(is_rescore_indic) n_repooled
      , COUNT(DISTINCT t.marker_read_id) n_marks
  FROM (
    SELECT mwtd.id mwtd_id
      , mwi.id
    , mwi.slug
    , mwi.marking_window_id
    , max(mrs.score_flag_id) score_flag_id
    , mcbr.marker_read_id
    , mcbr.claimed_batch_group_id
    , min(mcb.is_expert_score) is_expert_score
    , SUM(case when mso.is_pre_score_flag = 1 then 1 else 0 end) n_scored_flags
    , max(mcbr.is_read_rules_processed) is_read_rules_processed
    , ROW_NUMBER() OVER (PARTITION BY mcbr.claimed_batch_group_id ORDER BY mcbr.marker_read_id) AS read_id
    , max(mcbr.is_rescore_indic) is_rescore_indic
    FROM marking_window_items mwi
    JOIN marking_window_task_definitions mwtd
    ON mwtd.cached__group_to_mwi_id = mwi.group_to_mwi_id
    JOIN marking_claimed_batch_responses mcbr ON mcbr.window_item_id = mwi.id
    JOIN marking_claimed_batches mcb
    ON mcb.id = mcbr.claimed_batch_id
    LEFT JOIN marking_response_scores mrs
        ON mrs.batch_response_id = mcbr.id
    LEFT JOIN marking_score_profile_options mspo
    	ON mrs.score_option_id = mspo.id
    LEFT JOIN marking_score_options mso
    	ON mso.id = mspo.score_option_id
    WHERE mwi.marking_window_id IN (:mw_id)
    AND mcbr.claimed_batch_group_id IS NOT NULL
      AND mcb.is_marked = 1
      AND (mcbr.is_invalid IS NULL OR mcbr.is_invalid = 0)
    GROUP BY mcbr.claimed_batch_group_id, mcbr.marker_read_id
    ORDER BY mcbr.claimed_batch_group_id, mcbr.marker_read_id
  ) t
  WHERE read_id IS NOT NULL
  GROUP BY mwtd_id, read_id;
`

export const SQL_GET_READ_INFO_FROM_BATCH_GROUPS = `
  -- SQL_GET_READ_INFO_FROM_BATCH_GROUPS
  SELECT mcbr.id
    , mcbr.window_item_id
    , mcbr.claimed_batch_group_id
    , mcbr.is_prorated 
    , mso.slug score_slug
    , mso.id score_option_id
    , mso.raw_score_value
    , msf.slug flag_slug
    , mwi.score_profile_id
    , mcbr.read_id
    , mrs.uid
    , mwi.batch_alloc_policy_id
    , mwtd.max_read_process
    , msp.is_non_scored_profile
  FROM marking_claimed_batch_responses mcbr
  JOIN marking_window_items mwi
    ON mwi.id = mcbr.window_item_id
  LEFT JOIN marking_response_scores mrs
    ON mrs.batch_response_id = mcbr.id
  LEFT JOIN marking_score_profile_options mspo
    ON mspo.id = mrs.score_option_id
  LEFT JOIN marking_score_options mso
    ON mso.id = mspo.score_option_id
  LEFT JOIN marking_score_profiles msp
    ON msp.id = mwi.score_profile_id
  LEFT JOIN marking_score_profile_flags mspf
    ON mspf.id = mrs.score_flag_id
  LEFT JOIN marking_score_options msf
    ON msf.id = mspf.score_option_id
  LEFT JOIN marking_window_task_definitions mwtd
    ON mwtd.cached__group_to_mwi_id = mwi.group_to_mwi_id
  WHERE mcbr.claimed_batch_group_id in (?) 
    and (mcbr.is_invalid = 0 or mcbr.is_invalid is null)
    AND (mcbr.is_revoked IS NULL OR mcbr.is_revoked = 0)
  AND (mrs.id is not null OR mcbr.is_scale_supressed = 1 OR msp.is_non_scored_profile = 1)
  ;
`

export const SQL_GET_READ_INFO_FOURTH_READS = `
  -- SQL_GET_READ_INFO_FOURTH_READS
  SELECT 
  mcbr.claimed_batch_group_id
  , mcbr.marker_read_id
  , mcbr.id mcbr_id
  , mwi.id mwi_id
  , read_table.read_id
  , mcbr.id
  , mcbr.is_scale_supressed
  , msp.is_non_scored_profile
  , mso.slug score_slug
  , mso.id score_option_id
  , mso.raw_score_value
  , msf.slug flag_slug
  , mwi.score_profile_id
  , mwi.batch_alloc_policy_id
  , mcbr.read_rule_type
  , mcbr.from_read_rule 
  , mcbr.is_prorated
  , mwi.sync_batches_to_wmi_id 
  , mwi.group_to_mwi_id 
  , mcbr.tls_id
  , msp.supress_condition 
  FROM (
    SELECT mwi.id mwi_id, mcbr.marker_read_id, mcbr.claimed_batch_group_id, ROW_NUMBER() OVER (PARTITION BY mcbr.claimed_batch_group_id ORDER BY mcbr.marker_read_id) AS read_id
    FROM marking_window_items mwi
    JOIN marking_window_task_definitions mwtd
      ON mwtd.cached__group_to_mwi_id = mwi.group_to_mwi_id
    JOIN marking_claimed_batch_responses mcbr 
      ON mcbr.window_item_id = mwi.id
    JOIN marking_claimed_batches mcb
      ON mcb.id = mcbr.claimed_batch_id
    LEFT JOIN marking_response_scores mrs
        ON mrs.batch_response_id = mcbr.id
    WHERE mcbr.claimed_batch_group_id IS NOT NULL
      AND mcbr.claimed_batch_group_id in (:mcbgIds)
      AND mcb.is_marked = 1
      AND mcb.is_expert_score = 0 -- if the response was expert scored, no more reads are needed
      AND (mcbr.is_invalid IS NULL OR mcbr.is_invalid = 0)
      AND (mcbr.is_revoked IS NULL OR mcbr.is_revoked = 0)
    GROUP BY mcbr.claimed_batch_group_id, mcbr.marker_read_id
    ORDER BY mcbr.claimed_batch_group_id, mcbr.marker_read_id
  ) read_table
  JOIN marking_claimed_batch_responses mcbr
    ON mcbr.marker_read_id = read_table.marker_read_id
  JOIN marking_window_items mwi
    ON mwi.id = mcbr.window_item_id
  LEFT JOIN marking_response_scores mrs
    ON mrs.batch_response_id = mcbr.id
  LEFT JOIN marking_score_profile_options mspo
    ON mspo.id = mrs.score_option_id
  LEFT JOIN marking_score_options mso
    ON mso.id = mspo.score_option_id
  LEFT JOIN marking_score_profiles msp
    ON msp.id = mwi.score_profile_id
  LEFT JOIN marking_score_profile_flags mspf
    ON mspf.id = mrs.score_flag_id
  LEFT JOIN marking_score_options msf
    ON msf.id = mspf.score_option_id
  ORDER BY read_table.claimed_batch_group_id, read_table.read_id
  ;
`
export const SQL_FIND_READS_TO_PROCESS_FROM_COUNT = (isThirdReads: boolean, hasLimit: boolean, limit: number) => `
  -- SQL_FIND_READS_TO_PROCESS_FROM_COUNT
  SELECT * FROM (
    SELECT
        mcbr.claimed_batch_group_id
      , mwi.id
      , mwi.slug
      , count(distinct mcbr.marker_read_id) num_reads
      , COUNT(distinct NULLIF(is_rescore_indic,0)) is_rescore_indic_count
      , SUM(case when mrs.score_flag_id is null then 0 else 1 end) as total_flags
      ${isThirdReads ? `, SUM(mcbr.is_read_rules_processed) is_read_rules_processed_sum` : ``}
    FROM marking_window_items mwi
    JOIN marking_window_task_definitions mwtd
      ON mwtd.cached__group_to_mwi_id = mwi.group_to_mwi_id
    JOIN marking_claimed_batch_responses mcbr ON mcbr.window_item_id = mwi.id
    JOIN marking_claimed_batches mcb
      ON mcb.id = mcbr.claimed_batch_id
    LEFT JOIN marking_response_scores mrs
      ON mrs.batch_response_id = mcbr.id
    LEFT JOIN marking_score_profile_flags mspf
      ON mspf.id = mrs.score_flag_id
    WHERE mwi.marking_window_id IN (:mw_id)
      AND mcbr.claimed_batch_group_id is not null
        AND mcb.is_marked = 1
        AND mcb.is_expert_score = 0 -- if the response was expert scored, no more reads are needed
        AND (mcbr.is_invalid is null OR mcbr.is_invalid = 0)
        AND mcbr.is_revoked = 0
        AND mwi.slug = :assignment_item_code
    GROUP BY mcbr.claimed_batch_group_id
  ) read_counts
  WHERE read_counts.num_reads = :read_count 
  and is_rescore_indic_count < :read_count -- to ensure that confirmed INS/NR does not get pulled (no rescore)
  AND total_flags = 0
  ${isThirdReads ? `AND is_read_rules_processed_sum = 0` : ``}
  ${hasLimit ? `LIMIT ${limit}` : ``}
  ;
`

export const SQL_FIND_FOURTH_READS_OR_HIGHER = `
  -- SQL_FIND_FOURTH_READS_OR_HIGHER
  SELECT * FROM (
    SELECT
        mcbr.claimed_batch_group_id
      , mwi.id
      , mwi.slug
      , count(distinct mcbr.marker_read_id) num_reads
      , sum(mcbr.is_rescore_indic) is_rescore_indic_count
      , SUM(case when mrs.score_flag_id is null then 0 else 1 end) as total_flags
      , min(mcbr.is_read_rules_processed) min_is_read_rules_processed
    FROM marking_window_items mwi
    JOIN marking_window_task_definitions mwtd
      ON mwtd.cached__group_to_mwi_id = mwi.id
    JOIN marking_claimed_batch_responses mcbr ON mcbr.window_item_id = mwi.id
    JOIN marking_claimed_batches mcb
      ON mcb.id = mcbr.claimed_batch_id
    LEFT JOIN marking_response_scores mrs
      ON mrs.batch_response_id = mcbr.id
    LEFT JOIN marking_score_profile_flags mspf
      ON mspf.id = mrs.score_flag_id
    WHERE mwi.marking_window_id IN (:mw_id)
      AND mcbr.claimed_batch_group_id is not null
        AND mcb.is_marked = 1
        AND (mcbr.is_invalid is null OR mcbr.is_invalid = 0)
        AND mcbr.is_revoked = 0
        AND mwi.slug = :assignment_item_code
    GROUP BY mcbr.claimed_batch_group_id
  ) read_counts
  WHERE read_counts.num_reads >= :read_count AND total_flags = 0 AND read_counts.min_is_read_rules_processed = 0
  AND read_counts.is_rescore_indic_count = read_counts.num_reads - 1 -- to ensure that confirmed INS/NR does not get pulled (rescore count will not match)
  ;
`

export const SQL_FIND_PROCESSED_READS = `
  -- SQL_FIND_READS_TO_PROCESS_FROM_COUNT
  SELECT * FROM (
    SELECT
        mcbr.claimed_batch_group_id
      , mwi.id
      , mwi.slug
      , mwi.batch_alloc_policy_id
      , count(distinct mcbr.marker_read_id) num_reads
      , COUNT(distinct NULLIF(is_rescore_indic,0)) is_rescore_indic_count
      , SUM(case when mrs.score_flag_id is null then 0 else 1 end) as total_flags
    FROM marking_window_items mwi
    JOIN marking_window_task_definitions mwtd
      ON mwtd.cached__group_to_mwi_id = mwi.group_to_mwi_id
    JOIN marking_claimed_batch_responses mcbr ON mcbr.window_item_id = mwi.id
    JOIN marking_claimed_batches mcb
      ON mcb.id = mcbr.claimed_batch_id
    LEFT JOIN marking_response_scores mrs
      ON mrs.batch_response_id = mcbr.id
    LEFT JOIN marking_score_profile_flags mspf
      ON mspf.id = mrs.score_flag_id
    WHERE mwi.marking_window_id IN (:mw_id)
      AND mcbr.claimed_batch_group_id is not null
        AND mcb.is_marked = 1
        AND (mcbr.is_invalid is null OR mcbr.is_invalid = 0)
        AND mcbr.is_revoked = 0
    GROUP BY mcbr.claimed_batch_group_id
  ) read_counts
  -- WHERE read_counts.num_reads >= 2
  
  ;
`;

export const SQL_GET_MWI = `
    -- SQL_GET_MWI
    SELECT * FROM marking_window_items
    WHERE id = :mwi_id
`

export const SQL_UNAVAIL_RESPONSE_CONFIRM_BY_TAQR_ID = `
  select mcbr.taqr_id
      , mcbr.id mcbr_id 
      , mcb.marking_window_item_id 
      , mcb.is_revoked 
      , mcbr.is_rescore_indic
      , mcbr.is_invalid_task_specific 
      , mcbr.is_invalid
  from marking_claimed_batch_responses mcbr 
  join marking_claimed_batches mcb 
    on mcb.id  = mcbr.claimed_batch_id 
  where mcb.marking_window_item_id = :mwi_id
  and mcb.is_revoked = 0
  and mcbr.is_rescore_indic = 0
  and (mcbr.is_invalid is null or mcbr.is_invalid = 0)
  and mcbr.taqr_id in (:taqr_ids)
  group by mcbr.taqr_id
`

export const SQL_STU_TITLE_BY_TAQR_MWI = `/*SQL_STU_TITLE_BY_TAQR_MWI*/
select mtc.taqr_id
     , mtc.student_defined_label
from marking_taqr_cache mtc 
where mtc.taqr_id in (:taqr_ids)
  and mtc.mwi_id = :mwi_id
`

export const SQL_AVAIL_UNF_RESPONSE = `
  select 
    mtc.ta_id
  , mcbr.taqr_id
  , r.resp_claim_id as prev_resp_claim_id
  , mwum.value marker_number
  , mcb.uid prev_uid
  , mcbr.is_rescore_indic
  , mcbr.is_reuse_scorer
  , mcb.is_marked
  , mrs.last_touched_on
  from (
  select mtc.taqr_id
    , taqr.test_attempt_id
    , mtc.schl_group_id
    , max(mmcbr.id) as resp_claim_id
    , mmcbr.prev_uid
    , mmcbr.is_rescore_indic
    , mmcbr.is_reuse_scorer
  from marking_taqr_cache mtc
  left join (
  select max(mcbr.id) id
        , mcbr.taqr_id
        , mcb.marking_window_item_id
        , min(mcbr.is_rescore_indic) is_rescore_indic
        , max(mcbr.is_reuse_scorer) is_reuse_scorer
        , mcb.uid prev_uid
  from marking_claimed_batches mcb
  join marking_claimed_batch_responses mcbr
    on mcb.id = mcbr.claimed_batch_id
    and mcbr.is_revoked = 0
    and (mcbr.is_invalid is null or mcbr.is_invalid = 0)
  where mcb.marking_window_item_id = :mwi_id
    and mcb.is_training = 0
  group by mcbr.taqr_id
          , mcb.marking_window_item_id
  ) mmcbr
    on mmcbr.taqr_id = mtc.taqr_id
    and mmcbr.marking_window_item_id = mtc.mwi_id
  join test_attempt_question_responses taqr
      on taqr.id = mtc.taqr_id
  where mtc.mwi_id = :mwi_id
  and mtc.is_material = 0
  group by mtc.taqr_id
  ) r
  join marking_claimed_batch_responses mcbr
      on mcbr.id = r.resp_claim_id
  join marking_taqr_cache mtc
      on mtc.taqr_id = mcbr.taqr_id
  join marking_claimed_batches mcb
      on mcb.id = mcbr.claimed_batch_id
  join marking_response_scores mrs 
      on mrs.batch_response_id = mcbr.id
  join marking_score_profile_flags mspf 
      on mspf.id = mrs.score_flag_id 
  join marking_score_options mso 
      on mso.id = mspf.score_option_id 
  LEFT JOIN marking_window_user_meta mwum 
    ON mwum.marking_window_id = :marking_window_id
    AND mwum.meta_key = 'Marker_Number'
    AND mwum.marker_uid = mcb.uid
    AND mwum.is_revoked = 0
  where (
    r.resp_claim_id is not null
    and mcb.is_marked = 1
    AND mso.slug_code in ('OBS')
  )
  AND (
    r.test_attempt_id not in ( -- checking test attempt no matter the task
      select test_attempt_id from (
        select taqr.test_attempt_id, max(mcbr.is_reuse_scorer) is_reuse_scorer, max(mcbr.is_invalid) is_invalid, is_rescore_indic, mwi.id, mwi.slug, mwi.group_to_mwi_id from marking_claimed_batch_responses mcbr
        join marking_claimed_batches mcb
          on mcb.id = mcbr.claimed_batch_id
        join test_attempt_question_responses taqr
          on taqr.id = mcbr.taqr_id
        join marking_window_items mwi
          on mwi.id = mcbr.window_item_id
        where mcb.uid = :uid
        and (mcbr.is_revoked = 0 or mcbr.is_revoked is null)
        and mwi.marking_window_id = :marking_window_id
        group by taqr_id, mcbr.read_id, mcb.uid, mwi.group_to_mwi_id
      ) r
      where (
        (r.is_reuse_scorer = 1 AND (r.is_invalid IS NULL OR r.is_invalid = 0))
        OR r.group_to_mwi_id = :mwi_id
        OR (r.is_reuse_scorer = 0 AND (r.is_invalid IS NULL OR r.is_invalid = 0))
      )
    )
  )
    GROUP BY r.taqr_id, r.resp_claim_id
    -- order by r.taqr_id -- temp
    LIMIT 3000
  ;
`

// [uid, window_item_id, window_item_id]
export const SQL_AVAIL_SCORE_RESPONSE = `
-- SQL_AVAIL_SCORE_RESPONSE
select r.taqr_id
  , r.schl_group_id
  , r.student_accommodation_cache
  , 0 as is_invalid
  , 0 as is_nr
  , r.resp_claim_id as prev_resp_claim_id
  , r.prev_uid
  , r.is_rescore_indic
  , r.is_reuse_scorer
  from (
  select mtc.taqr_id
    , taqr.test_attempt_id
    , mtc.schl_group_id
    , mtc.student_accommodation_cache 
    , max(mmcbr.id) as resp_claim_id
    , mmcbr.prev_uid
    , mmcbr.is_rescore_indic
    , mmcbr.is_reuse_scorer
    , mmcbr.is_expert_score
  from marking_taqr_cache mtc
  left join (
  select mcbr.id
        , mcbr.taqr_id
        , mcb.marking_window_item_id
        , min(mcbr.is_rescore_indic) is_rescore_indic
        , max(mcbr.is_reuse_scorer) is_reuse_scorer
        , mcb.uid prev_uid
        , mcb.is_expert_score
  from marking_claimed_batches mcb
  join marking_claimed_batch_responses mcbr
    on mcb.id = mcbr.claimed_batch_id
    and mcbr.is_revoked = 0
    and (mcbr.is_invalid is null or mcbr.is_invalid = 0)
  where mcb.marking_window_item_id = :mwi_id
    and mcb.is_training = 0
  group by mcbr.taqr_id
          , mcb.marking_window_item_id
  ) mmcbr
    on mmcbr.taqr_id = mtc.taqr_id
    and mmcbr.marking_window_item_id = mtc.mwi_id
  join test_attempt_question_responses taqr
	  on taqr.id = mtc.taqr_id
  where mtc.mwi_id = :mwi_id
  and mtc.is_material = 0
  group by mtc.taqr_id
  ) r
where (
  r.resp_claim_id is null
  or (
    r.is_rescore_indic = 1
    and r.prev_uid != :uid
    and r.is_expert_score = 0
  )
)
AND (
  r.test_attempt_id not in ( -- checking test attempt no matter the task
    select test_attempt_id from (
      select taqr.test_attempt_id, max(mcbr.is_reuse_scorer) is_reuse_scorer, max(mcbr.is_invalid) is_invalid, is_rescore_indic, mwi.id, mwi.slug, mwi.group_to_mwi_id from marking_claimed_batch_responses mcbr
      join marking_claimed_batches mcb
        on mcb.id = mcbr.claimed_batch_id
      join test_attempt_question_responses taqr
        on taqr.id = mcbr.taqr_id
      join marking_window_items mwi
        on mwi.id = mcbr.window_item_id
      where mcb.uid = :uid
      and (mcbr.is_revoked = 0 or mcbr.is_revoked is null)
      and mwi.marking_window_id = :marking_window_id
      group by taqr_id, mcbr.read_id, mcb.uid, mwi.group_to_mwi_id
    ) r
    where (
      (r.is_reuse_scorer = 1 AND (r.is_invalid IS NULL OR r.is_invalid = 0))
      OR r.group_to_mwi_id = :mwi_id
      OR (r.is_reuse_scorer = 0 AND (r.is_invalid IS NULL OR r.is_invalid = 0))
    )
  )
)
  -- order by r.taqr_id -- temp
  LIMIT 3000
;`;

// < 0.1 sec
// [uid]
export const SQL_LEADER_MARKING_WINDOWS = (isRafi:boolean = false) => `
-- SQL_LEADER_MARKING_WINDOWS
select mw.id as window_id
     , mw.group_id
     , mw.name as window_name
     , mw.start_on
     , mw.end_on
     , mw.is_archived
     , mw.is_scoring_disabled
     , mw.meta_config
     , mw.is_rescore
     , mw.is_scan_reassign_allowed
     , mw.is_scorer_annotation_allowed
     , mw.is_score_profile_single_focus
     , mw.is_paired_marking_allowed
     , mw.is_scorer_summary_view_allowed
     , mw.is_group_marking
     , mw.is_scorings_inspect_access
     , mw.is_expert_score_edit
     , mw.is_second_read_recheck_disabled
     , tw.type_slug as tw_type_slug
     , tw.id as tw_id
     , tw.title as tw_title
from marking_windows mw
join user_roles ur
  on ur.group_id = mw.group_id
  and 
  ${isRafi ? "role_type = 'mrkg_rafi'" : "(role_type = 'mrkg_ctrl' or role_type = 'mrkg_supr')"}
  and is_revoked = 0
  and ur.uid = ?
join marking_window_test_window mwtw
  on mwtw.marking_window_id = mw.id
  and (mwtw.is_material = 0 or mwtw.is_material is null)
join test_windows tw
  on tw.id = mwtw.test_window_id
${isRafi ? "where mw.is_scoring_disabled = 0":""}
group by mw.id
;`

// {window_id, uids}
export const SQL_LEAD_SCOR_STATS = `
  -- SQL_LEAD_SCOR_STATS
  select
    t2.*
    , mimt_tm.status as tm_status
    , mimt_pr.status as pr_status
    , mimt_qt.status as qt_status
    , mimt_sc.status as scoring_status
    , mimt_sc.is_revoked as is_scoring_revoked
    , mimt_sc.cache_roll_val_exact as val_ex
    , mimt_sc.cache_roll_val_adj as val_adj
    , mimt_sc.cache_batches_rem as cache_batches_rem
    , mimt_sc.id as task_id
    , mimt_sc.created_on as invit_date
    , count(distinct mimpb.id) as num_sendbacks
  from (
    select t.*
      , mwum.value swum_marker_number
      , mwum_mr.value swum_max_read_level
      , mg.group_number marking_group_number
      , u.first_name
      , u.last_name
      , u.contact_email
      , a.email
    from (
    select ur.uid as uid
      , mimt.marking_window_item_id as window_item_id
      , mwi.marking_window_id
      , mwi.item_id
      , mwi.slug as item_slug
      , mwi.caption as item_caption
      from marking_windows mw
      join user_roles ur
        on ur.group_id = mw.group_id
        and ur.role_type = 'mrkg_mrkr'
        and ur.uid in (:uids)
        -- and ur.is_revoked = 0
      join marking_item_marker_tasks mimt
        on mimt.uid = ur.uid
        and mimt.is_removed = 0 -- dont want to exclude revoked, because they may still have relevant work, but removed is to explicitly allow symbolic deletion
      join marking_window_items mwi
        on mwi.marking_window_id = mw.id
        and mwi.id = mimt.marking_window_item_id
        and mwi.id = mwi.group_to_mwi_id  -- multi-scale specific
      where mw.id = :window_id
      group by ur.uid, mimt.marking_window_item_id
    ) t
    join users u
      on u.id = t.uid
    left join auths a
      on a.uid = t.uid
    left join marking_window_user_meta mwum
      on mwum.marker_uid = u.id
      and mwum.marking_window_id  = t.marking_window_id
      and mwum.meta_key  = 'Marker_Number'
      and mwum.is_revoked  = 0
    left join marking_window_user_meta mwum_mr
      on mwum_mr.marker_uid = u.id
      and mwum_mr.marking_window_id  = t.marking_window_id
      and mwum_mr.meta_key  = 'Max_Read_Level'
      and mwum_mr.is_revoked  = 0
    left join marking_groups_users mgu
      on mgu.uid = u.id
      and mgu.is_revoked = 0
    left join marking_groups mg
      on mg.marking_window_id = t.marking_window_id
      and mg.id = mgu.group_id
      and mg.is_revoked = 0
  ) t2
  left join marking_item_marker_tasks mimt_tm
    on mimt_tm.marking_window_item_id = t2.window_item_id
    and mimt_tm.uid = t2.uid
    and mimt_tm.component_type = 'TRAINING_MATERIALS'
    and mimt_tm.is_removed = 0
  left join marking_item_marker_tasks mimt_pr
    on mimt_pr.marking_window_item_id = t2.window_item_id
    and mimt_pr.uid = t2.uid
    and mimt_pr.component_type = 'PRACTICE'
    and mimt_pr.is_removed = 0
  left join marking_item_marker_tasks mimt_qt
    on mimt_qt.marking_window_item_id = t2.window_item_id
    and mimt_qt.uid = t2.uid
    and mimt_qt.component_type = 'QUALIFYING'
    and mimt_qt.is_removed = 0
  left join marking_item_marker_tasks mimt_sc
    on mimt_sc.marking_window_item_id = t2.window_item_id
    and mimt_sc.uid = t2.uid
    and mimt_sc.component_type = 'SCORING'
    and mimt_sc.is_removed = 0
  left join marking_item_marker_perf_blocks mimpb
    on  mimpb.window_item_id = t2.window_item_id
    and mimpb.uid = t2.uid
    and mimpb.is_revoked = 0
  group by t2.uid, t2.window_item_id
;`

// {window_id, uids}
export const SQL_LEAD_SCOR_STATUS = `
-- SQL_LEAD_SCOR_STATUS
SELECT DISTINCT
    a.uid,
    a.marking_window_item_id,
    a.timestamp,
    a.is_blocked,
    a.scoring_block_type_id,
    a.block_message,
    a.marking_window_id,
    sbt.slug as block_slug,
    sbt.caption as block_caption,
    sbt.category as block_category
  FROM scorer_status_by_item_log a
  LEFT JOIN scoring_block_types sbt
    on sbt.id = a.scoring_block_type_id
  JOIN (
    SELECT MAX(timestamp) timestamp, c.uid, c.marking_window_item_id
    FROM scorer_status_by_item_log c
    where c.uid in (:uids)
    GROUP BY c.uid, c.marking_window_item_id
  ) max_timestamp
    ON max_timestamp.timestamp = a.timestamp
    AND max_timestamp.marking_window_item_id = a.marking_window_item_id
    AND max_timestamp.uid = a.uid
    AND a.marking_window_id = :window_id;
`


// [window_id]
export const SQL_LEAD_CUMUL_RECALC = `
-- SQL_LEAD_CUMUL_RECALC
select *
     , ( case when (adj_index_exp is null) then 0 else 1 end) is_validity
     , ( case when (adj_index_exp is null) then null else ( case when (abs(over_under) = 0) then 1 else 0 end) end) is_validity_exact
     , ( case when (adj_index_exp is null) then null else (case when (abs(over_under) = 1) then 1 else 0 end) end) is_validity_adj
     , ( case when (adj_index_exp is null) then null else over_under end) is_validity_overunder
from (
	select mcbr.id mcbr_id
	     , mcb.uid
	     , mcb.marking_window_item_id wmi_id
	     , mcbr.taqr_id
	     , mcbr.is_validity  is_validity__pre
	     , mcbr.is_validity_exact  is_validity_exact__pre
	     , mcbr.is_validity_adj  is_validity_adj__pre
	     , mcbr.is_validity_overunder  is_validity_overunder__pre
	     , mso.adj_index
	     , mso2.adj_index adj_index_exp
	     , (mso.adj_index - mso2.adj_index) over_under
	from marking_window_items mwi
	join marking_claimed_batches mcb on mcb.marking_window_item_id  = mwi.id
	join marking_claimed_batch_responses mcbr on mcbr.claimed_batch_id  = mcb.id
	join marking_response_scores mrs on mrs.batch_response_id = mcbr.id
	join marking_score_profile_options mspo on mspo.id = mrs.score_option_id and mspo.is_revoked = 0
	join marking_score_options mso on mso.id = mspo.score_option_id
	left join marking_response_selections mrs2 on mrs2.id  = mcbr.response_selection_id
	left join marking_score_profile_options mspo2 on mspo2.id = mrs2.score_option_id and mspo2.is_revoked = 0
	left join marking_score_options mso2 on mso2.id = mspo2.score_option_id
	where mwi.marking_window_id in (?)
	  and mcbr.is_validity = 1
	  and mcbr.is_marked = 1
) t
order by wmi_id, taqr_id, adj_index_exp, over_under
`

// {window_id}
export const SQL_LEAD_SCOR_STATS_MWI_IDS = `
  -- SQL_LEAD_SCOR_STATS_MWI_IDS, < 0.1 sec
  select distinct mimt.marking_window_item_id  mwi_id
  , CASE WHEN (mw.is_paired_marking_allowed and mbap.is_paired_marking) THEN 1 ELSE 0 END as is_paired_marking
  from marking_window_items mwi
  join marking_item_marker_tasks mimt
    on mimt.marking_window_item_id = mwi.id
    and mimt.component_type = 'SCORING'
    and mimt.is_removed = 0
  join marking_batch_alloc_policies mbap
    on mbap.id = mwi.batch_alloc_policy_id
  join marking_windows mw
	  on mw.id = mwi.marking_window_id 
  where mwi.marking_window_id = :window_id
    and mwi.id = mwi.sync_batches_to_wmi_id
`

// {window_id}
export const SQL_LEAD_SCOR_STATS_UIDS = `
  -- SQL_LEAD_SCOR_STATS_UIDS
  select distinct mimt.uid
  , mimt.pair_id 
  , mwi.id mwi_id
  from marking_window_items mwi
  join marking_item_marker_tasks mimt
    on mimt.marking_window_item_id = mwi.id
    and mimt.component_type = 'SCORING'
  where mwi.marking_window_id = :window_id
    and mwi.id = mwi.sync_batches_to_wmi_id
    and mimt.is_removed = 0
`

// {window_id, supervisor_uid}
export const SQL_LEAD_SCOR_STATS_UIDS_SUPERVISOR = `
  -- SQL_LEAD_SCOR_STATS_UIDS_SUPERVISOR, < 0.1 sec
  select distinct mimt.uid
  , mimt.pair_id 
  , mwi.id mwi_id
  from marking_window_items mwi
  join marking_item_marker_tasks mimt
    on mimt.marking_window_item_id = mwi.id
    and mimt.component_type = 'SCORING'
  join marking_groups mg
    on mg.marking_window_id = mwi.marking_window_id
    and mg.is_revoked = 0
  join marking_groups_users mgus
    on mgus.group_id = mg.id
    and mgus.uid = :supervisor_uid
    and mgus.is_revoked = 0
  join marking_groups_users mgu
    on mgu.group_id = mg.id
    and mgu.is_revoked = 0
    and mgu.uid = mimt.uid
  where mwi.marking_window_id = :window_id
    and mwi.id = mwi.sync_batches_to_wmi_id
    and mimt.is_removed = 0
`


export const SQL_LEAD_SCOR_STATS_MCB_COMPLETION = `
-- SQL_LEAD_SCOR_STATS_MCB_COMPLETION
select mimt.id task_id
      , mimt.uid
      , mcb.marking_window_item_id
      , mwi.item_id
      , mwi.slug as item_slug
      , mwi.caption as item_caption
      , mbap.batch_size
      , sum(mcb.is_revoked_on_empty) as num_batch_expired_empty
      , count(distinct mcb.id) num_batches_claimed_total
      , (count(distinct mcb.id) - sum(mcb.is_marked * (1-mcb.is_revoked)) - sum(mcb.is_revoked)) num_batches_claimed
      , sum(mcb.is_marked) as num_batches_completed2
      , sum(mcb.is_revoked) as num_batch_expired
from marking_claimed_batches mcb -- this is the first main source of slowdown
join marking_window_items mwi 
    on mwi.id = mcb.marking_window_item_id
join marking_batch_alloc_policies mbap
    on mbap.id = mwi.batch_alloc_policy_id
join marking_item_marker_tasks mimt
	on mimt.uid = mcb.uid
	and mimt.marking_window_item_id = mcb.marking_window_item_id
  and mimt.is_removed = 0
where mcb.uid in (:uids)
  and mcb.marking_window_item_id in (:mwi_ids)
  and mcb.is_training = 0
group by mcb.uid, mcb.marking_window_item_id 
`

export const SQL_LEAD_SCOR_STATS_MCBR_COMPLETION = `
-- SQL_LEAD_SCOR_STATS_MCBR_COMPLETION
select mimt.id task_id
      , count(mcbr.id) as num_responses_scored
from marking_claimed_batches mcb -- this is the first main source of slowdown
join marking_window_items mwi 
    on mwi.id = mcb.marking_window_item_id
join marking_batch_alloc_policies mbap
    on mbap.id = mwi.batch_alloc_policy_id
join marking_claimed_batch_responses mcbr
	on mcbr.claimed_batch_id = mcb.id
	and mcbr.is_marked = 1
join marking_item_marker_tasks mimt
	on mimt.uid = mcb.uid
	and mimt.marking_window_item_id = mcb.marking_window_item_id
  and mimt.is_removed = 0
where mcb.uid in (:uids)
  and mcb.marking_window_item_id in (:mwi_ids)
  and mcb.is_training = 0
group by mimt.id 
`


export const SQL_LEAD_SCOR_STATS_COMPLETION = `
  -- SQL_LEAD_SCOR_STATS_COMPLETION
  select t5c.*
      , t5c.num_responses_scored/t5c.batch_size as num_batches_scored
  from (
  select t5b.*
      , count(mcbr.id) as num_responses_scored
  from (
  select t5.*
      , sum(mcbr.is_validity) as num_validity_total
      , sum(mcbr.is_validity_exact) as num_validity_exact
      , sum(mcbr.is_validity_adj) as num_validity_adj
      , avg(mso.value) as avg_score_value
      , (t5.num_batch_expired - t5.num_batch_expired_empty) as num_batch_expired_partial
  from (
  select t2.*
      , mwi.item_id
      , mwi.slug as item_slug
      , mwi.caption as item_caption
      , mbap.batch_size
      , sum(mcb.is_revoked_on_empty) as num_batch_expired_empty
      , count(distinct mcb.id) num_batches_claimed_total
      , (count(distinct mcb.id) - sum(mcb.is_marked * (1-mcb.is_revoked)) - sum(mcb.is_revoked)) num_batches_claimed
      , sum(mcb.is_marked) as num_batches_completed2
      , sum(mcb.is_revoked) as num_batch_expired
  from (

    select mimt.uid
      , mimt.marking_window_item_id as window_item_id
      , mimt.id as task_id
      , mimt.cache_roll_val_exact as num_rol_validity_exact
      , mimt.cache_roll_val_adj as num_rol_validity_adj
    from marking_window_items mwi
    join marking_item_marker_tasks mimt
      on mimt.marking_window_item_id = mwi.id
      and mimt.component_type = 'SCORING'
      and mimt.is_removed = 0
    where mwi.marking_window_id = ?
      and mwi.id = mwi.sync_batches_to_wmi_id

  ) t2
  left join marking_claimed_batches mcb
    on mcb.uid = t2.uid
    and mcb.marking_window_item_id = t2.window_item_id
    and mcb.is_training = 0
  left join marking_window_items mwi
    on mwi.id = t2.window_item_id
  left join marking_batch_alloc_policies mbap
    on mbap.id = mwi.batch_alloc_policy_id
  group by t2.uid, t2.window_item_id
  ) t5
  left join marking_claimed_batches mcb
    on mcb.uid = t5.uid
    and mcb.marking_window_item_id = t5.window_item_id
    and mcb.is_training = 0
  left join marking_claimed_batch_responses mcbr
    on mcbr.claimed_batch_id = mcb.id
    and mcbr.is_marked = 1
    and is_validity = 1
  left join marking_response_scores mrs
    on mrs.uid = t5.uid
    and mrs.batch_response_id = mcbr.id
  left join marking_score_profile_options mspo
    on mspo.id = mrs.score_option_id
    and mspo.is_revoked = 0
  left join marking_score_options mso
    on mso.id = mspo.score_option_id
  group by t5.uid, t5.window_item_id
  ) t5b
  left join marking_claimed_batches mcb
  on mcb.uid = t5b.uid
  and mcb.marking_window_item_id = t5b.window_item_id
  and mcb.is_training = 0
  left join marking_claimed_batch_responses mcbr
  on mcbr.claimed_batch_id = mcb.id
  and mcbr.is_marked = 1
  group by t5b.uid, t5b.window_item_id
  ) t5c
  ;
`;

// [window_id]
export const SQL_LEAD_SCOR_STATS_EVENTS = `
    -- SQL_LEAD_SCOR_STATS_EVENTS
	  select mimt.uid
		     , mimt.marking_window_item_id as window_item_id
         , max(mimt.id) as task_id
         , sum(mimt.cache_hours_spent) active_time_total
    from marking_window_items mwi
		join marking_item_marker_tasks mimt
		  on mimt.marking_window_item_id = mwi.id
		  and mimt.component_type = 'SCORING'
      and mimt.is_removed = 0
		where mwi.marking_window_id = :window_id
      and mimt.uid in (:uids)
      and mwi.id = mwi.sync_batches_to_wmi_id
    group by mimt.uid, mimt.marking_window_item_id
;`;

// {markingWindowId}
export const SQL_ITEM_COUNTS_AVAILABLE_RESPONSES = `
-- SQL_ITEM_COUNTS_AVAILABLE_RESPONSES
select mwtd.id mwtd_id, nyc_count.n_resp n_resp from 
  marking_window_task_definitions mwtd 
  join
  (select group_to_mwi_id, COUNT(*) n_resp
  from
    (select ta.id test_attempt_id
        , mwi.slug assignment_item_code
        , r.taqr_id
        , ta.uid student_uid
        , CONCAT(u.first_name, ' ', u.last_name) student_name
        , mw.id marking_window_id
        , mw.name marking_window_name
        , mw.is_rescore
        , s.name school_name
        , sc.name class_name
        , mwi.group_to_mwi_id group_to_mwi_id
    from (select mtc.taqr_id
          , mtc.schl_group_id
          , mtc.mwi_id
          , max(mmcbr.id) as resp_claim_id
          , mtc.marking_window_id
        from marking_taqr_cache mtc
        left join (
          select mcbr.id
              , mcbr.taqr_id
              , mcb.marking_window_item_id
              , min(mcbr.is_rescore_indic) is_rescore_indic
          from marking_window_items mwi
          join marking_claimed_batches mcb
            on mcb.marking_window_item_id = mwi.id
            and mwi.marking_window_id = :markingWindowId
          join marking_claimed_batch_responses mcbr
            on mcb.id = mcbr.claimed_batch_id
            and mcbr.is_revoked = 0
            and mcbr.is_rescore_indic = 0
            and (mcbr.is_invalid is null OR mcbr.is_invalid = 0)
            and mcb.is_training = 0
          group by mcbr.taqr_id, mcb.marking_window_item_id
        ) mmcbr
        on mmcbr.taqr_id = mtc.taqr_id
        and mmcbr.marking_window_item_id = mtc.mwi_id
      where mtc.marking_window_id = :markingWindowId
        and mtc.is_material = 0
      group by mtc.taqr_id
    ) r
  join test_attempt_question_responses taqr
    on r.taqr_id = taqr.id
  join test_attempts ta
    on ta.id = taqr.test_attempt_id
  join schools s
    on s.group_id = r.schl_group_id
  join school_class_test_sessions scts
    on ta.test_session_id = scts.test_session_id
  join school_classes sc
    on sc.id = scts.school_class_id
  join users u on
    ta.uid = u.id
  join marking_windows mw
    on mw.id = r.marking_window_id
  join marking_window_items mwi
    on mwi.id = r.mwi_id
  where r.resp_claim_id is null
  group by taqr.test_attempt_id, mwi.slug) nyc
  group by group_to_mwi_id) nyc_count
on mwtd.cached__group_to_mwi_id = nyc_count.group_to_mwi_id
;
`

export const SQL_ITEM_COUNTS_AVAILABLE_RESPONSES_CAEC = `
select mwtd.id mwtd_id, nyc_count.n_resp n_resp from 
  marking_window_task_definitions mwtd 
  join
  (select group_to_mwi_id, COUNT(*) n_resp
  from
    (select ta.id test_attempt_id
        , mwi.slug assignment_item_code
        , r.taqr_id
        , ta.uid student_uid
        , CONCAT(u.first_name, ' ', u.last_name) student_name
        , mw.id marking_window_id
        , mw.name marking_window_name
        , mw.is_rescore
        , i.name institution_name
        , twtar.long_name twtar_name
        , mwi.group_to_mwi_id group_to_mwi_id
    from (select mtc.taqr_id
          , mtc.schl_group_id
          , mtc.mwi_id
          , max(mmcbr.id) as resp_claim_id
          , mtc.marking_window_id
        from marking_taqr_cache mtc
        left join (
          select mcbr.id
              , mcbr.taqr_id
              , mcb.marking_window_item_id
              , min(mcbr.is_rescore_indic) is_rescore_indic
          from marking_window_items mwi
          join marking_claimed_batches mcb
            on mcb.marking_window_item_id = mwi.id
            and mwi.marking_window_id = :markingWindowId
          join marking_claimed_batch_responses mcbr
            on mcb.id = mcbr.claimed_batch_id
            and mcbr.is_revoked = 0
            and mcbr.is_rescore_indic = 0
            and (mcbr.is_invalid is null OR mcbr.is_invalid = 0)
            and mcb.is_training = 0
          group by mcbr.taqr_id, mcb.marking_window_item_id
        ) mmcbr
        on mmcbr.taqr_id = mtc.taqr_id
        and mmcbr.marking_window_item_id = mtc.mwi_id
      where mtc.marking_window_id = :markingWindowId
        and mtc.is_material = 0
      group by mtc.taqr_id
    ) r
  join test_attempt_question_responses taqr
    on r.taqr_id = taqr.id
  join test_attempts ta
    on ta.id = taqr.test_attempt_id
  join test_window_td_alloc_rules twtar
    on twtar.id = ta.twtdar_id 
    and twtar.is_custom = 0
  join institutions i 
    on i.group_id = r.schl_group_id
  join test_sessions ts
    on ts.instit_group_id = i.group_id
  join users u on
    ta.uid = u.id
  join marking_windows mw
    on mw.id = r.marking_window_id
  join marking_window_items mwi
    on mwi.id = r.mwi_id
  where r.resp_claim_id is null
  group by taqr.test_attempt_id, mwi.slug) nyc
  group by group_to_mwi_id) nyc_count
on mwtd.cached__group_to_mwi_id = nyc_count.group_to_mwi_id
;


` 
export const SQL_LEAD_SCOR_SCORED = (config:{isSupervisorView?: boolean, isUidGroupRestricted?: boolean}) => `
-- SQL_LEAD_SCOR_SCORED
select mcbr.*
, max(mcbr_max.is_rescore_indic) is_rescore_indic
, max(mcbr_max.is_read_rules_processed) is_read_rules_processed 
, mcb_max_valid.uid max_valid_read_uid
from (
  select mcbg.id
      , mcbg.is_available_to_scorers
      , mwi.caption
      , mwi.slug assignment_item_code
      , taqr.test_attempt_id
      , mcbg.created_by_uid
      , CONCAT(u.first_name, ' ', u.last_name) scorer_name
      , mcbg.created_on date
      , count(distinct case when mcbr.is_invalid is null or mcbr.is_invalid = 0 then mcbr.marker_read_id end) num_reads
      , max(mcbr.inspected) is_partially_inspected
      , IF(sum(mcbr.inspected) = count(mcbr.id), 1, 0) is_inspected
      , max(mcbr.marker_read_id) max_read
      , max(mcbr.marker_read_id * CASE WHEN (mcbr.is_invalid = 0 or mcbr.is_invalid is null) THEN 1 ELSE 0 END) max_valid_read
      , MIN(COALESCE(mcbr.is_invalid, 0)) is_invalid
      , MAX(mcb.is_expert_score * (CASE WHEN mcbr.is_invalid = 1 THEN 0 ELSE 1 END)) is_expert_score
      , CASE WHEN SUM(CASE WHEN (mso.slug_code = 'INS' OR mso.slug_code = 'NO_RESPONSE') THEN 1 ELSE 0 END) > 0 THEN 1 ELSE 0 END AS includes_nr
      ${ config.isUidGroupRestricted ? `, max(mcb.uid in (:uids)) has_target_scorers_read` : ``}
  from marked_claimed_batches_groups mcbg
  join marking_claimed_batch_responses mcbr
    on mcbr.claimed_batch_group_id = mcbg.id
    and mcbr.is_revoked = 0
  join marking_claimed_batches mcb
    on mcb.id = mcbr.claimed_batch_id
  join marking_window_items mwi
    on mwi.id = mcb.marking_window_item_id
    and mwi.id = mwi.group_to_mwi_id  
  left join users u
    on u.id = mcbg.created_by_uid
  left join test_attempt_question_responses taqr
    on taqr.id = mcbr.taqr_id
  left join marking_response_scores mrs
  	on mrs.batch_response_id = mcbr.id
  left join marking_score_profile_options mspo
  	on mspo.id = mrs.score_option_id
  left join marking_score_options mso
  	on mso.id = mspo.score_option_id
  where mcbg.is_revoked = 0
    and mwi.marking_window_id = :markingWindowId
    ${ config.isSupervisorView ? `and mwi.slug = :assignmentItemCode` : '' }
  group by mcbg.id
  order by mcbg.id
) mcbr
join marking_claimed_batch_responses mcbr_max
	on mcbr_max.claimed_batch_group_id = mcbr.id
	and mcbr_max.marker_read_id = mcbr.max_read
  ${ config.isUidGroupRestricted ? `and mcbr.has_target_scorers_read = 1` : ``}
left join marking_claimed_batch_responses mcbr_max_valid
  on mcbr_max_valid.claimed_batch_group_id = mcbr.id
  and mcbr_max_valid.marker_read_id = mcbr.max_valid_read
  and (mcbr_max_valid.is_invalid = 0 or mcbr_max_valid.is_invalid is null)
left join marking_claimed_batches mcb_max_valid
  on mcb_max_valid.id = mcbr_max_valid.claimed_batch_id
group by mcbr.id
;
`
export const SQL_LEADER_SUPER_BY_MCBR_AND_UID = `/* SQL_LEADER_SUPER_BY_MCBR_AND_UID */
  select mw.group_id, mcb.uid
  from marking_claimed_batch_responses mcbr 
  join marking_claimed_batches mcb 
    on mcb.id = mcbr.claimed_batch_id 
  join marking_window_items mwi 
    on mwi.id = mcb.marking_window_item_id 
  join marking_windows mw 
    on mw.id = mwi.marking_window_id  
  join user_roles ur 
    on ur.group_id = mw.group_id 
    and ur.is_revoked = 0 
    and ur.role_type in ('mrkg_supr','mrkg_ctrl')
    and ur.uid = :uid
  where mcbr.id = :mcbr_id
`

// Input: markingWindowId
export const SQL_LEAD_SCOR_FLAG_SENSI_BG = `/* SQL_LEAD_SCOR_FLAG_SENSI_BG */
select distinct mcbr.claimed_batch_group_id  
from marking_window_items mwi 
join marking_claimed_batches mcb 
	on mcb.marking_window_item_id = mwi.id 
join marking_claimed_batch_responses mcbr 
	on mcbr.claimed_batch_id = mcb.id 
join marking_response_scores mrs 
	on mrs.batch_response_id = mcbr.id 
join marking_score_profile_flags mspf 
	on mspf.id = mrs.score_flag_id 
join marking_score_options mso 
	on mso.id = mspf.score_option_id 
where mwi.marking_window_id in (:markingWindowId)
	and mso.is_sensitive = 1
`

// Input: isCIN, markingWindowId
export const SQL_LEAD_SCOR_FLAGED = (config:{isBatchGroupFilter?:boolean, isSupervisorView?: boolean, isUidGroupRestricted?: boolean}) => `/* SQL_LEAD_SCOR_FLAGED */
select mcbr.*
, max(mcbr_max.is_rescore_indic) is_rescore_indic
, max(mcbr_max.is_read_rules_processed) is_read_rules_processed 
, mcb_max_valid.uid max_valid_read_uid
from (
  select mcbg.id
      , mcbg.is_available_to_scorers
      , mwi.caption
      , mwi.slug assignment_item_code
      , taqr.test_attempt_id
      , GROUP_CONCAT(DISTINCT ifnull(mso.slug_code, mso.slug) ORDER BY mso.slug ASC) AS flags
      , CONCAT('[',GROUP_CONCAT(DISTINCT mrs.meta), ']') AS comments -- todo: not a good way of doing this
      , mcbg.created_by_uid
      , CONCAT(u.first_name, ' ', u.last_name) scorer_name
      , mcbg.created_on date
      , count(distinct case when mcbr.is_invalid is null or mcbr.is_invalid = 0 then mcbr.marker_read_id end) num_reads
      , max(mcbr.read_id) max_read
      , max(mcbr.marker_read_id * CASE WHEN (mcbr.is_invalid = 0 or mcbr.is_invalid is null) THEN 1 ELSE 0 END) max_valid_read
      , max(mcbr.inspected) is_partially_inspected
      , IF(sum(mcbr.inspected) = count(mcbr.id), 1, 0) is_inspected
      , COUNT(case when mrs.score_flag_id is not null then mcbr.id end ) flagged_count
      , COUNT(case when mrs.score_flag_id is null and mrs.score_option_id is not null then mcbr.id end ) non_flagged_count
      ${ config.isUidGroupRestricted ? `, max(mcb.uid in (:uids)) has_target_scorers_read` : ``}
  from marked_claimed_batches_groups mcbg
  join marking_claimed_batch_responses mcbr
    on mcbr.claimed_batch_group_id = mcbg.id
    and mcbr.is_revoked = 0
    and mcbr.taqr_id
    and (mcbr.is_invalid is null or mcbr.is_invalid = 0 or mcbg.is_available_to_scorers = 1 or :isCIN = 1)
  join marking_claimed_batches mcb
    on mcb.id = mcbr.claimed_batch_id
  join marking_window_items mwi
    on mwi.id = mcb.marking_window_item_id
    and mwi.id = mwi.sync_batches_to_wmi_id
  left join users u
    on u.id = mcbg.created_by_uid
  JOIN marking_response_scores mrs
    ON mrs.batch_response_id = mcbr.id
  left JOIN marking_score_profile_flags mspf
    ON mspf.id = mrs.score_flag_id
  left JOIN marking_score_options mso
    ON mso.id = mspf.score_option_id
  LEFT JOIN test_attempt_question_responses taqr
    on taqr.id = mcbr.taqr_id
  left join marking_score_profile_options scored_mspo
  	on scored_mspo.id = mrs.score_option_id
  left join marking_score_options scored_mso
  	on scored_mso.id = scored_mspo.score_option_id
  where mcbg.is_revoked = 0
    and mwi.marking_window_id = :markingWindowId
    ${ config.isBatchGroupFilter ? `and mcbg.id in (:claimed_batch_group_ids)` : '' }
    ${ config.isSupervisorView ? `and mwi.slug = :assignmentItemCode` : '' }
  group by mcbg.id
  order by mcbg.id
) mcbr
join marking_claimed_batch_responses mcbr_max
	on mcbr_max.claimed_batch_group_id = mcbr.id
	and mcbr_max.read_id = mcbr.max_read
	and mcbr.flagged_count > 0
	and mcbr.flags is not null
${ config.isUidGroupRestricted ? `and mcbr.has_target_scorers_read = 1` : ``}
left join marking_claimed_batch_responses mcbr_max_valid
  on mcbr_max_valid.claimed_batch_group_id = mcbr.id
  and mcbr_max_valid.marker_read_id = mcbr.max_valid_read
  and (mcbr_max_valid.is_invalid = 0 or mcbr_max_valid.is_invalid is null)
left join marking_claimed_batches mcb_max_valid
  on mcb_max_valid.id = mcbr_max_valid.claimed_batch_id
group by mcbr.id
;
`

export const SQL_LEAD_TA_ID_MTC_RECS = `
  -- SQL_LEAD_TA_ID_MTC_RECS
  SELECT mtc.taqr_id
  , mtc.mwi_id
  , mwi.group_to_mwi_id
  , mwi.slug
  , mtc.schl_group_id
  , mtc.student_accommodation_cache
  FROM test_attempt_question_responses taqr
  JOIN marking_taqr_cache mtc
    ON mtc.taqr_id = taqr.id
  JOIN marking_window_items mwi
    ON mwi.id = mtc.mwi_id
  WHERE test_attempt_id = :test_attempt_id
  AND mwi.group_to_mwi_id = :group_to_mwi_id
  GROUP BY taqr.id, mtc.mwi_id
  ORDER BY mwi.group_to_mwi_id, mtc.mwi_id
  ;
`

export const SQL_LEAD_SCOR_NOT_YET_CLAIMED = (limit:number) =>  `
  -- SQL_LEAD_SCOR_NOT_YET_CLAIMED
  select ta.id test_attempt_id
    , mwi.slug assignment_item_code
    , r.taqr_id
    , ta.uid student_uid
    , CONCAT(u.first_name, ' ', u.last_name) student_name
    , mw.id marking_window_id
    , mw.name marking_window_name
    , mw.is_rescore
    , s.name school_name
    , sc.name class_name
    , mwi.group_to_mwi_id
  from (
    select mtc.taqr_id
        , mtc.schl_group_id
        , mtc.mwi_id
        , max(mmcbr.id) as resp_claim_id
        , mtc.marking_window_id
    from marking_taqr_cache mtc
    left join (
      -- select * from (
        select mcbr.id
            , mcbr.taqr_id
            , mcb.marking_window_item_id
            , min(mcbr.is_rescore_indic) is_rescore_indic
        from marking_claimed_batches mcb
        join marking_claimed_batch_responses mcbr
          on mcb.id = mcbr.claimed_batch_id
          and mcbr.is_revoked = 0
          and mcbr.is_rescore_indic = 0
          and (mcbr.is_invalid is null OR mcbr.is_invalid = 0)
          and mcb.is_training = 0
        join marking_window_items mwi
          on mwi.id = mcbr.window_item_id
        where mwi.marking_window_id = :markingWindowId
        group by mcbr.taqr_id
              , mcb.marking_window_item_id
      -- ) mmcbr_inner
    ) mmcbr
      on mmcbr.taqr_id = mtc.taqr_id
      and mmcbr.marking_window_item_id = mtc.mwi_id
    where mtc.marking_window_id = :markingWindowId
      and mtc.is_material = 0
    group by mtc.taqr_id
  ) r
  join test_attempt_question_responses taqr
    on r.taqr_id = taqr.id
  join test_attempts ta
    on ta.id = taqr.test_attempt_id
  join schools s
    on s.group_id = r.schl_group_id
  join school_class_test_sessions scts
    on ta.test_session_id = scts.test_session_id
  join school_classes sc
    on sc.id = scts.school_class_id
  join users u on
    ta.uid = u.id
  join marking_windows mw
    on mw.id = r.marking_window_id
  join marking_window_items mwi
    on mwi.id = r.mwi_id
  where r.resp_claim_id is null
  group by taqr.test_attempt_id, mwi.slug
  order by ta.id
  LIMIT ${limit}
  ;
`
export const SQL_LEAD_SCOR_NOT_YET_CLAIMED_TEST_CENTRE = (limit:number) =>  `
  -- SQL_LEAD_SCOR_NOT_YET_CLAIMED
  select ta.id test_attempt_id
    , mwi.slug assignment_item_code
    , r.taqr_id
    , ta.uid student_uid
    , CONCAT(u.first_name, ' ', u.last_name) student_name
    , mw.id marking_window_id
    , mw.name marking_window_name
    , mw.is_rescore
    , i.name institution_name
    , twtar.long_name twtar_name
    , mwi.group_to_mwi_id
  from (
    select mtc.taqr_id
        , mtc.schl_group_id
        , mtc.mwi_id
        , max(mmcbr.id) as resp_claim_id
        , mtc.marking_window_id
    from marking_taqr_cache mtc
    left join (
      -- select * from (
        select mcbr.id
            , mcbr.taqr_id
            , mcb.marking_window_item_id
            , min(mcbr.is_rescore_indic) is_rescore_indic
        from marking_claimed_batches mcb
        join marking_claimed_batch_responses mcbr
          on mcb.id = mcbr.claimed_batch_id
          and mcbr.is_revoked = 0
          and mcbr.is_rescore_indic = 0
          and (mcbr.is_invalid is null OR mcbr.is_invalid = 0)
          and mcb.is_training = 0
        join marking_window_items mwi
          on mwi.id = mcbr.window_item_id
        where mwi.marking_window_id = :markingWindowId
        group by mcbr.taqr_id
              , mcb.marking_window_item_id
      -- ) mmcbr_inner
    ) mmcbr
      on mmcbr.taqr_id = mtc.taqr_id
      and mmcbr.marking_window_item_id = mtc.mwi_id
    where mtc.marking_window_id = :markingWindowId
      and mtc.is_material = 0
    group by mtc.taqr_id
  ) r
  join test_attempt_question_responses taqr
    on r.taqr_id = taqr.id
  join test_attempts ta
    on ta.id = taqr.test_attempt_id
  join test_window_td_alloc_rules twtar
    on twtar.id = ta.twtdar_id 
    and twtar.is_custom = 0
  join institutions i 
    on i.group_id = r.schl_group_id
  join test_sessions ts
  on ts.instit_group_id = i.group_id 
  join users u on
    ta.uid = u.id
  join marking_windows mw
    on mw.id = r.marking_window_id
  join marking_window_items mwi
    on mwi.id = r.mwi_id
  where r.resp_claim_id is null
  group by taqr.test_attempt_id, mwi.slug
  order by ta.id
  LIMIT ${limit}
  ;
`

// {window_id, uids}
export const SQL_LEAD_SCOR_STATS_LAST_ACTIVE_ON = `
  -- SQL_LEAD_SCOR_STATS_LAST_ACTIVE_ON
  select mcb.uid
       , mcb.marking_window_item_id
       , MAX(mrs.last_touched_on) as last_active_on
  from marking_window_items mwi
  join marking_claimed_batches mcb
    on mcb.marking_window_item_id = mwi.id
    and mcb.component_type in ('QUALIFYING', 'SCORING')
  join marking_claimed_batch_responses mcbr
    on mcbr.claimed_batch_id = mcb.id
  join marking_response_scores mrs
    on mrs.batch_response_id = mcbr.id
  where mwi.marking_window_id = :window_id
    and mcb.uid in (:uids)
    and mwi.id = mwi.sync_batches_to_wmi_id
  group by mcb.uid, mcb.marking_window_item_id;
;`;

// [window_item_id]
export const SQL_ITEMS_REALLOC_CANDIDATES = (limit:number) => `
-- SQL_ITEMS_REALLOC_CANDIDATES
select mcbrt.mcbr_id from (
  select mcbr.id mcbr_id
     , mcb.marking_window_item_id window_item_id
     , mcbr.taqr_id
     , max(mcbr.is_rescore_indic) is_rescore_indic
  from marking_claimed_batches mcb
  join marking_claimed_batch_responses mcbr
    on mcbr.claimed_batch_id = mcb.id
    and mcb.is_training = 0
    and mcbr.is_revoked = 0
    and mcbr.is_invalid is null
    and mcbr.is_validity is null
  where mcb.marking_window_item_id = ?
  group by mcbr.taqr_id
) mcbrt
where mcbrt.is_rescore_indic = 0
limit ${+limit}
`

// [window_item_id, window_item_id]
export const SQL_ITEM_AVAIL_CLAIM = `
-- SQL_ITEM_AVAIL_CLAIM
select r.taqr_id, r.schl_group_id
from (
	select mtc.taqr_id as taqr_id
		 , mtc.schl_group_id
		 , mmcbr.id as resp_claim_id
	from marking_taqr_cache mtc
	left join (
		select mcbr.id, mcbr.taqr_id, mcb.marking_window_item_id
		from marking_claimed_batches mcb
		join marking_claimed_batch_responses mcbr
		  on mcb.id = mcbr.claimed_batch_id
		  and mcbr.is_revoked = 0
          and mcbr.is_rescore_indic = 0 -- included
          and mcbr.is_invalid is null
		where mcb.marking_window_item_id = ?
		  and mcb.is_training = 0
	) mmcbr
		on mmcbr.taqr_id = mtc.taqr_id
		and mmcbr.marking_window_item_id = mtc.mwi_id
	where mtc.mwi_id = ?
	  and mtc.is_material = 0
) r
where r.resp_claim_id is null
`

// [window_item_id, window_item_id]
export const SQL_ITEM_AVAIL_SCORE = `
-- SQL_ITEM_AVAIL_SCORE
select r.taqr_id, r.schl_group_id
from (
	select mtc.taqr_id as taqr_id
		 , mtc.schl_group_id
		 , mmcbr.id as resp_claim_id
	from marking_taqr_cache mtc
	left join (
		select mcbr.id, mcbr.taqr_id, mcb.marking_window_item_id
		from marking_claimed_batches mcb
		join marking_claimed_batch_responses mcbr
		  on mcb.id = mcbr.claimed_batch_id
		  and mcbr.is_revoked = 0
      and mcbr.is_rescore_indic = 0 -- included
      and mcbr.is_marked = 1
      and mcbr.is_invalid is null
		where mcb.marking_window_item_id = ?
		  and mcb.is_training = 0
	) mmcbr
		on mmcbr.taqr_id = mtc.taqr_id
		and mmcbr.marking_window_item_id = mtc.mwi_id
	where mtc.mwi_id = ?
	  and mtc.is_material = 0
) r
where r.resp_claim_id is null
`

// [window_item_id]
export const SQL_ITEMS_REALLOC_SUMMARY = `
-- SQL_ITEMS_REALLOC_SUMMARY
select mcbrt.window_item_id
     , sum(mcbrt.taqr_tally) num_responses
     , sum(mcbrt.w_rescore_tally) num_responses_w_rescore
     , sum(mcbrt.rescore_indic_tally) num_rel_realloc
     , sum(mcbrt.rescore_indic_tally)/sum(mcbrt.taqr_tally) perc_reliability_target_rescore
     , (sum(mcbrt.w_rescore_tally)/sum(mcbrt.taqr_tally))-1 perc_reliability_actual_rescore
from (
	select mcbr.id
		 , mcb.marking_window_item_id window_item_id
		 , mcbr.taqr_id
		 , count(distinct mcbr.taqr_id) taqr_tally
         , count(distinct mcbr.id) w_rescore_tally
		 , max(mcbr.is_rescore_indic) rescore_indic_tally
	from marking_claimed_batches mcb
	join marking_claimed_batch_responses mcbr
		on mcbr.claimed_batch_id = mcb.id
        and mcb.is_training = 0
		and mcbr.is_revoked = 0
		and mcbr.is_invalid is null
        and mcbr.is_validity is null
	where mcb.marking_window_item_id in (?)
	group by mcb.marking_window_item_id, mcbr.taqr_id
) mcbrt
group by mcbrt.window_item_id
`

// [uid, window_item_id, component_type]
export const SQL_SCORER_SCORING_TASK = `
-- SQL_SCORER_SCORING_TASK
select mimt.marking_window_item_id
     , mimt.id
     , mimt.status
     , mimt.allow_revisit
     , cache_batches_rem
     , mimt.is_pass_fail
     , mimt.pair_id as marking_pair_id
from  marking_item_marker_tasks mimt
where mimt.uid = ?
  and mimt.marking_window_item_id in (?)
  and mimt.component_type = ?
  and mimt.is_removed = 0
-- and mimt.is_revoked = 0
`

// {window_item_ids}
export const SQL_ITEM_PAIR_MARKING_STATUS = `
  -- SQL_ITEM_PAIR_MARKING_STATUS, < 0.1 sec
  select 
  CASE WHEN (mw.is_paired_marking_allowed and mbap.is_paired_marking) THEN 1 ELSE 0 END as is_paired_marking
  from marking_window_items mwi
  join marking_windows mw 
    on mw.id = mwi.marking_window_id
  join marking_batch_alloc_policies mbap
    on mbap.id = mwi.batch_alloc_policy_id
  where mwi.id in (:window_item_ids)
`
// {pair_ids}
export const SQL_ITEM_PAIR_USERS = `
  -- SQL_ITEM_PAIR_USERS, < 0.1 sec
  select 
  mpu.pair_id
  , mpu.uid
  , mpu.is_read_only
  , mp.group_to_mwi_id
  from marking_pairs_users mpu
  join marking_pairs mp
    on mp.id = mpu.pair_id
  where mp.id in (:pair_ids)
  and mp.is_revoked = 0;
`
// {window_item_ids, uid, activeUid}
export const SQL_ITEM_PAIR_DRAFT_STATUS = `
  -- SQL_ITEM_PAIR_DRAFT_STATUS
  select mcb.id mcb_id, 
      mcbpd.is_marked,
      mcbpd.uid
      from marking_window_items mwi 
      join marking_claimed_batches mcb
        on mwi.id = mcb.marking_window_item_id 
      join marking_claimed_batches_paired_draft mcbpd 
        on mcbpd.marking_claimed_batch_id = mcb.id 
        and mcbpd.is_revoked = 0
      where mwi.id in (:window_item_ids)
        and mcb.uid = :activeUid
        and mcb.is_revoked = 0
        and mcb.is_marked = 0
        and mcb.is_training = 0
      order by mcb.id asc
    ;
`

// [window_id]
export const SQL_SCORER_SCORING_TASKS = `
-- SQL_SCORER_SCORING_TASKS
select mimt.id
  , mimt.marking_window_item_id as window_item_id
  , mimt.uid
from marking_window_items mwi
join marking_item_marker_tasks mimt
  on mimt.marking_window_item_id = mwi.id
  and mimt.component_type = 'SCORING'
  and mimt.is_revoked = 0
  and mimt.is_removed = 0
where mwi.marking_window_id = ?
`


export const SQL_BATCH_GROUP_INFO = `
select  mcbg.id
  , mcbg.created_by_uid uid
  , MAX(mcbr.marker_read_id) min_marker_read_id
  from marked_claimed_batches_groups mcbg
  join marking_claimed_batch_responses mcbr
    on mcbr.claimed_batch_group_id = mcbg.id
    and mcbr.is_revoked = 0
  where mcbg.id = ?
    and mcbg.is_revoked = 0
  group by mcbg.id
    ;
`

// {claimed_batch_group_id:id, markerReadId:markerReadId, markingWindowId: markingWindowId}
export const SQL_BATCHES_INFO = `
select mcb.id
  , mcb.marking_window_item_id
  , mcb.is_marked
  , mwi.item_id
  , mwi.slug item_slug
  , mwi.marking_window_id
  , mwi.score_profile_group_id
  , mspg.group_name
  , mspg.order mspg_order
  , msp.description scale_name
  , msp.order msp_order
  , mcb.created_on 
  from marking_claimed_batch_responses mcbr 
  join marking_claimed_batches mcb 
    on mcb.id = mcbr.claimed_batch_id
  join marking_window_items mwi 
    on mwi.id = mcb.marking_window_item_id
  left join marking_score_profiles msp 
    on msp.id = mwi.score_profile_id
  left join marking_score_profile_groups mspg
    on mwi.score_profile_group_id = mspg.id
  where mcbr.claimed_batch_group_id = :claimed_batch_group_id
    and mcbr.marker_read_id = :markerReadId
    and mwi.marking_window_id = :markingWindowId
    order by mwi.slug, mspg.order
;
`

// {markingWindowId}
export const SQL_AVAIL_MARKERS = `
  SELECT mwum.value marker_id
       , mimt.uid
       , a.email 
       , ur.id ur_id 
  FROM marking_item_marker_tasks mimt
  JOIN marking_window_items mwi 
    ON mwi.id = mimt.marking_window_item_id 
  JOIN marking_window_user_meta mwum 
    ON mwum.marker_uid = mimt.uid
    AND mwum.marking_window_id = mwi.marking_window_id 
    AND mwum.meta_key = 'Marker_Number'
    AND mwum.is_revoked = 0
  JOIN marking_windows mw 
  	on mw.id = mwi.marking_window_id 
  JOIN auths a
    ON a.uid = mimt.uid
  JOIN user_roles ur  
  	on ur.group_id = mw.group_id
  	and ur.uid = a.uid 
  	and ur.is_revoked = 0
  WHERE mwi.marking_window_id = :markingWindowId
  group by mimt.uid
  order by marker_id
`
// [id, itemId]
export const SQL_SCORER_HISTORY=`
SELECT 
    ROW_NUMBER() OVER (ORDER BY mcbr.created_on) as read_num
  , mcb.uid
  , mcb.is_marked
  , mcb.is_expert_score
  , mcbr.created_on
  , mcbr.inspected
  , mcbr.inspected_by_uid
  , mcbr.is_invalid
  , mcbr.marker_read_id
  , mcbr.is_sent_back
  , mcbr.is_rescore
  , mcbr.is_before_rescore
  , mcbr.is_valid_before_rescore
  , gm.group_number marking_group_number_when_scored
  , gm.group_revoked_on marking_group_number_when_scored_revoked_on
FROM marking_claimed_batch_responses mcbr
join marking_claimed_batches mcb
  on mcb.id = mcbr.claimed_batch_id 
left join (
	select 
    mg.group_number
    , mg.revoked_on group_revoked_on
    , mgu.uid
    , mgu.created_on
    , mgu.is_revoked
    , mgu.revoked_on
    from marking_groups mg
    join marking_groups_users mgu
      on mgu.group_id = mg.id
	where mg.marking_window_id = :markingWindowId
) as gm
on gm.uid = mcb.uid
and gm.created_on <= mcbr.marked_on
and (gm.is_revoked = 0 or gm.revoked_on >= mcbr.marked_on)
where mcbr.claimed_batch_group_id = :id
and mcb.marking_window_item_id = :itemId
and mcbr.is_revoked = 0
group by mcbr.id
order by mcbr.created_on
;
`

// [window_id]
export const SQL_LEAD_ITEM_STATS = `
-- SQL_LEAD_ITEM_STATS
select t4.*
  , sum(__mrs_tag1) as mrs_stage1
  , sum(__mrs_score_option) as mrs_stage2
  , sum(__mrs_is_proposed) as mrs_stage3
  , sum(__mrs_is_included) as mrs_stage4
from (
select t3.*
  , if (mrs.tag1  is null, 0, 1) as __mrs_tag1
  , if (mrs.tag2  is null, 0, 1) as __mrs_is_proposed
  , if (mrs.score_option_id is null, 0, 1) as __mrs_score_option
  , mrs.is_included as __mrs_is_included
from (
select t2.*
    , sum(t2.__num_assigned) as num_assigned
    , sum(t2.__is_marked) as num_marked
    , sum(t2.__is_random_alloc) as num_random_alloc
    , sum(t2.__is_dbl_marked) as num_dbl_marked
from (
  select t.*
      , max(mcbr.is_marked) as __is_marked
      , IF(sum(mcbr.is_marked)>1, 1, 0) as __is_dbl_marked
      , max(mcbr.is_rescore_indic) as __is_random_alloc
      , count(mcbr.id) as __num_assigned
  from (
    SELECT mwi.*
        , mwi.cached_taqr_tally as num_responses
        , msp.short_name as score_profile_name
        , msp.description as score_profile_description
        , mbap.description as policy_description
        , mbap.notes as policy_notes
        , mbap.max_batch_num_claim
        , mbap.batch_size
        , mbap.batch_validity_num
        , mbap.claim_dur_hours
        , mbap.access_rolling_batch_num
        , mbap.access_rolling_min_exact_rate
        , mbap.access_rolling_min_exact_adj_rate
        , mbap.rescore_rolling_batch_num
        , mbap.rescore_rolling_min_exact_rate
        , mbap.rescore_rolling_min_exact_adj_rate
        , mbap.auto_rescore_rate
    FROM marking_window_items mwi
    join marking_score_profiles msp
      on msp.id = mwi.score_profile_id
    join marking_batch_alloc_policies mbap
      on mbap.id = mwi.batch_alloc_policy_id
    where mwi.marking_window_id = ?
    group by mwi.id
  ) t
  left join (
    select mcbr0.id, mcbr0.window_item_id, mcbr0.taqr_id, mcbr0.is_marked, mcbr0.is_random_alloc, mcbr0.is_rescore_indic
    from marking_claimed_batches mcb
    join marking_claimed_batch_responses mcbr0
      on mcbr0.claimed_batch_id = mcb.id
      and mcbr0.is_revoked = 0
      and mcbr0.is_validity is null
    where mcb.is_training = 0
  ) mcbr
  on mcbr.window_item_id = t.id
  group by t.id, mcbr.taqr_id
) t2
group by t2.id
) t3
left join marking_response_selections mrs
  on mrs.window_item_id = t3.id
  and (
       mrs.tag1 is not null
    or mrs.score_option_id is not null
    or mrs.is_included = 1
  )
) t4
group by t4.id
;`

export const SQL_NUM_TQAR_BY_ITEM = `
    -- SQL_NUM_TQAR_BY_ITEM
    select t.mwi_id
         , count(0) as num_responses
    from (
        select mwi.id as mwi_id
             , count(0) as num_responses
        ${marking_window_taqr_pre(false)}
        where mtc.mwi_id IN (?)
          and mtc.is_material = 0
    ) t
    group by t.mwi_id
;`;

// select t4.* , count(0) as num_responses FROM ( select t3.* 	FROM ( ) t3 join
// marking_window_test_window mwtw   on mwtw.marking_window_id =
// t3.marking_window_id left join test_sessions ts   on ts.test_window_id =
// mwtw.test_window_id left join school_class_test_sessions scts   on
// scts.test_session_id = ts.id left join school_classes sc   on sc.id =
// scts.school_class_id left join test_attempts ta   on ts.id =
// ta.test_session_id left join test_attempt_question_responses taqr   on ta.id
// = taqr.test_attempt_id   and t3.item_id = taqr.test_question_id group by
// t3.id,taqr.id ) t4 group by t4.id and ${taqr_nr_clause} and
// ${taqr_invalid_clause} [window_id]
export const SQL_LEAD_ITEMS_VALIDITY_OVERVIEW_CORE = `
  -- SQL_LEAD_ITEMS_VALIDITY_OVERVIEW_CORE
  SELECT mwi.*
      , mbap.batch_size
      , mbap.batch_validity_num
  FROM marking_window_items mwi
  join marking_batch_alloc_policies mbap
    on mbap.id = mwi.batch_alloc_policy_id
  where mwi.marking_window_id = ?
  group by mwi.id
;`

// < 1 sec
// {sync id, set_id, taqr_id, test_attempt_id}
export const SQL_TAQR_EXEMPLARS_BY_ITEM = (isSet: boolean = false, isTaqrId: boolean = false, isTestAttemptId: boolean = false, offset?: number, limit?: number, stage_num?: number, is_search?: boolean, isForeignId?: boolean, isTagFilter?: boolean) =>  `
  select * from 
  (
    select
    mtc.taqr_id as id
  , mtc.ta_id as test_attempt_id
  , schl.foreign_id as schl_foreign_id
  , schl.group_id as schl_group_id
  , mrsel.id as mrsel_id
  , mrsel.tag1
  , mrsel.tag2
  , mrsel.mwi_id_to_score_option_id
  , mrsel.is_expert_score
  , mrsel.expert_marker_read_id
  , mrsel.rationale
  , mwi.id as mwi_id
  , rset.id as set_id
  , mrset.response_set_order
  , mrset.response_set_num
  , mrset.id as mrset_id
  , if (mrsel.tag1 is not null, 1, 0) is_range_tag
  , if (mrsel.tag2 is not null, 1, 0) is_prop_tag
  , if (mrsel.mwi_id_to_score_option_id is not null and mrsel.mwi_id_to_score_option_id != '{}', 1, 0) is_some_scored
  , if (rset.id is not null, 1, 0) is_included
  from marking_taqr_cache mtc
  join marking_window_items mwi
    on mtc.mwi_id = mwi.id
    ${is_search?"":"and mtc.is_standard_confirming = 1"}
    and mwi.sync_batches_to_wmi_id =  :sync_id
  left join schools schl
    on schl.group_id = mtc.schl_group_id
  left join marking_response_selections mrsel
    on mrsel.sync_batches_to_wmi_id = mwi.sync_batches_to_wmi_id
    and mrsel.taqr_id = mtc.taqr_id
  left join marking_response_set_selections mrset
	  on mrset.sync_batches_to_wmi_id = mwi.sync_batches_to_wmi_id
    and mrset.taqr_id = mtc.taqr_id
  left join marking_response_set rset
	  on mrset.set_id = rset.id
    and (rset.is_revoked = 0 or rset.is_revoked is null)
  where mtc.mwi_id = mwi.id -- dummy to initialize a where at first line
  ${isTestAttemptId ? `and mtc.ta_id = :test_attempt_id`: ''}
  ${isForeignId ? `and schl.foreign_id = :schl_foreign_id`: ''}
  ${isTaqrId ? 'and mtc.taqr_id = :taqr_id' : ''}
  ${isSet ? 'and rset.id = :set_id' : ''}
  ${isTagFilter? 'and (mrsel.tag1 is null or mrsel.tag1  not in (:filter_tag_ids))': ''}
  group by mtc.taqr_id, mwi.id, rset.id
  order by mtc.taqr_id, mwi.id
  ) t
  ${ stage_num == 2 ? 'where is_range_tag = 1 or is_some_scored = 1' : ''}
  ${ stage_num == 3 ? 'where is_some_scored = 1 or is_prop_tag = 1' : ''}
  ${ stage_num == 4 ? 'where is_prop_tag = 1 or is_included = 1' : ''}
  ${limit ? 'limit :limit' : ''}
  ${offset ? 'offset :offset' : ''};
`

// [window_item_id]
export const SQL_TAQR_EXEMPLARS_BY_ITEM_OLD = (reqTag?: boolean, reqScore?: boolean, reqIncl?: boolean) => `
  -- SQL_TAQR_EXEMPLARS_BY_ITEM
  select *
  from (
    select
        mtc.taqr_id as id
      , RAND(mtc.taqr_id) as sort_order
      , mrsel.id as mrsel_id
      , mrsel.tag1
      , mrsel.tag2
      , mrsel.score_option_id
      , mrsel.rationale
      , mrsel.is_included
      , mrsel.is_excluded
      , mrset.id as mrset_id
      , mrset.set_id
      , mrset.response_set_order
      , mrset.response_set_num
      , mrs.is_revoked
    ${marking_window_taqr_pre()}
    left join marking_response_selections mrsel
      on mrsel.taqr_id = mtc.taqr_id
      and mrsel.window_item_id = mtc.mwi_id
    right join marking_response_set_selections mrset
      on mrset.selection_id = mrsel.id
      and mrset.window_item_id = mrsel.window_item_id
    right join marking_response_set mrs
      on mrs.id = mrset.set_id
    where mtc.mwi_id = ?

    UNION

    select
        mtc.taqr_id as id
      , RAND(mtc.taqr_id) as sort_order
      , mrsel.id as mrsel_id
      , mrsel.tag1
      , mrsel.tag2
      , mrsel.score_option_id
      , mrsel.rationale
      , mrsel.is_included
      , mrsel.is_excluded
      , mrset.id as mrset_id
      , mrset.set_id
      , mrset.response_set_order
      , mrset.response_set_num
      , mrs.is_revoked
    ${marking_window_taqr_pre()}
    left join marking_response_selections mrsel
      on mrsel.taqr_id = mtc.taqr_id
      and mrsel.window_item_id = mtc.mwi_id
    left join marking_response_set_selections mrset
      on mrset.selection_id = mrsel.id
      and mrset.window_item_id = mrsel.window_item_id
    left join marking_response_set mrs
      on mrs.id = mrset.set_id
    where mtc.mwi_id = ?
  ) t
  where (t.is_excluded = 0 or t.is_excluded is null)
  ${/* Stage 2*/reqTag
    ? `and t.tag1 is not null`
    : ''}
  ${/* Stage 3*/reqScore
        ? `and t.score_option_id is not null`
        : ''}
  ${/* Stage 4*/reqIncl
            ? `and t.tag2 is not null`
            : ''}
  order by t.sort_order
;`;

export const SQL_MARKING_WINDOW_BY_SYNC_ID = `
  -- SQL_MARKING_WINDOW_BY_SYNC_ID
  select mwi.marking_window_id 
  from marking_window_items mwi 
  where mwi.sync_batches_to_wmi_id = :sync_id
`;

export const SQL_STANDARD_CONFIRMING_ACCESS = `
    -- SQL_STANDARD_CONFIRMING_ACCESS
      select mw.id as window_id
     , mw.group_id
     , mw.name as window_name
     , mw.start_on
     , mw.end_on
     , mw.is_archived
     , mw.is_scoring_disabled
     , mw.meta_config
     , mw.is_rescore
     , tw.type_slug as tw_type_slug
     , tw.id as tw_id
     , tw.title as tw_title
    from marking_windows mw
    join user_roles ur
      on ur.group_id = mw.group_id
      and is_revoked = 0
      and ur.uid = :uid
    join marking_window_test_window mwtw
      on mwtw.marking_window_id = mw.id
      and (mwtw.is_material = 0 or mwtw.is_material is null)
    join test_windows tw
      on tw.id = mwtw.test_window_id
    where (role_type = 'mrkg_rafi' and mw.is_scoring_disabled = 0)
      or (role_type = 'mrkg_ctrl' or role_type = 'mrkg_supr')
      and mw.id = :marking_window_id
    group by mw.id
`;

// [taqr_id, window_item_id]
export const SQL_TAQR_EXEMPLAR = `
  -- SQL_TAQR_EXEMPLAR
  select mrs.*
  from marking_response_selections mrs
  where mrs.taqr_id = ?
    and mrs.sync_batches_to_wmi_id = ?
`

// [window_item_id, component_type]
export const SQL_ITEM_COMPONENT_RESPONSE_SET = `
-- SQL_ITEM_COMPONENT_RESPONSE_SET
SELECT mrs.id
     , mrs.set_type_id
     , mrs.set_type_variant_num
FROM marking_response_set_types mrst
join marking_response_set mrs
  on mrs.set_type_id = mrst.id
  and mrs.window_item_id = ?
  and is_revoked is null
where mrst.component_type = ?
order by mrs.set_type_variant_num
;`

// [response_set_id]
export const SQL_ITEM_COMPONENT_SET_RESPONSES = `
-- SQL_ITEM_COMPONENT_SET_RESPONSES
SELECT mrss.*
from marking_response_set_selections mrss
where mrss.set_id = ?
order by mrss.response_set_order
;`

// [window_item_id, uid]
export const SQL_MCBR_ALREADY_CLAIMED = `
  -- SQL_MCBR_ALREADY_CLAIMED
  select mcbr.id mcbr_id
    , mcbr.claimed_batch_group_id
    , mcbr2.id  mcbr_dupe_id
    , mcbr.taqr_id
    , mcbr2.created_on 
    , mcbr2.is_marked 
    , mcbr2.is_revoked 
  from marking_claimed_batch_responses mcbr
  join marking_claimed_batch_responses mcbr2 
    on mcbr.taqr_id = mcbr2.taqr_id
    and mcbr.window_item_id = mcbr2.window_item_id
      and mcbr2.is_revoked = 0
      and (mcbr2.is_invalid = 0 OR mcbr2.is_invalid IS NULL)
    and mcbr2.is_rescore_indic = 0
    and mcbr2.marker_read_id < mcbr.marker_read_id
  where mcbr.id in (:mcbrIds)
  group by claimed_batch_group_id;
`

// [window_item_id, uid]
export const SQL_ITEM_SCORER_VALIDITY_RESPONSES = `
-- SQL_ITEM_SCORER_VALIDITY_RESPONSES
SELECT t.*
FROM (
	SELECT mrss.id
		 , mrss.selection_id as response_selection_id
		 , mrss.taqr_id
		 , mc.id as is_prev_assigned
	FROM marking_response_set_types mrst
	join marking_response_set mrs
	  on mrs.set_type_id = mrst.id
	  and mrs.window_item_id = ?
	  and is_revoked is null
	join marking_response_set_selections mrss
	  on mrss.set_id = mrs.id
	left join (
		select mcbr.id, mcbr.response_selection_id, mcb.uid
		from marking_claimed_batches mcb
        join marking_claimed_batch_responses mcbr
		  on mcbr.is_validity = 1
		  and mcbr.is_validity_repooled is NULL
          and mcbr.claimed_batch_id = mcb.id
		where mcb.uid = ?
    ) mc
      on mc.response_selection_id = mrss.id
	where mrst.slug = 'V'
) t
where t.is_prev_assigned is NULL
group by t.response_selection_id
;`

// [window_item_id]
export const SQL_ITEM_SYNC_BATCH = `
-- SQL_ITEM_SYNC_BATCH
SELECT id
FROM marking_window_items mwi
WHERE group_to_mwi_id = ?
;`


// [window_item_id]
export const SQL_SCORE_PROFILE_INIT_SUPRESSION = `
-- SQL_SCORE_PROFILE_INIT_SUPRESSION
SELECT mwi.id mwi_id
  , msp.supress_condition 
FROM marking_window_items mwi
JOIN marking_score_profiles msp 
  on msp.id = mwi.score_profile_id 
WHERE group_to_mwi_id in (:mwiIds)
;`


export const SQL_PREV_READS = `
  -- SQL_PREV_READS
  SELECT mcb.id mcb_id
      , mcb_l.id mcb_l_id
      , mcbr.id mcbr_id
      , mwi.id mwi_id
      , mcbr.taqr_id
      , mcb_l.uid
      , mcbr_l.id mcbr_l_id
      , mcbr_l.read_id
      , mcbr_l.is_invalid
      , mwum.value marker_number
  from marking_claimed_batches mcb
  join marking_claimed_batch_responses mcbr
    on mcbr.claimed_batch_id  = mcb.id
  join marking_claimed_batch_responses mcbr_l
    on mcbr_l.taqr_id = mcbr.taqr_id
    and mcbr_l.id != mcbr.id
    -- and mcbr_l.is_marked = 1
    and mcbr_l.is_revoked = 0
  join marking_claimed_batches mcb_l
    on mcb_l.marking_window_item_id  = mcb.marking_window_item_id
    and mcb_l.id = mcbr_l.claimed_batch_id
    and mcb_l.is_marked = 1
  join marking_window_items mwi
    on mwi.id = mcb.marking_window_item_id
  left join marking_window_user_meta mwum
    on mwum.marker_uid = mcb_l.uid
    and mwum.marking_window_id  = mwi.marking_window_id
    and mwum.meta_key  = 'Marker_Number'
    and mwum.is_revoked  = 0
  where mcb.id = :claimed_batch_id
  group by marker_number, read_id, mcbr_id
  order by mcbr_l.read_id
`

export const SQL_TAQR_TO_ITEM_GROUP_TO_TAQR_ID = `
  -- SQL_TAQR_TO_ITEM_GROUP_TO_TAQR_ID
  select mwi_src.id mwi_src_id
      , mtc.taqr_id
      , mtc.mwi_id
      , mtc.marking_window_id
  from marking_window_items mwi
  join marking_window_items mwi_src
    on mwi_src.group_to_mwi_id = mwi.group_to_mwi_id
  join marking_taqr_cache mtc_src
    on mtc_src.mwi_id = mwi_src.id
  join marking_taqr_cache mtc
    on mtc.mwi_id = mwi.id
    and mtc.ta_id = mtc_src.ta_id
  where mwi.id = :markingWindowItemId
    and mtc_src.taqr_id = :taqr_id
`


export const SQL_ITEM_GROUP_TO_TAQR_ID = `
-- SQL_ITEM_GROUP_TO_TAQR_ID
select * from (
  select mtc.taqr_id
       , mtc.mwi_id
       , mtc.marking_window_id
       , max(mmcbr.id) as resp_claim_id
  from (
    select taqr.test_attempt_id
    from marking_taqr_cache mtc
    join test_attempt_question_responses taqr
      on taqr.id = mtc.taqr_id
    where mtc.taqr_id = :taqr_id
    group by taqr.test_attempt_id
  ) mtc1
  join test_attempt_question_responses taqr2
    on taqr2.test_attempt_id = mtc1.test_attempt_id
  join marking_taqr_cache mtc
    on taqr2.id = mtc.taqr_id
  left join (
      select mcbr.id
          , mcbr.taqr_id
          , mcb.marking_window_item_id
          , min(mcbr.is_rescore_indic) is_rescore_indic
      from marking_claimed_batches mcb
      join marking_claimed_batch_responses mcbr
        on mcb.id = mcbr.claimed_batch_id
        and mcbr.is_revoked = 0
        and mcbr.is_rescore_indic = 0
        and mcbr.is_invalid is null
      where mcb.marking_window_item_id = :markingWindowItemId
        and mcb.is_training = 0
      group by mcbr.taqr_id
            , mcb.marking_window_item_id
  ) mmcbr
    on mmcbr.taqr_id = mtc.taqr_id
        and mmcbr.marking_window_item_id = mtc.mwi_id
  where mtc.mwi_id = :markingWindowItemId
  group by mtc.taqr_id
) records
where records.resp_claim_id is null
;
`

// [c]
export const SQL_SCORE_OPTION_VAL = `
  -- SQL_SCORE_OPTION_VAL
  select mspo.id, mspo.order, mso.adj_index, mso.value
  from marking_score_profile_options mspo
  join marking_score_options mso
    on mso.id = mspo.score_option_id
  where mspo.id in (?)
    and mspo.is_revoked = 0
`

// [uid, window_item_id]
export const SQL_SCORER_REPORT = `
-- SQL_SCORER_REPORT
select count(DISTINCT mcb.id) num_batches_claimed
     , count(DISTINCT mcbr.id ) num_responses_scored
     , count(case when mcbr.is_validity_overunder > 0 then 1 end) as num_validity_over
     , count(case when mcbr.is_validity_overunder < 0 then 1 end) as num_validity_under
     , sum(mcbr.is_validity) as num_validity_total
	 , sum(mcbr.is_validity_exact) as num_validity_exact
	 , sum(mcbr.is_validity_adj) as num_validity_adj
from marking_claimed_batches mcb
left join marking_claimed_batch_responses mcbr
  on mcbr.claimed_batch_id = mcb.id
  and mcbr.is_marked = 1
where mcb.uid = ?
  and mcb.marking_window_item_id = ?
  and mcb.is_training = 0
;`

// [marking_window_item_id, taqr_id]
export const SQL_SCOR_PREV_SCORING_CHECK = `
-- SQL_SCOR_PREV_SCORING_CHECK
select mcbr.id
     , mcbr.taqr_id
     , mcb.marking_window_item_id
from marking_window_items mwi
join marking_claimed_batches mcb
	on mcb.marking_window_item_id = mwi.id
join marking_claimed_batch_responses mcbr
  on mcb.id = mcbr.claimed_batch_id
  and mcbr.is_revoked = 0
  and mcbr.is_marked = 1
  and mcbr.is_invalid is null
where mcb.is_training = 0
	and mcb.marking_window_item_id = ?
	and mcbr.taqr_id = ?
limit 1
`

// [window_id, window_id]
export const SQL_SCOR_LEAD_RESPONSES_AVAIL = (config:IScorLeadResponesConfig) => `
-- SQL_SCOR_LEAD_RESPONSES_AVAIL
select mwi.id as item_id
     , mwi.slug as item_slug
     , mwi.caption as item_caption
     , mtc_agg.taqr_id
from (
	select mtc.mwi_id
         , mtc.taqr_id
         , mmcbr.id mmcbr_id
    from marking_taqr_cache mtc
    left join (
        select mcbr.id
             , mcbr.taqr_id
             , mcb.marking_window_item_id
        from marking_window_items mwi
        join marking_claimed_batches mcb
        	on mcb.marking_window_item_id = mwi.id
        	and mwi.marking_window_id = ?
        join marking_claimed_batch_responses mcbr
          on mcb.id = mcbr.claimed_batch_id
          and mcbr.is_revoked = 0
          and mcbr.is_invalid is null
        where mcb.is_training = 0
        group by mcb.marking_window_item_id, mcbr.taqr_id
    ) mmcbr
      on mmcbr.taqr_id = mtc.taqr_id
      and mmcbr.marking_window_item_id = mtc.mwi_id
    where mtc.is_material = 0
    group by mtc.mwi_id, mtc.taqr_id
) mtc_agg
join marking_window_items mwi
	on mtc_agg.mwi_id = mwi.id
	and mwi.marking_window_id = ?
where mtc_agg.mmcbr_id is null
limit ${ +config.limit }
`


// [window_id]
interface IScorLeadResponesConfig {
  limit: number,
  isFlags?:boolean,
  isValidity?:boolean,
  isSensitive?:boolean,
  uid?: number
}
export const SQL_SCOR_LEAD_RESPONSES = (config:IScorLeadResponesConfig) => `
  -- SQL_SCOR_LEAD_RESPONSES
  SELECT mrs_agg.*
       , mso_scor.id mso_scor_id
       , mso_scor.slug as slug
       , mso_scor.value as score
       , mso_flag.slug as flag
       , mso_flag.is_sensitive
       , mspo_lead.id as leader_score_option_id
       , mso_lead.value as leader_score
       , CONCAT(scor.first_name, " ", scor.last_name) as scorer_name
       , scor.first_name
       , scor.last_name
       , scor.contact_email
       , um.value as profile_id
       ${
        config.isSensitive ? `
          , um2.value student_number
          , CONCAT(u.first_name, ' ', u.last_name) student_name
          , sc.name sc_name
          , s.foreign_id s_code
          , s.name s_name
        `: ``
      }
  FROM (
    select mrs.id as mrs_id
        , mrs.last_touched_on as \`timestamp\`
        , mrs.uid
        , mrs.inspected
        , mrs.score_option_id
        , mrs.score_flag_id
        , mrs.meta as meta
        , mcbr.id mcbr_id
        , mcbr.taqr_id
        , mcbr.claimed_batch_id
        , mcbr.is_validity_exact
        , mcbr.is_validity_adj
        , mcbr.is_validity
        , mcbr.is_invalid
        , mcb.component_type
        , mwi.id as item_id
        , mwi.slug as item_slug
        , mwi.caption as item_caption
        , mcbr.window_item_id
    from marking_response_scores mrs
    join marking_claimed_batch_responses mcbr
      on mcbr.id = mrs.batch_response_id
      and mcbr.is_marked = 1
    join marking_claimed_batches mcb
      on mcbr.claimed_batch_id = mcb.id
      and mcb.is_training = 0
    join marking_window_items mwi
      on mwi.id = mcb.marking_window_item_id
    ${
      config.isFlags ? `
        join marking_score_profile_flags mspf
          on mspf.id = mrs.score_flag_id
        join marking_score_options mso_flag
          on mso_flag.id = mspf.score_option_id
          and mso_flag.is_sensitive = ${ config.isSensitive ? 1 : 0 }
      `: ``
    }
    where mwi.marking_window_id in (?)
    ${
      config.isFlags ? `
      and mrs.score_flag_id is not null
      `: ``
    }
    ${
      config.isValidity ? `
      and mcbr.is_validity = 1
      `: ``
    }
    ${
      config.uid ? `
      and mrs.uid = ${ +config.uid }
      `: ``
    }
    order by mrs.id desc
    ${
      config.limit ? `
      limit ${ +config.limit }
      `: ``
    }

  ) mrs_agg
  LEFT JOIN marking_response_selections mrsel
    on mrsel.taqr_id = mrs_agg.taqr_id
    AND mrs_agg.window_item_id = mrsel.window_item_id
  LEFT JOIN users scor
    on scor.id = mrs_agg.uid
  LEFT JOIN marking_score_profile_flags mspf
    on mspf.id = mrs_agg.score_flag_id
  LEFT JOIN marking_score_options mso_flag
    on mso_flag.id = mspf.score_option_id
  LEFT JOIN marking_score_profile_options mspo
    on mspo.id = mrs_agg.score_option_id
    and mspo.is_revoked = 0
  LEFT JOIN marking_score_options mso_scor
    on mso_scor.id = mspo.score_option_id
  LEFT JOIN marking_score_profile_options mspo_lead
    on mspo_lead.id = mrsel.score_option_id
    and mspo_lead.is_revoked = 0
  LEFT JOIN marking_score_options mso_lead
    on mso_lead.id = mspo_lead.score_option_id
  LEFT JOIN user_metas um
    on um.uid = mrs_agg.uid
    and um.key_namespace = 'eqao_scoring'
    and um.key = 'ProfileId'
  ${
    config.isSensitive ? `
    LEFT JOIN test_attempt_question_responses taqr
      on taqr.id = mrs_agg.taqr_id
    LEFT JOIN test_attempts ta
      on ta.id = taqr.test_attempt_id
    left join school_class_test_sessions scts
      on scts.test_session_id  = ta.test_session_id
    left join school_classes sc
      on sc.id = scts.school_class_id
    left join schools s
      on s.group_id  = sc.schl_group_id
    left join users u
      on u.id = ta.uid
    left join user_metas um2
      on um2.key_namespace = 'eqao_sdc'
      and um2.key  = 'StudentOEN'
      and um2.uid = ta.uid
    `: ``
  }
;`

export const SQL_SCOR_LEAD_RESPONSES_SCORER = (config:IScorLeadResponesConfig) => `
  -- SQL_SCOR_LEAD_RESPONSES_SCORER
  SELECT mrs_agg.uid,
    scor.first_name,
    scor.last_name,
    scor.contact_email,
    um.value as profile_id
  FROM (
    SELECT DISTINCT(mrs.uid)
    FROM marking_response_scores mrs
    JOIN marking_claimed_batch_responses mcbr
      on mcbr.id = mrs.batch_response_id
      and mcbr.is_marked = 1
    JOIN marking_claimed_batches mcb
      on mcbr.claimed_batch_id = mcb.id
      and mcb.is_training = 0
    JOIN marking_window_items mwi
      on mwi.id = mcb.marking_window_item_id
    WHERE mwi.marking_window_id in (?)
    ORDER BY mrs.id desc
    ${
      config.limit ? `
        LIMIT ${ +config.limit }
      `: ``
    }
  ) mrs_agg
  LEFT JOIN users scor
    on scor.id = mrs_agg.uid
  LEFT JOIN user_metas um
    on um.uid = mrs_agg.uid
    and um.key_namespace = 'eqao_scoring'
    and um.key = 'ProfileId'
;`

// [window_id]
/*
export const SQL_SCOR_LEAD_BATCHES = `select mcb.id as batch_id,
    -- SQL_SCOR_LEAD_BATCHES
mcb.batch_size, mcb.created_on, mcb.completed_on, mcb.is_marked, mcb.uid, mwi.id as item_id, mwi.caption, mwi.slug, u.first_name, u.last_name, u.contact_email
from marking_claimed_batches mcb
left join marking_window_items mwi on mwi.id = mcb.marking_window_item_id
left join users u on u.id = mcb.uid
where mwi.marking_window_id = ?;`
*/
export const SQL_SCOR_LEAD_BATCHES = `
-- SQL_SCOR_LEAD_BATCHES
select mcb.id as batch_id,
           mcb.batch_size,
           mcb.created_on,
           mcb.completed_on,
           mcb.is_marked,
           mcb.uid,
           mwi.id as item_id,
           mwi.caption,
           mwi.slug,
           u.first_name,
           u.last_name,
           u.contact_email,
           um.value as profile_id
      from marking_claimed_batches mcb
 left join marking_window_items mwi
        on mwi.id = mcb.marking_window_item_id
 left join users u on u.id = mcb.uid
 left join user_metas um
        on um.uid = mcb.uid
       and um.key_namespace = 'eqao_scoring'
       and um.key = 'ProfileId'
where mwi.marking_window_id = ?
and mcb.is_training = 0
order by mcb.id  desc
;`

export const SQL_ASSIGNMENT_FLAGS = `
  select mwi.id mwi_id, IFNULL(mso.slug_code, mso.slug) slug_code from marking_window_items mwi 
  join marking_score_profile_flags mspf 
    on mspf.score_profile_id = mwi.score_profile_id
    and mspf.is_revoked = 0
  join marking_score_options mso 
    on mso.id = mspf.score_option_id 
    and mso.is_revoked = 0
  where mwi.id in (:windowItemIds);
`


export const SQL_SW_QUICK_SUMMARY = `/*SQL_SW_QUICK_SUMMARY*/
  select ta_id
    , uid
    , mwum.value marker_number
    , count(0) n
  from (select t.ta_id
      , mcbr.marker_read_id
      , mcb.uid
      , mcbr.is_invalid
    from (select mtc.ta_id ta_id
        , mtc.taqr_id taqr_id
      from marking_taqr_cache mtc
      where mtc.marking_window_id = :marking_window_id
        and mtc.is_revoked = 0
      group by taqr_id 
      ) t
    join marking_claimed_batch_responses mcbr 
      on mcbr.taqr_id = t.taqr_id
      and (mcbr.is_invalid = 0 or mcbr.is_invalid is null)
      and mcbr.is_revoked = 0
    join marking_claimed_batches mcb 
      on mcbr.claimed_batch_id = mcb.id
    group by t.ta_id, mcbr.marker_read_id 
    ) t2
  join marking_window_user_meta mwum
    on mwum.marker_uid = t2.uid
    and mwum.marking_window_id = :marking_window_id
    and mwum.meta_key  = 'Marker_Number'
    and mwum.is_revoked  = 0
  group by ta_id, uid
  order by n desc
`

export const SQL_TYPE_SLUG_TO_COURSE_CODE = ` /*SQL_TYPE_SLUG_TO_COURSE_CODE*/
select twtt.type_slug
     , ac.course_code
     , ac.course_code_foreign 
from test_window_td_types twtt 
join assessment_courses ac 
	on ac.course_code  = twtt.course_code 
where twtt.type_slug in (:type_slugs)
  and twtt.test_window_id is null
  and twtt.is_revoked = 0 
`

export const SQL_SW_EXTRACT_COURSE_OVERRIDES = ` /*SQL_SW_EXTRACT_COURSE_OVERRIDES*/
select tes.test_window_id
     , tes.twtar_type_slug
     , tes.uid 
     , tes.action_config 
from tw_exceptions_students tes 
where tes.test_window_id in (:test_window_ids)
  and tes.twtar_type_slug in (:twtar_type_slugs) 
  and tes.category = 'COURSE_REMAP'
  and tes.is_revoked = 0
  `

export const SQL_SW_EXTRACT_WITHHOLD_OVERRIDES = ` /*SQL_SW_EXTRACT_WITHHOLD_OVERRIDES*/
select tes.test_window_id
     , tes.twtar_type_slug
     , tes.uid 
     , test_attempt_id
from tw_exceptions_students tes 
where tes.test_window_id in (:test_window_ids)
  and tes.twtar_type_slug in (:twtar_type_slugs) 
  and tes.category = 'WITHHOLD'
  and tes.is_revoked = 0
  `

export const SQL_SW_EXTRACT = `/*SQL_SW_EXTRACT*/
  select /*+ MAX_EXECUTION_TIME(1440000000)*/ 
          twtar.type_slug 
        , ta.id attempt_id
        , um.value stu_gov_id
        , tw.window_code test_term
        , ac.course_code_foreign course_code 
        , twtar.lang
        , twtar.form_code -- will change to tf.source eventually
        , twtt.foreign_component_code component_slug
        , mspg.item_nbr -- used to be msp.order 
        , mwi.item_id 
        , msp.order item_scale_nbr
        , msp.skill_code scale_report_label_short
        , mcbr.read_id read_nbr
        , mcb.uid marker_uid
        , mwum.value marker_profile_id
        , mso.value score_value
        , (CASE WHEN slug_code = 'NO_RESPONSE' THEN 1 ELSE 0 END) is_non_response 
        , (CASE WHEN slug_code = 'INS' THEN 1 ELSE 0 END)  is_insufficient
        , mcb.is_expert_score
        , ( (ifnull(mcbr.is_invalid, 0)=1) or (mcbr.is_revoked=1)) is_deleted
        , ( (ifnull(mcbr.is_invalid, 0)=0) and (mcbr.is_marked=0)) is_deleted_cause_pending
        , (100*mcbr.claimed_batch_group_id + read_id) item_human_score_id
        , s.foreign_id school_code
        , mcbr.is_read_rules_processed is_read_rules_processed
        , mcbr.is_rescore_indic is_rescore_indic
        , mcbr.is_scale_rescore_supressed is_scale_rescore_surpressed
        , mcbr.is_scale_supressed is_scale_surpressed
        , mcbr.read_rule_type read_rule_type
        , mcbr.id mcbr_id
        , mcbr.is_sent_back is_sent_back
        , mcbr.is_rescore
        , mcbr.is_before_rescore
        , mcbr.is_valid_before_rescore
        , mwi.slug task_slug
        , mcbr.marker_read_id marker_read_id
        , mcbr.from_read_rule
        , CASE WHEN tls_id IS NULL THEN 0 ELSE 1 END AS is_local_mark
        , twtar.test_window_id
        , ta.uid stu_uid
  -- #### TABLES ####
  from marking_window_test_window mwtw 
  -- MARKING WINDOW SETUP
  join marking_windows mw 
    on mw.id = mwtw.marking_window_id 
  join marking_window_items mwi 
    on mwi.marking_window_id = mw.id
  join marking_score_profile_groups mspg
    on mspg.id = mwi.score_profile_group_id
  join marking_score_profiles msp 
    on msp.id = mwi.score_profile_id 
  -- MARKING ACTIVITY
  join marking_claimed_batches mcb 
    on mcb.marking_window_item_id  = mwi.id
    and mcb.is_marked = 1
  join marking_claimed_batch_responses mcbr
    on mcbr.claimed_batch_id = mcb.id
  left join marking_response_scores mrs 
    on mrs.batch_response_id  = mcbr.id
  -- .. ASSIGNED SCORE
  left join marking_score_profile_options mspo
    on mspo.id = mrs.score_option_id
  left join marking_score_options mso
    on mso.id = mspo.score_option_id
  -- .. ASSIGNED FLAGS (NOT RELEVANT, ONLY FLAGS THAT ARE CONFIRMED AS SCORES ARE PASSED THROUGH)
  -- .. MARKER IDENTITY
  left join marking_window_user_meta mwum 
    on mwum.marker_uid = mcb.uid 
    and mwum.marking_window_id = mw.id
    and mwum.meta_key = 'Marker_Number' -- abed specific (sort of)
    and mwum.is_revoked = 0 
  -- STUDENT RESPONSE PROVENANCE
  join test_attempt_question_responses taqr 
    on taqr.id = mcbr.taqr_id 
  join test_attempts ta 
    on ta.id = taqr.test_attempt_id 
  join school_class_test_sessions scts 
    on scts.test_session_id = ta.test_session_id
  join school_classes sc 
    on sc.id = scts.school_class_id
  join schools s
    on sc.schl_group_id = s.group_id
  join school_districts sd 
    on sd.group_id = s.schl_dist_group_id
  -- .. STUDENT INFO
  join user_metas um 
    on um.uid = ta.uid 
    and um.key = 'StudentIdentificationNumber'  -- abed specific
  -- TEST DESIGN SETUP (AND ASSOC COURSE)
  join test_window_td_alloc_rules twtar 
    on twtar.id = ta.twtdar_id 
    and twtar.test_window_id  = mwtw.test_window_id -- this helps exclude training materials
    and twtar.is_custom = 0
  join test_windows tw 
    on tw.id = twtar.test_window_id 
  join test_window_td_types twtt 
    on twtt.type_slug = twtar.type_slug
    and twtt.test_window_id is null
    and twtt.is_revoked = 0
  join assessment_courses ac 
    on ac.course_code = twtt.course_code
  -- #### FILTERS ####
  where mwi.marking_window_id = :marking_window_id
  and mcbr.is_rescore = mw.is_rescore
  and (mwum.id is not null or mcb.is_expert_score = 1) -- expert score could be given by a scoring leader or standards confirmer without a marker number
  -- mwtw.test_window_id IN (:test_window_ids)
  -- and mw.is_active = 1
  -- and mwtw.is_sample = 0
  -- and sd.is_sample = 0
  group by mcbr.id
  order by ta.id
      , mwi.slug
      , mcbr.marker_read_id
      , mspg.order
      , msp.order
`

// < 0.5 sec
// {marking_window_id, withholdCategory}
export const SQL_POOLED_WITHHELD_RESPONSES = ` /*SQL_POOLED_WITHHELD_RESPONSES*/
  select distinct
  mtc.ta_id as test_attempt_id,
  mtc.taqr_id
  from marking_taqr_cache mtc
  join test_attempts ta
    on mtc.ta_id = ta.id
  join test_window_td_alloc_rules twtdar
    on ta.twtdar_id = twtdar.id
    and twtdar.is_custom = 0
  join tw_exceptions_students tes 
    on tes.uid = ta.uid
      and tes.test_window_id = twtdar.test_window_id
      and (tes.test_attempt_id = ta.id or tes.test_attempt_id is null)
  where tes.category = :withholdCategory
    and tes.is_revoked = 0
    and mtc.marking_window_id = :marking_window_id;
  `

// < 0.5 sec
// {taqr_ids, marking_window_id}
export const MCBR_AND_SCORES_FOR_TAQR_IN_WINDOW = ` /*MCBR_IDS_FOR_TAQR_IN_WINDOW*/
  select mcbr.id as mcbr_id, claimed_batch_group_id, mrs.id as mrs_id
  from marking_claimed_batch_responses mcbr
  join marking_window_items mwi
    on mwi.id = mcbr.window_item_id
  left join marking_response_scores mrs
	  on mrs.batch_response_id = mcbr.id
  where mcbr.taqr_id in (:taqr_ids)
  and mwi.marking_window_id = :marking_window_id;
  `

// {syncId}
export const SQL_RF_SYNC_IDS_IN_SAME_GROUP = `
  -- SQL_RF_SYNC_IDS_IN_SAME_GROUP, < 0.1 sec
  select distinct 
  mwi_sync.sync_batches_to_wmi_id id
  , mwi_sync.group_to_mwi_id
  , mspg.group_name as caption
  from marking_window_items mwi_target
  join marking_window_items mwi_sync
    on mwi_target.group_to_mwi_id = mwi_sync.group_to_mwi_id
  join marking_score_profile_groups mspg
	on mwi_sync.score_profile_group_id = mspg.id
  where mwi_target.id = :syncId
  order by mwi_sync.id;
`


// < 0.1 sec
export const SQL_RF_MWI_DETAIL_BY_SYNC_ID = `
  -- SQL_RF_MWI_DETAIL_BY_SYNC_ID
  select mwi.id as mwi_id
  , mwi.slug as sync_slug
  , mwi.caption as sync_caption
  , mspg.group_name as sync_score_profile_group_name
  , msp.description as scale_description
  , msp.skill_code as scale_skill_code
  from marking_window_items mwi
    join marking_score_profiles msp
  on mwi.score_profile_id = msp.id
    join marking_score_profile_groups mspg
      on mwi.score_profile_group_id = mspg.id
    where mwi.sync_batches_to_wmi_id = :syncId
    order by msp.order
`

export const SQL_HAS_MCBR_SCAN_RESPONSES = `
    -- SQL_HAS_MCBR_SCAN_RESPONSES, < 0.1 sec
    select tasr.id from
    marking_claimed_batch_responses mcbr
    join test_attempt_scan_responses tasr
      on tasr.taqr_id = mcbr.taqr_id
      and tasr.is_discarded = 0
    where mcbr.id in (:mcbrIds)
    limit 1;
`
export const TEST_ATTEMPT_SCAN_RECORDS = `
      -- TEST_ATTEMPT_SCAN_RECORDS, < 0.1 sec
      select tasr.scan
           , tasr.full_scan
           , tasr.id as tasr_id
           , taqr.id as taqr_id
           , taqr.test_question_id as item_id
           , mspg.id as group_id
           , mspg.group_name
      from test_attempt_question_responses taqr
      join test_attempt_scan_responses tasr
        on tasr.taqr_id = taqr.id
      join marking_window_items mwi 
      on mwi.item_id = taqr.test_question_id
          and mwi.marking_window_id = :mw_id
      join marking_score_profiles msp 
      on msp.id = mwi.score_profile_id
      join marking_score_profile_groups mspg
      on mwi.score_profile_group_id = mspg.id
        where taqr.test_attempt_id = :ta_id
          and taqr.is_invalid  = 0
          and tasr.is_discarded = 0
      group by taqr.id
      order by taqr.id;
`

export const STANDARD_CONFIRMING_SUMMARY = (isPooledSumamry: boolean = false) => {
  return ` -- STANDARD_CONFIRMING_SUMMARY < 0.3 sec
    select sync_id
    , slug
    , caption
    , score_profile_group_name
    , count(distinct taqr_id) as num_responses
    , count(distinct taqr_id) as num_stage_1
    , sum(is_stage_2) as num_stage_2
    , sum(is_stage_3) as num_stage_3
    , sum(is_stage_4) as num_stage_4
    from (
      select *
      , if (is_range_tag = 1 or is_some_scored = 1, 1, 0) as is_stage_2
      , if (is_some_scored = 1 or is_prop_tag = 1, 1, 0) as is_stage_3
      , if (is_prop_tag = 1 or is_included = 1, 1, 0) as is_stage_4
      from (
        select mwi.sync_batches_to_wmi_id as sync_id
        , mwi.slug
        , mwi.caption
        , mspg.group_name as score_profile_group_name
        , mtc.taqr_id
        , if (mrs.tag1 is not null, 1, 0) is_range_tag
        , if (mrs.tag2 is not null, 1, 0) is_prop_tag
        , if (mrs.mwi_id_to_score_option_id is not null and mrs.mwi_id_to_score_option_id != '{}', 1, 0) is_some_scored
        , if (rset.id is not null, 1, 0) is_included
        from marking_window_items mwi
        join marking_score_profile_groups mspg
        on mwi.score_profile_group_id = mspg.id
        left join marking_taqr_cache mtc
        on mtc.mwi_id = mwi.id
          and mtc.marking_window_id = mwi.marking_window_id
        left join marking_response_selections mrs
          on mtc.taqr_id = mrs.taqr_id
          and mwi.sync_batches_to_wmi_id = mrs.sync_batches_to_wmi_id
        left join marking_response_set_selections mrset
          on mrset.selection_id = mrs.id
        left join marking_response_set rset
        on mrset.set_id = rset.id 
        and (rset.is_revoked is null or rset.is_revoked = 0)
        and rset.sync_batches_to_wmi_id = mwi.sync_batches_to_wmi_id
        where mwi.marking_window_id = :marking_window_id
          ${isPooledSumamry?"and mtc.is_standard_confirming = 1":""}
          group by mtc.taqr_id
      ) t
    ) t2
    group by sync_id;
`
} 

export const STANDARD_CONFIRMING_POOL = (limit: number) => `
  -- STANDARD_CONFIRMING_POOL < 0.1s
  SELECT mtc.id 
  FROM marking_taqr_cache mtc 
  where mtc.marking_window_id = :marking_window_id 
   and mtc.is_standard_confirming = 0
   and mtc.mwi_id = :sync_id
  group by mtc.taqr_id 
  limit ${limit}
`